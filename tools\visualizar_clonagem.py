#!/usr/bin/env python3
"""
Visualizador da clonagem de frontend.
"""

import json
import os
from pathlib import Path

def visualizar_clonagem(pasta="cloned_terra_com_br"):
    """Visualizar o que foi clonado."""
    pasta_path = Path(pasta)
    
    if not pasta_path.exists():
        print(f"❌ Pasta {pasta} não encontrada!")
        return
    
    print(f"🎨 VISUALIZANDO CLONAGEM: {pasta}")
    print("="*60)
    
    # 1. Resumo geral
    print("📊 RESUMO GERAL:")
    
    # Contar arquivos por tipo
    css_files = list(pasta_path.glob("css/*.css"))
    js_files = list(pasta_path.glob("js/*.js"))
    img_files = list(pasta_path.glob("images/*"))
    font_files = list(pasta_path.glob("fonts/*"))
    
    print(f"   📄 HTML: {len(list(pasta_path.glob('html/*.html')))} arquivos")
    print(f"   🎨 CSS: {len(css_files)} arquivos")
    print(f"   ⚡ JavaScript: {len(js_files)} arquivos")
    print(f"   🖼️ Imagens: {len(img_files)} arquivos")
    print(f"   🔤 Fontes: {len(font_files)} arquivos")
    
    # 2. Estrutura do site
    structure_file = pasta_path / "data" / "structure.json"
    if structure_file.exists():
        print(f"\n🏗️ ESTRUTURA DO SITE:")
        with open(structure_file, 'r', encoding='utf-8') as f:
            structure = json.load(f)
        
        print(f"   📄 Título: {structure.get('title', 'N/A')}")
        print(f"   🏷️ Meta tags: {len(structure.get('meta_tags', []))}")
        
        # Headings
        headings = structure.get('headings', {})
        total_headings = sum(len(h) for h in headings.values())
        print(f"   📋 Headings: {total_headings} total")
        for level, texts in headings.items():
            if texts:
                print(f"      {level.upper()}: {len(texts)} ({texts[0][:50]}...)")
        
        # Navegação
        nav = structure.get('navigation', [])
        if nav:
            print(f"   🧭 Navegação: {len(nav)} menus")
            for i, menu in enumerate(nav[:2]):
                print(f"      Menu {i+1}: {len(menu)} links")
        
        # Formulários
        forms = structure.get('forms', [])
        if forms:
            print(f"   📝 Formulários: {len(forms)}")
            for i, form in enumerate(forms):
                print(f"      Form {i+1}: {len(form.get('inputs', []))} campos")
    
    # 3. Análise de CSS
    print(f"\n🎨 ANÁLISE DE CSS:")
    css_externos = [f for f in css_files if 'external' in f.name]
    css_inline = [f for f in css_files if 'inline' in f.name]
    
    print(f"   📁 Externos: {len(css_externos)}")
    print(f"   📝 Inline: {len(css_inline)}")
    
    # Tamanhos dos CSS
    if css_externos:
        total_size = sum(f.stat().st_size for f in css_externos)
        print(f"   📏 Tamanho total CSS: {total_size:,} bytes")
        
        # Maiores arquivos CSS
        css_sorted = sorted(css_externos, key=lambda f: f.stat().st_size, reverse=True)
        print(f"   📊 Maiores CSS:")
        for css in css_sorted[:5]:
            size = css.stat().st_size
            print(f"      {css.name}: {size:,} bytes")
    
    # 4. Análise de JavaScript
    print(f"\n⚡ ANÁLISE DE JAVASCRIPT:")
    js_externos = [f for f in js_files if 'external' in f.name]
    js_inline = [f for f in js_files if 'inline' in f.name]
    
    print(f"   📁 Externos: {len(js_externos)}")
    print(f"   📝 Inline: {len(js_inline)}")
    
    if js_externos:
        total_size = sum(f.stat().st_size for f in js_externos)
        print(f"   📏 Tamanho total JS: {total_size:,} bytes")
        
        # Maiores arquivos JS
        js_sorted = sorted(js_externos, key=lambda f: f.stat().st_size, reverse=True)
        print(f"   📊 Maiores JS:")
        for js in js_sorted[:5]:
            size = js.stat().st_size
            print(f"      {js.name}: {size:,} bytes")
    
    # 5. Análise de imagens
    print(f"\n🖼️ ANÁLISE DE IMAGENS:")
    if img_files:
        # Por extensão
        extensoes = {}
        total_size = 0
        for img in img_files:
            ext = img.suffix.lower()
            size = img.stat().st_size
            total_size += size
            
            if ext not in extensoes:
                extensoes[ext] = {'count': 0, 'size': 0}
            extensoes[ext]['count'] += 1
            extensoes[ext]['size'] += size
        
        print(f"   📏 Tamanho total: {total_size:,} bytes")
        print(f"   📊 Por tipo:")
        for ext, data in extensoes.items():
            print(f"      {ext}: {data['count']} arquivos, {data['size']:,} bytes")
        
        # Maiores imagens
        img_sorted = sorted(img_files, key=lambda f: f.stat().st_size, reverse=True)
        print(f"   📊 Maiores imagens:")
        for img in img_sorted[:5]:
            size = img.stat().st_size
            print(f"      {img.name}: {size:,} bytes")
    
    # 6. Como usar
    print(f"\n🚀 COMO USAR A CLONAGEM:")
    print(f"   📁 Pasta: {pasta_path.absolute()}")
    print(f"   📄 HTML original: {pasta}/html/original.html")
    print(f"   🎨 CSS: {pasta}/css/ ({len(css_files)} arquivos)")
    print(f"   ⚡ JavaScript: {pasta}/js/ ({len(js_files)} arquivos)")
    print(f"   🖼️ Imagens: {pasta}/images/ ({len(img_files)} arquivos)")
    print(f"   📊 Estrutura: {pasta}/data/structure.json")
    
    # 7. Próximos passos
    print(f"\n🎯 PRÓXIMOS PASSOS PARA REPLICAR:")
    print(f"   1. 📄 Abra: {pasta}/html/original.html")
    print(f"   2. 🎨 Adapte CSS em: {pasta}/css/")
    print(f"   3. ⚡ Adapte JS em: {pasta}/js/")
    print(f"   4. 🖼️ Use imagens de: {pasta}/images/")
    print(f"   5. 📊 Consulte estrutura: {pasta}/data/structure.json")
    print(f"   6. 🔧 Modifique caminhos para seu projeto")
    
    print(f"\n✅ CLONAGEM COMPLETA E PRONTA PARA USO!")


def main():
    """Função principal."""
    print("🔍 VISUALIZADOR DE CLONAGEM DE FRONTEND")
    print("="*50)
    
    # Procurar pastas de clonagem
    pastas_clonagem = [p for p in Path('.').iterdir() if p.is_dir() and p.name.startswith('cloned_')]
    
    if not pastas_clonagem:
        print("❌ Nenhuma clonagem encontrada!")
        print("💡 Execute primeiro: python frontend_cloner.py")
        return
    
    if len(pastas_clonagem) == 1:
        pasta = pastas_clonagem[0].name
        print(f"📁 Encontrada clonagem: {pasta}")
        visualizar_clonagem(pasta)
    else:
        print("📁 Clonagens encontradas:")
        for i, pasta in enumerate(pastas_clonagem, 1):
            print(f"   {i}. {pasta.name}")
        
        try:
            escolha = int(input("\nEscolha uma clonagem (número): ")) - 1
            if 0 <= escolha < len(pastas_clonagem):
                visualizar_clonagem(pastas_clonagem[escolha].name)
            else:
                print("❌ Escolha inválida!")
        except ValueError:
            print("❌ Digite um número válido!")


if __name__ == "__main__":
    main()
