#!/usr/bin/env python3
"""
Teste básico do MVP do WebScraper.

Este script testa os componentes principais implementados na Fase 0.
"""

import asyncio
import sys
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.config import get_config
from src.core.http_client import AsyncHTTPClient
from src.core.logging import configure_logging, get_logger
from src.core.storage import FilesystemStorage, StorageManager
from src.crawl.sitemap import SitemapDiscovery
from src.domains.generic import GenericParser


async def test_basic_components():
    """Testar componentes básicos do sistema."""
    # Configurar logging
    configure_logging(level="DEBUG", structured=True)
    logger = get_logger(__name__)
    
    logger.info("Starting MVP test")
    
    try:
        # 1. Testar configuração
        logger.info("Testing configuration loading")
        config = get_config()
        logger.info("Configuration loaded", domains_count=len(config.domains))
        
        # 2. Testar cliente HTTP
        logger.info("Testing HTTP client")
        async with AsyncHTTPClient(config) as http_client:
            # Testar requisição simples
            response = await http_client.get("https://httpbin.org/html")
            logger.info(
                "HTTP request successful",
                status_code=response.status_code,
                content_length=len(response.content),
            )
        
        # 3. Testar parser
        logger.info("Testing generic parser")
        domain_config = config.get_domain_config("httpbin.org")
        parser = GenericParser(domain_config)
        
        # Parse da resposta
        page_data = await parser.parse(response)
        logger.info(
            "Parsing successful",
            title=page_data.title,
            quality_score=page_data.quality_score,
            word_count=page_data.word_count,
        )
        
        # 4. Testar storage
        logger.info("Testing storage")
        storage_backend = FilesystemStorage("./test_data")
        storage_manager = StorageManager(storage_backend)
        
        # Salvar dados
        saved_paths = await storage_manager.save_page(
            page_data,
            raw_content=response.content,
            raw_metadata={
                "status_code": response.status_code,
                "headers": response.headers.dict(),
            }
        )
        logger.info("Data saved", paths=saved_paths)
        
        # Carregar dados
        loaded_data = await storage_manager.load_page(str(page_data.url))
        if loaded_data:
            logger.info("Data loaded successfully", title=loaded_data.title)
        else:
            logger.error("Failed to load data")
        
        # 5. Testar descoberta de sitemap (opcional, pode falhar)
        logger.info("Testing sitemap discovery")
        try:
            async with AsyncHTTPClient(config) as http_client:
                sitemap_discovery = SitemapDiscovery(http_client, config)
                urls = await sitemap_discovery.discover_urls("httpbin.org", max_urls=5)
                logger.info("Sitemap discovery", urls_found=len(urls))
        except Exception as e:
            logger.warning("Sitemap discovery failed", error=str(e))
        
        logger.info("MVP test completed successfully! 🎉")
        return True
        
    except Exception as e:
        logger.error("MVP test failed", error=str(e), exc_info=True)
        return False


async def test_flow():
    """Testar flow básico."""
    logger = get_logger(__name__)
    
    try:
        from src.flows.mvp_flow import webscraper_daily_flow
        
        logger.info("Testing basic flow")
        
        result = await webscraper_daily_flow(
            domain="httpbin.org",
            max_pages=2,
            dry_run=True,
        )
        
        logger.info("Flow test completed", result=result)
        return True
        
    except Exception as e:
        logger.error("Flow test failed", error=str(e), exc_info=True)
        return False


async def main():
    """Função principal de teste."""
    print("🧪 Testando MVP do WebScraper...")
    
    # Testar componentes básicos
    basic_success = await test_basic_components()
    
    if basic_success:
        print("✅ Componentes básicos funcionando!")
        
        # Testar flow
        flow_success = await test_flow()
        
        if flow_success:
            print("✅ Flow básico funcionando!")
            print("🎉 MVP está funcionando corretamente!")
            return 0
        else:
            print("❌ Flow básico falhou")
            return 1
    else:
        print("❌ Componentes básicos falharam")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
