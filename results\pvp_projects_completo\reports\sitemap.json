[{"url": "https://pvpprojects.netlify.app", "title": "", "word_count": 277, "last_mapped": "2025-09-07T21:51:51.901384"}, {"url": "https://pvpprojects.netlify.app/projetos-dedicada/", "title": "", "word_count": 236, "last_mapped": "2025-09-07T21:51:52.077731"}, {"url": "https://pvpprojects.netlify.app/projetos/casa-gp/", "title": "", "word_count": 219, "last_mapped": "2025-09-07T21:51:52.290998"}, {"url": "https://pvpprojects.netlify.app/servicos/hidrossanitarios/", "title": "", "word_count": 233, "last_mapped": "2025-09-07T21:51:52.477518"}, {"url": "https://pvpprojects.netlify.app/contato/", "title": "", "word_count": 228, "last_mapped": "2025-09-07T21:51:52.705393"}, {"url": "https://pvpprojects.netlify.app/projetos/categoria/predial/", "title": "", "word_count": 210, "last_mapped": "2025-09-07T21:51:52.891586"}]