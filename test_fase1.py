#!/usr/bin/env python3
"""
Teste da Fase 1 - Dinâmica & Qualidade.

Este script testa as novas funcionalidades implementadas na Fase 1:
- Playwright para páginas dinâmicas
- Sistema de versionamento por hash
- Normalização avançada de conteúdo
- Score de qualidade aprimorado
"""

import asyncio
import sys
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.browser import PlaywrightClient
from src.core.config import get_config
from src.core.http_client import AsyncHTTPClient
from src.core.logging import configure_logging, get_logger
from src.core.normalize import ContentNormalizer
from src.core.quality import AdvancedQualityScorer
from src.core.storage import FilesystemStorage, StorageManager
from src.core.versioning import ContentVersionManager
from src.domains.generic import GenericParser


async def test_playwright_integration():
    """Testar integração com Playwright."""
    logger = get_logger(__name__)
    logger.info("Testing Playwright integration")
    
    try:
        config = get_config()
        
        # Testar Playwright client
        async with PlaywrightClient(config) as playwright_client:
            domain_config = config.get_domain_config("httpbin.org")
            
            # Fazer fetch de página dinâmica
            response = await playwright_client.fetch_dynamic_page(
                "https://httpbin.org/html",
                domain_config
            )
            
            logger.info(
                "Playwright fetch successful",
                status_code=response.status_code,
                content_length=len(response.content),
                duration_ms=response.duration_ms,
            )
            
            # Obter estatísticas
            stats = playwright_client.get_stats()
            logger.info("Playwright stats", **stats)
            
            return True
            
    except Exception as e:
        logger.error("Playwright test failed", error=str(e), exc_info=True)
        return False


async def test_advanced_quality_scoring():
    """Testar sistema de qualidade avançado."""
    logger = get_logger(__name__)
    logger.info("Testing advanced quality scoring")
    
    try:
        config = get_config()
        
        # Fazer requisição HTTP
        async with AsyncHTTPClient(config) as http_client:
            response = await http_client.get("https://httpbin.org/html")
        
        # Parse com parser genérico
        domain_config = config.get_domain_config("httpbin.org")
        parser = GenericParser(domain_config, config)
        page_data = await parser.parse(response)
        
        # Normalização avançada
        normalizer = ContentNormalizer()
        normalized_data = normalizer.normalize_page_data(page_data, str(response.url))
        
        # Score de qualidade avançado
        quality_scorer = AdvancedQualityScorer()
        score, metrics = quality_scorer.calculate_quality_score(
            normalized_data, domain_config.quality
        )
        
        # Indicadores de qualidade
        content_indicators = normalizer.get_content_quality_indicators(normalized_data)
        
        logger.info(
            "Advanced quality scoring completed",
            url=response.url,
            overall_score=score,
            quality_tier=metrics.quality_tier,
            confidence=metrics.confidence,
            content_length_score=metrics.content_length_score,
            readability_score=metrics.readability_score,
            information_density_score=metrics.information_density_score,
            content_indicators=content_indicators,
        )
        
        # Verificações
        assert score > 0
        assert metrics.quality_tier in ["poor", "fair", "good", "excellent"]
        assert 0 <= metrics.confidence <= 1
        assert content_indicators["overall"] >= 0
        
        return True
        
    except Exception as e:
        logger.error("Advanced quality scoring test failed", error=str(e), exc_info=True)
        return False


async def test_content_versioning():
    """Testar sistema de versionamento de conteúdo."""
    logger = get_logger(__name__)
    logger.info("Testing content versioning")
    
    try:
        config = get_config()
        version_manager = ContentVersionManager()
        
        # Simular duas versões de conteúdo
        async with AsyncHTTPClient(config) as http_client:
            response = await http_client.get("https://httpbin.org/html")
        
        domain_config = config.get_domain_config("httpbin.org")
        parser = GenericParser(domain_config, config)
        
        # Primeira versão
        page_data_v1 = await parser.parse(response)
        
        # Detectar mudanças (primeira vez)
        content_changed, semantic_changed, changes = version_manager.detect_changes(
            str(response.url), page_data_v1
        )
        
        # Criar primeira versão
        version_v1 = version_manager.create_version(str(response.url), page_data_v1, changes)
        
        logger.info(
            "First version created",
            version_number=version_v1.version_number,
            content_hash=version_v1.content_hash[:16],
            changes=changes,
        )
        
        # Simular segunda versão (mesmo conteúdo)
        content_changed_v2, semantic_changed_v2, changes_v2 = version_manager.detect_changes(
            str(response.url), page_data_v1
        )
        
        logger.info(
            "Second version check",
            content_changed=content_changed_v2,
            semantic_changed=semantic_changed_v2,
            changes=changes_v2,
        )
        
        # Obter estatísticas
        stats = version_manager.get_stats()
        logger.info("Versioning stats", **stats)
        
        # Verificações
        assert version_v1.version_number == 1
        assert content_changed is True  # Primeira versão sempre é mudança
        assert semantic_changed is True
        assert content_changed_v2 is False  # Segunda vez, sem mudança
        assert semantic_changed_v2 is False
        
        return True
        
    except Exception as e:
        logger.error("Content versioning test failed", error=str(e), exc_info=True)
        return False


async def test_advanced_storage():
    """Testar sistema de storage avançado."""
    logger = get_logger(__name__)
    logger.info("Testing advanced storage")
    
    try:
        config = get_config()
        
        # Configurar storage
        storage_backend = FilesystemStorage("./test_data_fase1")
        storage_manager = StorageManager(storage_backend)
        
        # Fazer requisição e parse
        async with AsyncHTTPClient(config) as http_client:
            response = await http_client.get("https://httpbin.org/html")
        
        domain_config = config.get_domain_config("httpbin.org")
        parser = GenericParser(domain_config, config)
        page_data = await parser.parse(response)
        
        # Normalização
        normalizer = ContentNormalizer()
        normalized_data = normalizer.normalize_page_data(page_data, str(response.url))
        
        # Salvar dados
        saved_paths = await storage_manager.save_page(
            normalized_data,
            raw_content=response.content,
            raw_metadata={
                "status_code": response.status_code,
                "headers": response.headers.model_dump(),
                "test_metadata": "fase1_test",
            }
        )
        
        logger.info("Data saved", paths=saved_paths)
        
        # Carregar dados
        loaded_data = await storage_manager.load_page(str(response.url))
        
        if loaded_data:
            logger.info(
                "Data loaded successfully",
                title=loaded_data.title,
                quality_score=loaded_data.quality_score,
            )
        
        # Obter estatísticas do domínio
        domain_stats = await storage_manager.get_domain_stats("httpbin.org")
        logger.info("Domain stats", **domain_stats)
        
        # Verificações
        assert "processed" in saved_paths
        assert loaded_data is not None
        assert loaded_data.title == normalized_data.title
        assert domain_stats["total_pages"] >= 1
        
        return True
        
    except Exception as e:
        logger.error("Advanced storage test failed", error=str(e), exc_info=True)
        return False


async def test_full_pipeline_fase1():
    """Testar pipeline completo da Fase 1."""
    logger = get_logger(__name__)
    logger.info("Testing full Fase 1 pipeline")
    
    try:
        from src.flows.mvp_flow import webscraper_daily_flow
        
        # Executar flow com funcionalidades da Fase 1
        result = await webscraper_daily_flow(
            domain="httpbin.org",
            max_pages=2,
            dry_run=False,  # Testar salvamento real
        )
        
        logger.info("Fase 1 flow completed", result=result)
        
        # Verificações específicas da Fase 1
        assert "versions_created" in result["save_stats"]
        assert "version_stats" in result["save_stats"]
        assert "versioning_stats" in result["save_stats"]
        
        return True
        
    except Exception as e:
        logger.error("Full pipeline test failed", error=str(e), exc_info=True)
        return False


async def main():
    """Função principal de teste da Fase 1."""
    print("🧪 Testando Fase 1 - Dinâmica & Qualidade...")
    
    # Configurar logging
    configure_logging(level="INFO", structured=True)
    
    tests = [
        ("Playwright Integration", test_playwright_integration),
        ("Advanced Quality Scoring", test_advanced_quality_scoring),
        ("Content Versioning", test_content_versioning),
        ("Advanced Storage", test_advanced_storage),
        ("Full Pipeline Fase 1", test_full_pipeline_fase1),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Executando: {test_name}")
        try:
            success = await test_func()
            if success:
                print(f"✅ {test_name}: PASSOU")
                results.append(True)
            else:
                print(f"❌ {test_name}: FALHOU")
                results.append(False)
        except Exception as e:
            print(f"💥 {test_name}: ERRO - {e}")
            results.append(False)
    
    # Resumo final
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Resumo dos Testes da Fase 1:")
    print(f"✅ Passou: {passed}/{total}")
    print(f"❌ Falhou: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 Fase 1 implementada com sucesso!")
        return 0
    else:
        print("⚠️ Alguns testes falharam. Verifique os logs.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
