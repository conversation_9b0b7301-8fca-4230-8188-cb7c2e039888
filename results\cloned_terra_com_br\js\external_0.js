/*! zaz-ui-t360 - v1.0.25 - 06/08/2025 -- 1:38pm */

const isHomePage=pathname=>{"use strict";return""===pathname.replace("/","")},isChannelPage=pathname=>{"use strict";const channelPattern=/\/[a-z\-]+\/$/;return channelPattern.test(pathname)},isContentPage=pathname=>{"use strict";const contentPattern=/\/[a-z0-9\-\/]+,[a-z0-9]{40}\.html$/;return contentPattern.test(pathname)},determineTransitionType=(oldNavigationEntry,newNavigationEntry)=>{"use strict";if(!oldNavigationEntry||!newNavigationEntry)return"unknown";try{var currentPathname=new URL(oldNavigationEntry).pathname,destinationPathname=new URL(newNavigationEntry).pathname;if(isHomePage(currentPathname)&&(isChannelPage(destinationPathname)||isContentPage(destinationPathname)))return"forwards";if(isChannelPage(currentPathname)){if(isContentPage(destinationPathname)||isChannelPage(destinationPathname))return"forwards";if(isHomePage(destinationPathname))return"backwards"}if(isContentPage(currentPathname)){if(isContentPage(destinationPathname))return"forwards";if(isChannelPage(destinationPathname)||isHomePage(destinationPathname))return"backwards"}}catch(error){console.error("Error on parsing URL",error,oldNavigationEntry,newNavigationEntry)}return"unknown"};window.addEventListener("pageswap",async e=>{"use strict";var transitionType;e.viewTransition&&(transitionType=determineTransitionType(e.activation.from.url,e.activation.entry.url),e.viewTransition.types.add(transitionType),sessionStorage.setItem("transitionType",transitionType))}),window.addEventListener("pagereveal",async e=>{"use strict";var transitionType;e.viewTransition&&(transitionType=sessionStorage.getItem("transitionType"),e.viewTransition.types.add(transitionType))});