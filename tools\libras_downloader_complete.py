#!/usr/bin/env python3
"""
🎬 LIBRAS VIDEO DOWNLOADER COMPLETO
Sistema para download de TODOS os vídeos de Libras do site https://libras.cin.ufpe.br

Características:
- Download de todas as 69 páginas (1364 sinais, 4089 vídeos)
- Organização por sinal e articulador
- Sistema de retry e rate limiting
- Relatórios detalhados
- Controle de progresso
- Verificação de integridade
"""

import requests
from bs4 import BeautifulSoup
import re
import os
import time
import json
from pathlib import Path
from urllib.parse import urljoin
from datetime import datetime
import logging
from typing import List, Dict, Optional
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('libras_downloader_complete.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LibrasCompleteDownloader:
    """Downloader completo para todos os vídeos de Libras."""
    
    def __init__(self, output_dir: str = "libras_videos_complete", max_workers: int = 3):
        self.base_url = "https://libras.cin.ufpe.br"
        self.output_dir = Path(output_dir)
        self.max_workers = max_workers
        
        # Configurar sessão
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Estatísticas
        self.stats = {
            'pages_processed': 0,
            'total_pages': 69,
            'videos_found': 0,
            'videos_downloaded': 0,
            'videos_skipped': 0,
            'errors': 0,
            'start_time': datetime.now().isoformat(),
            'total_size_bytes': 0
        }
        
        # Controle de progresso
        self.progress_lock = threading.Lock()
        
        # Criar diretórios
        self.setup_directories()
        
    def setup_directories(self):
        """Criar estrutura de diretórios."""
        self.output_dir.mkdir(exist_ok=True)
        (self.output_dir / "videos").mkdir(exist_ok=True)
        (self.output_dir / "metadata").mkdir(exist_ok=True)
        (self.output_dir / "reports").mkdir(exist_ok=True)
        (self.output_dir / "progress").mkdir(exist_ok=True)
        
        logger.info(f"📁 Diretórios criados em: {self.output_dir}")
    
    def extract_video_links_from_page(self, page_num: int) -> List[Dict]:
        """Extrair todos os links de vídeo de uma página específica."""
        page_url = f"{self.base_url}/?page={page_num}"
        
        try:
            logger.info(f"🔍 Processando página {page_num}/69")
            
            response = self.session.get(page_url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            video_links = []
            table_rows = soup.find_all('tr')
            current_signal = None
            
            for row in table_rows[1:]:  # Pular cabeçalho
                cells = row.find_all(['td', 'th'])
                
                if len(cells) >= 5:
                    # Primeira célula tem o nome do sinal
                    signal_name = cells[0].get_text().strip()
                    if signal_name and len(signal_name) > 1:
                        current_signal = signal_name
                    
                    # Células 3, 4, 5 têm os links dos articuladores
                    for i, articulador_num in enumerate([1, 2, 3], start=2):
                        if i < len(cells):
                            cell = cells[i]
                            link = cell.find('a')
                            if link and link.get('href'):
                                video_links.append({
                                    'signal_name': current_signal,
                                    'articulador': articulador_num,
                                    'page_url': link.get('href'),
                                    'full_url': urljoin(page_url, link.get('href')),
                                    'source_page': page_num,
                                    'page_url_source': page_url
                                })
            
            with self.progress_lock:
                self.stats['pages_processed'] += 1
                self.stats['videos_found'] += len(video_links)
            
            logger.info(f"✅ Página {page_num}: {len(video_links)} vídeos encontrados")
            
            # Salvar progresso
            self.save_page_progress(page_num, video_links)
            
            return video_links
            
        except Exception as e:
            logger.error(f"❌ Erro ao processar página {page_num}: {e}")
            with self.progress_lock:
                self.stats['errors'] += 1
            return []
    
    def extract_video_url_from_sign_page(self, video_link: Dict) -> Optional[str]:
        """Extrair URL real do vídeo de uma página de sinal."""
        try:
            response = self.session.get(video_link['full_url'], timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Procurar por tags source
            source_tags = soup.find_all('source')
            
            if source_tags:
                # Determinar qual source usar baseado no intérprete
                interpreter_match = re.search(r'#interpreter_(\d+)', video_link['full_url'])
                interpreter_num = int(interpreter_match.group(1)) if interpreter_match else 1
                
                # Os vídeos estão em ordem: interpreter_3, interpreter_2, interpreter_1
                source_index = 3 - interpreter_num
                
                if 0 <= source_index < len(source_tags):
                    video_url = source_tags[source_index].get('src')
                else:
                    video_url = source_tags[0].get('src')  # Fallback
                
                if video_url:
                    return urljoin(self.base_url, video_url)
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Erro ao extrair vídeo de {video_link['full_url']}: {e}")
            return None
    
    def download_video(self, video_link: Dict) -> bool:
        """Fazer download de um vídeo específico."""
        try:
            # Extrair URL do vídeo
            video_url = self.extract_video_url_from_sign_page(video_link)
            
            if not video_url:
                logger.warning(f"⚠️ URL não encontrada para {video_link['signal_name']} - Articulador {video_link['articulador']}")
                return False
            
            # Criar nome de arquivo seguro
            safe_signal_name = re.sub(r'[^\w\s-]', '', video_link['signal_name']).strip()
            safe_signal_name = re.sub(r'[-\s]+', '_', safe_signal_name)
            
            filename = f"{safe_signal_name}_articulador_{video_link['articulador']}.mp4"
            filepath = self.output_dir / "videos" / filename
            
            # Verificar se já existe
            if filepath.exists():
                file_size = filepath.stat().st_size
                if file_size > 1000:  # Arquivo válido (> 1KB)
                    logger.info(f"⏭️ Já existe: {filename}")
                    with self.progress_lock:
                        self.stats['videos_skipped'] += 1
                        self.stats['total_size_bytes'] += file_size
                    return True
            
            logger.info(f"⬇️ Baixando: {filename}")
            
            response = self.session.get(video_url, timeout=60, stream=True)
            response.raise_for_status()
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            file_size = filepath.stat().st_size
            
            with self.progress_lock:
                self.stats['videos_downloaded'] += 1
                self.stats['total_size_bytes'] += file_size
            
            logger.info(f"✅ Download concluído: {filename} ({file_size:,} bytes)")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao baixar vídeo {video_link['signal_name']}: {e}")
            with self.progress_lock:
                self.stats['errors'] += 1
            return False
    
    def save_page_progress(self, page_num: int, video_links: List[Dict]):
        """Salvar progresso de uma página."""
        progress_file = self.output_dir / "progress" / f"page_{page_num:02d}.json"
        
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump({
                'page_num': page_num,
                'video_count': len(video_links),
                'video_links': video_links,
                'processed_at': datetime.now().isoformat()
            }, f, indent=2, ensure_ascii=False)
    
    def save_final_metadata(self, all_video_links: List[Dict]):
        """Salvar metadados finais."""
        metadata = {
            'total_videos': len(all_video_links),
            'stats': self.stats.copy(),
            'video_links': all_video_links,
            'generated_at': datetime.now().isoformat()
        }
        
        metadata_file = self.output_dir / "metadata" / "complete_metadata.json"
        
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Metadados salvos: {metadata_file}")
    
    def generate_final_report(self):
        """Gerar relatório final completo."""
        start_time = datetime.fromisoformat(self.stats['start_time'])
        duration = datetime.now() - start_time
        
        # Contar arquivos baixados
        downloaded_files = list((self.output_dir / "videos").glob("*.mp4"))
        total_files_size = sum(f.stat().st_size for f in downloaded_files)
        
        report = f"""
🎬 RELATÓRIO COMPLETO - LIBRAS VIDEO DOWNLOADER
{'='*70}

📊 ESTATÍSTICAS FINAIS:
- Páginas processadas: {self.stats['pages_processed']}/{self.stats['total_pages']}
- Vídeos encontrados: {self.stats['videos_found']}
- Vídeos baixados: {self.stats['videos_downloaded']}
- Vídeos já existentes: {self.stats['videos_skipped']}
- Total de arquivos: {len(downloaded_files)}
- Erros: {self.stats['errors']}
- Tempo total: {duration}

💾 ARMAZENAMENTO:
- Tamanho total: {total_files_size:,} bytes ({total_files_size/1024/1024:.1f} MB)
- Diretório: {self.output_dir}
- Vídeos: {self.output_dir}/videos/
- Metadados: {self.output_dir}/metadata/

🎯 PERFORMANCE:
- Taxa de sucesso: {(self.stats['videos_downloaded']/max(self.stats['videos_found'], 1)*100):.1f}%
- Velocidade média: {self.stats['videos_downloaded']/max(duration.total_seconds()/60, 1):.1f} vídeos/min
- Páginas por minuto: {self.stats['pages_processed']/max(duration.total_seconds()/60, 1):.1f}

📁 ESTRUTURA DE ARQUIVOS:
- {len(downloaded_files)} arquivos .mp4
- Organização: [SINAL]_articulador_[1-3].mp4
- Metadados completos em JSON
- Logs detalhados disponíveis

🎉 DOWNLOAD COMPLETO DA BASE DE DADOS V-LIBRASIL!
"""
        
        report_file = self.output_dir / "reports" / f"final_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        logger.info(f"📋 Relatório final salvo: {report_file}")
    
    def run_complete_download(self):
        """Executar o download completo de todos os vídeos."""
        logger.info("🚀 INICIANDO DOWNLOAD COMPLETO DE VÍDEOS DE LIBRAS")
        logger.info("="*70)
        logger.info("📊 Estimativa: 69 páginas, ~1364 sinais, ~4089 vídeos")
        
        # Fase 1: Extrair todos os links
        logger.info("\n📄 FASE 1: EXTRAINDO LINKS DE TODAS AS PÁGINAS")
        
        all_video_links = []
        
        for page_num in range(1, 70):  # Páginas 1-69
            video_links = self.extract_video_links_from_page(page_num)
            all_video_links.extend(video_links)
            
            # Rate limiting entre páginas
            time.sleep(1)
            
            # Log de progresso
            if page_num % 10 == 0:
                logger.info(f"📊 Progresso: {page_num}/69 páginas, {len(all_video_links)} vídeos encontrados")
        
        logger.info(f"✅ FASE 1 CONCLUÍDA: {len(all_video_links)} vídeos encontrados")
        
        # Salvar metadados
        self.save_final_metadata(all_video_links)
        
        # Fase 2: Download dos vídeos
        logger.info(f"\n⬇️ FASE 2: DOWNLOAD DOS VÍDEOS")
        logger.info(f"🎯 Total para baixar: {len(all_video_links)} vídeos")
        
        # Download com threading para melhor performance
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(self.download_video, video_link) for video_link in all_video_links]
            
            for i, future in enumerate(as_completed(futures), 1):
                try:
                    success = future.result()
                    if i % 50 == 0:  # Log a cada 50 downloads
                        logger.info(f"📊 Progresso downloads: {i}/{len(all_video_links)}")
                except Exception as e:
                    logger.error(f"❌ Erro em download: {e}")
                
                # Rate limiting
                time.sleep(0.5)
        
        # Fase 3: Relatório final
        logger.info(f"\n📋 FASE 3: GERANDO RELATÓRIO FINAL")
        self.generate_final_report()
        
        logger.info("🎉 DOWNLOAD COMPLETO FINALIZADO!")

def main():
    """Função principal."""
    print("🎬 LIBRAS VIDEO DOWNLOADER COMPLETO")
    print("="*60)
    print("📊 Sistema para download de TODOS os vídeos de Libras")
    print("🌐 Site: https://libras.cin.ufpe.br")
    print("📈 Estimativa: ~4089 vídeos, ~1364 sinais, 69 páginas")
    print()
    
    # Confirmação do usuário
    response = input("🤔 Deseja continuar com o download completo? (s/N): ").strip().lower()
    
    if response in ['s', 'sim', 'y', 'yes']:
        # Criar downloader
        downloader = LibrasCompleteDownloader(max_workers=2)  # 2 threads para ser respeitoso
        
        # Executar download completo
        downloader.run_complete_download()
    else:
        print("❌ Download cancelado pelo usuário")

if __name__ == "__main__":
    main()
