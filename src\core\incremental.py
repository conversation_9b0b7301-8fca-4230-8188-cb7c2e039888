"""
Incremental - Sistema de coleta incremental com ETag e cache inteligente.

Este módulo implementa coleta incremental usando headers HTTP condicionais
para otimizar bandwidth e detectar mudanças de conteúdo.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import structlog
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from .database import get_db_session
from .http_client import AsyncHTTPClient
from .metrics import metrics_collector, time_http_request
from .models import Page
from .validators import HTTPResponse, ScrapingConfig

logger = structlog.get_logger(__name__)


class IncrementalCollector:
    """Coletor incremental com cache inteligente."""
    
    def __init__(self, config: ScrapingConfig):
        self.config = config
        self.cache_hit_count = 0
        self.cache_miss_count = 0
        self.bandwidth_saved = 0
    
    async def fetch_with_cache_headers(
        self,
        http_client: AsyncHTTPClient,
        url: str,
        session: AsyncSession
    ) -> <PERSON><PERSON>[HTTPResponse, bool]:
        """
        Fazer fetch com headers condicionais.
        
        Returns:
            (response, is_cache_hit)
        """
        domain = self._extract_domain(url)
        
        # Buscar informações da última coleta
        last_page_info = await self._get_last_page_info(session, url)
        
        # Preparar headers condicionais
        conditional_headers = {}
        if last_page_info:
            if last_page_info.etag:
                conditional_headers["If-None-Match"] = last_page_info.etag
            elif last_page_info.last_modified:
                conditional_headers["If-Modified-Since"] = last_page_info.last_modified.strftime(
                    "%a, %d %b %Y %H:%M:%S GMT"
                )
        
        # Fazer requisição com timing
        async with time_http_request(metrics_collector, domain) as timing_ctx:
            try:
                # Fazer requisição HTTP
                response = await http_client._make_request(
                    url, 
                    headers=conditional_headers,
                    use_cache=False  # Usar nosso próprio cache
                )
                
                # Atualizar contexto de timing
                timing_ctx['status_code'] = response.status_code
                timing_ctx['response_size'] = len(response.content)
                timing_ctx['content_type'] = response.headers.content_type or "text/html"
                
                # Verificar se é cache hit (304 Not Modified)
                if response.status_code == 304:
                    logger.debug("Cache hit (304 Not Modified)", url=url)
                    
                    # Retornar resposta da última coleta
                    cached_response = await self._get_cached_response(session, url)
                    if cached_response:
                        self.cache_hit_count += 1
                        self._record_cache_metrics(domain, "hit")
                        return cached_response, True
                    else:
                        logger.warning("304 received but no cached content found", url=url)
                
                # Cache miss - conteúdo novo ou modificado
                self.cache_miss_count += 1
                self._record_cache_metrics(domain, "miss")
                
                logger.debug(
                    "Cache miss - content updated",
                    url=url,
                    status_code=response.status_code,
                    content_length=len(response.content),
                )
                
                return response, False
                
            except Exception as e:
                timing_ctx['status_code'] = 0
                logger.error("Failed to fetch with cache headers", url=url, error=str(e))
                raise
    
    async def _get_last_page_info(self, session: AsyncSession, url: str) -> Optional[Page]:
        """Obter informações da última coleta de uma página."""
        try:
            url_hash = self._calculate_url_hash(url)
            
            stmt = select(Page).where(
                Page.url_hash == url_hash
            ).order_by(Page.fetched_at.desc()).limit(1)
            
            result = await session.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.debug("Failed to get last page info", url=url, error=str(e))
            return None
    
    async def _get_cached_response(self, session: AsyncSession, url: str) -> Optional[HTTPResponse]:
        """Obter resposta em cache."""
        try:
            page_info = await self._get_last_page_info(session, url)
            if not page_info:
                return None
            
            # Reconstruir HTTPResponse a partir dos dados salvos
            response = HTTPResponse(
                url=url,
                status_code=page_info.http_status_code or 200,
                content="",  # Conteúdo será carregado do storage se necessário
                headers={
                    "content-type": page_info.content_type,
                    "etag": page_info.etag,
                    "last-modified": page_info.last_modified.isoformat() if page_info.last_modified else None,
                },
                fetched_at=page_info.fetched_at or datetime.utcnow(),
                duration_ms=0,  # Cache hit não tem duração
            )
            
            return response
            
        except Exception as e:
            logger.debug("Failed to get cached response", url=url, error=str(e))
            return None
    
    async def update_cache_info(
        self,
        session: AsyncSession,
        url: str,
        response: HTTPResponse
    ) -> None:
        """Atualizar informações de cache no banco."""
        try:
            url_hash = self._calculate_url_hash(url)
            
            # Extrair headers relevantes
            etag = response.headers.etag
            last_modified = None
            if response.headers.last_modified:
                try:
                    from email.utils import parsedate_to_datetime
                    last_modified = parsedate_to_datetime(response.headers.last_modified)
                except Exception:
                    pass
            
            # Atualizar ou inserir informações de cache
            stmt = update(Page).where(
                Page.url_hash == url_hash
            ).values(
                etag=etag,
                last_modified=last_modified,
                http_status_code=response.status_code,
                content_type=response.headers.content_type,
                content_length=len(response.content),
                fetched_at=response.fetched_at,
            )
            
            result = await session.execute(stmt)
            
            if result.rowcount == 0:
                logger.debug("No existing page found to update cache info", url=url)
            else:
                logger.debug("Cache info updated", url=url, etag=etag)
                
        except Exception as e:
            logger.error("Failed to update cache info", url=url, error=str(e))
    
    def _extract_domain(self, url: str) -> str:
        """Extrair domínio de uma URL."""
        from urllib.parse import urlparse
        return urlparse(url).netloc.lower()
    
    def _calculate_url_hash(self, url: str) -> str:
        """Calcular hash da URL."""
        import hashlib
        return hashlib.sha256(url.encode('utf-8')).hexdigest()
    
    def _record_cache_metrics(self, domain: str, result: str) -> None:
        """Registrar métricas de cache."""
        metrics_collector.storage_operations_total.labels(
            operation="cache_lookup",
            backend="http_cache",
            status=result
        ).inc()
    
    def get_cache_stats(self) -> Dict:
        """Obter estatísticas de cache."""
        total_requests = self.cache_hit_count + self.cache_miss_count
        hit_rate = (self.cache_hit_count / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "cache_hits": self.cache_hit_count,
            "cache_misses": self.cache_miss_count,
            "total_requests": total_requests,
            "hit_rate_percent": round(hit_rate, 2),
            "bandwidth_saved_bytes": self.bandwidth_saved,
        }


class SmartCrawlScheduler:
    """Agendador inteligente de crawling."""
    
    def __init__(self, config: ScrapingConfig):
        self.config = config
    
    async def get_urls_for_incremental_crawl(
        self,
        session: AsyncSession,
        domain: str,
        max_urls: int = 1000
    ) -> List[str]:
        """Obter URLs para crawling incremental baseado em prioridade."""
        
        # Critérios de priorização:
        # 1. Páginas nunca coletadas
        # 2. Páginas com alta frequência de mudança
        # 3. Páginas importantes (alto quality score)
        # 4. Páginas antigas (não coletadas há muito tempo)
        
        try:
            # Query complexa para priorização
            stmt = select(Page.url).where(
                Page.domain.has(name=domain)
            ).order_by(
                # Prioridade 1: Páginas nunca coletadas
                Page.fetched_at.is_(None).desc(),
                
                # Prioridade 2: Páginas antigas
                Page.fetched_at.asc(),
                
                # Prioridade 3: Páginas de alta qualidade
                Page.quality_score.desc(),
                
                # Prioridade 4: Páginas com muitas versões (mudança frequente)
                Page.id  # Placeholder - seria contagem de versões
            ).limit(max_urls)
            
            result = await session.execute(stmt)
            urls = [row[0] for row in result.fetchall()]
            
            logger.info(
                "URLs selected for incremental crawl",
                domain=domain,
                url_count=len(urls),
                max_requested=max_urls,
            )
            
            return urls
            
        except Exception as e:
            logger.error("Failed to get URLs for incremental crawl", domain=domain, error=str(e))
            return []
    
    async def calculate_crawl_frequency(
        self,
        session: AsyncSession,
        url: str
    ) -> timedelta:
        """Calcular frequência ideal de crawling para uma URL."""
        try:
            # Buscar histórico de versões
            url_hash = self._calculate_url_hash(url)
            
            # Query para contar mudanças nos últimos 30 dias
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            
            stmt = select(Page).where(
                Page.url_hash == url_hash,
                Page.updated_at >= thirty_days_ago
            )
            
            result = await session.execute(stmt)
            page = result.scalar_one_or_none()
            
            if not page:
                # Página nova - crawl frequente inicialmente
                return timedelta(hours=6)
            
            # Calcular frequência baseada no histórico
            # TODO: Implementar lógica mais sofisticada baseada em:
            # - Número de mudanças detectadas
            # - Importância da página (quality score)
            # - Tipo de conteúdo
            
            # Por enquanto, frequência simples baseada na qualidade
            if page.quality_score >= 80:
                return timedelta(hours=12)  # Páginas importantes
            elif page.quality_score >= 60:
                return timedelta(days=1)    # Páginas médias
            else:
                return timedelta(days=3)    # Páginas de baixa qualidade
                
        except Exception as e:
            logger.debug("Failed to calculate crawl frequency", url=url, error=str(e))
            return timedelta(days=1)  # Fallback
    
    def _calculate_url_hash(self, url: str) -> str:
        """Calcular hash da URL."""
        import hashlib
        return hashlib.sha256(url.encode('utf-8')).hexdigest()


class BandwidthOptimizer:
    """Otimizador de bandwidth."""
    
    def __init__(self):
        self.total_bytes_saved = 0
        self.compression_savings = 0
        self.cache_savings = 0
    
    def estimate_bandwidth_savings(
        self,
        original_size: int,
        compressed_size: int,
        cache_hit: bool
    ) -> Dict[str, int]:
        """Estimar economia de bandwidth."""
        savings = {
            "compression_saved": original_size - compressed_size if compressed_size < original_size else 0,
            "cache_saved": original_size if cache_hit else 0,
            "total_saved": 0,
        }
        
        savings["total_saved"] = savings["compression_saved"] + savings["cache_saved"]
        
        # Atualizar contadores
        self.compression_savings += savings["compression_saved"]
        self.cache_savings += savings["cache_saved"]
        self.total_bytes_saved += savings["total_saved"]
        
        return savings
    
    def get_bandwidth_stats(self) -> Dict:
        """Obter estatísticas de bandwidth."""
        return {
            "total_bytes_saved": self.total_bytes_saved,
            "compression_savings": self.compression_savings,
            "cache_savings": self.cache_savings,
            "savings_mb": round(self.total_bytes_saved / 1024 / 1024, 2),
        }


# Instâncias globais
bandwidth_optimizer = BandwidthOptimizer()


async def perform_incremental_crawl(
    domain: str,
    config,
    max_urls: int = 1000
) -> Dict:
    """Realizar crawling incremental de um domínio."""
    logger.info("Starting incremental crawl", domain=domain, max_urls=max_urls)
    
    collector = IncrementalCollector(config)
    scheduler = SmartCrawlScheduler(config)
    
    stats = {
        "domain": domain,
        "urls_processed": 0,
        "cache_hits": 0,
        "cache_misses": 0,
        "errors": 0,
        "bandwidth_saved": 0,
    }
    
    async with AsyncHTTPClient(config) as http_client:
        async with get_db_session() as session:
            # Obter URLs para crawling
            urls = await scheduler.get_urls_for_incremental_crawl(
                session, domain, max_urls
            )
            
            for url in urls:
                try:
                    # Fetch com cache headers
                    response, is_cache_hit = await collector.fetch_with_cache_headers(
                        http_client, url, session
                    )
                    
                    # Atualizar estatísticas
                    stats["urls_processed"] += 1
                    if is_cache_hit:
                        stats["cache_hits"] += 1
                    else:
                        stats["cache_misses"] += 1
                        
                        # Atualizar informações de cache
                        await collector.update_cache_info(session, url, response)
                    
                except Exception as e:
                    stats["errors"] += 1
                    logger.error("Failed to process URL in incremental crawl", url=url, error=str(e))
    
    # Obter estatísticas finais
    cache_stats = collector.get_cache_stats()
    bandwidth_stats = bandwidth_optimizer.get_bandwidth_stats()
    
    stats.update({
        "cache_stats": cache_stats,
        "bandwidth_stats": bandwidth_stats,
    })
    
    logger.info("Incremental crawl completed", domain=domain, stats=stats)
    return stats
