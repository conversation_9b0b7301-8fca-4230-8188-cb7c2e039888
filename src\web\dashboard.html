<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebScraper Enterprise Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .card {
            @apply bg-white rounded-lg shadow-md p-6 mb-6;
        }
        .metric-card {
            @apply bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg p-6 shadow-lg;
        }
        .status-healthy { @apply text-green-500; }
        .status-degraded { @apply text-yellow-500; }
        .status-unhealthy { @apply text-red-500; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-spider text-blue-600 text-2xl mr-3"></i>
                    <h1 class="text-2xl font-bold text-gray-900">WebScraper Enterprise</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="health-status" class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-gray-400 mr-2"></div>
                        <span class="text-sm text-gray-600">Checking...</span>
                    </div>
                    <button onclick="refreshDashboard()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Quick Actions -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="showCrawlModal()" class="bg-green-600 text-white p-4 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-play mr-2"></i>Start Crawl
                </button>
                <button onclick="clearCache()" class="bg-orange-600 text-white p-4 rounded-lg hover:bg-orange-700 transition-colors">
                    <i class="fas fa-trash mr-2"></i>Clear Cache
                </button>
                <button onclick="viewLogs()" class="bg-purple-600 text-white p-4 rounded-lg hover:bg-purple-700 transition-colors">
                    <i class="fas fa-file-alt mr-2"></i>View Logs
                </button>
            </div>
        </div>

        <!-- Metrics Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="metric-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-blue-100 text-sm">Total Pages</p>
                        <p id="total-pages" class="text-3xl font-bold">-</p>
                    </div>
                    <i class="fas fa-file-alt text-blue-200 text-2xl"></i>
                </div>
            </div>
            
            <div class="metric-card bg-gradient-to-r from-green-500 to-green-600">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-green-100 text-sm">Avg Quality</p>
                        <p id="avg-quality" class="text-3xl font-bold">-</p>
                    </div>
                    <i class="fas fa-star text-green-200 text-2xl"></i>
                </div>
            </div>
            
            <div class="metric-card bg-gradient-to-r from-purple-500 to-purple-600">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-100 text-sm">Active Sessions</p>
                        <p id="active-sessions" class="text-3xl font-bold">-</p>
                    </div>
                    <i class="fas fa-cogs text-purple-200 text-2xl"></i>
                </div>
            </div>
            
            <div class="metric-card bg-gradient-to-r from-red-500 to-red-600">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-red-100 text-sm">Active Alerts</p>
                        <p id="active-alerts" class="text-3xl font-bold">-</p>
                    </div>
                    <i class="fas fa-exclamation-triangle text-red-200 text-2xl"></i>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Quality Distribution -->
            <div class="card">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quality Distribution</h3>
                <canvas id="qualityChart" width="400" height="200"></canvas>
            </div>
            
            <!-- Pages Over Time -->
            <div class="card">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Pages Processed (Last 7 Days)</h3>
                <canvas id="pagesChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- System Status -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <!-- Database Status -->
            <div class="card">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Database</h3>
                <div id="db-status" class="space-y-2">
                    <div class="flex justify-between">
                        <span>Status:</span>
                        <span id="db-health" class="font-medium">-</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Response Time:</span>
                        <span id="db-response-time">-</span>
                    </div>
                </div>
            </div>
            
            <!-- Redis Status -->
            <div class="card">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Redis Cache</h3>
                <div id="redis-status" class="space-y-2">
                    <div class="flex justify-between">
                        <span>Status:</span>
                        <span id="redis-health" class="font-medium">-</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Memory Used:</span>
                        <span id="redis-memory">-</span>
                    </div>
                </div>
            </div>
            
            <!-- Elasticsearch Status -->
            <div class="card">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Elasticsearch</h3>
                <div id="es-status" class="space-y-2">
                    <div class="flex justify-between">
                        <span>Status:</span>
                        <span id="es-health" class="font-medium">-</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Documents:</span>
                        <span id="es-docs">-</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
            <div id="recent-activity" class="space-y-3">
                <div class="text-gray-500 text-center py-8">Loading recent activity...</div>
            </div>
        </div>

        <!-- Search -->
        <div class="card">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Search</h3>
            <div class="flex space-x-4">
                <input 
                    type="text" 
                    id="search-input" 
                    placeholder="Search pages..." 
                    class="flex-1 px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    onkeypress="handleSearchKeypress(event)"
                >
                <button onclick="performSearch()" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            <div id="search-results" class="mt-4"></div>
        </div>
    </main>

    <!-- Crawl Modal -->
    <div id="crawl-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg p-6 w-96">
                <h3 class="text-lg font-semibold mb-4">Start New Crawl</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Domain</label>
                        <input type="text" id="crawl-domain" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="example.com">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Max Pages</label>
                        <input type="number" id="crawl-max-pages" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md" value="10" min="1" max="1000">
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button onclick="hideCrawlModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                    <button onclick="startCrawl()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Start Crawl</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let qualityChart, pagesChart;
        const API_BASE = '/api/v1';

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            refreshDashboard();
            
            // Auto-refresh every 30 seconds
            setInterval(refreshDashboard, 30000);
        });

        // Initialize charts
        function initCharts() {
            // Quality Distribution Chart
            const qualityCtx = document.getElementById('qualityChart').getContext('2d');
            qualityChart = new Chart(qualityCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Excellent', 'Good', 'Fair', 'Poor'],
                    datasets: [{
                        data: [0, 0, 0, 0],
                        backgroundColor: ['#10B981', '#3B82F6', '#F59E0B', '#EF4444']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // Pages Over Time Chart
            const pagesCtx = document.getElementById('pagesChart').getContext('2d');
            pagesChart = new Chart(pagesCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Pages Processed',
                        data: [],
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Refresh dashboard data
        async function refreshDashboard() {
            try {
                // Health check
                const healthResponse = await fetch('/health');
                const health = await healthResponse.json();
                updateHealthStatus(health);

                // Quick stats
                const statsResponse = await fetch(`${API_BASE}/quick/stats`);
                const stats = await statsResponse.json();
                updateStats(stats);

                // Analytics (mock data for now)
                updateAnalytics();

            } catch (error) {
                console.error('Failed to refresh dashboard:', error);
                updateHealthStatus({ status: 'unhealthy', error: error.message });
            }
        }

        // Update health status
        function updateHealthStatus(health) {
            const statusElement = document.getElementById('health-status');
            const statusClass = `status-${health.status}`;
            
            statusElement.innerHTML = `
                <div class="w-3 h-3 rounded-full ${getStatusColor(health.status)} mr-2"></div>
                <span class="text-sm ${statusClass}">${health.status.toUpperCase()}</span>
            `;

            // Update service statuses
            if (health.services) {
                updateServiceStatus('db', health.services.database);
                updateServiceStatus('redis', health.services.redis);
                updateServiceStatus('es', health.services.elasticsearch);
            }
        }

        // Update service status
        function updateServiceStatus(service, status) {
            const healthElement = document.getElementById(`${service}-health`);
            const responseTimeElement = document.getElementById(`${service}-response-time`);
            
            if (healthElement) {
                healthElement.textContent = status.status.toUpperCase();
                healthElement.className = `font-medium status-${status.status}`;
            }
            
            if (responseTimeElement && status.response_time_ms) {
                responseTimeElement.textContent = `${status.response_time_ms}ms`;
            }
        }

        // Update stats
        function updateStats(stats) {
            // Update metric cards (mock data)
            document.getElementById('total-pages').textContent = '1,234';
            document.getElementById('avg-quality').textContent = '78';
            document.getElementById('active-sessions').textContent = '3';
            document.getElementById('active-alerts').textContent = '0';

            // Update service-specific stats
            if (stats.redis && stats.redis.redis_info) {
                document.getElementById('redis-memory').textContent = stats.redis.redis_info.used_memory_human || '-';
            }
            
            if (stats.elasticsearch && stats.elasticsearch.storage) {
                document.getElementById('es-docs').textContent = stats.elasticsearch.storage.total_docs || '-';
            }
        }

        // Update analytics
        function updateAnalytics() {
            // Update quality chart (mock data)
            qualityChart.data.datasets[0].data = [25, 45, 20, 10];
            qualityChart.update();

            // Update pages chart (mock data)
            const last7Days = Array.from({length: 7}, (_, i) => {
                const date = new Date();
                date.setDate(date.getDate() - (6 - i));
                return date.toLocaleDateString();
            });
            
            pagesChart.data.labels = last7Days;
            pagesChart.data.datasets[0].data = [120, 150, 180, 200, 175, 190, 210];
            pagesChart.update();
        }

        // Utility functions
        function getStatusColor(status) {
            switch (status) {
                case 'healthy': return 'bg-green-500';
                case 'degraded': return 'bg-yellow-500';
                case 'unhealthy': return 'bg-red-500';
                default: return 'bg-gray-400';
            }
        }

        // Modal functions
        function showCrawlModal() {
            document.getElementById('crawl-modal').classList.remove('hidden');
        }

        function hideCrawlModal() {
            document.getElementById('crawl-modal').classList.add('hidden');
        }

        // Action functions
        async function startCrawl() {
            const domain = document.getElementById('crawl-domain').value;
            const maxPages = document.getElementById('crawl-max-pages').value;
            
            if (!domain) {
                alert('Please enter a domain');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/quick/crawl`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ domain, max_pages: parseInt(maxPages) })
                });
                
                if (response.ok) {
                    alert(`Crawl started for ${domain}`);
                    hideCrawlModal();
                } else {
                    alert('Failed to start crawl');
                }
            } catch (error) {
                alert('Error starting crawl: ' + error.message);
            }
        }

        async function clearCache() {
            if (confirm('Are you sure you want to clear the cache?')) {
                try {
                    const response = await fetch(`${API_BASE}/cache/clear`, { method: 'DELETE' });
                    if (response.ok) {
                        alert('Cache cleared successfully');
                    } else {
                        alert('Failed to clear cache');
                    }
                } catch (error) {
                    alert('Error clearing cache: ' + error.message);
                }
            }
        }

        function viewLogs() {
            window.open('/logs', '_blank');
        }

        // Search functions
        function handleSearchKeypress(event) {
            if (event.key === 'Enter') {
                performSearch();
            }
        }

        async function performSearch() {
            const query = document.getElementById('search-input').value;
            if (!query) return;

            try {
                const response = await fetch(`${API_BASE}/quick/search?q=${encodeURIComponent(query)}`);
                const results = await response.json();
                
                const resultsDiv = document.getElementById('search-results');
                if (results.results && results.results.length > 0) {
                    resultsDiv.innerHTML = results.results.map(result => `
                        <div class="border-l-4 border-blue-500 pl-4 py-2">
                            <h4 class="font-medium text-blue-600">${result.title}</h4>
                            <p class="text-sm text-gray-600">${result.url}</p>
                            <p class="text-xs text-gray-500">Quality: ${result.quality_score}/100</p>
                        </div>
                    `).join('');
                } else {
                    resultsDiv.innerHTML = '<p class="text-gray-500">No results found</p>';
                }
            } catch (error) {
                document.getElementById('search-results').innerHTML = '<p class="text-red-500">Search failed</p>';
            }
        }
    </script>
</body>
</html>
