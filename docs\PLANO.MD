🧭 Visão Geral do Projeto

Objetivo: construir um pipeline de coleta, parsing, validação e versionamento de conteúdos web (estáticos e dinâmicos), com observabilidade, tolerância a falhas, respeito a requisitos legais e capacidade de reprocessar ou reindexar facilmente.

Stack sugerida (Python + VS Code):

Coleta HTTP: httpx (assíncrono) + aiohttp (opcional)

Browser headless (para páginas dinâmicas): Playwright (preferível) ou Selenium

Parsing: selectolax (rápido) + lxml + beautifulsoup4 (pontual)

Agendamento/Orquestração: Prefect (mais simples) ou Airflow (se precisar enterprise)

Storage:

Raw: S3/MinIO ou pasta versionada (/data/raw/YYYY-MM-DD/…)

Estruturado: PostgreSQL (metadados e index), SQLite no MVP

Busca semântica (se for RAG): ChromaDB ou pgvector

Observabilidade: structlog, logfire/loguru, métricas com Prometheus e dashboards no Grafana

Testes: pytest, VCR.py (gravação de respostas HTTP), Playwright Test p/ seletores

Implantação: Docker + docker-compose; depois Kubernetes (quando escalar)

⚖️ Ética, Legal & Conformidade

Respeite robots.txt e termos de uso (documente a decisão: coletar apenas o permitido).

Rate limiting: limite de requisições por host (ex.: 0.5–2 req/s), exponential backoff com jitter.

Identificação: User-Agent claro (com email de contato).

Dados sensíveis: não colete PII indevida; mascare logs.

Reprodutibilidade: logue timestamp, URL, status, hash de conteúdo e versão do coletor.

Cache & ETags: use If-None-Match / If-Modified-Since para coleta incremental.

✅ Dica: Crie uma “Política de Coleta” como README no repositório, com limites de tráfego por domínio e canais para request de remoção de dados.

🧱 Arquitetura de Referência
[Scheduler/Prefect]
      │
      ▼
[Dispatcher]──▶[HTTP Fetcher (httpx, cache, retries)]
      │                         │
      │                         └──▶[Headless (Playwright) p/ dinâmicos]
      ▼
[Parser Layer (selectolax/lxml)]──▶[Normalização/Validação (Pydantic)]
      │
      ▼
[Deduplicação & Versionamento (hash, ETag)]
      │
      ├──▶[Raw Storage (S3/MinIO/Filesystem datado)]
      ├──▶[DB Metadados (PostgreSQL)]
      └──▶[Index Semântico (Chroma/pgvector)]  ← opcional/RAG
      │
      ▼
[Observabilidade (logs, métricas, alertas)]

🧩 Estratégia de Coleta (o “segredo” da robustez)
1) Descoberta de URLs (Crawling)

Ordem preferencial: sitemap.xml → páginas de índice e TOCs → BFS controlado por regra de escopo (mesmo host/path)

Regras de escopo: whitelist de domínios e prefixos; bloqueio de loops (hash de URL normalizada)

Prioritização: heap por importância (páginas com TOC, docs API, changelogs) e novidade (última modificação)

Pista (conceito de crawler BFS com fila de prioridade):

# Ideia (não completo): fila priorizada por (profundidade, prioridade_semântica)
# Explicação: você combina heurística de "valor" (ex.: URLs com 'api', 'reference', 'docs') a uma profundidade mínima.

2) Fetching HTTP “inteligente”

Assíncrono com httpx.AsyncClient (timeouts curtos, retries com backoff)

Headers condicionais (ETag/Last-Modified) → economia e incremental

Cache local (disco) conforme TTL por domínio

Pista (conceito de backoff com jitter):

# Conceito: backoff = base * (2 ** tentativas) + random.uniform(0, base)
# Explicação: evita rajadas sincronizadas após falhas de rede ou 429/503.

3) Conteúdo Dinâmico (JS)

Prefira Playwright a Selenium (mais estável e simples para waits)

Estratégias de espera: wait_for_selector, networkidle apenas quando necessário

Seletores estáveis: prefira data-testid/atributos fixos; evite XPaths frágeis

Pista (espera robusta no Playwright):

# Ideia: aguardar seletor do conteúdo principal e só então extrair innerText/innerHTML
# Conceito: "networkidle" pode ser enganoso em SPAs com websockets; combine sinais (DOM + visibilidade).

4) Parsing & Normalização

Use selectolax (AST HTML rápido) para extrair títulos, headings, breadcrumbs, corpo, links internos

Normalize:

Remover boilerplate (nav, footer) por seletor

Preservar estrutura (headings H1..H6 → hierarquia)

Resolver links relativos → absolutos

Extrair tabelas e códigos com metadados de linguagem

Pista (árvore semântica de headings):

# Conceito: construa uma árvore {h1: [... h2: [... h3 ...]]} para navegação e para RAG.
# Explicação: melhora pesquisa e chunking contextual.

5) Deduplicação & Versionamento

Hash de conteúdo canônico (ex.: sha256 do HTML limpo)

Controlar “pequenas mudanças”: ignore timestamps e banners (normalize antes do hash)

Versionar por url + content_hash + fetched_at

6) Validação de Qualidade (Data Quality)

Pydantic para validar schemas de saída (campos obrigatórios, tamanhos, tipos)

Regras:

Título não vazio

Corpo ≥ N palavras

≥ X links internos válidos

Score de qualidade baseado em completude

🗃️ Esquema de Dados (sugestão)
Campo	Tipo	Exemplo/Notas
url	TEXT (PK)	URL canônica
fetched_at	TIMESTAMP	Quando buscou
status_code	INT	200, 304, 404…
etag	TEXT	Para condicional
last_modified	TEXT	From headers
content_hash	TEXT	sha256 do HTML normalizado
title	TEXT	H1
headings_tree	JSONB	Hierarquia H1..H6
text_content	TEXT	Corpo limpo (sem boilerplate)
code_blocks	JSONB	Lista com {lang, snippet_hash, first_line_preview}
tables	JSONB	Conteúdo de tabelas em JSON
out_links	JSONB	URLs externas
in_links	JSONB	URLs internas
quality_score	INT	0–100
errors	JSONB	Lista de warnings/erros do parser

✅ Dica: use índice por content_hash para achar versões idênticas e GIN em JSONB para consultas flexíveis.

🚦 Anti-bloqueio (ético) & Robustez

Respeite limites do site (documente MAX_RPS por domínio)

Retry apenas em 429/5xx, com backoff crescente

Rotação leve de UA (lista curta, estável), sem técnicas agressivas

Circuit breaker por host: se exceder erros consecutivos, pausa N minutos

Warm cache para páginas base (TOCs e índices)

🧪 Testes & Confiabilidade

Unitários: parsers por domínio (golden files de HTML armazenados)

Contract tests: Pydantic garantindo schema estável

VCR.py: grava HTTP para testes determinísticos

Seletores: testes com Playwright para garantir que mudanças de DOM quebrem cedo

Pista (teste de parser por “golden file”):

# Conceito: carregar HTML salvo, rodar o parser e comparar com saída esperada (snapshot).
# Explicação: garante que pequenas mudanças no DOM não passem despercebidas.

📦 Estrutura de Pastas (MVP → produção)
webscraper/
  ├─ configs/
  │   ├─ domains.yml            # regras por domínio (escopo, seletores, rate limit)
  │   └─ prefect_blocks.json    # credenciais/orquestração
  ├─ data/
  │   ├─ raw/2025-09-07/…       # HTML bruto (versões)
  │   └─ processed/…            # JSON normalizado
  ├─ src/
  │   ├─ core/
  │   │   ├─ http_client.py     # httpx + retries + cache + condicional
  │   │   ├─ browser.py         # Playwright helpers (esperas, screenshots on fail)
  │   │   ├─ parser_base.py     # interfaces e utilidades de parsing
  │   │   ├─ normalize.py       # limpeza de boilerplate, resolve links
  │   │   ├─ validators.py      # Pydantic models + quality checks
  │   │   └─ storage.py         # S3/FS + PostgreSQL
  │   ├─ domains/
  │   │   ├─ generic.py         # fallback parser
  │   │   └─ revit_docs.py      # parser específico (árvore, TOC, exemplos de código)
  │   ├─ crawl/
  │   │   ├─ sitemap.py         # descoberta via sitemap
  │   │   └─ frontier.py        # fila, dedupe, regras de escopo
  │   └─ flows/
  │       ├─ mvp_flow.py        # Prefect Flow (MVP diário)
  │       └─ incremental.py     # coleta incremental (ETag/If-Modified-Since)
  ├─ tests/
  │   ├─ golden_html/
  │   └─ test_parsers.py
  ├─ docker/
  │   └─ Dockerfile
  ├─ .env.example
  ├─ pyproject.toml
  └─ README.md

🛠️ Playbook de Implementação (Passo a Passo)
Fase 0 — MVP “sólido”

Configurar projeto (pyproject.toml, venv, lint/format)

HTTP assíncrono com cache (httpx + async_lru opc.)

Descoberta por sitemap + fronteira BFS com escopo

Parser genérico (title, headings, texto, links)

Storage raw + metadados em SQLite

Validação Pydantic + logs estruturados

Job diário com Prefect (agendado)

Fase 1 — Dinâmica & qualidade

Playwright com esperas robustas

Normalização (remover boilerplate, resolver links)

Versionamento por hash

Qualidade (score; falhas → reprocesso)

Testes com VCR + golden files

Fase 2 — Escala & Observabilidade

Migrar metadados p/ PostgreSQL, raw p/ MinIO/S3

Métricas Prometheus: latência, taxa de sucesso, QPS por host

Alertas (ex.: 5xx>10% por 10min)

Incremental: If-None-Match e If-Modified-Since

Fase 3 — Pesquisa e RAG (opcional)

Chunking semântico por headings

Índice vetorial (Chroma/pgvector)

API interna para consulta (FastAPI)

🧠 Conceitos-Chave (explicados)

Assincronismo (asyncio + httpx): permite concurrência de I/O. Você agenda centenas de requisições sem bloquear a thread, limitado por semáforos (ex.: 10 por host).

Backoff com jitter: amortiza picos e evita thundering herd.

ETag/If-Modified-Since: coleta incremental, reduz custo e respeita infra do site.

Seletores estáveis (Playwright): preferir data-* e CSS robusto; XPath só quando inevitável.

Golden tests: fixam um contrato de parsing; mudanças involuntárias aparecem como diffs.

Versionamento por hash canônico: mantém linhagem de conteúdo e facilita auditoria.

Observabilidade: sem métricas e logs estruturados, você voa às cegas ao escalar.

🧪 “Pistas” de Código (sem solução completa)
1) Cliente HTTP resiliente
# Ideia: criar um wrapper com timeouts, retries seletivos e condicional.
# Explicação: motivo e quando cada retry acontece (429/5xx), com backoff e jitter.

2) Frontier (fila de URLs) com escopo
# Ideia: armazenar 'visited' por URL normalizada (lowercase, sem fragmentos).
# Explicação: evita loops e reprocesso, controla profundidade, prioriza TOCs.

3) Parser base + domínio específico
# Ideia: classe base com hooks (preprocess, parse, postprocess).
# Explicação: domínios especiais (ex.: docs Revit) podem sobrepor seletores/limpezas.

4) Validação com Pydantic
# Ideia: modelo PageData(title, text_content, headings_tree, quality_score, ...)
# Explicação: regra de score (peso para título, número de headings, tamanho do corpo).

5) Incremental por ETag
# Ideia: guardar ETag/Last-Modified em DB; mandar If-None-Match / If-Modified-Since
# Explicação: se 304 → pular parse; se 200 → atualizar versão.

📊 Tabela de Componentes x Ferramentas
Componente	Ferramenta-base	Alternativas/Notas
HTTP assíncrono	httpx	aiohttp
Headless browser	Playwright	Selenium (se já dominar)
Parser HTML	selectolax + lxml	beautifulsoup4 (pontual)
Validação de dados	Pydantic	Marshmallow
Orquestração	Prefect	Airflow
Banco de metadados	PostgreSQL / SQLite	MySQL
Armazenamento raw	S3/MinIO	FS com versão por dia
Logs & métricas	structlog + Prometheus	loguru, OpenTelemetry
Índice vetorial (RAG)	Chroma / pgvector	Pinecone (SaaS)
🧰 Para o seu caso (docs de APIs como a do Revit)

As páginas costumam ter árvore lateral:

Colete o TOC inteiro (links com nível)

Ordene o crawl conforme essa árvore (evita páginas órfãs)

Extraia exemplos de código em blocos e linguagens (C#, Python, etc.)

Monte chunks por seções de heading (ótimo para RAG)

Páginas SPA exigem Playwright + espera por seletor “conteúdo renderizado”

Snapshot HTML + renderização em PDF (opcional) para auditoria/benchmark

✅ Checklist de “Pronto para Produção”

 robots.txt & limites por domínio configurados

 Retries e backoff com jitter

 Cache condicional (ETag/Last-Modified)

 Parser com golden tests e score de qualidade

 Storage raw versionado + DB de metadados

 Observabilidade (logs estruturados + métricas)

 Orquestração diária + incremental

 Documentação: política de coleta, schema, SLOs

📈 Próximos passos (recomendação prática)

Iniciar repositório com a estrutura acima e pyproject.toml.

Implementar MVP-0: sitemap → fetch → parse genérico → salvar (raw + SQLite) → Prefect diário.

Adicionar Playwright para 2–3 páginas dinâmicas críticas.

Criar 2 parsers específicos (ex.: docs Revit + outro site de referência).

Migrar para PostgreSQL e ligar métricas Prometheus.

Ativar incremental por ETag e testes com VCR.py.

Se quiser, já preparo um esqueleto de repositório com os arquivos-base (Dockerfile, pyproject, módulos vazios e comentários-guia) e uma planilha de tarefas. Quer que eu gere esse esqueleto aqui no chat para você começar hoje?