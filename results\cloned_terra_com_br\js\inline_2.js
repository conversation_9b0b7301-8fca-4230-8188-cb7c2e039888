
var tgmKey="br.homepage.home360";
    var terra_info_channeldetail = '/';
var terra_info_id= "20e07ef2795b2310VgnVCM3000009af154d0RCRD";
var terra_info_service = "PubCMSTerra";
var terra_info_type = "CAP";
var terra_info_clssfctn = "novo_portal";
var terra_info_pagetype = "home";
var terra_stats_dvctype = "web";
var terra_size_version = "default";
window.terraVersion = 'terra360';

    

window.info_path = {
    channel: "capa",
    subchannel: "",
    channeldetail: "",
    breadcrumb: "capa"
};

//Context
window.trr = {};
window.trr.contextData = {
    device : "web",
    lang : "pt-BR",
    country : "br",
    channelId : "20e07ef2795b2310VgnVCM3000009af154d0RCRD",
    idItemMenu: "home",
    socialreader : false,
    liveTicker: {
        idItemMenu: "home",
        countryLive: "br"
    }
}
