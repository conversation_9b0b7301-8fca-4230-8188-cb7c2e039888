<!DOCTYPE html>

<html data-theme="light" lang="pt-BR">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width,initial-scale=1" name="viewport"/>
<title>Projetos Residenciais - PVP Projects - Engenharia de Qualidade</title>
<meta content="Projetos residenciais da PVP Projects - Engenharia de Qualidade" name="description"/>
<meta content="engenharia elétrica, projetos hidrossanitários, Porto Alegre, engenheiro eletricista, Revit, AutoCAD" name="keywords"/>
<meta content="Eng. Pedro Vitor <PERSON>" name="author"/>
<meta content="index, follow" name="robots"/>
<meta content="#1e40af" name="theme-color"/>
<meta content="#1e40af" name="msapplication-TileColor"/>
<meta content="yes" name="apple-mobile-web-app-capable"/>
<meta content="default" name="apple-mobile-web-app-status-bar-style"/>
<meta content="PVP Projects - Engenharia de Qualidade" name="apple-mobile-web-app-title"/>
<meta content="website" property="og:type"/>
<meta content="Projetos Residenciais" property="og:title"/>
<meta content="Projetos residenciais da PVP Projects - Engenharia de Qualidade" property="og:description"/>
<meta content="https://pvp-projects.com/projetos/categoria/residencial/" property="og:url"/>
<meta content="https://pvp-projects.com/assets/images/og-default.jpg" property="og:image"/>
<meta content="PVP Projects - Engenharia de Qualidade" property="og:site_name"/>
<meta content="pt_BR" property="og:locale"/>
<meta content="summary_large_image" name="twitter:card"/>
<meta content="Projetos Residenciais" name="twitter:title"/>
<meta content="Projetos residenciais da PVP Projects - Engenharia de Qualidade" name="twitter:description"/>
<meta content="https://pvp-projects.com/assets/images/og-default.jpg" name="twitter:image"/>
<link href="/assets/imagens/favicon-new.png" rel="icon" type="image/png"/>
<link href="/assets/imagens/favicon-new.png" rel="apple-touch-icon"/>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;family=Poppins:wght@400;500;600;700;800&amp;family=JetBrains+Mono:wght@400;500&amp;display=swap" rel="stylesheet"/>
<link as="style" href="/styles/style.css" rel="preload"/>
<link as="script" href="/scripts/main.js" rel="preload"/>
<link href="https://fonts.googleapis.com" rel="dns-prefetch"/>
<link href="https://cdnjs.cloudflare.com" rel="dns-prefetch"/>
<link href="/styles/style.css" rel="stylesheet"/>
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet"/>

</head>
<body class="antialiased bg-white text-gray-900 transition-colors duration-200" data-theme="light">
<a class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-lg z-50" href="#main-content">
Pular para o conteúdo principal
</a>
<div class="loader fixed inset-0 bg-white z-50 flex items-center justify-center transition-opacity duration-300">
<div class="animate-spin rounded-full h-12 w-12 border-4 border-primary-600 border-t-transparent"></div>
</div>
<header class="sticky top-0 z-40 bg-white/80 backdrop-blur-md border-b border-gray-200 transition-all duration-200">
<nav class="container mx-auto px-4 sm:px-6 lg:px-8">
<div class="flex items-center justify-between h-16">
<div class="flex-shrink-0">
<a class="flex items-center space-x-4" href="/">
<img alt="PVP Projects Logo" class="w-20 h-20 object-contain rounded-full" src="/assets/imagens/favicon-new.png"/>
<span class="font-bold text-xl text-gray-900">PVP Projects</span>
</a>
</div>
<div class="hidden md:block">
<div class="flex items-center space-x-8">
<div class="relative group">
<a class="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium" href="/">
Home
</a>
</div>
<div class="relative group">
<a class="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium" href="/sobre">
Sobre
</a>
</div>
<div class="relative group">
<a class="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium" href="/servicos">
Serviços
</a>
<div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
<div class="py-2">
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/servicos/eletricos">
<div class="font-medium">Projetos Elétricos</div>
<div class="text-sm text-gray-500">Instalações de baixa tensão</div>
</a>
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/servicos/hidrossanitarios">
<div class="font-medium">Projetos Hidrossanitários</div>
<div class="text-sm text-gray-500">Redes de água e esgoto</div>
</a>
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/servicos/comunicacao">
<div class="font-medium">Projetos de Comunicação</div>
<div class="text-sm text-gray-500">Infraestrutura de dados</div>
</a>
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/servicos/consultoria-bim">
<div class="font-medium">Consultoria BIM</div>
<div class="text-sm text-gray-500">Modelagem 3D em Revit</div>
</a>
</div>
</div>
</div>
<div class="relative group">
<a class="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium" href="/projetos-dedicada">
Projetos
</a>
<div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
<div class="py-2">
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/projetos-dedicada">
<div class="font-medium">Todos os Projetos</div>
<div class="text-sm text-gray-500">Portfólio completo</div>
</a>
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/projetos/categoria/predial">
<div class="font-medium">Projetos Prediais</div>
<div class="text-sm text-gray-500">Edifícios residenciais e comerciais</div>
</a>
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/projetos/categoria/residencial">
<div class="font-medium">Projetos Residenciais</div>
<div class="text-sm text-gray-500">Casas e apartamentos</div>
</a>
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/projetos/categoria/comercial">
<div class="font-medium">Projetos Comerciais</div>
<div class="text-sm text-gray-500">Lojas e estabelecimentos</div>
</a>
</div>
</div>
</div>
<div class="relative group">
<a class="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium" href="/blog">
Blog
</a>
</div>
<div class="relative group">
<a class="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium" href="/contato">
Contato
</a>
</div>
</div>
</div>
<div class="flex items-center space-x-4">
<button aria-label="Alternar tema" class="p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200" id="theme-toggle">
<svg class="w-5 h-5" fill="none" stroke="currentColor" viewbox="0 0 24 24">
<path d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</button>
<button aria-label="Buscar" class="p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200" id="search-toggle">
<svg class="w-5 h-5" fill="none" stroke="currentColor" viewbox="0 0 24 24">
<path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</button>
<button aria-label="Abrir menu" class="md:hidden p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200" id="mobile-menu-toggle">
<svg class="w-6 h-6" fill="none" stroke="currentColor" viewbox="0 0 24 24">
<path d="M4 6h16M4 12h16M4 18h16" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</button>
<a class="hidden md:inline-flex bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200 font-medium" href="/contato">
Solicitar Orçamento
</a>
</div>
</div>
<div class="md:hidden hidden" id="mobile-menu">
<div class="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
<a class="block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/">
Home
</a>
<a class="block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/sobre">
Sobre
</a>
<a class="block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/servicos">
Serviços
</a>
<div class="pl-4 space-y-1">
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/servicos/eletricos">
Projetos Elétricos
</a>
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/servicos/hidrossanitarios">
Projetos Hidrossanitários
</a>
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/servicos/comunicacao">
Projetos de Comunicação
</a>
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/servicos/consultoria-bim">
Consultoria BIM
</a>
</div>
<a class="block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/projetos-dedicada">
Projetos
</a>
<div class="pl-4 space-y-1">
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/projetos-dedicada">
Todos os Projetos
</a>
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/projetos/categoria/predial">
Projetos Prediais
</a>
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/projetos/categoria/residencial">
Projetos Residenciais
</a>
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/projetos/categoria/comercial">
Projetos Comerciais
</a>
</div>
<a class="block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/blog">
Blog
</a>
<a class="block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/contato">
Contato
</a>
</div>
</div>
</nav>
</header>
<div class="fixed inset-0 z-50 hidden" id="search-modal">
<div class="absolute inset-0 bg-black/50"></div>
<div class="absolute inset-0 flex items-center justify-center p-4">
<div class="bg-white rounded-lg shadow-xl w-full max-w-2xl">
<div class="p-6">
<div class="flex items-center justify-between mb-4">
<h3 class="text-lg font-semibold text-gray-900">Buscar</h3>
<button class="text-gray-400 hover:text-gray-600 transition-colors duration-200" id="search-close">
<svg class="w-6 h-6" fill="none" stroke="currentColor" viewbox="0 0 24 24">
<path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</button>
</div>
<input class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" id="search-input" placeholder="Digite para buscar projetos..." type="text"/>
<div class="mt-4 max-h-96 overflow-y-auto hidden" id="search-results"></div>
</div>
</div>
</div>
</div>
<main id="main-content">
<section class="bg-gradient-to-br from-primary-50 to-secondary-50 py-20">
<div class="container mx-auto px-4 sm:px-6 lg:px-8">
<div class="text-center" data-aos="fade-up">
<h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
Projetos <span class="text-primary-600">Residencial</span>
</h1>
<p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
Portfólio de projetos residencial da PVP Projects. Cada projeto representa nossa dedicação à excelência técnica e inovação.
</p>
<nav aria-label="Breadcrumb" class="flex justify-center mb-8">
<ol class="flex items-center space-x-2 text-sm text-gray-600">
<li><a class="hover:text-primary-600 transition-colors duration-200" href="/">Home</a></li>
<li class="flex items-center">
<svg class="w-4 h-4 mx-2" fill="currentColor" viewbox="0 0 20 20">
<path clip-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" fill-rule="evenodd"></path>
</svg>
<a class="hover:text-primary-600 transition-colors duration-200" href="/projetos">Projetos</a>
</li>
<li class="flex items-center">
<svg class="w-4 h-4 mx-2" fill="currentColor" viewbox="0 0 20 20">
<path clip-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" fill-rule="evenodd"></path>
</svg>
<span class="text-primary-600 font-medium">Residencial</span>
</li>
</ol>
</nav>
</div>
</div>
</section>
<section class="py-16 bg-gray-50">
<div class="container mx-auto px-4 sm:px-6 lg:px-8">
<div class="mb-8 text-center">
<p class="text-gray-600">
Mostrando <span id="project-count">9</span> projetos residencial
</p>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="projects-grid">
<div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
<div class="relative h-48">
<img alt="Casa Sítio" class="w-full h-full object-cover" loading="lazy" src="/assets/imagens/casa-sitio_casa-sitio.png"/>
<div class="absolute top-4 right-4">
<span class="bg-primary-600 text-white px-2 py-1 rounded-full text-xs font-semibold">residencial</span>
</div>
</div>
<div class="p-6">
<div class="flex items-center justify-between mb-2">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
Residencial
</span>
<span class="text-sm text-gray-500">2024</span>
</div>
<h3 class="text-xl font-semibold text-gray-900 mb-2">Casa Sítio</h3>
<p class="text-gray-600 mb-4">Projeto completo para residência rural com dois pavimentos e sistema séptico autônomo</p>
<div class="flex flex-wrap gap-1 mb-4">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Elétrico
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Hidrossanitário
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Residencial
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
AutoCAD
</span>
</div>
<div class="flex items-center justify-between">
<span class="text-sm text-gray-500">Sítio em Porto Alegre – RS</span>
<a class="text-primary-600 hover:text-primary-700 font-medium" href="/projetos/casa-sitio">
Ver detalhes →
</a>
</div>
</div>
</div>
<div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
<div class="relative h-48">
<img alt="Casa GP" class="w-full h-full object-cover" loading="lazy" src="/assets/imagens/rendergp.png"/>
<div class="absolute top-4 right-4">
<span class="bg-primary-600 text-white px-2 py-1 rounded-full text-xs font-semibold">residencial</span>
</div>
</div>
<div class="p-6">
<div class="flex items-center justify-between mb-2">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
Residencial
</span>
<span class="text-sm text-gray-500">2023</span>
</div>
<h3 class="text-xl font-semibold text-gray-900 mb-2">Casa GP</h3>
<p class="text-gray-600 mb-4">Residência unifamiliar — 3 pavimentos / 280 m² — com rooftop e espera fotovoltaica</p>
<div class="flex flex-wrap gap-1 mb-4">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Elétrico
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Residencial
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
AutoCAD
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Fotovoltaico
</span>
</div>
<div class="flex items-center justify-between">
<span class="text-sm text-gray-500">Porto Alegre</span>
<a class="text-primary-600 hover:text-primary-700 font-medium" href="/projetos/casa-gp">
Ver detalhes →
</a>
</div>
</div>
</div>
<div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
<div class="relative h-48">
<img alt="Casa CD" class="w-full h-full object-cover" loading="lazy" src="/assets/imagens/render cd.png"/>
<div class="absolute top-4 right-4">
<span class="bg-primary-600 text-white px-2 py-1 rounded-full text-xs font-semibold">residencial</span>
</div>
</div>
<div class="p-6">
<div class="flex items-center justify-between mb-2">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
Residencial
</span>
<span class="text-sm text-gray-500">2022</span>
</div>
<h3 class="text-xl font-semibold text-gray-900 mb-2">Casa CD</h3>
<p class="text-gray-600 mb-4">Residência de alto padrão — 2 pavimentos / entrada de energia detalhada</p>
<div class="flex flex-wrap gap-1 mb-4">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Elétrico
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Residencial
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
AutoCAD
</span>
</div>
<div class="flex items-center justify-between">
<span class="text-sm text-gray-500">Gravataí</span>
<a class="text-primary-600 hover:text-primary-700 font-medium" href="/projetos/casa-cd">
Ver detalhes →
</a>
</div>
</div>
</div>
<div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
<div class="relative h-48">
<img alt="Casa GT" class="w-full h-full object-cover" loading="lazy" src="/assets/imagens/render gt.png"/>
<div class="absolute top-4 right-4">
<span class="bg-primary-600 text-white px-2 py-1 rounded-full text-xs font-semibold">residencial</span>
</div>
</div>
<div class="p-6">
<div class="flex items-center justify-between mb-2">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
Residencial
</span>
<span class="text-sm text-gray-500">2022</span>
</div>
<h3 class="text-xl font-semibold text-gray-900 mb-2">Casa GT</h3>
<p class="text-gray-600 mb-4">Residência unifamiliar — projeto elétrico completo com espera para energia solar</p>
<div class="flex flex-wrap gap-1 mb-4">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Elétrico
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Residencial
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
AutoCAD
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Fotovoltaico
</span>
</div>
<div class="flex items-center justify-between">
<span class="text-sm text-gray-500">Porto Alegre</span>
<a class="text-primary-600 hover:text-primary-700 font-medium" href="/projetos/casa-gt">
Ver detalhes →
</a>
</div>
</div>
</div>
<div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
<div class="relative h-48">
<img alt="Reforma Casa" class="w-full h-full object-cover" loading="lazy" src="/assets/imagens/1_photo-1_adriene.jpg"/>
<div class="absolute top-4 right-4">
<span class="bg-primary-600 text-white px-2 py-1 rounded-full text-xs font-semibold">residencial</span>
</div>
</div>
<div class="p-6">
<div class="flex items-center justify-between mb-2">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
Residencial
</span>
<span class="text-sm text-gray-500">2024</span>
</div>
<h3 class="text-xl font-semibold text-gray-900 mb-2">Reforma Casa</h3>
<p class="text-gray-600 mb-4">Reforma elétrica completa de residência térrea — atualização total da infraestrutura elétrica</p>
<div class="flex flex-wrap gap-1 mb-4">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Elétrico
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Residencial
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
AutoCAD
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Reforma
</span>
</div>
<div class="flex items-center justify-between">
<span class="text-sm text-gray-500">Porto Alegre, RS</span>
<a class="text-primary-600 hover:text-primary-700 font-medium" href="/projetos/reforma-adriene">
Ver detalhes →
</a>
</div>
</div>
</div>
<div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
<div class="relative h-48">
<img alt="AL" class="w-full h-full object-cover" loading="lazy" src="/assets/imagens/render_photo-2_alexandre.jpg"/>
<div class="absolute top-4 right-4">
<span class="bg-primary-600 text-white px-2 py-1 rounded-full text-xs font-semibold">residencial</span>
</div>
</div>
<div class="p-6">
<div class="flex items-center justify-between mb-2">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
Residencial
</span>
<span class="text-sm text-gray-500">2019</span>
</div>
<h3 class="text-xl font-semibold text-gray-900 mb-2">AL</h3>
<p class="text-gray-600 mb-4">Projeto completo para residência unifamiliar de alto padrão com 3 pavimentos</p>
<div class="flex flex-wrap gap-1 mb-4">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Elétrico
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Hidrossanitário
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Residencial
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
AutoCAD
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Revit
</span>
</div>
<div class="flex items-center justify-between">
<span class="text-sm text-gray-500">Condomínio fechado – RS</span>
<a class="text-primary-600 hover:text-primary-700 font-medium" href="/projetos/projeto-alexandre">
Ver detalhes →
</a>
</div>
</div>
</div>
<div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
<div class="relative h-48">
<img alt="NF" class="w-full h-full object-cover" loading="lazy" src="/assets/imagens/foto_photo-1_nairo.jpg"/>
<div class="absolute top-4 right-4">
<span class="bg-primary-600 text-white px-2 py-1 rounded-full text-xs font-semibold">residencial</span>
</div>
</div>
<div class="p-6">
<div class="flex items-center justify-between mb-2">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
Residencial
</span>
<span class="text-sm text-gray-500">2018</span>
</div>
<h3 class="text-xl font-semibold text-gray-900 mb-2">NF</h3>
<p class="text-gray-600 mb-4">Projeto completo para residência de luxo com 2 pavimentos + subsolo, piscina e garagem subterrânea</p>
<div class="flex flex-wrap gap-1 mb-4">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Elétrico
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Residencial
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
AutoCAD
</span>
</div>
<div class="flex items-center justify-between">
<span class="text-sm text-gray-500">Condomínio fechado – RS</span>
<a class="text-primary-600 hover:text-primary-700 font-medium" href="/projetos/projeto-nairo">
Ver detalhes →
</a>
</div>
</div>
</div>
<div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
<div class="relative h-48">
<img alt="Projeto LEP" class="w-full h-full object-cover" loading="lazy" src="/assets/imagens/01-personalizado_lep.jpg"/>
<div class="absolute top-4 right-4">
<span class="bg-primary-600 text-white px-2 py-1 rounded-full text-xs font-semibold">residencial</span>
</div>
</div>
<div class="p-6">
<div class="flex items-center justify-between mb-2">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
Residencial
</span>
<span class="text-sm text-gray-500">2020</span>
</div>
<h3 class="text-xl font-semibold text-gray-900 mb-2">Projeto LEP</h3>
<p class="text-gray-600 mb-4">Projeto completo de instalações elétricas e hidrossanitárias para residência de alto padrão</p>
<div class="flex flex-wrap gap-1 mb-4">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Elétrico
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Residencial
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
AutoCAD
</span>
</div>
<div class="flex items-center justify-between">
<span class="text-sm text-gray-500">Condomínio fechado – RS</span>
<a class="text-primary-600 hover:text-primary-700 font-medium" href="/projetos/projeto-lep">
Ver detalhes →
</a>
</div>
</div>
</div>
<div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
<div class="relative h-48">
<img alt="RP" class="w-full h-full object-cover" loading="lazy" src="/assets/imagens/render_rafael.jpg"/>
<div class="absolute top-4 right-4">
<span class="bg-primary-600 text-white px-2 py-1 rounded-full text-xs font-semibold">residencial</span>
</div>
</div>
<div class="p-6">
<div class="flex items-center justify-between mb-2">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
Residencial
</span>
<span class="text-sm text-gray-500">2020</span>
</div>
<h3 class="text-xl font-semibold text-gray-900 mb-2">RP</h3>
<p class="text-gray-600 mb-4">Projeto completo para residência unifamiliar com sistema séptico individual</p>
<div class="flex flex-wrap gap-1 mb-4">
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Elétrico
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Hidrossanitário
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Residencial
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
AutoCAD
</span>
<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
Revit
</span>
</div>
<div class="flex items-center justify-between">
<span class="text-sm text-gray-500">São Leopoldo – RS</span>
<a class="text-primary-600 hover:text-primary-700 font-medium" href="/projetos/projeto-rafael">
Ver detalhes →
</a>
</div>
</div>
</div>
</div>
</div>
</section>

</main>
<footer class="bg-gray-900 text-white">
<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
<div class="grid grid-cols-1 md:grid-cols-4 gap-8">
<div class="md:col-span-2">
<div class="flex items-center space-x-2 mb-4">
<img alt="PVP Projects Logo" class="w-20 h-20 object-contain rounded-lg" src="/assets/imagens/favicon-new.png"/>
<span class="font-bold text-xl">PVP Projects</span>
</div>
<p class="text-gray-300 mb-4 max-w-md">Soluções técnicas lideradas por um Engenheiro Eletricista com experiência em projetos de alta qualidade – unindo segurança e design para encantar arquitetos e clientes.</p>
<div class="flex space-x-4">
<a aria-label="WhatsApp" class="text-gray-400 hover:text-white transition-colors duration-200" href="https://wa.me/5554991590379" target="_blank">
<svg class="w-5 h-5" fill="currentColor" viewbox="0 0 24 24">
<path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"></path>
</svg>
</a>
<a aria-label="Email" class="text-gray-400 hover:text-white transition-colors duration-200" href="mailto:<EMAIL>" target="_blank">
<svg class="w-5 h-5" fill="none" stroke="currentColor" viewbox="0 0 24 24">
<path d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</a>
<a aria-label="LinkedIn" class="text-gray-400 hover:text-white transition-colors duration-200" href="https://linkedin.com/in/pedro-vitor-pagliarin" target="_blank">
<svg class="w-5 h-5" fill="currentColor" viewbox="0 0 24 24">
<path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"></path>
</svg>
</a>
</div>
</div>
<div>
<h3 class="font-semibold text-white mb-4">Empresa</h3>
<ul class="space-y-2">
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/sobre">
Sobre Nós
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/servicos">
Serviços
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/projetos-dedicada">
Projetos
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/blog">
Blog
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/contato">
Contato
</a>
</li>
</ul>
</div>
<div>
<h3 class="font-semibold text-white mb-4">Serviços</h3>
<ul class="space-y-2">
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/servicos/eletricos">
Projetos Elétricos
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/servicos/hidrossanitarios">
Projetos Hidrossanitários
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/servicos/comunicacao">
Projetos de Comunicação
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/servicos/consultoria-bim">
Consultoria BIM
</a>
</li>
</ul>
</div>
<div>
<h3 class="font-semibold text-white mb-4">Legal</h3>
<ul class="space-y-2">
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/legal/privacidade">
Política de Privacidade
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/legal/termos">
Termos de Uso
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/legal/cookies">
Cookies
</a>
</li>
</ul>
</div>
</div>
<div class="border-t border-gray-800 mt-8 pt-8">
<div class="flex flex-col md:flex-row justify-between items-center">
<p class="text-gray-400 text-sm">
© 2024 PVP Projects. Todos os direitos reservados.
</p>
<div class="flex space-x-6 mt-4 md:mt-0">
<a class="text-gray-400 hover:text-white text-sm transition-colors duration-200" href="/legal/privacidade">
Política de Privacidade
</a>
<a class="text-gray-400 hover:text-white text-sm transition-colors duration-200" href="/legal/termos">
Termos de Uso
</a>
</div>
</div>
</div>
</div>
</footer>
<button aria-label="Voltar ao topo" class="scroll-to-top fixed bottom-8 right-8 bg-primary-600 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 transition-all duration-200 opacity-0 pointer-events-none z-50">
<svg class="w-6 h-6" fill="none" stroke="currentColor" viewbox="0 0 24 24">
<path d="M5 10l7-7m0 0l7 7m-7-7v18" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</button>
<div class="fixed top-4 right-4 z-50" id="notification-container"></div>
<a aria-label="Contatar via WhatsApp" class="fixed bottom-20 right-8 bg-green-500 text-white p-4 rounded-full shadow-lg hover:bg-green-600 transition-all duration-200 z-50 group" href="https://wa.me/5554991590379" target="_blank">
<svg class="w-6 h-6" fill="currentColor" viewbox="0 0 24 24">
<path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"></path>
</svg>
<span class="absolute left-full ml-3 bg-white text-gray-800 px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap text-sm font-medium">
Fale conosco!
</span>
</a>






</body>
</html> 