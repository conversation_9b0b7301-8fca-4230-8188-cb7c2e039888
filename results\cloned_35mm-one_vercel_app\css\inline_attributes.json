[{"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-0", "h-0", "border-l-[calc(var(--s)*4)]", "absolute", "left-[0]", "translate-x-[-50%]", "top-0", "border-l-[transparent]", "border-r-[transparent]", "border-r-[calc(var(--s)*4)]", "border-t-[calc(var(--s)*8)]", "border-t-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "border-l", "border-dark2", "h-[calc(var(--s)*16)]"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "border-l", "border-dark2", "h-[calc(var(--s)*16)]"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "border-l", "border-dark2", "h-[calc(var(--s)*16)]"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "border-l", "border-dark2", "h-[calc(var(--s)*16)]"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*8)]", "border-l", "border-dark2"]}, {"tag": "span", "style": "visibility:hidden", "classes": ["block", "w-[calc(var(--s)*8)]", "h-[calc(var(--s)*16)]", "border-l", "border-dark2"]}, {"tag": "div", "style": "position:absolute;top:50dvh;transform:translateY(-50%);right:0;z-index:999", "classes": ["hidden", "md:block"]}, {"tag": "p", "style": "opacity:0", "classes": []}, {"tag": "div", "style": "", "classes": ["absolute", "top-[50%]", "left-[50%]", "-translate-x-[50%]", "-translate-y-[50%]", "aspect-square"]}]