/*! zaz-app-t360-navbar - v1.0.0 - 01/07/2025 -- 6:00pm */

zaz.use(function(pkg){"use strict";var console=pkg.console,dictFactory;pkg.factoryManager.get("dict").create({name:"t360.navbar",version:"1.0.0",state:"ok",extends:[],langs:{global:{termWithPlural:["plural","singular"]},pt:{term:"Termo em Português"},es:{term:"Termo in Español"},en:{term:"Term in English"},"es-AR":{term:"Termo en Argentina"}}})}),zaz.use(function navbarMenu(pkg){"use strict";var appFactory,STATIC_PUBLIC=null,STATIC_PRIVATE={};pkg.factoryManager.get("app").create({name:"navbarMenu",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/t360-navbar",source:"http://github.tpn.terra.com/Terra/t360-navbar",description:"Just another app",tests:"http://s1.trrsf.com/fe/t360-navbar/tests/index.htm?zaz[env]=tests",dependencies:[],dictionaries:["t360.navbar"],templates:{menu:'<div class="navbar-modules__item navbar-menu" data-plugin-name="menu"><div class="navbar-menu--list--container"></div></div>',mainList:'<ul class="navbar-menu--list navbar-menu--list--main on-left displayed" data-link="navbar-mainlist"><div class="navbar-menu--search-container"><form method="GET" action="https://www.terra.com.br/busca/"><input type="text" name="q" placeholder="Buscar no portal"><button type="submit"><span class="icon-solid icon-16 icon-color-black icon-search"></span></button></form></div>{% for item in items %}{% set label = item.label %}{% if item.children %}<li class="navbar-menu--list--item navbar-menu--list--item--with-navigation"><a href="{{ item.url | safe }}" data-parent="navbar-mainlist" data-link="navbar-{{ item.id }}"><div class="navbar-menu--list--item--label">{{ label }} {% if item.image %}<img src="https://p2.trrsf.com/image/fget/cf/0/0/{{ item.image|replace(\'http://\',\'\') }}" alt="img" height="20px">{% endif %}</div><span class="icon-solid icon-16 icon-chevron-right"></span></a></li>{% else %}<li class="navbar-menu--list--item navbar-menu--list--item--exit-link"><a href="{{ item.url | safe }}" target="{{\'_blank\' if not item.isInternalDomain }}" data-label="{{ label }}">{{ label }}</a></li>{% endif %}{% endfor %}</ul>',secondaryList:'{% set parentLabel = parentItem.label if parentItem.label else parentItem.channel.label %}<ul class="navbar-menu--list navbar-menu--list--secondary" data-link="navbar-{{id}}"><li class="navbar-menu--list--item navbar-menu--list--item--voltar"><a href="#" data-parent="navbar-{{parent}}" data-link="navbar-{{id}}"><span class="icon-solid icon-16 icon-color-black icon-chevron-left"></span> Voltar</a></li><hr><li class="navbar-menu--list--item navbar-menu--list--item--header navbar-menu--list--item--exit-link">{% if parentItem.url %}<a href="{{ parentItem.url | safe }}" target="{{\'_blank\' if not item.isInternalDomain }}"style="color:{{ (\'var(--landing-primary-text-color-\' + landing + \')\') if landing else \'var(--default-highlight-text-color)\' }}" data-label="{{ parentLabel }}">Tudo sobre {{ parentLabel }}</a>{% else %}<span style="color:{{ (\'var(--landing-primary-text-color-\' + landing + \')\') if landing else \'var(--default-highlight-text-color)\' }}"data-label="{{ parentLabel }}">{{ parentLabel }}</span>{% endif %}</li>{% for item in items %}{% set label = item.label %}{% if item.children %}<li class="navbar-menu--list--item navbar-menu--list--item--with-navigation"><a href="{{ item.url | safe }}" target="{{\'_blank\' if not item.isInternalDomain }}" data-parent="navbar-{{id}}" data-link="navbar-{{ item.id }}"><div class="navbar-menu--list--item--label">{{ label }} {% if item.image %}<img src="https://p2.trrsf.com/image/fget/cf/0/0/{{ item.image|replace(\'http://\',\'\') }}" alt="img" height="20px">{% endif %}</div><span class="icon-solid icon-16 icon-chevron-right"></span></a></li>{% elif isInternalDomain(item.url) %}<li class="navbar-menu--list--item navbar-menu--list--item--exit-link"><a href="{{ item.url | safe }}" target="{{\'_blank\' if not item.isInternalDomain }}" data-label="{{ label }}"><div class="navbar-menu--list--item--label">{{ label }} {% if item.image %}<img src="https://p2.trrsf.com/image/fget/cf/0/0/{{ item.image|replace(\'http://\',\'\') }}" alt="img" height="20px">{% endif %}</div></a></li>{% else %}<li class="navbar-menu--list--item navbar-menu--list--item--exit-link"><a href="{{ item.url | safe }}" target="{{\'_blank\' if not item.isInternalDomain }}" data-label="{{ label }}" target="_blank"><div class="navbar-menu--list--item--label">{{ label }} {% if item.image %}<img src="https://p2.trrsf.com/image/fget/cf/0/0/{{ item.image|replace(\'http://\',\'\') }}" alt="img" height="20px">{% endif %}</div></a></li>{% endif %}{% endfor %}</ul>'},setup:function(STATIC_PUBLIC,PUBLIC,__shared){},init:function(data,__shared){var PRIVATE={},PUBLIC=this;return PRIVATE.elemModules=data.container,PRIVATE.navbar=data.navbar,PRIVATE.alreadyCreatedMenu=!1,PRIVATE.IOSAppExcludedItems={PPSN:332,PPV:344},PUBLIC.init=function(elemModules){var fragmentUserArea;PRIVATE.alreadyCreatedMenu?(PRIVATE.currentlyDisplayedList.classList.remove("displayed","on-left","on-right"),PRIVATE.mainList.classList.add("displayed","on-left"),PRIVATE.currentlyDisplayedList=PRIVATE.mainList):(fragmentUserArea=PUBLIC.templates.render("menu",{}),PRIVATE.elemModules.appendChild(fragmentUserArea),PRIVATE.elemUserArea=PRIVATE.elemModules.querySelector(".navbar-menu--list--container"),PRIVATE.scrollArea=PRIVATE.elemModules.querySelector(".navbar-menu"),PRIVATE.alreadyCreatedMenu=!0,PRIVATE.currentlyDisplayedList=void 0,PRIVATE.mainList=void 0,PRIVATE.isIOSApp=pkg.context.platform.get("isIOSApp"),fetch("https://www.terra.com.br/feeder/navbar/catalog/1.json").then(function(response){return response.json()}).then(function(data){PRIVATE.isIOSApp?PRIVATE.menuList=data.filter(function(item){return!1===Object.values(PRIVATE.IOSAppExcludedItems).includes(item.id)}):PRIVATE.menuList=data,PRIVATE.recursiveBuilder(PRIVATE.menuList,void 0,void 0),PRIVATE.mainList=PRIVATE.elemUserArea.querySelector(".navbar-menu--list--main"),PRIVATE.currentlyDisplayedList=PRIVATE.mainList,Array.prototype.forEach.call(PRIVATE.elemUserArea.querySelectorAll("li.navbar-menu--list--item--exit-link a"),function(node){node.addEventListener("click",function(el){var el=el.currentTarget.getAttribute("data-label");window.tga.send("send","event","nav-burger","click",el)},!1)}),Array.prototype.forEach.call(PRIVATE.elemUserArea.querySelectorAll("li.navbar-menu--list--item--with-navigation a"),function(node){node.addEventListener("click",function(whereTo){whereTo.stopPropagation(),whereTo.preventDefault(),PRIVATE.scrollArea.scrollTop=0;var parent=whereTo.currentTarget.getAttribute("data-parent"),whereTo=whereTo.currentTarget.getAttribute("data-link");return PRIVATE.advanceMenu(PRIVATE.elemUserArea.querySelector('ul[data-link="'+parent+'"]'),PRIVATE.elemUserArea.querySelector('ul[data-link="'+whereTo+'"]')),!1},!1)}),Array.prototype.forEach.call(PRIVATE.elemUserArea.querySelectorAll("li.navbar-menu--list--item--voltar a"),function(node){node.addEventListener("click",function(currentPosition){currentPosition.stopPropagation(),currentPosition.preventDefault(),PRIVATE.scrollArea.scrollTop=0;var parent=currentPosition.currentTarget.getAttribute("data-parent"),currentPosition=currentPosition.currentTarget.getAttribute("data-link");return PRIVATE.returnMenu(PRIVATE.elemUserArea.querySelector('ul[data-link="'+currentPosition+'"]'),PRIVATE.elemUserArea.querySelector('ul[data-link="'+parent+'"]')),!1},!1)})}))},PRIVATE.onTransitionEnd=function(from,to){PRIVATE.elemUserArea.classList.remove("transition","go-forward","go-back"),from.classList.remove("displayed","on-right","on-left"),to.classList.remove("on-right"),to.classList.add("on-left"),PRIVATE.elemUserArea.removeEventListener("transitionend",PRIVATE.transitionEndClosure)},PRIVATE.advanceMenu=function(from,to){PRIVATE.elemUserArea.classList.remove("transition","go-forward","go-back"),from.classList.remove("on-right"),from.classList.add("on-left"),to.classList.add("displayed","on-right"),PRIVATE.elemUserArea.classList.add("transition","go-forward"),PRIVATE.transitionEndClosure=function(){PRIVATE.onTransitionEnd(from,to)},PRIVATE.elemUserArea.addEventListener("transitionend",PRIVATE.transitionEndClosure),PRIVATE.currentlyDisplayedList=to},PRIVATE.returnMenu=function(from,to){PRIVATE.elemUserArea.classList.remove("transition","go-forward","go-back"),from.classList.add("on-right"),from.classList.remove("on-left"),to.classList.remove("on-right"),to.classList.add("displayed","on-left"),PRIVATE.elemUserArea.classList.add("go-forward"),PRIVATE.elemUserArea.classList.add("transition"),PRIVATE.elemUserArea.classList.add("go-back"),PRIVATE.elemUserArea.classList.remove("go-forward"),PRIVATE.transitionEndClosure=function(){PRIVATE.onTransitionEnd(from,to)},PRIVATE.elemUserArea.addEventListener("transitionend",PRIVATE.transitionEndClosure),PRIVATE.currentlyDisplayedList=to},PRIVATE.captureLandingNameOutOfURL=function(url){if(url)for(var validLandingNames=["vida-e-estilo","noticias","esportes","diversao","economia","gameon","nos","eleicoes","vidav","visao-do-corre","carros-motos","degusta","byte","saudebucal","entre-telas","planeta","black-friday","verao","papo-de-arena","carnaval","voce","parada-sp","educacao","allsignature"],i=0;i<validLandingNames.length;i++)if(-1<url.toString().indexOf(validLandingNames[i]))return validLandingNames[i].replaceAll("-","")},PRIVATE.recursiveBuilder=function(items,parentId,currentId,parentItem){var template,fragmentList;parentId?(template="secondaryList",fragmentList=PRIVATE.captureLandingNameOutOfURL(parentItem.url)):(template="mainList",currentId=parentId="mainlist"),items.forEach(function(item){item.url&&(-1<item.url.search("www.terra.com.br")?item.isInternalDomain=!0:item.isInternalDomain=!1)});var fragmentList=PUBLIC.templates.render(template,{items:items,parent:parentId,id:currentId,parentItem:parentItem,isInternalDomain:PRIVATE.isInternalDomain,landing:fragmentList});PRIVATE.elemUserArea.appendChild(fragmentList),items.forEach(function(item){item.children&&PRIVATE.recursiveBuilder(item.children,currentId,item.id,item)})},PRIVATE.isInternalDomain=function(url){return url&&(-1<url.search("www.terra.com.br")||-1<url.search("tpn.terra.com"))},PUBLIC},teardown:function(why,__static,__proto,__shared){}})}),zaz.use(function navbarUserArea(pkg){"use strict";var appFactory=pkg.factoryManager.get("app"),STATIC_PUBLIC=null,STATIC_PRIVATE={processingRequest:!1,topics:[{id:"breakingnews",text:"Principais notícias"},{id:"vidaeestilo_horoscopo",text:"Notícias de horóscopo"},{id:"esportes",text:"Notícias de esportes"},{id:"esportes_futebol",text:"Notícias de futebol"},{id:"noticias_educacao",text:"Notícias de educação"},{id:"vidaeestilo_saude_bemestar",text:"Notícias de bem-estar"},{id:"diversao",text:"Notícias de entretenimento"},{id:"diversao_games",text:"Notícias de games"},{id:"noticias_tecnologia",text:"Notícias de tecnologia"},{id:"vidaeestilo_culinaria",text:"Receitas"},{id:"horoscope",text:"Horóscopo do dia"},{id:"myteammatches",text:"Partidas do meu time"}]};appFactory.create({name:"navbarUserArea",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/t360-navbar",source:"http://github.tpn.terra.com/Terra/t360-navbar",description:"Just another app",tests:"http://s1.trrsf.com/fe/t360-navbar/tests/index.htm?zaz[env]=tests",dependencies:["mod.t360.notificationsHelper"],dictionaries:["t360.navbar"],templates:{userarea:'<div class="navbar-modules__item navbar-user-area" data-plugin-name="user-area"><div id="user-area-modal-shadow" class="navbar-user-area__modal-shadow"></div><div class="navbar-my-user"><div class="navbar-my-user__header"><div class="navbar-my-user__header__close"><span id="user-area-close" class="icon icon-16 icon-times icon-solid icon-color-black"></span></div><div class="navbar-my-user__header__area"><span class="navbar-my-user__header__area__title">Seu Terra</span><div class="navbar-my-user__header__area__loged-in"><span class="navbar-my-user__header__area__loged-in__name"></span><button id="user-area-login-btn" class="navbar-my-user__header__area__loged-in__logout-button hidden">Sair</button></div><p class="navbar-my-user__header__area__message hidden">Aproveite e configure suas preferências</p><div class="navbar-user-area__login" data-width="240"></div></div></div><div class="navbar-my-user__body"><div><details id="details-portal-content" class="navbar-my-user__body__details"><summary id="summary-portal-content" class="navbar-my-user__body__details__summary"><span>Conteúdos do Portal</span><span class="icon icon-16 icon-angle-right icon-solid"></span></summary><div class="navbar-my-user__body__details__content" id="navbar-portal-content"><nav class="navbar-portal-content"><ul class="navbar-portal-content__list"><li id="futebol-btn" class="navbar-portal-content__list__item active"><input type="radio" hidden value="futebol" id="portal-content-futebol" name="portal-content" checked><label class="item-text" for="portal-content-futebol">Futebol</label></li><li id="horoscopo-btn" class="navbar-portal-content__list__item"><input type="radio" hidden value="horoscopo" id="portal-content-horoscopo" name="portal-content"><label class="item-text" for="portal-content-horoscopo">Horóscopo</label></li><li id="clima-btn" class="navbar-portal-content__list__item"><input type="radio" hidden value="clima" id="portal-content-clima" name="portal-content"><label class="item-text" for="portal-content-clima">Clima</label></li>\x3c!-- <li id="futebol-button" class="navbar-portal-content__list__item {% if notificationTab == false %}active{% endif %}"><span class="item-text">Futebol</span></li><li id="horoscopo-button" class="navbar-portal-content__list__item"><span class="item-text">Horóscopo</span></li><li id="clima-button" class="navbar-portal-content__list__item"><span class="item-text">Clima</span></li> --\x3e</ul></nav></div><div class="navbar-user-area__widget__wrapper"><div id="widget-area-futebol" class="navbar-user-area__widget card card-app loading-app"></div><div id="widget-area-horoscopo" class="navbar-user-area__widget card card-app loading-app hidden"></div><div id="widget-area-clima" class="navbar-user-area__widget card card-app loading-app hidden"></div></div></details><details id="details-portal-settings" class="navbar-my-user__body__details"><summary id="summary-portal-settings" class="navbar-my-user__body__details__summary"><span>Configurações do Portal</span><span class="icon icon-16 icon-angle-right icon-solid"></span></summary><div class="navbar-my-user__body__details__summary__content"><div id="widget-area-notificacoes" class="navbar-user-area__widget loading-app navbar-user-area__my-user__notifications"></div></div></details></div></div></div><a href="https://whatsapp.com/channel/0029VaDGPXE0rGiN2XvTl03k" class="navbar-user-area__whatsapp" target="_blank"><span class="icon-solid icon-20 icon-whatsapp-icon-color-main"></span><div class="navbar-user-area__whatsapp--text">Inscreva-se no Whatsapp do Terra</div></a>{% include navbarServiceTerra %}</div>',userareaNotifications:'<div class="navbar-user-area__notifications-config"><div class="navbar-user-area__notifications-config__header"><h2>Gerencie suas notificações</h2></div><ul class="navbar-user-area__notifications-config__list">{% for topic in topics %}{% set topic_enabled = userArea and userArea.notifications and userArea.notifications[topic.id] %}<li class="navbar-user-area__notifications-config__list-item"><span class="navbar-user-area__notifications-config__list-item__text">{{ topic.text }}</span><span id="sw-{{ topic.id}}" class="navbar-user-area__notifications-config__list-item__switch-btn" data-state="{% if topic_enabled %}on{% else %}off{% endif %}"><div class="btn-background-layer {% if not topic_enabled %}disabled{% endif %}"></div><div class="btn-ball {% if not topic_enabled %}disabled-ball{% endif %}"></div></span></li>{% endfor %}<li><div class="navbar-user-area__settings__content"><ul class="navbar-user-area__settings-container"><li id="setting-user-theme" class="navbar-user-area__settings-container__item"><span>Escolha o tema do Portal</span><select id="select-user-theme" class="navbar-user-area__settings-container__select js-user-area-setting" data-setting-type="select"><option value="auto">Usar Tema do Dispositivo</option><option value="dark">Tema Escuro</option><option value="light">Tema Claro</option></select></li></ul></div></li> </ul></div>',notificationOverlay:'<div class="overlay-notification"><div id="overlay-title">Realizando inscrição, aguarde </div></div>',navbarServiceTerra:'<div class="navbar-servicesterra"><div class="navbar-servicesterra__header"><span class="navbar-servicesterra__header__title">Terra Serviços</span><span class="navbar-servicesterra__header__description">Acesse seus serviços e aproveite o máximo dos seus benefícios oferecidos</span></div><a href="http://central.terra.com.br/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=menu_usuario&utm_content=pg&utm_term=central&cdConvenio=CVTR00002081 "target="_blank" class="navbar-servicesterra__button-subscriber"><span>Acessar Central do Assinante</span></a><a href="https://www.terra.com.br/vida-e-estilo/horoscopo/terra-astral/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=barra-central&utm_content=pg&utm_term=terra-astral_navbar-portal&cdConvenio=CVTR00002055" class="navbar-servicesterra__button-terra-astral"><span class="navbar-servicesterra__button-terra-astral--icon icon-solid icon-20 icon-saturn-planet-new"></span><div class="navbar-servicesterra__button-terra-astral--text">Acessar Terra Astral</div></a><ul class="navbar-servicesterra__menu"><a class="navbar-servicesterra__menu__items"  target="_blank"href="https://mail.terra.com.br/signin/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=menu_usuario&utm_content=pg&utm_term=mail-terra&cdConvenio=CVTR00002081"><li>Acessar e-mail do Terra</li></a><a  class="navbar-servicesterra__menu__items" target="_blank"href="https://central.terra.com.br/atendimento/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=menu_usuario&utm_content=pg&utm_term=fale-terra&cdConvenio=CVTR00002081"><li>Fale com o Terra</li></a><a  class="navbar-servicesterra__menu__items"  target="_blank" href="https://central.terra.com.br/boleto-simplificado/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=menu_usuario&utm_content=pg&utm_term=2-via-boleto&cdConvenio=CVTR00002081"><li>2ª via de boleto</li></a><a  class="navbar-servicesterra__menu__items"  target="_blank" href="https://api.whatsapp.com/send/?phone=5508007771234&text=Quero+saber+mais&type=phone_number&app_absent=0&utm_campaign=menu_usuario&utm_source=portal-terra&utm_content=pg&utm_term=compre-via-whatsapp&cdConvenio=CVTR00002081"><li>Compre pelo Whatsapp</li></a><a  class="navbar-servicesterra__menu__items"  target="_blank" href="https://servicos.terra.com.br/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=menu_usuario&utm_content=pg&utm_term=capa-triagem&cdConvenio=CVTR00002081"><li>Conheça nossos produtos</li></a></ul></div>'},setup:function(STATIC_PUBLIC,PUBLIC,__shared){STATIC_PRIVATE.postSegments=__shared.dependencies["mod.t360.notificationsHelper"].postSegments,STATIC_PRIVATE.loadFirebaseMessaging=__shared.dependencies["mod.t360.notificationsHelper"].loadFirebaseMessaging,PUBLIC.subscribeSegments=function(action,segments,stalkerKeyName){if("www.terra.com.br"!=window.location.hostname)return STATIC_PRIVATE.printMessage("Desculpe, domínio atual não permite gerenciar inscrições.","error","sw-"+stalkerKeyName),void(STATIC_PRIVATE.processingRequest=!1);var oldPermission=window.Notification.permission;"granted"!=oldPermission&&STATIC_PRIVATE.showBlockingLayer(),pkg.context.user.get("userArea",function(userArea){STATIC_PRIVATE.loadFirebaseMessaging().then(function(messaging){console.log("Firebase JS loaded on userArea subscribeSegments() method."),navigator.serviceWorker.register("/push/fcm/fcm.js",{scope:"/push/fcm/"}).then(function(registration){console.log("Firebase serviceworker registered."),Notification.requestPermission().then(function(){console.log("Firebase Notification permission granted. Trying to get GCM token...");var getTokenStart=null;"granted"!=oldPermission&&(STATIC_PRIVATE.signupResult(stalkerKeyName,"request_permission"),STATIC_PRIVATE.showPleaseWait(),getTokenStart=performance.now(),window.tga.send("send","event","info","fcm","gettoken_start")),messaging.getToken({serviceWorkerRegistration:registration}).then(function(token){var getTokenMs,getTokenMs;token?(null!==getTokenStart&&(getTokenMs=performance.now(),getTokenMs=Math.trunc(getTokenMs-getTokenStart),window.tga.send("send","event","info","fcm","gettoken_end",getTokenMs)),console.log("Firebase Token OK",token),"granted"!==oldPermission&&STATIC_PRIVATE.signupResult(stalkerKeyName,"allowed"),STATIC_PRIVATE.postSegments(action,segments,token,stalkerKeyName,"fcm").then(function(){STATIC_PRIVATE.segmentResultSuccess(userArea,action,segments,stalkerKeyName,token)}).catch(function(){STATIC_PRIVATE.segmentResultError(action,segments,stalkerKeyName)})):(console.log("Firebase Couldnt get token. No Instance ID token available."),STATIC_PRIVATE.signupResult(stalkerKeyName,"couldnt_get_token"))}).catch(function(err){"messaging/permission-blocked"==err.code?(console.log("Firebase Unable to get permission to notify.",err),STATIC_PRIVATE.signupResult(stalkerKeyName,"denied")):(console.log("Firebase An error occurred while retrieving token. ",err),STATIC_PRIVATE.signupResult(stalkerKeyName,"couldnt_get_token_"+err.code))})})}).catch(function(err){console.log("Firebase Unable to register ServiceWorker.",err),STATIC_PRIVATE.signupResult(stalkerKeyName,"couldnt_register_serviceworker")})})})},STATIC_PUBLIC.subscribeSegments=PUBLIC.subscribeSegments,STATIC_PRIVATE.signupResult=function(stalkerKeyName,val){-1==["allowed","request_permission"].indexOf(val)&&(STATIC_PRIVATE.removeBlockingLayer(),STATIC_PRIVATE.processingRequest=!1,STATIC_PRIVATE.printMessage("Desculpe, ocorreu um erro na sua inscrição. Tente novamente.","error","sw-"+stalkerKeyName));var metricLabel;window.gaTerra("send","event","user_config","notifications",val,{dimension9:"terra"})},STATIC_PRIVATE.segmentResultSuccess=function(userArea,switchObj,teamId,stalkerKeyName,token){var metricAction="segments_add"==switchObj?"added":"removed",metricLabel="fcm_segment_"+stalkerKeyName+"_"+metricAction,switchObj="sw-"+stalkerKeyName,switchObj=document.getElementById(switchObj),teamId;"myteammatches"!=stalkerKeyName||(teamId=teamId.split("_matches_")[1])&&(metricLabel=metricLabel+"_"+teamId),userArea?userArea&&!userArea.notifications&&(userArea.notifications={}):userArea={theme:"default",notifications:{}},userArea.FirebaseToken=token,userArea.notifications[stalkerKeyName]="added"==metricAction,pkg.context.user.set("userArea",userArea),pkg.context.page.trigger("teams-notification-change",stalkerKeyName),switchObj&&(switchObj.querySelector(".btn-background-layer").classList.toggle("disabled"),switchObj.querySelector(".btn-ball").classList.toggle("disabled-ball"),switchObj.dataset.state="on"==switchObj.dataset.state?"off":"on"),window.gaTerra("send","event","user_config","notifications",metricLabel,{dimension9:"terra"}),STATIC_PRIVATE.removeBlockingLayer(),STATIC_PRIVATE.processingRequest=!1,pkg.require(["mod.notifications"],function(modNotification){modNotification.getAllNotifications(function(notificationsUpdated){for(var i=0,unreadWebPushNotification;i<notificationsUpdated.length;i++){notificationsUpdated[i].read||(unreadWebPushNotification=notificationsUpdated[i]).button.url.replace("#","")===stalkerKeyName&&modNotification.setRead(unreadWebPushNotification.id)}})})},STATIC_PRIVATE.segmentResultError=function(action,segments,stalkerKeyName,token){var metricLabel="fcm_segment_error";STATIC_PRIVATE.printMessage("Desculpe, ocorreu um erro na sua inscrição. Tente novamente.","error","sw-"+stalkerKeyName),window.gaTerra("send","event","user_config","notifications",metricLabel,{dimension9:"terra"}),STATIC_PRIVATE.removeBlockingLayer(),STATIC_PRIVATE.processingRequest=!1},STATIC_PRIVATE.showPleaseWait=function(){var overlayTitle=document.getElementById("overlay-title");overlayTitle&&(overlayTitle.style.display="inline-block")},STATIC_PRIVATE.removeBlockingLayer=function(){var elems=document.getElementsByClassName("overlay-notification");elems&&0<elems.length&&(document.body.removeChild(elems[0]),document.body.style.overflow="auto")},STATIC_PRIVATE.showBlockingLayer=function(){var elemOverlay=document.getElementsByClassName("overlay-notification"),elemOverlay;elemOverlay&&0==elemOverlay.length&&(elemOverlay=PUBLIC.templates.render("notificationOverlay"),document.body.appendChild(elemOverlay),document.body.style.overflow="hidden")},STATIC_PRIVATE.printMessage=function(message,type,li){var m=document.querySelector('.navbar-user-area__notifications-config__list span[id="'+li+'"]'),li,m,node;m&&((m=(li=m.parentElement).querySelector("span.error"))&&li.removeChild(m),""!==message&&((node=document.createElement("span")).append(message),node.classList.add(type),li.appendChild(node),setTimeout(function(){node.remove()},2e3)))}},init:function(data,__shared){var PRIVATE={},PUBLIC=this,supportsPush;return PRIVATE.elemModules=data.container,PRIVATE.navbar=data.navbar,PRIVATE.elemContainer=document.getElementById("zaz-app-t360-navbar"),PRIVATE.rendered=!1,PRIVATE.renderedWeather=!1,pkg.require(["app.t360.favoriteTeam"],function(FavoriteTeam){PRIVATE.favoriteTeam=new FavoriteTeam({container:document.getElementById("widget-area-futebol"),type:"navbar",choseTeam:!0})}),__shared.dependencies.mod.t360.notificationsHelper.browserSupportsPush()&&"denied"!=window.Notification.permission&&(PRIVATE.renderNotificationTab=!0),PRIVATE.elemUserArea=PUBLIC.templates.render("userarea",{notificationTab:PRIVATE.renderNotificationTab}),PRIVATE.elemModules.appendChild(PRIVATE.elemUserArea),PRIVATE.addedNotificationChangeEvent||(PRIVATE.addedNotificationChangeEvent=!0,pkg.context.page.on("navbar-notification-change",function(switchObj){var switchObj=document.getElementById("sw-"+switchObj);switchObj&&(switchObj.querySelector(".btn-background-layer").classList.toggle("disabled"),switchObj.querySelector(".btn-ball").classList.toggle("disabled-ball"),switchObj.dataset.state="on"==switchObj.dataset.state?"off":"on")})),PRIVATE.elemSettings=document.getElementById("user-area-settings"),PRIVATE.elemModalShadow=document.getElementById("user-area-modal-shadow"),PRIVATE.buttons={close:document.getElementById("user-area-close"),login:document.getElementById("user-area-login-btn"),summaryPortalContent:document.getElementById("summary-portal-content"),summaryPortalSettings:document.getElementById("summary-portal-settings"),whatsapp:document.querySelector(".navbar-user-area__whatsapp"),futebol:document.getElementById("futebol-btn"),horoscopo:document.getElementById("horoscopo-btn"),clima:document.getElementById("clima-btn")},PRIVATE.areas={notificacoes:document.getElementById("widget-area-notificacoes"),futebol:document.getElementById("widget-area-futebol"),horoscopo:document.getElementById("widget-area-horoscopo"),clima:document.getElementById("widget-area-clima")},PRIVATE.turnOnUserAreaSwitch=function(switchElement,value){var switchElement;"on"==value&&((switchElement=document.getElementById(switchElement)).dataset.state=value,switchElement.classList.remove("disabled"))},PRIVATE.turnOnUserAreaSelect=function(id,value){var selectElement;value&&(document.getElementById(id).querySelector("option[value="+value+"]").selected=!0)},PRIVATE.userAreaSettingsState={"select-user-theme":pkg.utils.getCookie("user_theme")},PRIVATE.loadUserAreaSettings=function(){for(var id in PRIVATE.userAreaSettingsState){var type;PRIVATE.userAreaSettingsState.hasOwnProperty(id)&&("switch"==(type=id.match(/(\w+)\-/)[1])?PRIVATE.turnOnUserAreaSwitch(id,PRIVATE.userAreaSettingsState[id]):"select"==type&&PRIVATE.turnOnUserAreaSelect(id,PRIVATE.userAreaSettingsState[id]))}pkg.context.user.get("userArea",function(userArea){userArea?(userArea.theme=pkg.utils.getCookie("user_theme"),pkg.context.user.set("userArea",userArea)):pkg.context.user.set("userArea",{theme:pkg.context.user.set("userArea",userArea)})})},PRIVATE.applyUserTheme=function(theme){document.cookie="user_theme="+theme+";expires=Mon, 3 Jan 2050 12:00:00 UTC;domain=.terra.com.br;path=/","auto"!=theme?(window.applyUserTheme(theme),window.tga.send("send","event","user-area","theme",theme)):window.applyUserTheme(window.osThemeColor)},PRIVATE.changetab=function(newTab){PRIVATE.renderNotificationTab&&(PRIVATE.buttons.futebol.classList.remove("active"),PRIVATE.areas.futebol.classList.add("hidden")),PRIVATE.buttons.futebol.classList.remove("active"),PRIVATE.buttons.horoscopo.classList.remove("active"),PRIVATE.buttons.clima.classList.remove("active"),PRIVATE.areas.futebol.classList.add("hidden"),PRIVATE.areas.horoscopo.classList.add("hidden"),PRIVATE.areas.clima.classList.add("hidden"),PRIVATE.areas[newTab].classList.remove("hidden"),PRIVATE.buttons[newTab].classList.add("active"),window.tga.send("send","event","user-area","click",newTab)},PRIVATE.fixTeamChannelID=function(musaId,resolve,reject){fetch("https://p1.trrsf.com/api/musa-api/profile-contents?lang=pt-BR&id="+musaId).then(function(response){return response.json()}).then(function(potentialChannelId){var data=potentialChannelId.root.result.profiles[0],potentialChannelId=data.contents.filter(function(channel){return 12==channel.id_content}),channelId="";potentialChannelId.length?PRIVATE.channelId=potentialChannelId[0].url_content:PRIVATE.channelId=void 0;var url="";data.contents.length&&data.contents[0].url_content&&(url=data.contents[0].url_content),resolve()})},PRIVATE.getWidgetWeather=function(){return new Promise(function(resolve,reject){PRIVATE.changetab("clima"),document.getElementById("details-portal-content").setAttribute("open",!0),PRIVATE.renderedWeather||pkg.require(["app.t360.weather"],function(T360Weather){PRIVATE.weather=new T360Weather({container:PRIVATE.areas.clima,data:{}})}).then(function(){PRIVATE.areas.clima.classList.remove("loading-app"),PRIVATE.renderedWeather=!0,resolve()})})},PRIVATE.getWidgetSigns=function(){PRIVATE.buttons.horoscopo.classList.contains("active")||(PRIVATE.changetab("horoscopo"),PRIVATE.areas.horoscopo.querySelector(".horoscope")||pkg.require(["app.t360.horoscope"],function(Horoscope){var ct0=new Horoscope({container:PRIVATE.areas.horoscopo})}).then(function(){PRIVATE.areas.horoscopo.classList.remove("loading-app")}))},PRIVATE.getWidgetTeams=function(){PRIVATE.changetab("futebol"),PRIVATE.favoriteTeam.getWidgetTeams()},PUBLIC.getNotifications=function(){pkg.context.user.get("userArea",function(userArea){PRIVATE.elemUserAreaNotifications=PUBLIC.templates.render("userareaNotifications",{userArea:userArea,topics:STATIC_PRIVATE.topics}),PRIVATE.areas.notificacoes.classList.remove("loading-app"),PRIVATE.areas.notificacoes.appendChild(PRIVATE.elemUserAreaNotifications),PRIVATE.bindNotifications(),PRIVATE.loadUserAreaSettings(),PRIVATE.elemContainer.querySelectorAll(".js-user-area-setting").forEach(function(setting){"select"==setting.dataset.settingType?setting.addEventListener("change",function(evt){PRIVATE.userAreaSettingsActions[setting.id](evt.target.value)},!1):"switch"==setting.dataset.settingType&&setting.addEventListener("click",function(evt){PRIVATE.userAreaSettingsActions[setting.id](evt.target.dataset.state)},!1)})})},PRIVATE.toggleUserAreaModal=function(command){if(command)return PRIVATE.elemModules.querySelector(".navbar-user-area").style.overflowY="hidden",void(PRIVATE.elemModules.querySelector("#user-area-modal-shadow").style.display="block");PRIVATE.elemModules.querySelector("#user-area-modal-shadow").style.display="",PRIVATE.elemModules.querySelector(".navbar-user-area").style.overflowY=""},PUBLIC.closeModule=function(moduleName){var module=document.querySelector(moduleName);module.classList.add("close");var shadow=document.querySelector(".navbar-modules__shadow");shadow.classList.add("close"),setTimeout(function(){PRIVATE.elemContainer.htmlDataset("activeModule",""),document.body.classList.remove("navbar-open");var tagHtml=document.querySelector("HTML"),existWebStoryEmbed;document.querySelector(".storyEmbed")&&(tagHtml.classList.add("i-amphtml-singledoc","i-amphtml-standalone"),tagHtml.classList.remove("storyEmbed"),tagHtml.style=""),window.removeEventListener("resize",PRIVATE.resizeModule),module.classList.remove("close"),shadow.classList.remove("close")},200)},PRIVATE.bindNotifications=function(){STATIC_PRIVATE.topics.forEach(function(topic){var element=document.getElementById("sw-"+topic.id);element.addEventListener("click",function(){var action,stalkerKeyName,segments;STATIC_PRIVATE.processingRequest||(STATIC_PRIVATE.processingRequest=!0,action="segments_"+("off"==element.dataset.state?"add":"remove"),stalkerKeyName=element.id.replace("sw-",""),"sw-myteammatches"==element.id?pkg.context.user.get("myTeam",function(segments){var segments;segments&&segments.userTeam&&segments.userTeam.teamName&&segments.userTeam.teamId?(segments="br_matches_"+segments.userTeam.teamId,PUBLIC.subscribeSegments(action,segments,stalkerKeyName)):(STATIC_PRIVATE.printMessage("Antes você precisa de um time configurado.","error",element.id),STATIC_PRIVATE.processingRequest=!1)}):"sw-horoscope"==element.id?pkg.context.user.get("horoscope",function(segments){var segments;segments?(segments="br_horoscope_"+segments,PUBLIC.subscribeSegments(action,segments,stalkerKeyName)):(STATIC_PRIVATE.printMessage("Antes você precisa de um signo configurado.","error",element.id),STATIC_PRIVATE.processingRequest=!1)}):(segments="br_"+element.id.replace("sw-",""),PUBLIC.subscribeSegments(action,segments,stalkerKeyName)))},!1)})},PRIVATE.loadLogin=function(){pkg.require(["mod.googleOneTap"],function(ModGOT){PRIVATE.modGOT=ModGOT,PRIVATE.userLoginStatus=!1,PRIVATE.buttons.googleLogin=PRIVATE.elemContainer.querySelector(".navbar-user-area__login"),PRIVATE.modGOT.getLoginStatus()?PRIVATE.userLoginSuccess():(PRIVATE.modGOT.renderButtonLogin(PRIVATE.buttons.googleLogin),pkg.context.user.on("googleSignInData",function(data){PRIVATE.modGOT.getLoginStatus()&&""!=data&&!PRIVATE.userLoginStatus&&PRIVATE.userLoginSuccess()}))})},PRIVATE.userLoginSuccess=function(){PRIVATE.userLoginStatus=!0;var userAreaMessage=document.querySelector(".navbar-my-user__header__area__message"),userData=PRIVATE.modGOT.getLoginData();PRIVATE.buttons.googleLogin&&PRIVATE.buttons.googleLogin.classList.add("hidden"),PRIVATE.buttons.login&&(PRIVATE.buttons.login.classList.remove("hidden"),PRIVATE.buttons.login.addEventListener("click",PRIVATE.userLogoff,!1)),userAreaMessage&&userAreaMessage.classList.remove("hidden"),userData&&userData.given_name&&(PRIVATE.elemLogin=PRIVATE.elemContainer.querySelector(".navbar-my-user__header__area__loged-in__name"),PRIVATE.elemLogin&&(PRIVATE.elemLogin.innerHTML+="Olá, "+userData.given_name))},PRIVATE.userLogoff=function(){PRIVATE.userLoginStatus=!1,PRIVATE.modGOT.logoff(),PRIVATE.buttons.login.classList.add("hidden"),PRIVATE.buttons.googleLogin.classList.remove("hidden");var elemUserName=document.querySelector(".navbar-my-user__header__area__message");elemUserName&&elemUserName.classList.add("hidden");var elemUserName=PRIVATE.elemContainer.querySelector(".navbar__right--user-area");PRIVATE.elemLogin.innerHTML="",elemUserName&&(elemUserName.classList.add("navbar__icon-size-auto"),elemUserName.innerHTML='<span class="icon-solid icon-color-auto icon-16 icon-user-account" title="Configurações da conta"></span>');var elemUserName=PRIVATE.elemContainer.querySelector(".navbar-user-area__header__title--login");elemUserName&&elemUserName.parentNode.removeChild(elemUserName),PRIVATE.buttons.login.removeEventListener("click",PRIVATE.userLogoff),PRIVATE.loadLogin()},PUBLIC.init=function(modules){PRIVATE.rendered?modules&&"weather"==modules?(PRIVATE.changetab("clima"),PRIVATE.getWidgetWeather()):modules&&"futebol"==modules&&(PRIVATE.getWidgetTeams("futebol"),document.getElementById("details-portal-content").setAttribute("open",!0)):(PRIVATE.userAreaSettingsActions={"select-user-theme":PRIVATE.applyUserTheme},modules&&"futebol"==modules&&(PRIVATE.getWidgetTeams("futebol"),document.getElementById("details-portal-content").setAttribute("open",!0)),PRIVATE.buttons.close.addEventListener("click",function(){PUBLIC.closeModule(".navbar-user-area")}),PRIVATE.buttons.whatsapp.addEventListener("click",function(){window.tga.send("send","event","user-area","click","channel-agora")}),PRIVATE.buttons.summaryPortalContent.addEventListener("click",function(){PRIVATE.buttons.futebol.classList.contains("active")&&PRIVATE.getWidgetTeams(),modules&&"weather"==modules&&(PRIVATE.changetab("clima"),PRIVATE.getWidgetWeather())}),PRIVATE.buttons.futebol.addEventListener("click",function(){PRIVATE.getWidgetTeams()}),PRIVATE.buttons.clima.addEventListener("click",function(){PRIVATE.getWidgetWeather()}),PRIVATE.buttons.horoscopo.addEventListener("click",PRIVATE.getWidgetSigns,!1),PRIVATE.buttons.summaryPortalSettings.addEventListener("click",function(){PRIVATE.buttons.notificacoes=document.getElementById("notificacoes-btn"),PRIVATE.elemUserAreaNotifications||PUBLIC.getNotifications()}),PRIVATE.loadLogin(),PRIVATE.rendered=!0)},PUBLIC},teardown:function(why,__static,__proto,__shared){}})}),zaz.use(function appT360Navbar(pkg){"use strict";var appFactory=pkg.factoryManager.get("app"),STATIC_PUBLIC={},STATIC_PRIVATE={};appFactory.create({name:"t360.navbar",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/t360-navbar",source:"http://github.tpn.terra.com/Terra/t360-navbar",description:"Just another app",tests:"http://s1.trrsf.com/fe/t360-navbar/tests/index.htm?zaz[env]=tests",dependencies:["app.navbarMenu","app.navbarUserArea"],dictionaries:["t360.navbar"],templates:{navbarSva:'<nav class="navbar__sva"><ul class="navbar__sva--items"><li><a href="https://servicos.terra.com.br/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=header&utm_content=home&utm_term=todos_produtos&cdConvenio=CVTR00001907" rel="noopener" target="_blank"><span class="icon icon-solid icon-16 icon-todos-produtos"></span>Todos os Produtos</a></li><li><a href="https://central.terra.com.br/login?next=http://central.terra.com.br/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=header&utm_content=pg&utm_term=central&cdConvenio=CVTR00001907" rel="noopener" target="_blank"><span class="icon icon-solid icon-16 icon-central-assinante"></span>Central do Assinante</a></li><li><a href="https://servicos.terra.com.br/mail-gigante/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=header&utm_content=pg&utm_term=terra-mail-gigante_pos-01&cdConvenio=CVTR00001907" rel="noopener" target="_blank"><span class="icon icon-solid icon-16 icon-terra-mail"></span>Terra Mail</a></li><li><a href="https://www.terraempresas.com.br/construtor-sites/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=header&utm_content=pg&utm_term=construtor-de-sites_pos-02&cdConvenio=CVTR00001907" rel="noopener" target="_blank"><span class="icon icon-solid icon-16 icon-construtor-sites"></span>Construtor de Sites</a></li><li><a href="https://servicos.terra.com.br/para-voce/valesaude/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=header&utm_content=pg&utm_term=vale-saude_pos-03&cdConvenio=CVTR00001907" rel="noopener" target="_blank"><span class="icon icon-solid icon-16 icon-vale-saude"></span> Consultas Médicas</a></li><li><a href="https://servicos.terra.com.br/para-voce/vivae/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=header&utm_content=pg&utm_term=vivae_pos-04&cdConvenio=CVTR00001907" rel="noopener" target="_blank"><span class="icon icon-solid icon-16 icon-vivae"></span>Curso Marketing Digital</a></li><li><a href="https://www.terraempresas.com.br/terra-ads/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=header&utm_content=pg&utm_term=terra-ads_pos-05&cdConvenio=CVTR00001907" rel="noopener" target="_blank"><span class="icon icon-solid icon-16 icon-terra-ads"></span>Anuncie sua Empresa</a></li><li><a href="https://servicos.terra.com.br/seguranca-digital/?utm_source=portal-terra&utm_medium=espaco-fixo&utm_campaign=header&utm_content=pg&utm_term=seguranca-digital_pos-06&cdConvenio=CVTR00001907" rel="noopener" target="_blank"><span class="icon icon-solid icon-16 icon-seguranca-digital"></span>Segurança Digital</a></li></ul></nav>',header:'<div class="navbar"><header class="navbar__content"><div class="navbar__ranges" color-scheme="dark"><div class="navbar__left"><div class="navbar__left--menu icon-solid icon-20 icon-color-auto icon-bars"></div><a href="https://www.terra.com.br" class="navbar__left--logo icon">Página principal</a></div><div class="navbar__right"><div class="navbar__right--user-info"><a href="https://mail.terra.com.br/?utm_source=portal-terra&utm_medium=home" rel="noopener" target="_blank" class="navbar__right--mail icon-solid icon-color-auto icon-16 icon-envelope" title="E-mail do Terra">E-mail</a><div class="navbar__right--user-area navbar__icon-size-auto"><span class="icon-solid icon-color-auto icon-16 icon-user-account" title="Configurações da conta"></span></div></div></div></div></header><div class="navbar__extension"></div><div class="navbar-modules-notifications"></div></div><div class="navbar-modules"><div class="navbar-modules__shadow"></div></div>'},setup:function(__static,PUBLIC,__shared){var PRIVATE={statusDisplay:"show",deviceType:pkg.context.platform.get("type")};return PRIVATE.isIOSApp=pkg.context.platform.get("isIOSApp"),PRIVATE.autoInit=function(){PRIVATE.elemContainer=document.getElementById("zaz-app-t360-navbar"),PRIVATE.eighteenPlus=PRIVATE.elemContainer.querySelector(".eighteen-plus");var templateNavbar=PRIVATE.elemContainer.querySelector(".navbar"),templateNavbar;templateNavbar?PRIVATE.elemNavbar=templateNavbar:(templateNavbar=PUBLIC.templates.render("header",{device:PRIVATE.deviceType}),PRIVATE.elemNavbar=templateNavbar.querySelector(".navbar"),PRIVATE.elemContainer.classList.add("fe"),PRIVATE.elemContainer.replaceChildren(templateNavbar)),PRIVATE.isIOSApp&&PRIVATE.clearSvaElements(),PRIVATE.elemModules=PRIVATE.elemContainer.querySelector(".navbar-modules"),PRIVATE.elemNotificationArea=PRIVATE.elemContainer.querySelector(".navbar-modules-notifications"),PRIVATE.elemModules&&PRIVATE.initModules(),PRIVATE.bindElements(),PRIVATE.autoHideHeader()},PRIVATE.autoHideHeader=function(){var lastScrollTop=0,findScrollDirection=function(event){var st=window.pageYOffset||document.documentElement.scrollTop,eighteenPlusHeight=0;PRIVATE.eighteenPlus&&(eighteenPlusHeight=36,"mob"==pkg.context.platform.get("type")&&(eighteenPlusHeight=41)),lastScrollTop<st?st>PRIVATE.elemNavbar.offsetHeight&&(PRIVATE.elemNavbar.style.top=-PRIVATE.elemNavbar.offsetHeight+eighteenPlusHeight+"px",PRIVATE.statusDisplay="hide"):PRIVATE.showNavbar(),lastScrollTop=st<=0?0:st};window.addEventListener("scroll",findScrollDirection)},PRIVATE.showNavbar=function(){PRIVATE.elemNavbar.style.position="fixed",PRIVATE.elemNavbar.style.top="0",PRIVATE.statusDisplay="show"},PRIVATE.clearSvaElements=function(){var webSvaMenu=PRIVATE.elemNavbar.querySelector(".navbar__center--sva"),btnProdutos=PRIVATE.elemNavbar.querySelector(".navbar__btn-services"),mobSva=document.querySelector(".navbar-sva-mob");webSvaMenu&&webSvaMenu.parentElement.removeChild(webSvaMenu),btnProdutos&&btnProdutos.parentElement.removeChild(btnProdutos),mobSva&&mobSva.parentElement.removeChild(mobSva)},PRIVATE.bindElements=function(){PRIVATE.elemMenuItemsSva=PRIVATE.elemNavbar.querySelectorAll(".navbar__center--sva .navbar__center--sva-item"),PRIVATE.elemBtMail=PRIVATE.elemNavbar.querySelector(".navbar__right--mail"),PRIVATE.elemBtUserArea=PRIVATE.elemNavbar.querySelector(".navbar__right--user-area"),PRIVATE.elemShadow=PRIVATE.elemContainer.querySelector(".navbar-modules__shadow"),PRIVATE.elemBtMenu=PRIVATE.elemNavbar.querySelector(".navbar__left--menu"),PRIVATE.elemBtMail&&PRIVATE.elemBtMail.addEventListener("click",function(){window.tga.send("send","event","navbar","click","email")}),PRIVATE.elemMenuItemsSva&&window.tga.event("navbar","sva-menu",PRIVATE.elemMenuItemsSva),PRIVATE.elemBtMenu&&PRIVATE.elemBtMenu.addEventListener("click",function(){STATIC_PUBLIC.toggleModule("menu")}),PRIVATE.elemBtUserArea&&(PRIVATE.elemBtUserArea.addEventListener("click",function(){STATIC_PUBLIC.toggleModule("user-area")}),"#config_notifications"==location.hash&&(document.getElementById("details-portal-settings").setAttribute("open",!0),STATIC_PUBLIC.toggleModule("user-area"),PRIVATE.modules.userArea.getNotifications())),PRIVATE.elemShadow&&PRIVATE.elemShadow.addEventListener("click",function(){STATIC_PUBLIC.closeModule(PRIVATE.activeModule)})},PRIVATE.initModules=function(){PRIVATE.modules={},PRIVATE.modules.menu=new __shared.dependencies.app.navbarMenu({navbar:STATIC_PUBLIC,container:PRIVATE.elemModules}),PRIVATE.modules.userArea=new __shared.dependencies.app.navbarUserArea({navbar:STATIC_PUBLIC,container:PRIVATE.elemModules})},STATIC_PUBLIC.toggleModule=function(moduleName,modules){var activeModule;if(PRIVATE.elemContainer.htmlDataset("activeModule")==moduleName)STATIC_PUBLIC.closeModule(moduleName);else switch(STATIC_PUBLIC.openModule(moduleName),moduleName){case"user-area":PRIVATE.modules.userArea.init(modules);break;case"menu":PRIVATE.modules.menu.init()}},STATIC_PUBLIC.closeModule=function(moduleName){PRIVATE.modules.userArea.closeModule(".navbar-"+moduleName)},STATIC_PUBLIC.openModule=function(moduleName){var existWebStoryEmbed,tagHtml;PRIVATE.activeModule=moduleName,PRIVATE.resizeModule(),PRIVATE.showNavbar(),PRIVATE.elemContainer.htmlDataset("activeModule",PRIVATE.activeModule),document.body.classList.add("navbar-open"),window.addEventListener("resize",PRIVATE.resizeModule),document.querySelector(".i-amphtml-singledoc.i-amphtml-standalone")&&((tagHtml=document.querySelector("HTML")).classList=[],tagHtml.classList.add("storyEmbed"),tagHtml.style.overflow="hidden"),window.tga.send("send","event","navbar","click",moduleName)},PRIVATE.resizeModule=function(){var heightNavbar=PRIVATE.elemNavbar.offsetHeight;"sva"==PRIVATE.activeModule&&"mob"!=pkg.context.platform.get("type")&&(heightNavbar=0);var height=window.innerHeight-heightNavbar;PRIVATE.elemModules.style.height=height+"px",PRIVATE.elemModules.style.top=heightNavbar+"px"},STATIC_PUBLIC.resizeModule=function(){return pkg.utils.make.debounce(function(){PRIVATE.resizeModule()})},STATIC_PUBLIC.getStatusDisplay=function(){return PRIVATE.statusDisplay},STATIC_PUBLIC.renderNotification=function(fragment){PRIVATE.elemNotificationArea&&(STATIC_PUBLIC.removeNotification(),PRIVATE.elemNotificationArea.appendChild(fragment))},STATIC_PUBLIC.removeNotification=function(fragment){PRIVATE.elemNotificationArea&&(PRIVATE.elemNotificationArea.innerHTML="",PRIVATE.elemContainer.style.marginTop="",PRIVATE.resizeModule())},PRIVATE.autoInit(),STATIC_PUBLIC},init:function(data,__shared){return STATIC_PUBLIC},teardown:function(why,__static,__proto,__shared){}})});