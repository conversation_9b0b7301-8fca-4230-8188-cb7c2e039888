if(!window.zaz&&window.performance&&"function"==typeof window.performance.mark)try{window.performance.mark("ZAZ_CEREBRO_DEFINED")}catch(perfException){window.console&&"object"==typeof window.console&&("function"==typeof window.console.warn?window.console.warn("Error registering performance metric ZAZ_CEREBRO_DEFINED. "+perfException.message):"function"==typeof window.console.log&&window.console.error("[ZaZ Warning] Error registering performance metric ZAZ_CEREBRO_DEFINED. "+perfException.message))}if(window.zaz&&window.zaz.fullyLoaded)throw new Error("Zaz framework has already been loaded!\nLoading the framework twice might cause unexpected problems!");if(!function(_d){"use strict";var zazVersion="1.7.6",lastRevision="August 15, 2025 at 13:17 UTC",PRIVATE={},PUBLIC={version:"1.7.6",utils:{}},PROTECTED={zazVersion:"1.7.6"},COMMON={zazVersion:"1.7.6",consoleLabels:{checkOk:"  [ OK ]  ::",checkFail:"![[FAIL]] ::",error:"!![DIED]> ::",info:" i[INFO]  ::",warning:" ![WARN]  ::",log:"  [ LG ]> ::",debug:" [DEBUG]  ::",checkpoint:" [CHCKPT] >>",meta:"  [META]  ::",space:"             "}},PKG={},tmp={},legacy,c,c=(PRIVATE.self=this,PRIVATE.scopeable={},COMMON.console=((c={}).log=c.log||function(){},c.warn=c.warn||function(){},c.warning=c.warning||function(){},c.error=c.error||function(){},c.debug=c.debug||function(){},c),-1<location.search.search("tpn=1|debuglog=1")),isStandalone=void 0===window.trr,msg,make,a;if(c||isStandalone?window.console&&(COMMON.console=window.console):window.console.log("===============================================================================\n============== Use a querystring debuglog=1 para ativar os logs. ==============\n==============================================================================="),COMMON.console.check=COMMON.console.line=function(){},PRIVATE.originalConsole=window.console,PRIVATE.isLegacy=function(){return legacy=null==legacy?navigator.appVersion.match(/MSIE 8/i):legacy},window.console=COMMON.console,navigator.userAgent.match(/MSIE [67]/))throw window.zaz={use:function(){},notSupported:!0},new Error(COMMON.consoleLabels.checkFail+" This browser is not supported by the Terra Network's framework");a={},make={cleanUpProperties:function(a,b){var c=null,d={};for(c in a)"__make"==c.substring(0,6)||!b&&"function"==typeof a[c]||("[object Object]"==Object.prototype.toString.call(a[c])?d[c]=make.cleanUpProperties(a[c],b):d[c]=a[c]);return d},debounce:function(a,b){function c(c){return function(){var d=arguments,e=this==window?a:this;window.clearTimeout(j[0]),j[0]=setTimeout(function(){c.apply(e,d),window.clearTimeout(j[1]),j[1]=!1},b.distance),i&&!j[1]&&(j[1]=setTimeout(function(){c.apply(e,d),window.clearTimeout(j[1]),j[1]=!1},b.timeout))}}if("object"!=typeof a&&"function"!=typeof a)return console.error('throttle:throttle maker requires a function, or an object as target(the first argument). All the methods will be treated as throttles. The second argument(options) accepts "methods" as an array with the names of methods you want to become throttle.');(b=void 0===b?{}:b).distance=b.distance||200;var d=b.methods,e=(d="object"==typeof a&&d?d.length&&"object"==typeof d?d:[d]:d)||Object.keys(a),f=null,g=0,h=e.length,i=(b.distance,b.timeout||!1),j=[];if("object"!=typeof a)return c(a);for(;g<h;g++)"function"==typeof a[e[g]]&&(f=e[g],a[f]=c(a[f]));return a},indexable:function(a){function b(){var a=this,b=function(a,b){return b=b||"",b="object"==typeof a&&a.length||b.match(/\W/)?"["+(b=isNaN(b)?'"'+b+'"':b)+"]":"."+b};this.find=function(c,d,e,f){if(void 0===c)return console.error("indexable:Method find expected one argument.");f=f||[],d=d||{};var g=null,h=!1,i=void 0,j=function(a,b,c){var e=null,f=c;d.regEx?c instanceof RegExp||(c=new RegExp(c,"i")):c=new RegExp(c,""),e=(e=d.lookingForKey?b:a[b]).valueOf?e.valueOf():"";try{if(e.match&&e.match(c)||e==f)return!0}catch(g){console.warn("compare","A problem occurred when trying to compare values. Possible error with a regExp, perhaps?",g)}return!1};for(g in e=e||this||a)if(!e.nodeType&&e.hasOwnProperty(g)&&"function"!=typeof e[g]){if(j(e,g,c)){f.push(b(e,g)),i=e[g],h=!0;break}if(e[g]&&"object"==typeof e[g]){if(f.push(b(e,g)),h=a.find(c,d,e[g],f))return h;f.pop()}}return!!(h&&f&&f.length)&&(!1!==d.returnPath?f.join(""):i)},this.locate=function(a){return this.find(a,{lookingForKey:!0})},this.search=this.findLike=function(a){return this.find(a,{regEx:!0})},this.locateLike=function(a){return this.find(a,{lookingForKey:!0,regEx:!0})},this.getAt=function(a,b){for(var c=a.replace(/\[/g,".").replace(/\]/g,"").split("."),d=c.length,e=b||this,f=0;f<d;f++){if(void 0===e[c[f]])return;e=e[c[f]]}return e},this.query=function(a,b,c,d){if("object"!=typeof this||void 0===this.length)return console.warn("Indexable must be an array to allow queries to execute"),target;for(var e=c||0,f=this.length,g=null,h=[];e<f;e++)if(g=this.find(b,{regEx:!0,returnPath:!1},this[e])){if(!d)return e;h.push(this[e])}return h},this.queryAll=function(a,b,c){return this.query(a,b,c,!0)},this.__makeData||(this.__makeData={}),this.__makeData.indexable=!0}if(!a||"object"!=typeof a)return console.error("indexable:Method makeIndexable expects first argument to be an Object");if(!a.indexable){if("function"==typeof a)return b.apply(a.prototype),a;b.apply(a)}},model:function(b){var c=function(){},d=null;for(d in b)b.hasOwnProperty(d)&&(c.prototype[d]=b[d]);c=make.tiable(c),b.identifier=(new Date).getTime(),a[b.identifier]=[],b.create=function(){var d=new c;return d.addSetterFilter(function(a,b){d.trigger(a,b)}),"function"==typeof d.oncreate&&d.oncreate.apply(d,Array.prototype.slice.call(arguments,0)),a[b.identifier].push(d),d}},collection:function(b){function c(b){var c=0,e=b.identifier;this.__makeData=this.__makeData||{},make.indexable(a[e]),this.getLength=function(){return a[e].length},this.first=function(){return a[e][0]||!1},this.last=function(){return a[e][this.getLength()-1]||!1},this.goTo=function(b){return b<0?b=0:b>=this.getLength()&&(b=this.getLength()-1),c=b,a[e][b]},this.current=function(){return a[e][c]||!1},this.currentIdx=function(){return c},this.next=function(){var b=a[e][c]||!1;return b&&c++,b},this.prev=function(){var b=a[e][c]||!1;return b&&c--,b},this.reset=function(){c=0},this.get=function(b){return a[e][b]||!1},this.list=function(b,c){return b?c?a[e].slice(b,c):a[e].slice(b):a[e].slice()},this.query=function(b,d){return!1!==(ret=a[e].query(b,d,c))&&(c=ret,ret=this.get(ret),c++),ret},this.queryAll=function(b,c){return a[e].queryAll(b,c)},this.add=function(a){d.create(a)},this.__makeData.tiable=!0}var d=b;return new c(b)}},function(){var a=function(a,b){b=b||{};var c={},d=(a=(a=void 0===a?this:a).name||a.id||a.toString().substring(0,30),this),e=function(a,b){var c=0;for(a=d.__makeData.observing[a]||[];c<a.length;){if(a[c]===b)return c;c++}return-1};return d.__makeData||(d.__makeData={}),d.__makeData.observing={"*":[]},this.on=function(a,b){if("function"!=typeof(b=b||a))throw new Error("observer:Invalid listener!\nWhen adding listeners to observables, it is supposed to receive a function as callback.");return"string"==typeof a?(d.__makeData.observing[a]||(d.__makeData.observing[a]=[]),d.__makeData.observing[a].push(b)):d.__makeData.observing["*"].push(b),this},this.once=function(a,b){return b.once=!0,this.on(a,b),this},this.onceAt=function(a,b){if(void 0!==c[a])try{b(c[a])}catch(d){console.error('observer:A listener produced an error! "atOnce" trigger '+a,d,b)}else this.once(a,b);return this},this.at=function(a,b){if(void 0!==c[a])try{b(c[a])}catch(d){console.error('observer:A listener produced an error! "at" trigger '+a,d,b)}return this.on(a,b),this},this.off=function(a,b){if(a)return"function"==typeof a&&(b=a,a="*"),d.__makeData.observing[a].splice(e(a,b),1),this;throw new Error('observer:Invalid function passed to "off" method')},b.onlyByTrigger||(this.settable||make.setAndGettable(this,b),this.addSetterFilter(function(a,c,e){return b&&b.recursive&&"object"==typeof c&&!c.length&&make.observable(c,b),d.trigger(a,c,e),c})),this.trigger=function(b,e){var f=0,g=0,h=[];for(void 0===e&&(e=b,b="*"),g=(h=d.__makeData.observing[b]||[]).length;f<g;f++)try{h[f](e),h[f]&&!0===h[f].once&&(this.off(b,h[f]),f--,g--)}catch(i){var j=a.name||(a.prototype?a.prototype.name:"");console.error('observer:Failed to execute a function from a listener.\nListening to changes on "'+b+'"\n'+(j?"At "+j:"")+"\nwith the message: "+i.message,i)}return"*"!=b&&this.trigger("*",e),c[b]=e,this},this.__makeData.observable||(this.__makeData.observable=!0),this};make.observable=function(b,c){var d=null;if(c=c||{},b.nodeType)return b;if("object"==typeof b&&!1!==c.recursive&&b!=make)for(d in b)!b[d]||b[d].__makeData&&b[d].__makeData.observable||"object"!=typeof b[d]||b[d].length||make.observable(b[d],c);return a.apply(!c.useStatic&&b.prototype||b,[b,c]),b}}(),make.persistent=function(a,b,c){"string"==typeof b||c||(c=b,b="__makePersistentGenericObject");var d=a.prototype?a.prototype.name||a.name:b;return c=c||window.localStorage,(a=a.__makeSetAndGettable?a:make.setAndGettable(a)).save=function(){c.setItem(d,JSON.stringify(a.__makeData.setGetValue))},a.load=function(b){var e=c.getItem(d,function(c){if("string"==typeof c)try{c=JSON.parse(c)}catch(d){}if(a.set(c),b&&"function"==typeof b)try{b(c)}catch(d){}});if(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(f){}if(a.set(e),b&&"function"==typeof b)try{b(e)}catch(f){}}return a},a.addSetterFilter(make.debounce(function(){a.save()},{distance:200})),a.load(),a.__makeData.Persistent=!0,a},function(){function a(a){return this.fn=a,this}make.promise=function(){var b=Array.prototype.slice.call(arguments),b;return 1<(b=b.map(function(b){return new a(b)})).length?b:b[0]}}(),make.readonly=function(a,b){var c=null;if("object"!=typeof a)return console.error('throttle:throttle maker requires an object as target(the first argument). All the methods will be treated as throttles. The second argument(options) accepts "methods" as an array with the names of methods you want to become throttle.');for(c in void 0===b&&(b={}),a)a.hasOwnProperty(c)&&Object.defineProperty(a,c,{enumerable:!0,configurable:!1,writable:!1,value:a[c]});return a},make.setAndGettable=function(a,b){function c(a,b,c,d,e){var f=c,g=f,h=0;if(a&&a.length)for(h=a.length,k=0;k<h;k++)void 0===(f="*"===b?a[k](d,f,e):a[k](f,e))&&(f=g);return f}function d(b,d,e){var f=d,g,f=c(g=i[b],b,d,void 0,a[b]),g;return c(g=i["*"],"*",f,b,e)}function e(a,b){var d=b,e,d=c(e=j[a],a,b),e;return c(e=j["*"],"*",d,a)}function f(a){a.__makeData.setGetValue||(make.__isIE8,a.__makeData.setGetValue={})}function g(a,c,g){var i=a[c],j=c[0].toUpperCase()+c.substring(1);if(a.__makeData||(a.__makeData={},a.__makeData.setGetValue={}),a["set"+j]||(a["set"+j]=function(e,g){f(this);var h=null;try{h=d(c,e,g)}catch(j){return a}return void 0!==h&&(b.isPrototypeOf?this.__makeData.setGetValue[c]=h:i=h),this}),a["get"+j]||"__make"==j.substring(0,6)||(a["get"+j]=function(){var a=null,a=b.isPrototypeOf?(f(this),this.__makeData.setGetValue[c]):i;return void 0===(a=e(c,a))&&void 0!==i?i:a}),"__make"!=j.substring(0,6)){if(b.protected)try{Object.defineProperty(a,c,{enumerable:!0,configurable:!0,set:function(a){return this["set"+j](a),this},get:function(){return this["get"+j]()}})}catch(k){}"string"!=typeof c&&isNaN(c)||(a.__makeData.setGetValue||(a.__makeData.setGetValue={}),a.__makeData.setGetValue[c]=h[c])}}if(b=b||{},"function"==typeof a&&!b.useStatic)return b.isPrototypeOf=a,make.setAndGettable(a.prototype,b);b.specificOnly=b.specificOnly||!1,b.setter=b.setter||!0,b.getter=b.getter||!0,b.filterIn=b.filterIn||!1,b.filterOut=b.filterOut||!1,b.protected=b.protected||!0,b.allowNewSetters=b.allowNewSetters||!0;var h=a,i={"*":[]},j={"*":[]},k=(b.filterIn&&i["*"].push(b.filterIn),b.filterOut&&j["*"].push(b.filterOut),null);for(k in a)"function"!=typeof a[k]&&g(a,k,b.isPrototypeOf);return b.setter&&!b.specificOnly&&(a.set=function(c,d,e){var f=null,h="";if("object"!=typeof c)return this[h="set"+(c[0].toUpperCase()+c.substring(1))]||!b.allowNewSetters&&b.protected?a[h](d,e):(g(a,c,b.isPrototypeOf),this[h](d,e));for(f in c)c.hasOwnProperty(f)&&(h="set"+(f[0].toUpperCase()+f.substring(1)),this[h]||!b.allowNewSetters&&b.protected||g(a,f,b.isPrototypeOf),this[h](c[f],e));return this}),b.getter&&!b.specificOnly&&(a.get=function(c){var d="get"+(c[0].toUpperCase()+c.substring(1));return a[d]||g(a,c,b.isPrototypeOf),this[d]()}),!a.addSetterFilter&&b.setter&&(a.addSetterFilter=function(a,b){return b||(b=a,a="*"),i[a]||(i[a]=[]),i[a].push(b),this}),!a.addGetterFilter&&b.getter&&(a.addGetterFilter=function(a,b){return b||(b=a,a="*"),j[a]||(j[a]=[]),j[a].push(b),this}),a.__makeData.setAndGettable=!0,a},make.throttle=function(a,b){function c(b){return function(){var c=this==window?a:this;return j.getTime()+i<=(new Date).getTime()?(j=new Date,b.apply(c,arguments)):void 0}}if("object"!=typeof a&&"function"!=typeof a)return console.error('throttle:throttle maker requires a function or an object as target(the first argument). All the methods will be treated as throttles. The second argument(options) accepts "methods" as an array with the names of methods you want to become throttle.');(b=void 0===b?{}:b).distance=b.distance||100;var d=b.methods,d,e=(d=d&&(d.length&&"object"==typeof d?d:[d]))||Object.keys(a),f=null,g=0,h=e.length,i=b.distance,j=new Date((new Date).getTime()-i);if("object"!=typeof a)return c(a);for(;g<h;g++)"function"==typeof a[e[g]]&&(f=e[g],a[f]=c(a[f]));return c(a,b)},make.tiable=function(a){function b(){function a(a,b){var d=null,e=null;for(d in c)c[d][a]?(e=c[d][a],e.target[e.prop]=b):c[d]["*"]&&(e=c[d]["*"],e.target[a]=b);return b}var b=this,c={},d=function(a){var b=a.name||a.id;return a._makeData=a._makeData||{},a._makeData.ref||(b=!b&&a.prototype?a.prototype.name||a.valueOf().substring(0,40):(new Date).getTime(),a._makeData.ref=b)};this.tie=function(a,e,f){var g;return f=f||"*",1===arguments.length&&(e=a,a="*"),g=d(e),c[g]||(c[g]={}),c[g][a]||(c[g][a]={target:e,prop:f}),b},make.observable(this),this.addSetterFilter(a),this.tiable=!0,this.untie=function(a,b){a._makeData&&a._makeData.ref&&(b?delete c[a._makeData.ref][b]:delete c[a._makeData.ref])}}if(!a||"object"!=typeof a&&"function"!=typeof a)throw new Error("Invalid type of object or class passed to tiable!");return"function"==typeof a&&a.prototype?(b.apply(a.prototype),a):(b.apply(a),this)},make.worker=function(a,b){function c(a){function b(b){var c=new Blob([b],{type:"application/javascript"}),b=new Worker(window.URL.createObjectURL(c));return b.onmessage=function(b){a.workerFinished(b.data),window.URL.revokeObjectURL(c)},b}var c,c=" self.onmessage = function(e) { \n self.postMessage(("+(c=a.toString());return(c+=").apply(self, e.data))};\n ").match(/document|window|body/)?(console.warn("worker:Function passed to make.worker tries to access DOM elements. The function will work, but will not be a worker"),c):function(){var d=Array.prototype.slice.call(arguments,0),e;"function"==typeof d[d.length-1]&&(a.workerFinished=d.pop()),b(c).postMessage(d)}}if("object"!=typeof a&&"function"!=typeof a)return console.error('worker:Worker maker requires a function or an object as target(the first argument). All the methods will be treated as workers, but you can pass in the second argument(options), the property "methods" with an array with the names of methods you want, only they will become workers.');var b=(b=b||{}).methods,b,e=(b=b&&(b.length&&"object"==typeof b?b:[b]))||Object.keys(a),f=null,g=0,h=e.length;if("object"!=typeof a)return c(a);for(;g<h;g++)"function"==typeof a[e[g]]&&(a[f=e[g]]=c(a[f]));return a},(PUBLIC.utils.make=make).observable(PUBLIC,{onlyByTrigger:!0,recursive:!1});var methods=["log","info","warn","error","assert","dir","clear","profile","profileEnd"],i=null,lockedEvents={},lockedListeners={},hadNativeDefineProperty=!0,c;if(Array.toArray||(Array.toArray=function toArray(obj){try{return Array.prototype.slice.call(obj)}catch(e){for(var keys=Object.keys(obj),i=0,l=keys.length,newArr=[];i<l;i++)"number"!=typeof keys[i]&&!keys[i].match(/^\d+$/)||newArr.push(obj[keys[i]]);return newArr}}),Object.defineProperty(Element.prototype,"htmlDataset",{get:function(){function setGetDataset(){var setDataset=function(element,attr,val){return document.documentElement.dataset?element.dataset[attr]=val:(attr=attr.replace(/([A-Z]+)/g,"-$1").toLowerCase(),element.setAttribute("data-"+attr,val))},getDataset=function(element,attr){return document.documentElement.dataset?element.dataset[attr]:(attr=attr.replace(/([A-Z]+)/g,"-$1").toLowerCase(),element.getAttribute("data-"+attr))};return 1==arguments.length?getDataset(this,arguments[0]):2==arguments.length?setDataset(this,arguments[0],arguments[1]):void 0}return setGetDataset}}),"Element"in(c=window)){var classListProp="classList",protoProp="prototype",c=c.Element.prototype,isStandalone=Object,strTrim=String.prototype.trim||function(){return this.replace(/^\s+|\s+$/g,"")},arrIndexOf=Array.prototype.indexOf||function(item){for(var i=0,len=this.length;i<len;i++)if(i in this&&this[i]===item)return i;return-1},DOMEx=function(type,message){this.name=type,this.code=DOMException[type],this.message=message},checkTokenAndGetIndex=function(classList,token){if(""===token)throw new DOMEx("SYNTAX_ERR","An invalid or illegal string was specified");if(/\s/.test(token))throw new DOMEx("INVALID_CHARACTER_ERR","String contains an invalid character");return arrIndexOf.call(classList,token)},ClassList=function(elem){for(var trimmedClasses=strTrim.call(elem.getAttribute("class")||""),classes=trimmedClasses?trimmedClasses.split(/\s+/):[],i=0,len=classes.length;i<len;i++)this.push(classes[i]);this._updateClassName=function(){elem.setAttribute("class",this.toString())}},classListProto=ClassList.prototype=[],classListGetter=function(){return new ClassList(this)};if(DOMEx.prototype=Error.prototype,classListProto.item=function(i){return this[i]||null},classListProto.contains=function(token){return-1!==checkTokenAndGetIndex(this,token+="")},classListProto.add=function(){for(var tokens=arguments,i=0,l=tokens.length,token,updated=!1;token=tokens[i]+"",-1===checkTokenAndGetIndex(this,token)&&(this.push(token),updated=!0),++i<l;);updated&&this._updateClassName()},classListProto.remove=function(){var tokens=arguments,i=0,l=tokens.length,token,updated=!1;do{var token=tokens[i]+"",token=checkTokenAndGetIndex(this,token)}while(-1!==token&&(this.splice(token,1),updated=!0),++i<l);updated&&this._updateClassName()},classListProto.toggle=function(token,force){var result=this.contains(token+=""),force=result?!0!==force&&"remove":!1!==force&&"add";return force&&this[force](token),!result},classListProto.toString=function(){return this.join(" ")},"document"in self&&!("classList"in document.createElement("_"))){if(isStandalone.defineProperty){var classListProto={get:classListGetter,enumerable:!0,configurable:!0};try{isStandalone.defineProperty(c,"classList",classListProto)}catch(ex){classListProto.enumerable=!1,isStandalone.defineProperty(c,"classList",classListProto)}}isStandalone.classList||isStandalone.prototype.__defineGetter__&&c.__defineGetter__("classList",classListGetter)}c.getClassList=function(){var hasClassList=this.classList,classListOk,el;return hasClassList=(classListOk=!!hasClassList&&((el=document.createElement("div")).classList.add("a","b"),"a b"==el.className))?hasClassList:classListGetter.call(this)},c.hasClass=function(cl){return this.getClassList().contains(cl)},c.addClass=function(cl){this&&this.getClassList().add(cl)},c.removeClass=function(cl){this&&this.getClassList().remove(cl)}}var scope=this,globalVars=(Object.equals=function(obj1,obj2){var k1,k2;return"object"!=typeof obj1||"object"!=typeof obj2?obj1===obj2:(k1=Object.keys(obj1),k2=Object.keys(obj2),obj1.constructor.name==obj2.constructor.name&&(k1.length==k2.length&&k1.join()==k2.join()&&JSON.stringify(obj1)==JSON.stringify(obj2)))},!function(){function moving(event){var manager=listenerManager,list=[],finalCoord,finalCoord_time=(new Date).getTime(),finalCoord_pos=[(touchCapable?event.changedTouches[0]:event).screenX,(touchCapable?event.changedTouches[0]:event).screenY],finalCoord_offset=[touchCapable?event.changedTouches[0].pageX-event.changedTouches[0].target.offsetLeft:event.offsetX,touchCapable?event.changedTouches[0].pageY-event.changedTouches[0].target.offsetTop:event.offsetY];manager.pos&&((eventData={}).minDistH=minDistH,eventData.distH=Math.abs(manager.pos[0]-finalCoord_pos[0]),eventData.distV=Math.abs(manager.pos[1]-finalCoord_pos[1]),(touchExceeded||8<=eventData.distH&&eventData.distH>eventData.distV)&&(event.preventDefault(),eventData.direction="moving",eventData.horizontalMove=manager.pos[0]<finalCoord_pos[0]?"right":"left",eventData.verticallMove=manager.pos[1]<finalCoord_pos[1]?"down":"up",eventData.endMoving=!1,touchExceeded=!0,(list=listeners[eventData.direction])&&list.length&&list.forEach(function(cur){try{cur.fn.apply(cur.target,[event,eventData])}catch(e){console.error("Listener for gesture "+eventData.direction+" triggered an error!\n"+e.message,e)}})))}function startMoving(event){touchExceeded=!1,listenerManager={time:(new Date).getTime(),pos:[(touchCapable?event.touches[0]:event).screenX,(touchCapable?event.touches[0]:event).screenY],offset:[touchCapable?event.touches[0].pageX-event.touches[0].target.offsetLeft:event.offsetX,touchCapable?event.touches[0].pageY-event.touches[0].target.offsetTop:event.offsetY]}}function endMoving(event){var list=[],that;event.target.removeEventListener(events.endEvent,moving),event.target.removeEventListener(events.moveEvent,moving),eventData.endMoving=!0,eventData.distH>eventData.distV?eventData.distH>minDistH&&(eventData.direction="right"==eventData.horizontalMove?"swipeRight":"swipeLeft"):eventData.distV>minDistV&&(eventData.direction="down"==eventData.verticallMove?"swipeDown":"swipeUp"),(list=listeners[eventData.direction])&&list.length&&(that=this,list.forEach(function(cur){if(cur.target==that)try{cur.fn.apply(cur.target,[event,eventData])}catch(e){console.error("Listener for gesture "+eventData.direction+" triggered an error!\n"+e.message,e)}}))}var scope,touchCapable,events,gestures,minDistH,minDistV,listeners,listenerManager,eventData,touchExceeded;scope=this,touchCapable="ontouchstart"in document.documentElement||"ontouchstart"in document||0<navigator.MaxTouchPoints||0<navigator.msMaxTouchPoints,events={startEvent:touchCapable&&"touchstart",endEvent:touchCapable&&"touchend",moveEvent:touchCapable&&"touchmove",tapEvent:touchCapable&&"tap",scrollEvent:touchCapable&&"touchmove"},gestures=["swipeLeft","swipeRight","swipeUp","swipeDown","swipeH","swipeV","moving"],minDistH=(document.offsetWidth||document.documentElement.offsetWidth)/10,minDistV=(document.offsetHeight||document.documentElement.offsetHeight)/3,listeners={targetList:[]},listenerManager={},touchExceeded=!(eventData={}),scope.addGestureListener=function(target,gesture,callback,settings){if(!touchCapable)return!1;if("string"!=typeof gesture)throw new TypeError("Gesture argument is supposed to be a string");if("object"!=typeof target||!target.nodeName)throw new TypeError("Target argument is supposed to be a DOM Element Node");if("function"!=typeof callback)throw new TypeError("Callback argument is supposed to be a Function");if(settings=settings&&"object"==typeof settings?settings:{},gestures.indexOf(gesture)<0)throw new Error("Gesture must be one of: "+gestures.join(", "));listeners[gesture]||(listeners[gesture]=[]),listeners.targetList.indexOf(target)<0&&(target.addEventListener(events.startEvent,startMoving),target.addEventListener(events.moveEvent,moving),target.addEventListener(events.endEvent,endMoving.bind(target)),listeners.targetList.push(target)),listeners[gesture].push({settings:settings,target:target,fn:callback})},scope.removeGestureListener=function(target,gesture,callback){if(!touchCapable)return!1;listeners[gesture]&&listeners[gesture].forEach&&listeners[gesture].forEach(function(cur,i){cur.target==target&&cur.callback==callback&&listeners[gesture].splice(i,1),target.removeEventListener(events.startEvent,function(){})})}}.call(PUBLIC.utils),!function(){var escope,PRIV,PUB;escope=this,PUB=!(PRIV={}),PRIV.validate=function(el,target,idx){if("number"!=typeof idx)throw new TypeError("index, in method appendToIndex, is supposed to be a number");if("object"==typeof el&&"object"==typeof target&&el.nodeType&&target.nodeType)return!0;throw new TypeError("In method appendToIndex, both newElement and target are supposed to be DOM Elements")},(PUB=function appendToIndex(el,target,idx,dir){var children=null,last=null,prev=null,first=null,i=idx-1;if(dir=dir||"ltr",PRIV.validate(el,target,idx),el.setAttribute("data-keep-index",idx),(children=target.children).length)if(last=children[children.length-1],first=children[0],parseInt(last.getAttribute("data-keep-index"),10)>idx){for(children[i=idx-1]||(i=children.length-1);prev=children[i];){if(parseInt(prev.getAttribute("data-keep-index"))<=idx){target.insertBefore(el,prev.nextSibling),el=null;break}i--}el&&("rtl"==dir?target.insertBefore(el,first):target.insertBefore(el,last))}else target.appendChild(el);else target.appendChild(el)}).setAsProto=function(__proto){(__proto=__proto||Element.prototype).appendToIndex=function(el,idx){appendToIndex(el,this,idx)}},escope.appendToIndex=PUB,PRIV.define=escope.define||window.define||!1,PRIV.define&&PRIV.define("appendToIndex",PUB)}.call(PUBLIC.utils),!function(){var CURRENT_SCOPE=this,CURRENT_SCOPE,WebLocation,STATIC_PRIVATE,chooseReference,EXP_PATH_ABSOLUTE,EXP_PATH_RELATIVE,EXP_PATH_RELATIVE_TO_DOMAIN,EXP_PATH_RELATIVE_TO_SCHEME,EXP_PATH_RELATIVE_TO_CURRENT_DIR,EXP_MATCH_PARENT_DIR,EXP_CURRENT_FILE_DECLARATION,EXP_LAST_DIRECTORY_DECLARATION,EXP_SINGLE_CHAR,EXP_VALID_ASSOCIATIVE_KEY,EXP_TRIM_QUERY,EXP_QUERY_SEPARETORS,EXP_LEVELS_KEYS;if(CURRENT_SCOPE.WebLocation)throw new Error("WebLocation already loaded");function normalize(levels){return levels.replace(EXP_TRIM_QUERY,"").replace(EXP_QUERY_SEPARETORS,"|")}function getRemainningLevels(reference,target){var levels=null;return levels=-1<target.indexOf(reference)?target.replace(reference,"").match(EXP_LEVELS_KEYS):levels}function WebLocationParams(){}function WebLocationComponent(){}CURRENT_SCOPE.WebLocation=((STATIC_PRIVATE={EXP_IS_PATH_ABSOLUTE:/^(https?|file):/,EXP_MATCH_PARAM_TOKENS:/[\?&]([^=]+)(?:=([^&#]*))?/g,EXP_VALID_PARAM_TYPES:/string|number|boolean|null/i,EXP_MATCH_TOKENS:/^((((?:https?|file)\:)\/\/\/?((.+?)(?:\:(\d+?))?))(\/[^\?#]*?([^\/\?]+?(?:\.([^\/]*?))?)?)?(\?.*?)?)&?(#.*)?$/,HREF_TOKEN_NAMES:["href","uri","origin","protocol","host","hostname","port","pathname","filename","extension","search","hash"],keyFilters:{},hostAliases:{},defaultSettings:{href:"",origin:"",uri:"",protocol:"http:",host:"",hostname:"",port:"",pathname:"",filename:"",extension:"",search:"",hash:""},alphabeticalSorter:function(a,b){return a=a.toLowerCase(),(b=b.toLowerCase())<a?1:a<b?-1:0},merge:function(){if(arguments.length<=1)throw new Error('The "merge" function must receive at least two objects as parameters.');for(var targetObject,current,attributeId,currentValueType,compiledValueType,objects=Array.prototype.slice.call(arguments),returnedObjectType=objects[0]instanceof Array?"array":"object",compiledObject="array"==returnedObjectType?[]:{};objects.length;){if("object"!=typeof(current=objects.shift())||"array"==returnedObjectType&&!(current instanceof Array)||"object"==returnedObjectType&&current instanceof Array)throw new Error('You can\'t combine different types of objects together. They must be all "objects" or "arrays".');for(attributeId in current)current.constructor.prototype.hasOwnProperty(attributeId)||void 0===current[attributeId]||(void 0===compiledObject[attributeId]?compiledObject[attributeId]=current[attributeId]:(currentValueType=Object.prototype.toString.call(current[attributeId]),compiledValueType=Object.prototype.toString.call(compiledObject[attributeId]),compiledValueType.match(/Object|Array/)&&compiledValueType==currentValueType?STATIC_PRIVATE.merge(compiledObject[attributeId],current[attributeId]):compiledObject[attributeId]=current[attributeId]));targetObject||objects.length||(objects.push(compiledObject),targetObject=compiledObject=arguments[0])}return targetObject},cloneComponents:function(obj){return WebLocationComponent.prototype=obj,new WebLocationComponent},cloneParams:function(obj){return WebLocationParams.prototype=obj,new WebLocationParams},printValues:function(validKeys,values){for(var current,i,output=[],i=0;i<validKeys.length;i++)current=validKeys[i],output.push(values[current]);return output.join("")},printSearch:function(params){var currentName,currentValue,subValue,output=[];for(currentName in params)if(params.hasOwnProperty(currentName))for(currentValue=params[currentName],currentValue="[object Array]"==Object.prototype.toString.call(currentValue)?Array.prototype.slice.call(currentValue):[currentValue];currentValue.length;)subValue=currentValue.shift(),null===subValue&&(subValue=""),subValue=encodeURIComponent(subValue),output.push(currentName+"="+subValue);return(output=output.sort(STATIC_PRIVATE.alphabeticalSorter)).length?"?"+output.join("&"):""}}).keyFilters.pattern=function(query,params){var output,key,keyTokens,keyTokens,value;if(!(query instanceof RegExp))throw new Error("Invalid query. It must be a RegExp instance");for(key in output={},params)params.hasOwnProperty(key)&&(keyTokens=query.exec(key),keyTokens&&(keyTokens=keyTokens.pop(),value=params[key],output.hasOwnProperty(keyTokens)||(output[keyTokens]=value)));return output},STATIC_PRIVATE.keyFilters.associative=(EXP_VALID_ASSOCIATIVE_KEY=/^([\w-_]+)((\[[\w-_]+\])|\.[\w-_]+)*$/,EXP_TRIM_QUERY=/^(\.|\[)|(\]|\.)$/g,EXP_QUERY_SEPARETORS=/(\.|\]?\[)/g,EXP_LEVELS_KEYS=/[^|]+/g,function(query,params){var piece,key,value,levels,currentLevel,currentKey,output={};if(!EXP_VALID_ASSOCIATIVE_KEY.test(query))throw new Error("Invalid query. The string must be a associative key the matches this pattern:"+EXP_VALID_ASSOCIATIVE_KEY.source);for(key in query=normalize(query),params)if(params.hasOwnProperty(key)&&(value=params[key],key=normalize(key),levels=getRemainningLevels(query,key))){for(currentLevel=piece={};levels.length;)currentKey=levels.shift(),levels.length?(currentLevel[currentKey]={},currentLevel=currentLevel[currentKey]):currentLevel[currentKey]=value;STATIC_PRIVATE.merge(output,piece)}return output}),STATIC_PRIVATE.convertPathToAbsolute=(EXP_PATH_ABSOLUTE=/^(https?:\/\/|file:)/,EXP_PATH_RELATIVE=/^\.\.\//,EXP_PATH_RELATIVE_TO_DOMAIN=/^\/[^\/]/,EXP_PATH_RELATIVE_TO_SCHEME=/^\/\//,EXP_PATH_RELATIVE_TO_CURRENT_DIR=/^\.\//,EXP_MATCH_PARENT_DIR=/(\.\.\/)/g,EXP_CURRENT_FILE_DECLARATION=/[^\/]+$/,EXP_LAST_DIRECTORY_DECLARATION=/[^\/]+\/?$/,EXP_SINGLE_CHAR=/./,chooseReference=function(declaration,target){if("string"==typeof declaration)return declaration;if(1==declaration.length)return declaration[0];for(var charactere,index,tokens=target.match(EXP_SINGLE_CHAR),sum=0;tokens.length;)sum+=(charactere=tokens.shift()).charCodeAt(0);return declaration[index=sum%declaration.length]},function(target,reference){if("string"!=typeof target)throw new Error("The method .convertPathToAbsolute() must receive a String representing de include path as first parameter");if(void 0!==reference&&"string"!=typeof reference&&("[object Array]"!=Object.prototype.toString.call(reference)||!reference.length))throw new Error("The method .convertPathToAbsolute() must receive a string or an array of strings representing the reference url(s). If you ommit it, it will be set as the document's location.");if(EXP_PATH_ABSOLUTE.test(target))return target;target=target.replace(EXP_PATH_RELATIVE_TO_CURRENT_DIR,"");try{if(reference=chooseReference(reference||location.href,target),!EXP_PATH_ABSOLUTE.test(reference))throw new Error("Location references must be absolute. Given value: "+reference);reference=new WebLocation(reference)}catch(exception){throw exception.message='Invalid reference to convert path "'+target+'" > '+exception.message,exception}(reference=reference.getAllComponents()).href==reference.origin&&(reference.href+="/");var backDirectories,i,base=reference.href.replace(EXP_CURRENT_FILE_DECLARATION,""),fileRealPath=target.replace(base,"").replace(EXP_MATCH_PARENT_DIR,"");if(EXP_PATH_RELATIVE_TO_DOMAIN.test(target))return"file:"==reference.protocol?reference.protocol+/.+\//.exec(reference.href)[0]+target.replace(EXP_PATH_RELATIVE_TO_DOMAIN,""):reference.protocol+"//"+reference.host+target;if(EXP_PATH_RELATIVE_TO_SCHEME.test(target))return"file:"==reference.protocol?reference.protocol+"/"+target:reference.protocol+target;if(EXP_PATH_RELATIVE.test(target))for(backDirectories=target.match(EXP_MATCH_PARENT_DIR).length,i=0;i<backDirectories;i++)base=base.replace(EXP_LAST_DIRECTORY_DECLARATION,"");return base+fileRealPath}),STATIC_PRIVATE.extractTokens=function(uri){var currentName,values,currentValue,i,tokens={},values=STATIC_PRIVATE.EXP_MATCH_TOKENS.exec(uri);if(!values)throw new Error("Invalid uri. It must be a valid string.");for(i=0;i<values.length;i++)currentValue=values[i],tokens[currentName=STATIC_PRIVATE.HREF_TOKEN_NAMES[i]]=currentValue;return tokens},STATIC_PRIVATE.extractParams=function(search){var tokens,paramDeclaration,paramDeclaration,name,paramDeclaration,params={},foundAny=!1;if(STATIC_PRIVATE.EXP_MATCH_PARAM_TOKENS.compile(STATIC_PRIVATE.EXP_MATCH_PARAM_TOKENS.source,"g"),!(tokens=search.match(STATIC_PRIVATE.EXP_MATCH_PARAM_TOKENS))||!tokens.length)return null;for(STATIC_PRIVATE.EXP_MATCH_PARAM_TOKENS.compile(STATIC_PRIVATE.EXP_MATCH_PARAM_TOKENS.source,"");tokens.length;){if(foundAny=!0,paramDeclaration=tokens.shift(),(paramDeclaration=STATIC_PRIVATE.EXP_MATCH_PARAM_TOKENS.exec(paramDeclaration)).shift(),name=paramDeclaration.shift(),paramDeclaration=paramDeclaration.shift()||null)try{paramDeclaration=decodeURIComponent(paramDeclaration)}catch(e){paramDeclaration=unescape(paramDeclaration)}params[name]?params[name].push?params[name].push(paramDeclaration):params[name]=[params[name],paramDeclaration]:params[name]=paramDeclaration}return foundAny?params:null},STATIC_PRIVATE.getComponents=function(href,referenceHosts){var params,href=STATIC_PRIVATE.convertPathToAbsolute(href,referenceHosts),referenceHosts=STATIC_PRIVATE.extractTokens(href);return referenceHosts.search&&(params=STATIC_PRIVATE.extractParams(referenceHosts.search),referenceHosts.search=STATIC_PRIVATE.printSearch(params),referenceHosts.uri=referenceHosts.origin+referenceHosts.pathname+referenceHosts.search,referenceHosts.href=referenceHosts.uri+(referenceHosts.hash||"")),{values:STATIC_PRIVATE.merge({},STATIC_PRIVATE.defaultSettings,referenceHosts),params:params||null}},STATIC_PRIVATE.getURIID=function(uri){var hostId,match;for(hostId in STATIC_PRIVATE.hostAliases)STATIC_PRIVATE.hostAliases.hasOwnProperty(hostId)&&(match=STATIC_PRIVATE.hostAliases[hostId],uri=uri.replace(match,"${"+hostId+"}"));return uri},(CURRENT_SCOPE=WebLocation=function WebLocation(href,referenceHosts){var PRIVATE={},PUBLIC=this;if(!(this instanceof WebLocation))throw new Error('WebLocation is a constructor. You must use the operator "new".');if(!href||"string"!=typeof href)throw new Error("Invalid href. It must be a valid string.");if(void 0!==referenceHosts&&"string"!=typeof referenceHosts&&("[object Array]"!=Object.prototype.toString.call(referenceHosts)||!referenceHosts.length))throw new Error("Invalid referenceHosts. It must be a valid string or an array of strings.");PRIVATE.originalHREF=href,PRIVATE.components=STATIC_PRIVATE.getComponents(href,referenceHosts),PRIVATE.values=PRIVATE.components.values,PRIVATE.params=PRIVATE.components.params,PRIVATE.updateValues=function(){PRIVATE.values.search=PRIVATE.params?STATIC_PRIVATE.printSearch(PRIVATE.params):"",PRIVATE.values.uri=PRIVATE.values.origin+PRIVATE.values.pathname+PRIVATE.values.search,PRIVATE.values.href=PRIVATE.values.uri+PRIVATE.values.hash},PUBLIC.getComponent=function(name){if(PRIVATE.values.hasOwnProperty(name))return PRIVATE.values[name];throw new Error("The requested WebLocation component doesn't exist: "+name)},PUBLIC.getAllComponents=function(copy){if(void 0!==copy&&"boolean"!=typeof copy)throw new Error('Invalid argument "copy". If you wanna receive a raw copy of all components, pass in "true" {Boolena}. If you ommit it (or pass "false", you will receive a cloned object.');return copy?STATIC_PRIVATE.merge({},PRIVATE.values):STATIC_PRIVATE.cloneComponents(PRIVATE.values)},PUBLIC.getAllParams=function(copy){if(void 0!==copy&&"boolean"!=typeof copy)throw new Error('Invalid argument "copy". If you wanna receive a raw copy of all params, pass in "true" {Boolena}. If you ommit it (or pass "false", you will receive a cloned object.');return PRIVATE.params?copy?STATIC_PRIVATE.merge({},PRIVATE.params):STATIC_PRIVATE.cloneParams(PRIVATE.params):null},PUBLIC.queryParams=function(query,filterID){if(!query||!(query instanceof RegExp)&&"string"!=typeof query)throw new Error("Invalid query. It must be a string or a RegExp instance");if(void 0===filterID)filterID=query instanceof RegExp?"pattern":"associative";else if("string"!=typeof filterID||!STATIC_PRIVATE.keyFilters[filterID])throw new Error("Invalid filterID:"+filterID);return STATIC_PRIVATE.keyFilters[filterID](query,PRIVATE.params)},PUBLIC.listParams=function(filterExp){var output,current;if(!(void 0===filterExp||filterExp&&(filterExp instanceof RegExp||"string"==typeof filterExp)))throw new Error("Invalid filterExp. It must be a string or a RegExp instance");if(output=[],!PRIVATE.params)return output;for(current in"string"==typeof filterExp&&(filterExp=new RegExp("^"+filterExp+"$")),PRIVATE.params)!PRIVATE.params.hasOwnProperty(current)||filterExp&&!filterExp.test(current)||output.push(current);return output},PUBLIC.getParam=function(name,multivalued){if(!name||"string"!=typeof name)throw new TypeError('Invalid param name: "'+name+'".');var currentValue=PRIVATE.params?PRIVATE.params[name]:void 0;if(void 0!==multivalued){if("boolean"!=typeof multivalued)throw new Error("Invalid definition for second paremeter. You should declare it as a boolean to define if you expected a multivalued param or not. Passed argument was "+name);if(multivalued&&"[object Array]"!=Object.prototype.toString.call(currentValue))throw new Error('Param "'+name+'" is not valid because it is NOT MULTIVALUED: '+currentValue);if(!multivalued&&"[object Array]"==Object.prototype.toString.call(currentValue))throw new Error('Param "'+name+'" is not valid because it IS MULTIVALUED: ['+currentValue.join(", ")+"].")}return currentValue},PUBLIC.setParams=function(newParams){if("[object Object]"!=Object.prototype.toString.call(newParams))throw new Error("Invalid params. It must be an object.");var newParamName,newParamValue;for(newParamName in newParams)if(newParams.hasOwnProperty(newParamName)&&(newParamValue=newParams[newParamName],!STATIC_PRIVATE.EXP_VALID_PARAM_TYPES.test(Object.prototype.toString.call(newParamValue))||"[object Array]"==Object.prototype.toString.call(newParamValue)))throw new Error('Invalid value for "'+newParamName+'".');return PRIVATE.params=newParams,PRIVATE.updateValues(),PUBLIC},PUBLIC.mergeParams=function(newParams){if("[object Object]"!=Object.prototype.toString.call(newParams))throw new Error("Invalid params. It must be an object.");var newParamName,newParamValue;for(newParamName in newParams)if(newParams.hasOwnProperty(newParamName)&&(newParamValue=newParams[newParamName],!STATIC_PRIVATE.EXP_VALID_PARAM_TYPES.test(Object.prototype.toString.call(newParamValue))||"[object Array]"==Object.prototype.toString.call(newParamValue)))throw new Error('Invalid value for "'+newParamName+'".');return PRIVATE.params=STATIC_PRIVATE.merge({},PRIVATE.params,newParams),PRIVATE.updateValues(),PUBLIC},PUBLIC.addParams=function(newParams){if("[object Object]"!=Object.prototype.toString.call(newParams))throw new Error("Invalid params. It must be an object.");var currentParamValue,newParamName,newParamValue,currentValues=STATIC_PRIVATE.merge({},PRIVATE.params||{});for(newParamName in newParams)if(newParams.hasOwnProperty(newParamName)&&(newParamValue=newParams[newParamName],currentParamValue=currentValues[newParamName],void 0!==newParamValue))if(currentParamValue)if("[object Array]"!=Object.prototype.toString.call(currentParamValue)&&(currentParamValue=currentValues[newParamName]=[currentParamValue]),STATIC_PRIVATE.EXP_VALID_PARAM_TYPES.test(Object.prototype.toString.call(newParamValue)))currentValues[newParamName].push(newParamValue);else{if("[object Array]"!=Object.prototype.toString.call(newParamValue))throw new Error('Invalid value for "'+newParamName+'".');currentValues[newParamName]=currentParamValue.concat(newParamValue)}else currentValues[newParamName]=newParamValue;return PRIVATE.params=currentValues,PRIVATE.updateValues(),PUBLIC},PUBLIC.removeParams=function(paramsList){if("string"==typeof paramsList&&(paramsList=[paramsList]),PRIVATE.params&&"[object Array]"==Object.prototype.toString.call(paramsList))for(;paramsList.length;)delete PRIVATE.params[paramsList.shift()];else PRIVATE.params=null;return PRIVATE.updateValues(),PUBLIC},PUBLIC.unsetParams=function(){return PRIVATE.params=null,PRIVATE.updateValues(),PUBLIC},PUBLIC.getID=function(){return STATIC_PRIVATE.getURIID(PRIVATE.components.values.uri)}}).convertPathToAbsolute=STATIC_PRIVATE.convertPathToAbsolute,CURRENT_SCOPE.setHostAlias=function(id,pattern){if("string"!=typeof id||!/^\w+/.test(id))throw new Error("Invalid host id. It must be a string.");if(!(pattern instanceof RegExp))throw new Error("Invalid alias expression. It must be an instance of RegExp.");STATIC_PRIVATE.hostAliases[id]=pattern},CURRENT_SCOPE)}.call(PUBLIC.utils),PUBLIC.webLocation=PUBLIC.pageWebLocation=new PUBLIC.utils.WebLocation(location.href),PUBLIC.getGlobalVars=function(){var list=[];if(Object&&Object.keys)list=Object.keys(window);else for(var prop in window)window.hasOwnProperty(prop)&&list.push(prop);return list},window.zaz&&window.zaz.globalVariables?window.zaz.globalVariables:PUBLIC.getGlobalVars()),holding=(PUBLIC.getInitialGlobalVars=function(){return globalVars},PUBLIC.getFunctionName=function(func){var name=func.name;if(!func.name){name=func.toString().match(/^function\s*([^\s(]+)/);try{func.name=name?name[1]:""}catch(e){}}return func.name},PRIVATE.allowExtensions=function(){return"building"==PRIVATE.zaz.status},PRIVATE.notAllowedExtension=function(fn){PUBLIC.getFunctionName(fn);var fn=fn?fn.name:'"invalid name"';throw new Error("use:Extension "+fn+" not created. Extensions should only be created during building stage of zaz. \nOnly extensions from zaz core project are allowed.")},PUBLIC.isLegacy=function(){return PRIVATE.isLegacy()},PROTECTED.isUnderTests=function(){return!!(PUBLIC.hasDevPrivilegies()&&!0===window.zaz.options.global.autoTests&&window.jQuery&&window.QUnit)},PUBLIC.hasDevPrivilegies=function(){return!!(PRIVATE.zaz.terraDeveloperMode||PRIVATE.zaz.options.global.devmode&&(0===location.host.indexOf("localhost:")||0===location.host.indexOf("localhost/")||0<=location.search.indexOf("zaz[env]=")||location.host.match(/(dsv|hlg)-fe([0-9]{1,2})?(.+)?\.terra\./)))&&(PRIVATE.zaz.terraDeveloperMode=!0)},PUBLIC.utils.strToDOM=function(str){var d=document,i,a=d.createElement("div"),b=d.createDocumentFragment();for(a.innerHTML=str;i=a.firstChild;)b.appendChild(i);return b},PUBLIC.utils.getCookie=function(cName,all){for(var cookies=document.cookie.split(";"),ret="",i=0,c=null,i=0;i<cookies.length;i++)if((c=cookies[i].split("="))[0].trim().toLowerCase()==cName.toLowerCase()){if(ret+=c[1].trim(),!all)break;ret+=";"}return ret},PUBLIC.utils.getHash=function(s){if("string"==typeof s)return s.split("").reduce(function(a,b){return(a=(a<<5)-a+b.charCodeAt(0))&a},0);throw new Error("getHash needs to receive a string via argument")},PUBLIC.utils.metrics=function(url,category){try{if("function"!=typeof window.terra_stats_regClick)throw new Error("Metrics method not found: window. terra_stats_regClick(). Bypassing registry.");window.terra_stats_regClick(url,category)}catch(e){console.log(e)}},PRIVATE.components={},PRIVATE.watchList={},PRIVATE.prepare=function(){function showBootupLog(label,msg){debugging&&(label=label?"✔ ":"✘ ",console.log(label,msg))}var wl=new PUBLIC.utils.WebLocation(location.href),debugging=/cerebro\.env=[^&]+/.test(location.search),wl=wl.queryParams("zaz"),welcomeMessage="",welcomeMessageLogo="",stl="";switch(Object.keys(wl).length||(wl={}),wl=PRIVATE.merge(window.zaz&&window.zaz.options?window.zaz.options:{},wl),window.top===window.window?(PUBLIC.inFrame=!1,PUBLIC.topFrameAccess=!0):(PUBLIC.inIFrame=!0,PUBLIC.topFrameAccess=function(){try{var h=top.location.href;return!0}catch(e){return!1}}()),welcomeMessage="",welcomeMessage="\n-- \nWelcome to Terra Networks.\nThis is the the Terra's framework booting up.\n\nStatus: building\nInitializing framework\nVersion: 1.7.6 from "+lastRevision+"\n",location.host.match(/\.terra\./)||(welcomeMessage="Terra framework is loading under a non-terra domain.\nVersion: 1.7.6 from "+lastRevision+"\n"),debugging&&(PUBLIC.inIFrame&&(welcomeMessage="The Terra Networks framework has loaded inside an iFrame or frame.\n             ",welcomeMessage+="Scripts "+(PUBLIC.topFrameAccess?"CAN":"CANNOT")+" access the top frame from here\n             Version: 1.7.6 from "+lastRevision+"\n             Status: building\n             Initializing framework"),console.log(welcomeMessage)),window.zaz||(window.zaz={getQueue:function(){return[]},getExtQueue:function(){return[]},options:{global:{}},environment:{}}),PRIVATE.queues=[window.zaz.getQueue(),window.zaz.getExtQueue()],PKG=new PRIVATE.Package("core"),COMMON=new PRIVATE.PrivilegedPackage,showBootupLog(!0,"Built packages"),PRIVATE.zaz.environment=window.zaz.environment,window.zaz=PRIVATE.clone(PRIVATE.zaz),PRIVATE.zaz.options=PRIVATE.merge(PRIVATE.zaz.options,wl),showBootupLog(!0,"Replaced global zaz"),PUBLIC.hasDevPrivilegies()&&showBootupLog(!0,"Running in TerraDeveloperMode"),PROTECTED.isUnderTests()&&(debugging=!0,window.zazUnitTestData={priv:PRIVATE,prot:PROTECTED,publ:PUBLIC},showBootupLog(!0,"Entering in unit tests mode")),showBootupLog(!0,"Applied options"),PRIVATE.zaz.status="building",PRIVATE.zaz.options.global.verbosity){case 0:case!1:case 2:case 3:console.log(COMMON.consoleLabels.checkOk,"Verbosity set to "+PRIVATE.zaz.options.global.verbosity),console.log("          Stoping log messages."),console.log=function(){}}showBootupLog(!0,"Enabling extensions")},!function(){this.utf8_encode=function(argString){if(null==argString)return"";for(var string=argString+"",utftext="",start,end,stringl=0,start=end=0,stringl=string.length,n=0;n<stringl;n++){var c1=string.charCodeAt(n),enc=null;if(c1<128)end++;else if(127<c1&&c1<2048)enc=String.fromCharCode(c1>>6|192,63&c1|128);else if(55296!=(63488&c1))enc=String.fromCharCode(c1>>12|224,c1>>6&63|128,63&c1|128);else{if(55296!=(64512&c1))throw new RangeError("Unmatched trail surrogate at "+n);var c2=string.charCodeAt(++n);if(56320!=(64512&c2))throw new RangeError("Unmatched lead surrogate at "+(n-1));c1=((1023&c1)<<10)+(1023&c2)+65536,enc=String.fromCharCode(c1>>18|240,c1>>12&63|128,c1>>6&63|128,63&c1|128)}null!==enc&&(start<end&&(utftext+=string.slice(start,end)),utftext+=enc,start=end=n+1)}return start<end&&(utftext+=string.slice(start,stringl)),utftext}}.call(PUBLIC.utils),!function(){function MD5(str){for(var str=obj.md5(str),md5Raw=(this.value=str.value,this.rawData=str.values,this.rawData),md5Str="",i=0;i<32*md5Raw.length;i+=8)md5Str+=String.fromCharCode(md5Raw[i>>5]>>>i%32&255);return this.rawString=md5Str,this.toString=function(){return this.value},this}var scope,obj;scope=this,obj={},function(){var scope=this;this.md5=function(str){for(var xl,rotateLeft=function(lValue,iShiftBits){return lValue<<iShiftBits|lValue>>>32-iShiftBits},addUnsigned=function(lX,lY){var lX4,lY4,lX8,lY8,lX,lX8=2147483648&lX,lY8=2147483648&lY,lX4=1073741824&lX,lY4=1073741824&lY,lX=(1073741823&lX)+(1073741823&lY);return lX4&lY4?2147483648^lX^lX8^lY8:lX4|lY4?1073741824&lX?3221225472^lX^lX8^lY8:1073741824^lX^lX8^lY8:lX^lX8^lY8},_F=function(x,y,z){return x&y|~x&z},_G=function(x,y,z){return x&z|y&~z},_H=function(x,y,z){return x^y^z},_I=function(x,y,z){return y^(x|~z)},_FF=function(a,b,c,d,x,s,ac){return a=addUnsigned(a,addUnsigned(addUnsigned(_F(b,c,d),x),ac)),addUnsigned(rotateLeft(a,s),b)},_GG=function(a,b,c,d,x,s,ac){return a=addUnsigned(a,addUnsigned(addUnsigned(_G(b,c,d),x),ac)),addUnsigned(rotateLeft(a,s),b)},_HH=function(a,b,c,d,x,s,ac){return a=addUnsigned(a,addUnsigned(addUnsigned(b^c^d,x),ac)),addUnsigned(rotateLeft(a,s),b)},_II=function(a,b,c,d,x,s,ac){return a=addUnsigned(a,addUnsigned(addUnsigned(c^(b|~d),x),ac)),addUnsigned(rotateLeft(a,s),b)},convertToWordArray,wordToHex=function(lValue){for(var wordToHexValue="",wordToHexValue_temp="",lByte,lCount,lCount=0;lCount<=3;lCount++)wordToHexValue+=(wordToHexValue_temp="0"+(lByte=lValue>>>8*lCount&255).toString(16)).substr(wordToHexValue_temp.length-2,2);return wordToHexValue},x=[],k,AA,BB,CC,DD,a,b,c,d,S11=7,S12=12,S13=17,S14=22,S21=5,S22=9,S23=14,S24=20,S31=4,S32=11,S33=16,S34=23,S41=6,S42=10,S43=15,S44=21,a=1732584193,b=4023233417,c=2562383102,d=271733878,xl=(x=function(str){for(var lWordCount,lMessageLength=str.length,lNumberOfWords_temp1=lMessageLength+8,lNumberOfWords_temp2,lNumberOfWords_temp1=16*(1+(lNumberOfWords_temp1-lNumberOfWords_temp1%64)/64),lWordArray=new Array(lNumberOfWords_temp1-1),lBytePosition=0,lByteCount=0;lByteCount<lMessageLength;)lBytePosition=lByteCount%4*8,lWordArray[lWordCount=(lByteCount-lByteCount%4)/4]=lWordArray[lWordCount]|str.charCodeAt(lByteCount)<<lBytePosition,lByteCount++;return lWordArray[lWordCount=(lByteCount-lByteCount%4)/4]=lWordArray[lWordCount]|128<<(lBytePosition=lByteCount%4*8),lWordArray[lNumberOfWords_temp1-2]=lMessageLength<<3,lWordArray[lNumberOfWords_temp1-1]=lMessageLength>>>29,lWordArray}(str=PUBLIC.utils.utf8_encode(str))).length,k=0,temp;k<xl;k+=16)b=_II(b=_II(b=_II(b=_II(b=_HH(b=_HH(b=_HH(b=_HH(b=_GG(b=_GG(b=_GG(b=_GG(b=_FF(b=_FF(b=_FF(b=_FF(BB=b,c=_FF(CC=c,d=_FF(DD=d,a=_FF(AA=a,b,c,d,x[k+0],7,3614090360),b,c,x[k+1],12,3905402710),a,b,x[k+2],17,606105819),d,a,x[k+3],22,3250441966),c=_FF(c,d=_FF(d,a=_FF(a,b,c,d,x[k+4],7,4118548399),b,c,x[k+5],12,1200080426),a,b,x[k+6],17,2821735955),d,a,x[k+7],22,4249261313),c=_FF(c,d=_FF(d,a=_FF(a,b,c,d,x[k+8],7,1770035416),b,c,x[k+9],12,2336552879),a,b,x[k+10],17,4294925233),d,a,x[k+11],22,2304563134),c=_FF(c,d=_FF(d,a=_FF(a,b,c,d,x[k+12],7,1804603682),b,c,x[k+13],12,4254626195),a,b,x[k+14],17,2792965006),d,a,x[k+15],22,1236535329),c=_GG(c,d=_GG(d,a=_GG(a,b,c,d,x[k+1],5,4129170786),b,c,x[k+6],9,3225465664),a,b,x[k+11],14,643717713),d,a,x[k+0],20,3921069994),c=_GG(c,d=_GG(d,a=_GG(a,b,c,d,x[k+5],5,3593408605),b,c,x[k+10],9,38016083),a,b,x[k+15],14,3634488961),d,a,x[k+4],20,3889429448),c=_GG(c,d=_GG(d,a=_GG(a,b,c,d,x[k+9],5,568446438),b,c,x[k+14],9,3275163606),a,b,x[k+3],14,4107603335),d,a,x[k+8],20,1163531501),c=_GG(c,d=_GG(d,a=_GG(a,b,c,d,x[k+13],5,2850285829),b,c,x[k+2],9,4243563512),a,b,x[k+7],14,1735328473),d,a,x[k+12],20,2368359562),c=_HH(c,d=_HH(d,a=_HH(a,b,c,d,x[k+5],4,4294588738),b,c,x[k+8],11,2272392833),a,b,x[k+11],16,1839030562),d,a,x[k+14],23,4259657740),c=_HH(c,d=_HH(d,a=_HH(a,b,c,d,x[k+1],4,2763975236),b,c,x[k+4],11,1272893353),a,b,x[k+7],16,4139469664),d,a,x[k+10],23,3200236656),c=_HH(c,d=_HH(d,a=_HH(a,b,c,d,x[k+13],4,681279174),b,c,x[k+0],11,3936430074),a,b,x[k+3],16,3572445317),d,a,x[k+6],23,76029189),c=_HH(c,d=_HH(d,a=_HH(a,b,c,d,x[k+9],4,3654602809),b,c,x[k+12],11,3873151461),a,b,x[k+15],16,530742520),d,a,x[k+2],23,3299628645),c=_II(c,d=_II(d,a=_II(a,b,c,d,x[k+0],6,4096336452),b,c,x[k+7],10,1126891415),a,b,x[k+14],15,2878612391),d,a,x[k+5],21,4237533241),c=_II(c,d=_II(d,a=_II(a,b,c,d,x[k+12],6,1700485571),b,c,x[k+3],10,2399980690),a,b,x[k+10],15,4293915773),d,a,x[k+1],21,2240044497),c=_II(c,d=_II(d,a=_II(a,b,c,d,x[k+8],6,1873313359),b,c,x[k+15],10,4264355552),a,b,x[k+6],15,2734768916),d,a,x[k+13],21,1309151649),c=_II(c,d=_II(d,a=_II(a,b,c,d,x[k+4],6,4149444226),b,c,x[k+11],10,3174756917),a,b,x[k+2],15,718787259),d,a,x[k+9],21,3951481745),a=addUnsigned(a,AA),b=addUnsigned(b,BB),c=addUnsigned(c,CC),d=addUnsigned(d,DD);return{value:(wordToHex(a)+wordToHex(b)+wordToHex(c)+wordToHex(d)).toLowerCase(),values:[a,b,c,d]}}}.call(obj),scope.md5=function(str,objectType){var str=new MD5(str);return objectType?str:str.value}}.call(PUBLIC.utils),PRIVATE.init=function(){delete window.zaz.init,delete window.zaz.extend,delete window.zaz.options,delete window.zaz.status,window.zaz.console.check(!0,"zaz cleanup"),PRIVATE.zaz.status="running",window.zaz.console.check(!0,"zaz started running");try{Object.keys(window.zaz.console.report("errors")).length&&window.zaz.console.check(!1,"There have been problems during the startup!\nPlease check the logs above to identify the problem.")}catch(e){"object"==typeof window.console&&"function"==typeof window.console.warn&&window.console.warn("Problems gettings startup errors: "+e.message)}PKG.context.page.onceAt("ready",function(){if(window.performance&&"function"==typeof window.performance.mark)try{window.performance.mark("ZAZ_CEREBRO_PAGE_READY")}catch(perfException){window.console&&"object"==typeof window.console&&("function"==typeof window.console.warn?window.console.warn("Error registering performance metric ZAZ_CEREBRO_PAGE_READY. "+perfException.message):"function"==typeof window.console.log&&window.console.error("[ZaZ Warning] Error registering performance metric ZAZ_CEREBRO_PAGE_READY. "+perfException.message))}}),PRIVATE.dequeue()},0),timer=null,holded=0,holdFor=350,holdStep=33,execUse=(PUBLIC.hold=function(){holding++},PUBLIC.release=function(){--holding<=0&&PRIVATE.dequeue()},function(fn,pkg,special,scope){var ret,msg;try{ret=fn.apply(scope,[pkg,special])}catch(e){return e.message=(fn.name||"use")+":Check your function "+(fn.name?'"'+fn.name+'" ':"")+"for this error.\n"+e.message,(COMMON.console||console).error(e)}return ret||window.zaz}),getBody,uiComponents;PRIVATE.use=function(fn,scope){var special=null,pkg=PKG,i=null,useName=null,useName=PUBLIC.getFunctionName(fn)||"anonymous";if(PRIVATE.allowExtensions())PKG.extend||(PKG.extend=function(fn,scopeable){var i=null;if(!PRIVATE.allowExtensions())return PRIVATE.notAllowedExtension();if(PUBLIC.getFunctionName(fn),!fn.name)throw new Error("extend:Extensions for Package must be NAMED functions");scopeable?(PRIVATE.scopeable[fn.name]=fn,PKG[fn.name]=new PRIVATE.scopeable[fn.name](PROTECTED,fn.name)):PKG[fn.name]=fn(PROTECTED),PRIVATE.Extension.apply(PKG[fn.name],[this,fn])}),special=COMMON;else{if(!PRIVATE.dequeued)return void PRIVATE.queues[0].push(fn);for(i in PKG.extend!=PRIVATE.notAllowedExtension&&(PKG.extend=PRIVATE.notAllowedExtension),pkg=PRIVATE.clone(PKG),PRIVATE.Package.apply(pkg,[useName,PKG]),PRIVATE.scopeable)PRIVATE.scopeable.hasOwnProperty(i)&&(pkg[i]=new PRIVATE.scopeable[i](PROTECTED,fn.name));special={extend:PRIVATE.notAllowedExtension}}return pkg.options=PRIVATE.zaz.options,execUse(fn,pkg,special,scope||window),window.zaz},PRIVATE.dequeue=function(){var queue=PRIVATE.queues[0],i=null,cur;if(!PRIVATE.dequeued){if(PRIVATE.dequeued=!0,PUBLIC.trigger("onbeforedequeue",queue),PRIVATE.booted||(console.check(!0,"framework bootup finished."),console.info("Dequeuing previeous zaz.use calls"),console.line(),PRIVATE.booted=!0),window.performance&&"function"==typeof window.performance.mark)try{window.performance.mark("ZAZ_CEREBRO_STARTED")}catch(perfException){window.console&&"object"==typeof window.console&&("function"==typeof window.console.warn?window.console.warn("Error registering performance metric ZAZ_CEREBRO_STARTED. "+perfException.message):"function"==typeof window.console.log&&window.console.error("[ZaZ Warning] Error registering performance metric ZAZ_CEREBRO_STARTED. "+perfException.message))}for(;queue.length;)cur=queue.shift(),PRIVATE.use(cur,cur.scope);console.info("Finished dequeuing stored zaz.use calls"),PUBLIC.hold=function(){},PUBLIC.release=function(){}}},PUBLIC.introduce=function(componentId,comp,options){function watchForInstance(comp,fn,firstOnly){PUBLIC.require(comp,function(Comp){Comp[firstOnly?"onceAt":"at"]("instanced",function(fn,comp){return function(instance){var Comp;PRIVATE.components[comp].options.instanced=instance,fn(oComp,instance)}}(fn,comp))})}var watchers,watcher,oComp=comp;if(options=options||{},componentId=componentId.replace(/^(Package|PrivilegedPackage)\./,""),watchers=PRIVATE.watchList[componentId],!(PRIVATE.components[componentId]={component:comp,options:options})!==options.observable&&PUBLIC.utils.make.observable(comp,{canTrigger:"function"==typeof comp&&comp instanceof PRIVATE.PrivilegedPackage}),watchers){for(;watchers.length;)try{(watcher=watchers.shift()).data.waitForInstance?watchForInstance(Array.isArray(componentId)?componentId:[componentId],watcher.fn,watcher.data.firstInstanceOnly):PRIVATE.components[componentId].options.defined?PUBLIC.require(Array.isArray(componentId)?componentId:[componentId],watcher.fn):watcher.fn(PRIVATE.components[componentId].component)}catch(e){console.error("watch:Callback triggered an error",e)}PRIVATE.watchList[componentId]=[]}},PUBLIC.watch=function(what,does,options){var defined=PRIVATE.components[what]||PKG[what],runtItNow=!1,waitForIt=!1;if(options=options||{},defined&&(!options.instances||defined.options.instanced)?runtItNow=!0:waitForIt=!0,waitForIt&&(PRIVATE.watchList[what]||(PRIVATE.watchList[what]=[]),PRIVATE.watchList[what].push({fn:does,data:{waitForInstance:options.instances||!1,firstInstanceOnly:options.first||!1}})),runtItNow)try{defined.options.defined?PUBLIC.require(Array.isArray(what)?what:[what],function(factory){try{does(factory,defined.options.instanced)}catch(e){console.error('Exception caught while triggering "watchFor" listener',e)}}):does(defined.component||defined)}catch(e){console.error("watch:Callback triggered an error",e)}return PUBLIC},PUBLIC.watchFor=function(what,does){return PUBLIC.watch(what,does,{instances:!0}),PUBLIC},PUBLIC.watchOnceFor=function(what,does){return PUBLIC.watch(what,does,{instances:!0,first:!0}),PUBLIC},PUBLIC.ui={},PUBLIC.ui.head=document.getElementsByTagName("head")[0],PUBLIC.ui.html=document.getElementsByTagName("html")[0],(getBody=function(){document.body?(PUBLIC.ui.body=document.body,PUBLIC.introduce("body",PUBLIC.ui.body)):setTimeout(getBody,33)})(),PUBLIC.ui.getWindowSizes=function(){return[window.innerWidth||document.documentElement.clientWidth,window.innerHeight||document.documentElement.clientHeight]},uiComponents={},PUBLIC.ui.components={add:function(compName,comp){uiComponents[compName]&&window.console&&"function"==typeof console.warn&&window.console.warn("Overwriting UI Component "+compName),uiComponents[compName]=comp},list:function(){return Object.keys(uiComponents)},get:function(compName){return uiComponents[compName]}},PUBLIC.ui.colors={white:"#fff",black:"#000",grayDarkest:"#191917",grayDarker:"#33332F",grayDark:"#65655D",gray:"#A5A598",grayLight:"#D7D7D7",grayLighter:"#F1F1F1",brandingDarkest:"#C3570F",brandingDarker:"#DE6311",branding:"#FF7212",brandingLight:"#FD9417",brandingLighter:"#FAB61D",brandingLightest:"#FFE4B4",importantDarker:"#C11C05",importantDark:"#D72007",important:"#FF290B",socialDark:"#2B2ED0",social:"#245BE7",socialLight:"#2388DD",notification:"#50BC37",notificationLight:"#98D62D",notificationLighter:"#C6EA28",svaDark:"#E3B204",sva:"#FFCE1E",svaLight:"#F0EC08",sportsDark:"#006962",sports:"#00958C",sportsLight:"#59B87E",fun:"#B040A2",funLight:"#C471B7"},PUBLIC.getComponents=function(){var list={},args=Array.isArray(arguments[0])?arguments[0]:Array.prototype.slice.call(arguments);return args.length?args.forEach(function(cur){PRIVATE.components[cur]?list[cur]=PRIVATE.components[cur]:list[cur]=!1}):list=PRIVATE.clone(PRIVATE.components),list},PUBLIC.getComponent=function(comp){return void 0!==(comp=PRIVATE.components[comp])&&comp},PRIVATE.Package=function Package(){var args=Array.prototype.slice.call(arguments,0),i=null,data=PUBLIC,scopeName=args[0]||"anonymous";for(i in args)args.hasOwnProperty(i)&&args[i]&&"object"==typeof args[i]&&(data=PRIVATE.merge(data,args[i]));return data.scopeName=scopeName,PRIVATE.merge(this,data),this.name="Package",this.set=function(name,value){return"string"==typeof name?this[name]=value:COMMON.console.error("package:When setting a property to a package, the name of the property must be a string."),this},this.get=function(name){return this[name]},this},PRIVATE.PrivilegedPackage=function PrivilegedPackage(){return PRIVATE.Package.apply(this,["privileged",COMMON,PUBLIC]),this.extend=function(fn){PRIVATE.extend(fn,this)},this},PUBLIC.ApplicationError=function ApplicationError(data){var args=Array.prototype.slice.call(arguments,1),peaces=[],stk=null,not="not specified",givenModule="core";if(data instanceof PUBLIC.ApplicationError)return data;this.name=PUBLIC.getFunctionName(this.constructor);var getFirstStack=function(stk){for(var i=0,l=stk.length,match;i<l;i++)if(match=stk[i].match(/\s*at (.+?)\:([0-9]+)?\:([0-9]+)/))return match;return!1},getStackTrace=function(e){var lines,i,len,callstack=[],currentFunction,isCallstackPopulated=!1,entry;if(e.stack){for(i=0,len=(lines=e.stack.split(/\n|\@/g)).length;i<len;i++)lines[i]=lines[i].replace(/    /g,""),callstack.push(lines[i]);isCallstackPopulated=!0}else if(window.opera&&e.message){for(i=0,len=(lines=e.message.split("\n")).length;i<len;i++){lines[i].match(/^\s*[A-Za-z0-9\-_\$\ \.]+\(/)&&(entry=lines[i],lines[i+1]&&(entry+=" at "+lines[i+1],i++),callstack.push(entry))}isCallstackPopulated=!0}return callstack},treatStackTrace=function(stack){return stack="string"==typeof stack?stack.split(/\n|\@/g):stack};return args.length&&PRIVATE.merge(data.data,args),PRIVATE.merge(this,data),this.stack=data.err?data.err.stack:[],this.stack||(this.stack=getStackTrace(data.err)),!this.stack.length&&data.data&&data.data[1]&&(this.stack=getStackTrace(data.data[1]),this.err=data.data[1]),(givenModule=this.message||data.err||"").match&&(givenModule=givenModule.match(/^[a-zA-Z0-9 \-_\$\&\*\(\)\{[\]\{\}]{1,30}\:/)),this.message=this.message||"",givenModule&&(this.message=this.message.replace(givenModule[0],"")),this.stack=treatStackTrace(this.stack)||not,this.file=this.line=this.char=not,this.stack.length?(this.char=not,this.line=not,this.file=not,(stk=getFirstStack(this.stack))&&(this.file=stk[1],this.line=stk[2],this.char=stk[3]),isNaN(this.line)&&!isNaN(this.char)&&(this.line=this.char,this.char=not)):this.stack=not,this.context||(this.context="global"),!this.line&&data.line&&(this.line=data.line,this.file=data.file),this.err||(this.err=data),this},PRIVATE.Extension=function Extension(target,construct){var construct=PUBLIC.getFunctionName(construct),tmpName="",msg="",tmpName=!target.name&&target.constructor&&target.constructor.name?target.constructor.name:target.name||!1;return construct&&tmpName&&(construct=tmpName+"."+construct),void 0===this?(msg=(msg="It was not possible to apply the extensions properties")+(construct?' to "'+construct+'"':"")+"\n Perhaps your extension is not returning correctly when instantiated.",console.warn(msg),!1):(this.extend=function(fn){PRIVATE.extend(fn,this)},PUBLIC.introduce(construct,this),!0)},PRIVATE.extend=function(fn,target,doNotAutoInstantiate){var fullName="",name="",Fn;if(!PRIVATE.allowExtensions())throw new Error("Extensions are only allowed during building proccess.");if(target=target||!1,name=PUBLIC.getFunctionName(fn)||!1,Fn={},!target)throw new Error("extend:Must define who is being extended! A target must be given.");if(target instanceof PRIVATE.PrivilegedPackage&&("function"!=typeof fn||!name||name&&name[0]!=name[0].toUpperCase()))throw new Error("extend:Extensions for PrivilegedPackage must be NAMED functions, in PascalCase, as they are constructors.");if(target[name])throw new Error("extend:Extension "+name+" already in use!");if(!name)throw new Error("extend:Extension must be a NAMED function");try{if(Fn[name]=fn,target[name]=Fn[name],target.prototype&&(target.prototype[name]=target[name]),target.apply&&target.apply(target[name]),target.name&&target!==PKG&&target!==COMMON&&(fullName=target.name+"."),doNotAutoInstantiate)return target;target[(name[0].toLowerCase()+name.substring(1)).replace(/Factory/,"")]=new Fn[name](PROTECTED),PRIVATE.Extension.apply(target[name],[target,fn])}catch(e){throw new Error("extend:Failed trying to extend with "+name+" !\n"+e.message,e,fn)}return target},PRIVATE.merge=function(){var options,name,src,copy,copyIsArray,src,target=arguments[0]||{},i=1,length=arguments.length,deep=!1;for("boolean"==typeof target&&(deep=target,target=arguments[1]||{},i=2),"object"!=typeof target&&"function"!=typeof target&&(target={}),length===i&&(target=this,--i);i<length;i++)if(options=arguments[i])for(name in options)options.hasOwnProperty(name)&&(src=target[name],target!==(copy=options[name])&&(deep&&copy&&("object"==typeof copy&&!copy.nodeType&&copy!=window||(copyIsArray=Array.isArray(copy)))?(src=copyIsArray?(copyIsArray=!1,src&&Array.isArray(src)?src:[]):src&&("object"==typeof copy&&!copy.nodeType&&copy!=window||(copyIsArray=Array.isArray(copy)))?src:{},target[name]=PRIVATE.merge({},deep,src,copy)):void 0!==copy&&(target[name]=copy)));return target},PRIVATE.deepMerge=function(target,src){var array=Array.isArray(target),list=Array.prototype.slice.call(arguments),dst=array?[]:{};return src instanceof RegExp?src:(target instanceof RegExp&&(dst=target),array?(target=target||[],dst=dst.concat(target),list.forEach(function(src){Array.isArray(src)?src.forEach(function(e,i){void 0===dst[i]?dst[i]=e:"object"==typeof e?dst[i]=PRIVATE.deepMerge(target[i],e):-1===target.indexOf(e)&&dst.push(e)}):console.error("You are trying to merge an object with an array!",target,src)})):(target&&"object"==typeof target&&Object.keys(target).forEach(function(key){dst[key]=target[key]}),list.forEach(function(src){Object.keys(src).forEach(function(key){"object"==typeof src[key]&&src[key]&&target[key]?dst[key]=PRIVATE.deepMerge(target[key],src[key]):dst[key]=src[key]})})),dst)},PRIVATE.clone=function(o){return PRIVATE.merge({},o)},PUBLIC.widthRanges={xsmall:[0,319],small:[320,767],medium:[768,1023],large:[1024,1279],xlarge:[1280,99999]},PUBLIC.getRangeFromValue=function(val,ranges){var i=0,cur=[],highest=0,highestRange="",smallest=99999999,smallestRange="";for(i in ranges=ranges||PUBLIC.widthRanges)if(ranges.hasOwnProperty(i)){if(val>=(cur=ranges[i])[0]&&val<=cur[1])return i;cur[1]>highest&&(highest=cur[1],highestRange=i),cur[0]<smallest&&(smallest=cur[0],smallestRange=i)}return val<smallest?smallestRange:highest<val&&highestRange},PUBLIC.makeObservable=function(target,observerOpts){return PUBLIC.utils.make.observable(target,observerOpts),this},PUBLIC.introduce("utils.make",PUBLIC.utils.make),PUBLIC.utils.clone=PRIVATE.clone,PUBLIC.utils.merge=PRIVATE.merge,PUBLIC.utils.deepMerge=PRIVATE.deepMerge,PUBLIC.utils.joinWithGlue=function(list,glue,lastGlue){var listMinusLast=list.slice(0,-1),list=list.slice(-1);return lastGlue=lastGlue||" and ",listMinusLast.join(glue=glue||", ")+lastGlue+list},PUBLIC.utils.arraysToObject=function(keys,values){var o={},l=values.length;return keys&&keys.forEach&&keys.forEach(function(key,i){o[key]=i<=l?values[i]:void 0}),o},PRIVATE.zaz={use:PRIVATE.use,init:PRIVATE.init,version:"1.7.6",lastRevision:lastRevision,fullyLoaded:!0,status:"pre-building",options:{global:{devmode:!1,verbosity:1}},environment:{}},PUBLIC.utils.createStyle=function(styles){if("string"!=typeof styles){var tmpStr="",prepareStyle=function(selector,cur){tmpStr+=cur+": "+styles[selector][cur]+"; "},selector;for(selector in styles)styles.hasOwnProperty(selector)&&(tmpStr+=selector+"{",Object.keys(styles[selector]).forEach(prepareStyle.bind(null,selector)),tmpStr+="} ");styles=tmpStr}var css=document.createElement("style");css.type="text/css",css.styleSheet?css.styleSheet.cssText=styles:css.appendChild(document.createTextNode(styles)),PUBLIC.ui.head.appendChild(css)},PUBLIC.utils.loadStyle=function(linkAddr,cb){var s=document.createElement("link");s.setAttribute("type","text/css"),s.setAttribute("rel","stylesheet"),s.setAttribute("href",linkAddr),PUBLIC.ui.head.appendChild(s)},PUBLIC.utils.loadScript=function(scriptSrc,cb){var tag=document.createElement("script");tag.type="text/javascript",tag.async=!0,tag.charset="utf-8",tag.onload=tag.onreadystatechange=function(){this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState||(this.onload=this.onreadystatechange=null,setTimeout(function(){cb()},0))},tag.src=scriptSrc},PUBLIC.utils.cheats=function(){var cheats={},shortcuts={},holding=[],typing="",timing=500,timer=null,exec=function(o){try{o.fn.forEach(function(f){f()})}catch(e){console.error("cheat:Failed executing a cheat code!\n"+e.message,e)}},checkTypedCheat=PUBLIC.utils.make.debounce(function(){return!!cheats[typing]&&exec(cheats[typing])},{distance:490}),checkForShortcut=function(){var keys=holding.join("");if(1<holding.length&&shortcuts[keys])return holding.length=0,keys=exec(shortcuts[keys])},parseCode=(document.addEventListener("keydown",function(evt){typing+=evt.keyCode,holding.push(evt.keyCode),clearTimeout(timer),timer=setTimeout(function(){typing=""},500),checkTypedCheat()||checkForShortcut()}),document.addEventListener("keyup",function(evt){holding.splice(holding.indexOf(evt.keyCode),1)}),window.addEventListener("blur",function(evt){(holding=[]).length=0}),function(arr){"string"==typeof arr&&(arr=arr.split(","));var code=[];return arr.forEach(function(cur){switch(cur=(cur="string"!=typeof cur?cur.toString():cur).toUpperCase()){case"UP":case"↑":code.push(38);break;case"DOWN":case"↓":code.push(40);break;case"LEFT":case"←":code.push(37);break;case"RIGHT":case"→":code.push(39);break;case"CTRL":code.push(17);break;case"ESC":code.push(27);break;case"SHIFT":code.push(16);break;case"ENTER":code.push(13);break;case"SPACE":code.push(32);break;default:code.push(cur.charCodeAt(0))}}),code.join("").toUpperCase()});return PUBLIC.utils.shortcuts={create:function(keys,fn,data){PUBLIC.utils.cheats.create(keys,fn,data)},list:function(){return shortcuts}},{create:function(arr,fn,together){var code=parseCode(arr);together?shortcuts[code]?(shortcuts[code].fn.push(fn),shortcuts[code].data.push(together)):shortcuts[code]={keys:arr.join("+"),fn:[fn],data:[{name:together.name,description:together.description}]}:cheats[code]?cheats[code].fn.push(fn):cheats[code]={fn:[fn],data:{}}},list:function(){return cheats}}}(),PRIVATE.prepare()}(document),zaz.use(function(pkg){"use strict";pkg.utils.cheats.create("↑ ↑ ↓ ↓ ← → ← → B A".split(" "),function(){debugger})}),window.Promise||!function(){var a,b,c,d,e,f;e={},f={},d=c=b=function(a){function c(b){if("."!==b.charAt(0))return b;for(var c=b.split("/"),d=a.split("/").slice(0,-1),e=0,f=c.length;e<f;e++){var g=c[e];".."===g?d.pop():"."!==g&&d.push(g)}return d.join("/")}if(d._eak_seen=e,f[a])return f[a];if(f[a]={},!e[a])throw new Error("Could not find module "+a);for(var g,h=e[a],i=h.deps,h=h.callback,k=[],l=0,m=i.length;l<m;l++)"exports"===i[l]?k.push(g={}):k.push(b(c(i[l])));var h=h.apply(this,k);return f[a]=g||h},(a=function(a,b,c){e[a]={deps:b,callback:c}})("promise/all",["./utils","exports"],function(a,b){"use strict";function c(a){var b=this;if(d(a))return new b(function(b,c){function d(a){return function(b){f(a,b)}}function f(a,c){h[a]=c,0==--i&&b(h)}var g,h=[],i=a.length;0===i&&b([]);for(var j=0;j<a.length;j++)(g=a[j])&&e(g.then)?g.then(d(j),c):f(j,g)});throw new TypeError("You must pass an array to all.")}var d=a.isArray,e=a.isFunction;b.all=c}),a("promise/asap",["exports"],function(a){"use strict";function b(){return function(){process.nextTick(e)}}function c(){var a=0,b=new i(e),c=document.createTextNode("");return b.observe(c,{characterData:!0}),function(){c.data=a=++a%2}}function d(){return function(){j.setTimeout(e,1)}}function e(){for(var a=0;a<k.length;a++){var b=k[a],c,d;(0,b[0])(b[1])}k=[]}function f(a,b){var c;1===k.push([a,b])&&g()}var g,h="undefined"!=typeof window?window:{},i=h.MutationObserver||h.WebKitMutationObserver,j="undefined"!=typeof global?global:this,k=[],g=("undefined"!=typeof process&&"[object process]"==={}.toString.call(process)?b:i?c:d)();a.asap=f}),a("promise/cast",["exports"],function(a){"use strict";function b(a){return a&&"object"==typeof a&&a.constructor===this?a:new this(function(b){b(a)});var b}a.cast=b}),a("promise/config",["exports"],function(a){"use strict";function b(a,b){return 2!==arguments.length?c[a]:void(c[a]=b)}var c={instrument:!1};a.config=c,a.configure=b}),a("promise/polyfill",["./promise","./utils","exports"],function(a,b,c){"use strict";function d(){var a;"Promise"in window&&"cast"in window.Promise&&"resolve"in window.Promise&&"reject"in window.Promise&&"all"in window.Promise&&"race"in window.Promise&&function(){var a;return new window.Promise(function(b){a=b}),f(a)}()||(window.Promise=e)}var e=a.Promise,f=b.isFunction;c.polyfill=d}),a("promise/promise",["./config","./utils","./cast","./all","./race","./resolve","./reject","./asap","exports"],function(a,b,c,d,e,f,g,h,i){"use strict";function j(a){if(!w(a))throw new TypeError("You must pass a resolver function as the first argument to the promise constructor");if(!(this instanceof j))throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.");this._subscribers=[],k(a,this)}function k(a,b){function c(a){p(b,a)}function d(a){r(b,a)}try{a(c,d)}catch(e){d(e)}}function l(a,b,c,d){var e,f,g,h,i=w(c);if(i)try{e=c(d),g=!0}catch(j){h=!0,f=j}else e=d,g=!0;o(b,e)||(i&&g?p(b,e):h?r(b,f):a===F?p(b,e):a===G&&r(b,e))}function m(a,b,c,d){var a=a._subscribers,f=a.length;a[f]=b,a[f+F]=c,a[f+G]=d}function n(a,b){for(var c,d,e=a._subscribers,f=a._detail,g=0;g<e.length;g+=3)l(b,c=e[g],d=e[g+b],f);a._subscribers=null}function o(a,b){var c,d=null;try{if(a===b)throw new TypeError("A promises callback cannot return that same promise.");if(v(b)&&(d=b.then,w(d)))return d.call(b,function(d){return!!c||(c=!0,void(b!==d?p:q)(a,d))},function(b){return!!c||(c=!0,void r(a,b))}),1}catch(e){return c||r(a,e),1}}function p(a,b){a!==b&&o(a,b)||q(a,b)}function q(a,b){a._state===D&&(a._state=E,a._detail=b,u.async(s,a))}function r(a,b){a._state===D&&(a._state=E,a._detail=b,u.async(t,a))}function s(a){n(a,a._state=F)}function t(a){n(a,a._state=G)}var u=a.config,v=(a.configure,b.objectOrFunction),w=b.isFunction,a=(b.now,c.cast),b=d.all,c=e.race,d=f.resolve,e=g.reject,f=h.asap,D=(u.async=f,void 0),E=0,F=1,G=2;j.prototype={constructor:j,_state:void 0,_detail:void 0,_subscribers:void 0,then:function(a,b){var c=this,d=new this.constructor(function(){}),e;return this._state?(e=arguments,u.async(function(){l(c._state,d,e[c._state-1],c._detail)})):m(this,d,a,b),d},catch:function(a){return this.then(null,a)}},j.all=b,j.cast=a,j.race=c,j.resolve=d,j.reject=e,i.Promise=j}),a("promise/race",["./utils","exports"],function(a,b){"use strict";function c(a){var b=this;if(d(a))return new b(function(b,c){for(var d,e=0;e<a.length;e++)(d=a[e])&&"function"==typeof d.then?d.then(b,c):b(d)});throw new TypeError("You must pass an array to race.")}var d=a.isArray;b.race=c}),a("promise/reject",["exports"],function(a){"use strict";function b(a){var b;return new this(function(b,c){c(a)})}a.reject=b}),a("promise/resolve",["exports"],function(a){"use strict";function b(a){var b;return new this(function(b){b(a)})}a.resolve=b}),a("promise/utils",["exports"],function(a){"use strict";function b(a){return c(a)||"object"==typeof a&&null!==a}function c(a){return"function"==typeof a}function d(a){return"[object Array]"===Object.prototype.toString.call(a)}var e=Date.now||function(){return(new Date).getTime()};a.objectOrFunction=b,a.isFunction=c,a.isArray=d,a.now=e}),b("promise/polyfill").polyfill()}(),zaz.use(function DebuggerBuilder(pkg,__privileged){"use strict";function shouldOutput(){var args=null,rx=null;if(options.showOnly||options.doNotShow){if(args=Array.prototype.slice.call(arguments,0).join(),options.doNotShow&&options.doNotShow.length&&(rx=new RegExp(options.doNotShow,"i"),args.match(rx)))return!1;if(options.showOnly&&options.showOnly.length&&(rx=new RegExp(options.showOnly,"i"),!args.match(rx)))return!1}return!0}function getTime(){return++logId}function prepareForRX(val){return val.join&&(val.join=val.join("|")),val}var PRIVATE={},tpn=/tpn=1/.test(location.search),crashReport=function(){var api={},queryStringOption=/cerebro\.crashReport=([^&]+)/.exec(location.search),tail=0,items=[],queryStringOption;if(queryStringOption&&(tail=parseInt(queryStringOption[1],10)||10,queryStringOption=sessionStorage.getItem("crashReport")))try{queryStringOption=JSON.parse(queryStringOption),queryStringOption=JSON.stringify(queryStringOption,null,"\t\n"),window.unescape&&window.unescape(queryStringOption)}catch(e){debugger}finally{alert("=-=-=-\nCRASH REPORT\n=-=-=-\n\n"+queryStringOption),sessionStorage.removeItem("crashReport")}return api.tail=tail,api.active=!!tail,api.add=function(value){api.active&&(items.push(value),items=items.slice(-1*api.tail),sessionStorage.setItem("crashReport",JSON.stringify(items)))},api}(),c=window.console,originalErrorFunc=c.error,tellOthers=!1,labels=__privileged.consoleLabels,options=pkg.options&&pkg.options.console||{},defaultVoid="not specified",logId=0,persistentLog=/cerebro\.persistentLog=(1|true)/i.test(location.search),isThirdParty=!1,supportErrorManipulation=!0;/cerebro\.console\.preferNative=(true|1)/.test(location.search)&&(options.doNotTreatErrors=!0);try{throw new Error("verifying")}catch(e){try{e.stack=e.stack+"\n"}catch(caught){supportErrorManipulation=!1}}PRIVATE.queue={all:[],errors:{},errorsLength:0,warnings:{},warningsLength:0,checks:{},logs:{},info:{},instances:[],startTime:(new Date).getTime()},pkg.watch("context.page",function(pg){var i=null;if(pg.get("thirdParty"))for(i in labels)labels.hasOwnProperty(i)&&(labels[i]="")});var trigger=function triggerConsoleEvents(inst,evt,data,scope){data={scope:inst.scope||this.scope||scope||"global",type:evt,data:data},__privileged.console.trigger(evt,data)},noGlobalsShallPass,treatLongMessages=(function(){var globalList=pkg.getInitialGlobalVars(),getGlobalVars=pkg.getGlobalVars}(),function(str,glue,len){var tokens=str.match(/\S+/gm),chunks=[],line="",word;if(len=len||70,glue=glue||"\n",tokens&&tokens.length)for(;tokens.length;)word=tokens.shift(),line.length+word.length+1<=len?(line&&(line+=" "),line+=word):(chunks.push(line),line=word),!tokens.length&&line&&chunks.push(line);return chunks.join(glue)}),addScope=function(scope){PRIVATE.queue.instances.indexOf(scope)<0&&PRIVATE.queue.instances.push(scope)},verifyScope=function(console,msgs){var msgs=msgs[0],scp=console.scope||"global",console;return console!=window.console||console.scope&&"global"!=console.scope||msgs&&msgs.match&&((console=msgs.match(/\[(.+)\]/))&&(scp=(scp=console[1]).replace(/\](\W+)?\[/g,"."),addScope(scp))),scp},applyVerbosity=("string"==typeof options.doNotShow&&(options.doNotShow=options.doNotShow.replace(/,/g,"|")),options&&options.showOnly&&(options.showOnly=prepareForRX(options.showOnly)),options.doNotShow=options.doNotShow||[],function(verbosityLevel,from){switch(c.log(labels.info,"Applying verbosity from "+(from||"")+" options\n"+labels.space+"Verbosity level: "+verbosityLevel),"string"==typeof verbosityLevel&&(verbosityLevel=parseInt(verbosityLevel,10),isNaN(verbosityLevel)&&(verbosityLevel=!1)),verbosityLevel){case 0:case!1:options.doNotShowLogs=1,options.doNotShowWarnings=1,options.doNotTreatErrors=1;break;case 2:options.doNotShowLogs=1;break;case 3:options.doNotShowLogs=1,options.doNotShowWarnings=1;break;case 4:options.doNotShowWarnings=1,options.doNotTreatErrors=1}}),applyVerbosity=(void 0!==options.verbosity&&applyVerbosity(options.verbosity,"console"),options.doNotShow&&(0<=options.doNotShow.indexOf("warnings")&&(options.doNotShowWarnings=1),0<=options.doNotShow.indexOf("errors")&&(options.doNotShowErrors=1),0<=options.doNotShow.indexOf("globalErrors")&&(options.doNotTreatGlobalErrors=1),0<=options.doNotShow.indexOf("checks")&&(options.doNotShowChecks=1),0<=options.doNotShow.indexOf("logs")&&(options.doNotShowLogs=1),0<=options.doNotShow.indexOf("info")&&(options.doNotShowLogs=1)),options&&options.doNotShow&&(options.doNotShow=prepareForRX(options.doNotShow)),__privileged.extend(function Debugger(__protected){function indent(args,extra){return(args=args.forEach?args:[args]).forEach(function(cur,i){"string"==typeof cur&&(cur=cur.replace(/\n/g,"\n"+labels.space+(extra||"")),args[i]=cur)}),args}var self=this,scope="global",i=null,func=function(i){return function(){var args=Array.prototype.slice.call(arguments,0);return c[i].apply(c,args)}};this.scope=scope,"string"==typeof __protected&&(scope=this.scope=__protected||"global",addScope(scope)),this.line=function(ch,len){var msg="";ch=ch||"-",len=len||80,shouldOutput(msg="  "+new Array(len+1).join(ch).substr(0,len))&&c.log(msg)},options.doNotTreatErrors?this.error=c.error.bind(c):this.error=function debuggerError(o){var msg=null,closing=null,ctx=null,peaces=null,args=Array.prototype.slice.call(arguments,0),output="",original=o;if(supportErrorManipulation||(o=Object.create("object"==typeof o?o:{})),options.doNotTreatErrors)return c.error(o),!1;if(("string"==typeof o||!o.context&&o.err)&&(o={context:(peaces=/^(?:\[(.+?)\])[\s:->→]*(.+)/.exec(o.message||o))?peaces[1]:"",message:peaces?peaces[2]:o,data:args,err:null},self.scope&&(o.context=self.scope),original.replace&&(o.err=original.replace("["+o.context+"]","    "))),original instanceof pkg.ApplicationError){var peaces="",errorData={},errMsg=(args.shift(),o.err&&("string"==typeof o.err?o.err:o.err.message)||""),f=(-1!==errMsg.indexOf(o.message)&&(o.message=errMsg),errMsg&&o.message&&errMsg!=o.message?errMsg+="\n"+o.message:errMsg=o.message,o.filename||o.file),l=o.lineno||o.line,ch=o.colno||o.char,f=f==defaultVoid?"--":f,l,ch=ch==defaultVoid?"--":ch;if("--"!=(l=l==defaultVoid?"--":l)&&"--"!=ch&&(f+=":"+l+":"+ch),pkg.isLegacy()){try{errorData=JSON.stringify(original.err||original,null,4).replace(/\n/g,"\n | ")}catch(e){for(var i in original)o[i]&&"message"!=i&&"data"!=i&&(errorData+=i+": "+o[i]+"\n")}peaces=errorData}else o.stack==defaultVoid&&o.error&&o.error.stack&&(o.stack=o.error.stack,o.stack=o.stack.split("\n")),o.stack.join&&(peaces=o.stack.join("\n"+labels.space));"string"!=typeof errMsg&&(errMsg=JSON.stringify(errMsg)),args.unshift(treatLongMessages(errMsg)+"\n\nType:     "+o.name+"\nScope:    "+(o.context?""+o.context:"")+(self.scope&&self.scope!=o.context?"["+self.scope+"]":"")+"\nFile:     "+f+"\nLine:     "+l+"\nChar:     "+ch+"\n"),tellOthers&&trigger(self,"error",[o,args[0],peaces]);var errMsg=[self.scope||o.context||"global"];return PRIVATE.queue.errors[errMsg]||(PRIVATE.queue.errors[errMsg]=[]),persistentLog&&PRIVATE.queue.errors[errMsg].push({triggeredAt:++logId,scope:self.scope,args:[labels.error,original.message].concat(args),type:"error"}),PRIVATE.queue.errorsLength++,supportErrorManipulation?c.error.apply(c,indent(args,"   ")):c.log(JSON.stringify(args)),peaces&&c.log.apply(c,[labels.space+"Stacktrace:\n +----------------^-----------------------------------\n | "+peaces.replace(/\n             /g,"\n |    "),"\n +----------------------------------------------------"]),o}return original instanceof Error?(ctx=self.scope||"global",o.message=original.message,o.err=original,o.stack=original.stack):("string"==typeof arguments[0]&&(o.message=arguments[0]),ctx=o.context||self.scope||self.runningAt||!1,o.context=ctx),o.data||(o.data=Array.prototype.slice.call(arguments,0)),self.error(new pkg.ApplicationError(o)),o},this.context=!1,this.report=function(what){return what?PRIVATE.queue[what]:PRIVATE.queue},this.check=function(status,message){var msg=status?"✔ ":"✘ ",args=Array.prototype.slice.call(arguments,1),type="check"+(status?"OK":"Fail"),chkQueue=[this.scope||"global"];if(PRIVATE.queue.checks[chkQueue]||(PRIVATE.queue.checks[chkQueue]={success:[],failures:[]}),persistentLog&&PRIVATE.queue.checks[chkQueue][status?"success":"failures"].push({triggeredAt:++logId,scope:chkQueue,args:[msg].concat(args),type:type}),!options.doNotShowChecks&&shouldOutput(msg,message)){args[0]=msg+" "+args[0];try{c.log.apply(c,args)}catch(e){args=[args.join(" ")],c.log.apply(c,args)}}tellOthers&&trigger(this,type,[status,message],scope)},this.warning=this.warn=function(){var args=Array.prototype.slice.call(arguments,0),q=[this.scope||"global"];if(PRIVATE.queue.warnings[q]||(PRIVATE.queue.warnings[q]=[]),persistentLog&&PRIVATE.queue.warnings[q].push({triggeredAt:++logId,args:args,scope:this.scope,type:"warn"}),PRIVATE.queue.warningsLength++,tellOthers&&trigger(this,"warn",args,scope),!options.doNotShowWarnings&&shouldOutput.apply(c,args))if(c.warn.apply)try{c.warn.apply(c,args)}catch(e){args=[args.join(" ")],c.warn.apply(c,args)}else c.warn(args[0],args[1]||"",args[2]||"",args[3]||"")},this.log=function(){var args=Array.prototype.slice.call(arguments,0),scp=verifyScope(this,args),q=[scp||"global"];if(PRIVATE.queue.logs[q]||(PRIVATE.queue.logs[q]=[]),persistentLog&&PRIVATE.queue.logs[q].push({triggeredAt:++logId,scope:scp,args:args,type:"log"}),scp&&"global"!=scp&&(args.unshift("["+scp+"]"),"string"==typeof args[0]&&(args[0]=args[0].replace(new RegExp("^\\s*?\\[\\s*?"+scp+"\\s*?\\][\\s:->→]*","i"),""))),tellOthers&&trigger(this,"log",args,scp),!options.doNotShowLogs&&shouldOutput.apply(c,args))if(c.log.apply)try{c.log.apply(c,args)}catch(e){args=[args.join(" ")],c.log.apply(c,args)}else{var i=0,l=args.length;c.log(args[0],args[1]||"",args[2]||"",args[3]||"",args[4]||"",{metadata:!0,scope:scope})}},this.info=function(){var args=Array.prototype.slice.call(arguments,0),scp=verifyScope(this,args),q=[this.scope||"global"];if(PRIVATE.queue.logs[q]||(PRIVATE.queue.logs[q]=[]),tellOthers&&trigger(this,"info",args,scope),persistentLog&&PRIVATE.queue.logs[q].push({triggeredAt:++logId,scope:scp,args:args,type:"info"}),!options.doNotShowInfo&&shouldOutput.apply(c,args))if("function"==typeof c.info&&c.info.apply)try{c.info.apply(c,args)}catch(e){args=[args.join(" ")],c.info.apply(c,args)}else c.log(args[0],args[1]||"",args[2]||"",args[3]||"")},this.checkpoint=function(){var args=Array.prototype.slice.call(arguments,0),scope="",scp=verifyScope(this,args),scope=(1<args.length&&(scope=args[0]||(this.scope?" ["+this.scope+"]":""),args.shift()),args.unshift("✔ "+scope),tellOthers&&trigger(this,"checkpoint",args,scope),[scp||"global"]);if(PRIVATE.queue.checks[scope]||(PRIVATE.queue.checks[scope]={success:[],failures:[]}),persistentLog&&PRIVATE.queue.checks[scope].success.push({triggeredAt:++logId,scope:scp,args:args,type:"checkpoint"}),!options.doNotShowChecks&&shouldOutput.apply(c,args)){if(c.log.apply){try{c.log.apply(c,args)}catch(e){args=[args.join(" ")],c.log.apply(c,args)}return c.log.apply(c,args)}c.log(args[0],args[1]||"",args[2]||"",args[3]||"")}},this.critical=this.error,this.debug=this.info,pkg.watch("utils.make",function(onFn){var wc=window.console;wc.__makeData&&wc.__makeData.observable?(self.on=wc.on,self.off=wc.off,self.once=wc.once,self.at=wc.at,self.onceAt=wc.onceAt,self.trigger=wc.trigger):pkg.utils.make.observable(self,{canTrigger:!0}),tellOthers=!0}),__privileged.console instanceof Debugger||(__privileged.console=this)}),function(){return!window.console||!(!/(MSIE|IEMobile|rv).+?([6-9]{1,}[\.0-9]{0,})/.test(navigator.userAgent)&&window.console.info)&&(window.console.debug||(window.console.debug=window.console.info),!0)}),errorFunc=(__privileged.console.zaz=!0,applyVerbosity()&&(window.zaz.nativeConsole=window.console,window.console=__privileged.console),window.zaz.console=__privileged.console,crashReport.active&&__privileged.console.on("*",function(details){crashReport.add(JSON.stringify(details))}),function(evt,url,number){if("Script error."==evt.message)return!1;var err=new pkg.ApplicationError(evt,url,number);return"string"==typeof evt&&(evt="global:"+evt),url&&(err.file=url,err.line=number),__privileged.console.error(err),crashReport.active&&crashReport.add(err),!1});pkg.extend(function console(__protected,scope){return new __privileged.Debugger(scope)},!0)}),console.check(!0,"Loaded extension debugger.js"),zaz.use(function buildingSchema(pkg,__privileged){"use strict";function validType(data,expected,enumered){var type,approved=!1;if(expected=expected||"mixed",Array.isArray(expected)?expected.map(function(cur){return cur&&cur.toLowerCase()}):expected=[expected],!(enumered&&enumered.indexOf(data)<0)){for(var i=0,l=expected.length;i<l&&("mixed"==(type=expected[i])&&(approved=!0),"array"==type&&(approved=Array.isArray(data)),"number"==type&&(approved=!isNaN(data)),!(approved=(approved="dom"==type?!!data.nodeType:approved)||typeof data==type));i++);return approved}}function validate(model,data,identifier){var i=null,tmp={},args=[],id=identifier||model.id||("string"==typeof model?model:""),name="";if("string"==typeof model&&!(model=schemas[model]))throw new Error('Invalid model to validate from schema "'+model+'"!');if(!data)return"No object was passed!";for(i in name=model.name||data.name||id,tmp=(model=model.implement?model.implement:model).methods){if("function"!=typeof data[i])return id+" instances must implement the following methods: "+Object.keys(tmp).join(", ");if(0<tmp[i]&&(!(args=((args=data[i]&&data[i].toString().match(/\((.+)?\)/)||[])[1]||"").split(",")).length||args.length<tmp[i]||""===args[0]))return'Method "'+i+'" when instanciating "'+id+'", is expected to receive at least '+tmp[i]+" arguments"}var tmp,required=[],notNamed="not-named",help,formatStr;for(i in tmp=model.properties)tmp[i].required&&required.push(i);for(i in required=required.join(", "),tmp)if(tmp.hasOwnProperty(i)){if(data[i]||"boolean"==tmp[i].type&&!1===data[i]||"number"==tmp[i].type&&0===data[i]||tmp[i].default&&(data[i]=tmp[i].default),void 0===data[i]&&tmp[i].required)return'"'+(id||name||notNamed)+'" instances are expected to have the following properties: '+required;if(void 0!==data[i]&&!validType(data[i],tmp[i].type,tmp[i].enum))return'"'+(id||name||notNamed)+'" instances, expect the property "'+i+'" to be of type "'+tmp[i].type+'" (received '+typeof data[i]+("object"!=typeof data[i]?' "'+data[i]+'"':"")+")"+(tmp[i].enum?' and the value must be one of: "'+pkg.utils.joinWithGlue(tmp[i].enum,'", "','" or "')+'"':"")+(model.docs?"\n * Check the documentation at "+model.docs:"")+(tmp[i].description?'\nDescription: "'+tmp[i].description+'"':"");if(data[i]&&tmp[i].format&&!data[i].toString().match(tmp[i].format))return help="",tmp[i].description&&(help=" ("+tmp[i].description+")"),formatStr=tmp[i].format.length?tmp[i].format:tmp[i].format.valueOf(),'"'+(name||id||notNamed)+'" instances must provide '+i+'(received "'+data[i]+'") with a value that matches '+formatStr+help;if(void 0!==data[i]&&"object"===tmp[i].type){var formatStr=validate(tmp[i],data[i]);if(!0!==formatStr)return'The given object is not valid for model "'+(id||name||"")+'", with the model specified in "'+i+'", with the message:\n'+formatStr}if("function"==typeof tmp[i].validate)try{var validated=tmp[i].validate(data[i]);if(!0!==validated)return'Invalid object for model "'+(id||notNamed)+'" for "'+i+'" with the value "'+data[i]+'"!\nThe validation function returned: '+validated}catch(e){var help='Validation function failed validating "'+(id||name||notNamed)+'" for "'+i+'" with the value "'+data[i]+'"!\nThe validation function triggered: '+e.message;return console.error(help),help}}return!0}var console=pkg.console,schemas={};pkg.schemas={create:function(schemaId,data){!schemas[schemaId]&&data&&"object"==typeof data&&(schemas[schemaId]=data)},validate:validate,showSchemaDescription:function(schema,indent){var props=(schema=schemas[schema]||schema).properties,methods=schema.methods,i=0,l=0,str="";for(i in indent="\n"+(indent&&indent.length?indent:"          "),props)props.hasOwnProperty(i)&&(str+=indent+((props[i].required?"":"[")+i+(props[i].required?"":"]"))+": "+props[i].type+(props[i].description?"  // "+props[i].description:""));return str}}}),console.check(!0,"Loaded extension schema.js"),zaz.use(function(pkg,__privileged){"use strict";function Context(name,desc,options){var PRIVATE={contextId:"zaz-context-"+name,contextPrefix:"zaz-"+name+"-",contextMigrationPrefix:"zaz-migrated-",data:{},migratedAttributes:null},PUBLIC=this,persistent=(options=options||{}).persistent||!1;if(persistent){var attrs=options.volatileAttrs||{},k;for(k in attrs)attrs.hasOwnProperty(k)&&(PRIVATE.data[k]=attrs[k])}else PRIVATE.data.description=desc||"";var get=function(attribute,cb){if(persistent){if(!cb||"function"!=typeof cb){if(-1!==Object.keys(PRIVATE.data).indexOf(attribute))return pkg.console.warning("[Cerebro] Volatile attribute",attribute,"of persistant Context was requested synchronously. This should be DEPRECATED!"),PRIVATE.data[attribute];throw pkg.console.error("[Cerebro] Persistant contexts are now asynchronous. No sync call allowed!"),new Error("Invalid sync use of context.get()")}pkg.require(["GlobalStorage","mod.stalker"],function(GlobalStorage,Stalker){var stalkerAttr=Stalker.translateAttribute(attribute),stalker;stalkerAttr?(new Stalker).getAttribute(stalkerAttr).then(cb):(globalStorage=globalStorage||new GlobalStorage).getItem(PRIVATE.contextPrefix+attribute,function(val){var obj;try{obj=JSON.parse(val||null)}catch(e){pkg.console.error("[Cerebro] Unable to parse Global Storage context string:",e)}cb(obj?obj.v:void 0)})})}else{if(!cb||"function"!=typeof cb)return PRIVATE.data[attribute];cb(PRIVATE.data[attribute])}},set=function(attribute,value,cb){return!persistent||-1!==Object.keys(PRIVATE.data).indexOf(attribute)?PRIVATE.data[attribute]=value:pkg.require(["GlobalStorage","mod.stalker"],function(GlobalStorage,Stalker){value&&"object"==typeof value&&(value=pkg.utils.make.cleanUpProperties(value));var stalkerAttr=Stalker.translateAttribute(attribute),stalker;if(stalkerAttr){0<=["pageviews","adsWall","newsletterSubscriberId"].indexOf(stalkerAttr)&&(value=[value[0]]),(new Stalker).setAttribute(stalkerAttr,value).then(function(){if(cb&&"function"==typeof cb)try{cb()}catch(e){pkg.console.error("[Cerebro] Exception caught on context.set() callback for Stalker attribute:",e)}})}else{globalStorage=globalStorage||new GlobalStorage;var Stalker={v:value};try{globalStorage.setItem(PRIVATE.contextPrefix+attribute,JSON.stringify(Stalker),function(){if(cb&&"function"==typeof cb)try{cb()}catch(e){pkg.console.error("[Cerebro] Exception caught on context.set() callback for GlobalStorage attribute:",e)}})}catch(e){pkg.console.error("[Cerebro] Unable to store value for",attribute,"in Global Storage:",e)}}}),value};return pkg.watch("utils.make",function(onFn){pkg.utils.make.observable(PUBLIC,{recursive:void 0===options.recursive||options.recursive}),PUBLIC.addSetterFilter(set),PUBLIC.get=get}),PUBLIC.list=function(cb){return cb&&"function"==typeof cb&&(persistent?pkg.require(["GlobalStorage"],function(GlobalStorage){(globalStorage=globalStorage||new GlobalStorage).getAll(function(list){var newList=pkg.utils.clone(PRIVATE.data),k,obj;for(k in list)if(list.hasOwnProperty(k)){try{obj=JSON.parse(list[k]||null)}catch(e){}newList[k.substr(PRIVATE.contextPrefix.length)]=obj?obj.v:void 0}cb(newList)})}):cb(pkg.utils.clone(PRIVATE.data))),pkg.utils.clone(PRIVATE.data)},PUBLIC}function ContextFactory(contextName,desc,options){return this.create=function(contextName,desc,options){return"string"!=typeof contextName?pkg.console.error("context:Invalid context name "+contextName):(this[contextName]=new Context(contextName,desc,options),setTimeout((name="context."+contextName,comp=this[contextName],function(){pkg.introduce(name,comp,{observable:!1})}),1),this[contextName]);var name,comp},contextName?this.create(contextName,desc,options):this}var globalStorage=null;__privileged.context=new ContextFactory,pkg.context=__privileged.context,zaz.use(function(pkg){var tst=pkg.context.create("test","This context is used to identify different tests running on the page."),wl,testOptions=new pkg.utils.WebLocation(location.href).queryParams("zaz-test"),active=testOptions.active||!1,chance=100,share=100;!active&&pkg.options.global&&pkg.options.global.test&&(testOptions=pkg.options.global.test,active=pkg.options.global.test.active),(active=(share=testOptions["audience-share"]||testOptions.audienceShare||100)<(chance=Math.ceil(100*Math.random()))?!1:active)?(testOptions.active=active,delete testOptions["audience-share"],tst.set(testOptions)):pkg.options.global&&pkg.options.global.autoTests?tst.set("active","qunit"):tst.set("active",!1)}),zaz.use(function(pkg){function getJSWidth(){var w;return document.documentElement&&document.documentElement.clientWidth?document.documentElement.clientWidth:document.body.clientWidth||document.width}function setPageState(){var pageState,pageState=4*(pageState=pkg.console.report()).errorsLength+pageState.warningsLength;pg.set("stability",pkg.getRangeFromValue(pageState,{sunny:[0,0],clean:[1,3],cloudy:[4,5],nebulous:[6,10],raining:[11,13],storming:[14]}))}for(var pg=pkg.context.create("page","Information about the page, are related to data retrieved from the current page, like the language set in the HTML, information related to the URL.",{recursive:!1}),html=document.getElementsByTagName("html")[0],locale=html.getAttribute("lang")||html.getAttribute("xml:lang")||"pt-BR",rx=/^([a-zA-Z]{2})(?:[\-_]([a-zA-Z]{2}))?$/,lang,country=((lang=rx.exec(locale))[2]||"BR").toUpperCase(),to=null,streamings=[],acronyms={AR:"ARG",BR:"BRA",CA:"CAN",CL:"CHL",CO:"COL",CR:"CRI",DO:"DOM",EC:"ECU",ES:"ESP",GB:"GBR",GT:"GTM",HN:"HND",MX:"MEX",NI:"NIC",PA:"PAN",PE:"PER",PR:"PRI",PT:"PRT",SV:"SLV",VE:"VEN",US:"USA",ZA:"ZAF"},defaultCountry="BR",defaultDomains,lang=lang[1],defaultDomains={AR:"terra.com.ar",BR:"terra.com.br",CA:"terra.com",CL:"terra.cl",CO:"terra.com.co",CR:"terra.com",DO:"terra.com",EC:"terra.com.ec",ES:"terra.es",GB:"terra.com",GT:"terra.com",HN:"terra.com",MX:"terra.com.mx",NI:"terra.com",PA:"terra.com",PE:"terra.com.pe",PR:"terra.com",PT:"terra.com.br",SV:"terra.com",VE:"terra.com.ve",US:"terra.com"},ctxData=window.contextData,objs=((ctxData=!ctxData&&window.trr?window.trr.contextData:ctxData)&&"object"==typeof ctxData&&(pkg.console.info("[Cerebro] Updating page's country from previously defined legacy contextData object. Country =",ctxData.country),ctxData.country&&(pg.set("country",ctxData.country),country=ctxData.country.toUpperCase())),window.contextData||(window.contextData={}),window.trr&&!window.trr.contextData?window.trr.contextData={}:window.trr||(window.trr={contextData:{}}),[window.contextData,window.trr.contextData]),i=0;i<objs.length;i++)Object.defineProperty(objs[i],"country",{get:function(){return pg.get("country")},set:function(val){pg.set("country",val)}});pg.get("lang")||pg.set("lang",lang),pg.get("domainRef")||pg.set("domainRef",defaultDomains[country]||defaultDomains.BR),pg.get("country")||pg.set("country",country.toLowerCase()),pg.get("acronym")||pg.set("acronym",acronyms[pg.get("country").toUpperCase()]),pg.get("locale")||((locale=locale.split(/-|_/))[0]=locale[0]||"pt",locale[1]=locale[1]||"BR",locale=locale[0].toLowerCase()+"-"+locale[1].toUpperCase(),pg.set("locale",locale)),pg.get("host")||pg.set("host",pkg.options.host||location.host),pg.get("hash")||pg.set("hash",{previous:"",current:location.hash}),pg.get("protocol")||pg.set("protocol",location.protocol.replace(":","")),pg.get("port")||pg.set("port",location.port||80),pg.get("query")||pg.set("query",location.search||""),pg.get("queryString")||pg.set("queryString",location.search||""),pg.get("path")||pg.set("path",location.pathname||""),pg.get("root")||pg.set("root",location.protocol+"//"+location.host+":"+(location.port||80)+"/"),pg.get("inFrame")||pg.set("inFrame",pkg.inFrame),pg.get("topFrameAccess")||pg.set("topFrameAccess",pkg.topFrameAccess);var pageReady=function(event){pg.get("ready")||pg.set("ready",!0)},ctxData=(document.addEventListener("DOMContentLoaded",pageReady),function(event){pg.get("ready")||pageReady(event),pg.set("load",!0)}),verifyForLegacyPageLoad=function(){/in/.test(document.readyState)?to=setTimeout(verifyForLegacyPageLoad,100):pageReady()},lang=function(evt){var p=evt.oldURL||"",evt=evt.newURL||"",p;p=(p=p.match(/(\#.+)$/))?p[1]:"",evt=(evt=evt.match(/(\#.+)$/))?evt[1]:"",pg.set("hash",{previous:p,current:evt})},defaultDomains=(pg.addSetterFilter("hash",function(newHash,oldHash){if(oldHash&&oldHash.current!=newHash.current)return"#"!=(newHash={previous:oldHash.current,current:newHash.current}).current[0]&&(newHash.current="#"+newHash.current),newHash;throw new Error("Setting the same hash")}),pg.on("hash",function(hash){hash&&location.hash!=hash.current&&(location.hash=hash.current)}),(/in/.test(document.readyState)?verifyForLegacyPageLoad:pageReady)(),window.addEventListener("load",ctxData),window.addEventListener("hashchange",lang),pg.get("host").match(/.+((\.com(\.[a-z]{0,2})?)|\.(cl|es))$/i)),country="",acronyms="",locale,locale,ctxData,lang,defaultDomains,defaultDomains,defaultDomains,acronyms=((defaultDomains=defaultDomains||pg.get("host").match(/.+((\.(com|org|me|net|tv)(\.[a-z]{0,2})?)|\.(co|es|mx))/i))&&(defaultDomains[1]?(pg.set("domainType",defaultDomains[1]),"terra"==(acronyms=(country=(country=defaultDomains[0].replace(defaultDomains[1],"")).split(".")).pop())?pg.set("underTerra",!0):pg.set("underTerra",!1),acronyms+=defaultDomains[1]||defaultDomains[0],pg.get("domain")||pg.set("domain",acronyms)):pg.get("domainType")||pg.set("domainType",!1),pg.get("subdomain")||pg.set("subdomain",country.length?country.join("."):""),pg.get("streaming")||pg.set("streaming",!1,!1),pg.addStreaming=function(what){var i;return(streamings&&streamings.indexOf(what))<0&&streamings.push(what),pg.set("streaming",streamings),this},pg.removeStreaming=function(what){var what=streamings&&streamings.indexOf(what);return 0<=what&&streamings.splice(what,1),0===streamings.length&&(streamings=!1),pg.set("streaming",streamings),this},pg.isStreaming=function(){return!!streamings.length},locale=(locale=!pg.get("underTerra"))&&pg.get("domain").replace(/\..*/g,""),pg.get("thirdParty")||pg.set("thirdParty",locale)),pg.get("langOrDomain")||(ctxData=html.getAttribute("lang")||html.getAttribute("xml:lang"),defaultDomains=(defaultDomains=(lang=rx.exec(ctxData))&&2<lang.length?lang[2].toLowerCase():"")?-1!==["cl","es"].indexOf(defaultDomains)?"."+defaultDomains:"us"===defaultDomains?".com":".com."+defaultDomains:(defaultDomains=pg.get("domainType"))||".com.br",pg.set("langOrDomain",defaultDomains)),new pkg.utils.WebLocation(location.href)),pg,country=(pg=pkg.utils.merge(pg,acronyms)).getAllParams(),range,rangeLimit,prepareRange,setScrollRange;pg.set("query",country,!0),pg.on("range",function(range){var currentRange;pkg.ui.html.getAttribute("data-range")!==range&&pkg.ui.html.setAttribute("data-range",range)}),window.addEventListener("resize",function(){var range=getJSWidth(),range=pkg.getRangeFromValue(range);pg.get("range")!=range&&pg.set("range",range)}),pg.set("range",pkg.getRangeFromValue(getJSWidth())),(country&&country.internalTest||0===location.host.indexOf("localhost/")||0===location.host.indexOf("localhost:")||0<=location.search.indexOf("zaz[env]=")||location.host.match(/(dsv|hlg)-fe([0-9]{1,2})?(.+)?\.terra\./))&&pg.set("devMode",!0),pg.get("inFrame")&&pkg.ui.html.setAttribute("data-terra-in-frame","true"),pg.get("thirdParty")&&pkg.ui.html.setAttribute("data-terra-in-third-party",pg.get("thirdParty")),null==document.hasFocus||document.hasFocus()?pg.set("focused",!0):pg.set("focused",!1),window.addEventListener("focus",function(evt){pg.set("focused",!0)}),window.addEventListener("blur",function(evt){pg.set("focused",!1)}),pkg.console.on("error",setPageState),pkg.console.on("warn",setPageState),range={},rangeLimit=30,pg.onceAt("range",function(){range={};for(var tableSize=(window.innerHeight||document.documentElement.clientHeight)-40,i=31,lastDigit=0,sufix="";--i;)lastDigit=i.toString().slice(-1),range[i+(sufix="1"==lastDigit&&(20<i||i<10)?"st":"2"==lastDigit&&(20<i||i<10)?"nd":"3"==lastDigit&&(20<i||i<10)?"rd":"th")]=1==i?[1,tableSize]:[tableSize*(i-1)+1,tableSize*i];return range.top=[0,0],range}),pg.at("scrollRange",function(range){pkg.ui.html.setAttribute("data-scroll-range",range||"top")}),setScrollRange=function(){var top=document.body?document.documentElement&&document.documentElement.scrollTop||document.body.scrollTop:0,s,top=pkg.getRangeFromValue(top&&40<top?top+40:0,range);top!=pg.get("scrollRange")&&pg.set("scrollRange",top)},pg.addSetterFilter("scrollRange",function(newScroll,oldScroll){return isNaN(newScroll)?newScroll:pkg.getRangeFromValue(newScroll,range)}),setScrollRange(),setTimeout(function(){window.addEventListener("scroll",setScrollRange,!1,{controller:"none"})},0)}),zaz.use(function(pkg){var locale,localeCodes=(navigator.language||navigator.userLanguage||"").match(/\w\w/g),userAttrs={lang:"pt",country:"BR",locale:"pt-BR"},user=(localeCodes&&localeCodes[1]&&(localeCodes[1]=localeCodes[1].toUpperCase()),pkg.context.create("user","Information related to user's definitions. For example, user language means his BROWSER is running with that language.",{persistent:!0,volatileAttrs:pkg.utils.merge(userAttrs,{lang:localeCodes?localeCodes[0]:void 0,country:localeCodes?localeCodes[1]:void 0,locale:localeCodes?localeCodes.join("-"):void 0})}))}),zaz.use(function(pkg){var browser=pkg.context.create("browser","These data will give you information related to the browser in which the page is currently running."),getBrowserData=function(what){var browsersToTest={ff:{name:"Firefox",rx:/(Firefox)\/([0-9]{1,}[\.0-9]{0,})/,vendor:"Mozilla",engine:/(Gecko)\//,cssVendor:"-moz-"},edge:{name:"Edge",rx:/(Edge).+?([0-9]{1,}[\.0-9]{0,})/,vendor:"Microsoft",engine:/(Gecko)/i,cssVendor:"-webkit-"},ie:{name:"Internet Explorer",rx:/(MSIE|IEMobile|rv).+?([0-9]{1,}[\.0-9]{0,})/,vendor:"Microsoft",engine:/(Chakra)|(Trident)/i,cssVendor:"-ms-"},op:{name:"Opera",rx:/(OPR)\/([0-9]{1,}[\.0-9]{0,})/,vendor:"Opera",engine:/(Presto)|(Futhark)|(Carakan)|(Webkit)|(blink)/i,cssVendor:"-webkit-"},ch:{name:"Chrome",rx:/(Chrome)\/([0-9]{1,}[\.0-9]{0,})/,vendor:"Google",engine:/(Webkit)|(blink)\//i,cssVendor:"-webkit-"},sf:{name:"Safari",rx:/(Safari)\/([0-9]{1,}[\.0-9]{0,})/,vendor:"Apple",engine:/(Webkit)|(blink)/i,cssVendor:"-webkit-"}},ret={},i=null,ua=navigator.userAgent,matches;for(i in browsersToTest)if(browsersToTest.hasOwnProperty(i)&&(matches=ua.match(browsersToTest[i].rx))){ret.acronym=i,ret.name=browsersToTest[i].name,ret.version=matches[2],/\./.test(ret.version)||(ret.version+=".0"),ret.vendor=browsersToTest[i].vendor||"other",ret.engine=ua.match(browsersToTest[i].engine),ret.engine=ret.engine?ret.engine[1]:"unknown",ret.cssVendor=browsersToTest[i].cssVendor;break}return ret.name||(ret.acronym=!1,ret.name="other",ret.version=0,ret.vendor="other",ret.engine="unknown"),ret},touchEnabled=bool=!!("ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch),bool,getBrowserData=(pkg.watch("body",function(){var prefixes,styles,css,styles,css;browser.get("touchEnabled")||(styles=["@media ("," -webkit- -moz- -o- -ms- ".split(" ").join("touch-enabled),("),"zaz-touch-identifier",")","{#zaz-touch-identifier{top:9px;position:absolute}}"].join(""),(css=document.createElement("style")).type="text/css",css.styleSheet?css.styleSheet.cssText=styles:css.appendChild(document.createTextNode(styles)),pkg.ui.head.appendChild(css),(styles=document.createElement("div")).id="zaz-touch-identifier",document.body.appendChild(styles),css=9===styles.offsetTop,document.body.removeChild(styles),css&&browser.set("touchEnabled",!0))}),getBrowserData());browser.set({name:getBrowserData.name,acronym:getBrowserData.acronym,legacy:"ie"==getBrowserData.acronym&&getBrowserData.version<=8,version:getBrowserData.version,engine:getBrowserData.engine,vendor:getBrowserData.vendor,cssVendor:getBrowserData.cssVendor,touchEnabled:touchEnabled,supports:function(what,where){if(where){if(what in where)return!0}else if(window[what]||navigator[what]||this[what])return!0;return!1}}),browser.get("legacy")&&pkg.ui.html.setAttribute("data-legacy",getBrowserData.acronym+getBrowserData.version)}),zaz.use(function(pkg){var platform=pkg.context.create("platform","These data will give you information related to the platform in which the page is currently running."),getBrowserData,browserData=function(what){var ret={},ua=navigator.userAgent,matches,tmp,tmp=pkg.ui.html.getAttribute("data-device-type");if(ret.type=tmp||(!/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua)?"desktop":"mobile"),ret.os=navigator.platform,/Win(?:dows )?([^do]{2})\s?(\d+\.\d+)?/.test(ua))if(ret.osName="Windows","NT"==RegExp.$1)switch(RegExp.$2){case"5.0":ret.osDetail="2000";break;case"5.1":ret.osDetail="XP";break;case"6.0":ret.osDetail="Vista";break;default:ret.osDetail="NT"}else"9x"==RegExp.$1?ret.osDetail="ME":ret.osDetail=RegExp.$1;else ret.os.match(/linux/i)?(ret.osDetail="Linux",ret.osName="Linux"):ret.os.match(/unix/i)?(ret.osDetail="Uinux",ret.osName="Unix"):ret.os.match(/mac/i)?(ret.osDetail="Mac",ret.osName="Mac"):(ret.osDetail="--",ret.osName="--");return ret.deviceType=-1<ua.indexOf("iPhone")?"iPhone":-1<ua.indexOf("iPod")?"iPod":-1<ua.indexOf("NokiaN")?"NokiaN":-1<ua.indexOf("Xbox")?"Xbox":-1<ua.indexOf("Wii")?"Wii":/playstation/i.test(ua)?"Playstation":/TV[\-\;\/]/.test(ua)?"TV":/android/i.test(ua)?"Android":/windows phone/i.test(ua)?"WindowsPhone":"Computer",ret.resolution=[screen.width,screen.height],ret.range=pkg.getRangeFromValue(ret.resolution[0]),ret}(),isIOS=/iPad|iPod|iPhone/i.test(navigator.userAgent),isApp=navigator.userAgent.match("TerraLauncher"),o={os:browserData.os,mode:browserData.type,type:window.contextData&&window.contextData.device?window.contextData.device:window.trr&&window.trr.contextData&&window.trr.contextData.device?window.trr.contextData.device:"desktop"==browserData.type?"web":"mob",resolution:browserData.resolution,device:browserData.deviceType,edition:browserData.osDetail,range:browserData.range,osName:browserData.osName,isIOSApp:!(!isIOS||!isApp)};Object.keys(o).forEach(function(cur){platform.get(cur)&&delete o[cur]}),platform.set(o),platform.at("type",function(val){pkg.ui.html.setAttribute("data-device-type",val)}),platform.at("device",function(val){pkg.ui.html.setAttribute("data-device",val)})}),zaz.use(function(pkg){var pub=pkg.context.create("publisher","Information about the publisher for the current page");pkg.context.page.on("ready",function(){pub.get("id")||pub.set("id",document.body.getAttribute("data-publisher-id")||"static"),pub.get("version")||pub.set("version",document.body.getAttribute("data-publisher-version")||"1.0.0"),pub.get("channelID")||pub.set("channelID",document.body.getAttribute("data-publisher-channel")||""),pub.get("template")||pub.set("template",document.body.getAttribute("data-publisher-template")||"home"),pub.get("env")||pub.set("env",document.body.getAttribute("data-publisher-env")||"prd"),pub.get("detail")||pub.set("detail",document.body.getAttribute("data-publisher-detail")||"")})}),zaz.use(function(pkg){var ui=pkg.context.create("ui","Context related to User Interation and also User Interface.",{recursive:!1}),validEvents=["content.add","content.remove","content.change","user.move","user.share","user.comment"],oTrigger=ui.trigger;pkg.schemas.create("uiEventData",{properties:{target:{type:"mixed",required:!0,description:"The element or id of the element to be identified"},type:{type:"string",required:!0,description:"The event type"},data:{type:"object",required:!0,description:"An object with extra information about the event"}}}),ui.trigger=function(event,data){var valid;if("*"!=event){if(validEvents.indexOf(event)<0)return console.warn("context.ui only accepts events of type: "+validEvents.toString()),!1;!0===(valid=pkg.schemas.validate("uiEventData",data))?oTrigger.call(this,event,data):console.error(valid)}}})}),console.check(!0,"Loaded extension context.js"),zaz.use(function IncluderSetup(pkg,__privileged){"use strict";var console=pkg.console,debugging=window.QUnit||/[&\?]tpn=1/.test(location.search)&&/[&\?]cerebro\.env=(dsv|hlg|prev)/.test(location.search),html=document.getElementsByTagName("html")[0],html=html.getAttribute("lang")||html.getAttribute("xml:lang")||"pt-BR",targetProtocol=location.protocol.match(/https?\:/)||"http:",defaultCountries={en:"US",pt:"BR",es:"MX"},rx,scope={},lang,defaultCountries,includer,instance,lang=/^([a-zA-Z]{2})(?:\-([a-zA-Z]{2}))?$/.exec(html);lang&&defaultCountries[lang[1]]?lang.shift():lang=["pt","BR"],defaultCountries=lang[1]||defaultCountries[lang[0]]||"BR",html=(lang=lang[0])+"-"+defaultCountries;try{!function(){var CURRENT_SCOPE;!function(CURRENT_SCOPE){function Package(){var CURRENT_SCOPE,STATIC,CURRENT_SCOPE,support,methods,CURRENT_SCOPE=((CURRENT_SCOPE=this).Log=(STATIC={instances:{},entries:[],handlers:[],EXP_SNIFF_FROM_USER_AGENT:/msie/i,dumpingOnBrowsersConsole:!1,verbosityLevels:[{info:!1,log:!1,warn:!1,error:!1,debug:!1},{info:!1,log:!1,warn:!1,error:!0,debug:!1},{info:!1,log:!1,warn:!0,error:!0,debug:!1},{info:!0,log:!0,warn:!0,error:!0,debug:!0}],LogEntry:function LogEntry(instance,entry,details){switch(this.id=instance.id,this.original=entry,this.recordDate=new Date,this.instanceStartupDate=instance.startup,this.name="object"==typeof entry&&entry.name?entry.name:"log",this.details=details||null,entry instanceof Error?(this.name="error",this.message=entry.message):this.message="object"==typeof entry?entry.message||window.JSON.stringify(entry):entry,this.friendlyDescription="["+this.id+("log"===this.name?"":" : "+this.name)+"] → "+this.message,entry.name){case"warning":this.name=window.console.hasOwnProperty("warn")?"warn":"log";break;case"critical":this.name=window.console.hasOwnProperty("error")?"error":"log";break;case"debug":this.name=window.console.hasOwnProperty("debug")?"debug":"log"}},browsersConsoleHandler:function(message){var entry=this,entryTokens=[],containerElement,writingMethod,entryElement;if(window.console&&window.console.log){try{writingMethod=window.console[this.name]?this.name:"log"}catch(exception){writingMethod="log"}if(this.details&&(this.friendlyDescription+=" → [Details] →→→",STATIC.EXP_SNIFF_FROM_USER_AGENT.test(navigator.userAgent)&&window.JSON))try{this.details=window.JSON.stringify(this.details,null,!0)}catch(exp){this.details=null}entryTokens.push(this.message instanceof Error?this.message:this.friendlyDescription),this.details&&entryTokens.push(this.details);try{window.console[writingMethod].apply(window.console,entryTokens),this.message instanceof Error&&this.message.stack&&window.console.log("stacktrace >>> \n"+this.message.stack)}catch(e){window.console.log(this.friendlyDescription),this.details&&window.console.log(">>> "+this.details)}}}},(CURRENT_SCOPE=function Log(id){if("string"!=typeof id)throw"Invalid log id. You must pass a string to this constructor";if(STATIC.instances[id])return STATIC.instances[id];var PRIVATE={},PUBLIC=this;PRIVATE.entries=[],PRIVATE.handlers=[],PRIVATE.verbosity=STATIC.verbosityLevels[2],PRIVATE.print=function(entry,handlers){var i;for(handlers=handlers instanceof Array?handlers:[handlers],i=0;i<handlers.length;i++)try{handlers[i].call(entry,entry.friendlyDescription)}catch(error){debugger;error.message="[Exception caught while handling a log entry] → "+error.message+" \nentry →"+window.JSON.stringify(entry)}},PUBLIC.id=id,PUBLIC.startup=new Date,PUBLIC.setVerbosityLevel=function(value){if("number"==typeof(value="boolean"==typeof value?value?3:0:value)&&STATIC.verbosityLevels[value])return PRIVATE.verbosity=STATIC.verbosityLevels[value],PUBLIC;throw new Error('Invalid verbosity level: "'+value+'". It must be number between 0 and '+(STATIC.verbosityLevels.length-1)+" or a boolean.")},PUBLIC.write=function(entry,details){entry=new STATIC.LogEntry(PUBLIC,entry,details),PRIVATE.verbosity[entry.name]&&(PRIVATE.entries.push(entry),PRIVATE.handlers.length&&PRIVATE.print(entry,PRIVATE.handlers))},PUBLIC.write.log=function(){PUBLIC.write.apply(this,Array.prototype.slice.call(arguments))},PUBLIC.write.warn=function(message){PUBLIC.write({name:"warn",message:message})},PUBLIC.write.error=function(message){PUBLIC.write(message instanceof Error?message:{name:"error",message:message})},PUBLIC.setHandler=function(customHandler){if("function"!=typeof customHandler&&"BROWSERS_CONSOLE"!==customHandler)throw'Invalid log handler. You must pass a function as the only parameter. You can use de string "BROWSERS_CONSOLE" as well...';var i,entry;if("BROWSERS_CONSOLE"===customHandler){if(STATIC.dumpingOnBrowsersConsole)return;customHandler=STATIC.browsersConsoleHandler,PRIVATE.handlers.push(customHandler),STATIC.dumpingOnBrowsersConsole=!0}else PRIVATE.handlers.push(customHandler);for(i=0;i<PRIVATE.entries.length;i++)entry=PRIVATE.entries[i],PRIVATE.print(entry,customHandler);return PUBLIC},PUBLIC.reset=function(){PRIVATE.entries.length=0,PRIVATE.handlers.length=0}}).reset=function(){var id,instance;for(id in STATIC.instances)STATIC.instances.hasOwnProperty(id)&&(instance=STATIC.instances[id]).reset();STATIC.instances={}},CURRENT_SCOPE),this);CURRENT_SCOPE.ApplicationError=function ApplicationError(reason,dismissableCalls){var instance=this,reason,caller;if(!reason||!("string"==typeof reason||reason instanceof Error))throw new ApplicationError("Invalid value for reason ("+reason+"). It must be a string or an instance of Error.");if(void 0!==dismissableCalls&&("number"!=typeof dismissableCalls||dismissableCalls<0))throw new ApplicationError("Invalid value for dismissableCalls ("+dismissableCalls+")");if(this.message=reason,void 0===dismissableCalls&&(dismissableCalls=reason instanceof Error?0:1),reason instanceof Error)this.message=reason.message,this.stack=reason.stack;else try{throw dismissableCalls++,new Error(this.message)}catch(e){this.stack=e.stack}if("string"==typeof this.stack){if(dismissableCalls>=(reason=this.stack.split(/\n/)).length-1)return this.stack="",this;0<dismissableCalls&&(dismissableCalls=Math.min(dismissableCalls,reason.length-1),reason.splice(1,dismissableCalls)),this.message+="\n"+reason[1],this.stack=reason.join("\n")}};try{CURRENT_SCOPE.ApplicationError.prototype=Object.create(Error.prototype)}catch(e){CURRENT_SCOPE.ApplicationError.prototype=new Error}function normalizeName(name){if("string"!=typeof name&&(name=name.toString()),/[^a-z0-9\-#$%&'*+.\^_`|~]/i.test(name))throw new TypeError("Invalid character in header field name");return name.toLowerCase()}function normalizeValue(value){return value="string"!=typeof value?value.toString():value}function Headers(headers){this.map={};var self=this;headers instanceof Headers?headers.forEach(function(name,values){values.forEach(function(value){self.append(name,value)})}):headers&&Object.getOwnPropertyNames(headers).forEach(function(name){self.append(name,headers[name])})}function consumed(body){if(body.bodyUsed)return Promise.reject(new TypeError("Already read"));body.bodyUsed=!0}function fileReaderReady(reader){return new Promise(function(resolve,reject){reader.onload=function(){resolve(reader.result)},reader.onerror=function(){reject(reader.error)}})}function readBlobAsArrayBuffer(blob){var reader=new FileReader;return reader.readAsArrayBuffer(blob),fileReaderReady(reader)}function readBlobAsText(blob){var reader=new FileReader;return reader.readAsText(blob),fileReaderReady(reader)}function Body(){return this.bodyUsed=!1,support.blob?(this._initBody=function(body){if("string"==typeof(this._bodyInit=body))this._bodyText=body;else if(support.blob&&Blob.prototype.isPrototypeOf(body))this._bodyBlob=body;else if(support.formData&&FormData.prototype.isPrototypeOf(body))this._bodyFormData=body;else{if(body)throw new Error("unsupported BodyInit type");this._bodyText=""}},this.blob=function(){var rejected=consumed(this);if(rejected)return rejected;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){return this.blob().then(readBlobAsArrayBuffer)},this.text=function(){var rejected=consumed(this);if(rejected)return rejected;if(this._bodyBlob)return readBlobAsText(this._bodyBlob);if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)}):(this._initBody=function(body){if("string"==typeof(this._bodyInit=body))this._bodyText=body;else if(support.formData&&FormData.prototype.isPrototypeOf(body))this._bodyFormData=body;else{if(body)throw new Error("unsupported BodyInit type");this._bodyText=""}},this.text=function(){var rejected=consumed(this);return rejected||Promise.resolve(this._bodyText)}),support.formData&&(this.formData=function(){return this.text().then(decode)}),this.json=function(){return this.text().then(JSON.parse)},this}function normalizeMethod(method){var upcased=method.toUpperCase();return-1<methods.indexOf(upcased)?upcased:method}function Request(url,options){if(options=options||{},this.url=url,this.credentials=options.credentials||"omit",this.headers=new Headers(options.headers),this.method=normalizeMethod(options.method||"GET"),this.mode=options.mode||null,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&options.body)throw new TypeError("Body not allowed for GET or HEAD requests");this._initBody(options.body)}function decode(body){var form=new FormData;return body.trim().split("&").forEach(function(bytes){var bytes,name,bytes;bytes&&(name=(bytes=bytes.split("=")).shift().replace(/\+/g," "),bytes=bytes.join("=").replace(/\+/g," "),form.append(decodeURIComponent(name),decodeURIComponent(bytes)))}),form}function headers(xhr){var head=new Headers,pairs;return xhr.getAllResponseHeaders().trim().split("\n").forEach(function(header){var header=header.trim().split(":"),key=header.shift().trim(),header=header.join(":").trim();head.append(key,header)}),head}function Response(bodyInit,options){options=options||{},this._initBody(bodyInit),this.type="default",this.url=null,this.status=options.status,this.ok=200<=this.status&&this.status<300,this.statusText=options.statusText,this.headers=options.headers,this.url=options.url||""}CURRENT_SCOPE.ApplicationError.prototype.constructor=CURRENT_SCOPE.ApplicationError,CURRENT_SCOPE.ApplicationError.prototype.name="ApplicationError",CURRENT_SCOPE.ApplicationError.prototype.toString=function(){var output=this.name+": "+this.message;return this.stack&&(output+=" \n\nstack >> "+this.stack),output},window.ApplicationError=CURRENT_SCOPE.ApplicationError,CURRENT_SCOPE.ApplicationError=function ApplicationError(m){return new Error(m)},"function"!=typeof Object.getOwnPropertyNames&&(Object.getOwnPropertyNames=function getOwnPropertyNames(object){if(object!==Object(object))throw new TypeError("Object.getOwnPropertyNames called on non-object");var buffer=[],key;for(key in object)buffer.push(key);return buffer}),function(CURRENT_SCOPE){function Clone(){}function extractStacktrace(e,offset,fileName){var stack,include,i;if(offset=void 0===offset?4:offset+4,e.stacktrace)return e.stacktrace.split("\n")[offset+3];if(e.stack){if(stack=e.stack.split("\n"),/^error$/i.test(stack[0])&&stack.shift(),fileName){for(include=[],i=offset;i<stack.length&&-1===stack[i].indexOf(fileName);i++)include.push(stack[i]);if(include.length)return include.join("\n")}return stack[offset]}return!e.sourceURL||/zaz([\-\.]min)?\.js$/.test(e.sourceURL)?null:e.sourceURL+":"+e.line}function ready(){if(!readyFired){readyFired=!0;for(var i=0;i<readyList.length;i++)try{readyList[i].fn.call(window,readyList[i].ctx)}catch(e){window.console&&"object"==typeof window.console&&("function"==typeof window.console.error?window.console.error("Exception triggering DOMContentLoaded callback: "+e.message):"function"==typeof window.console.log&&window.console.log("[ERROR] Exception triggering DOMContentLoaded callback: "+e.message))}readyList=[]}}function readyStateChange(){"complete"===document.readyState&&ready()}var funcName,baseObj,readyList,readyFired,readyEventHandlersInstalled;CURRENT_SCOPE.util||(CURRENT_SCOPE.util={}),CURRENT_SCOPE.util.extend=function(){if(arguments.length<=1)throw new Error('The "extend" function must receive at least two objects as parameters.');for(var objects=Array.prototype.slice.call(arguments),returnedObjectType=objects[0]instanceof Array?"array":"object",compiledObject="array"==returnedObjectType?[]:{},targetObject,current,attributeId,currentValueType,compiledValueType;objects.length;){if("object"!=typeof(current=objects.shift())||"array"==returnedObjectType&&!(current instanceof Array)||"object"==returnedObjectType&&current instanceof Array)throw new Error('You can\'t combine different types of objects together. They must be all "objects" or "arrays".');for(attributeId in current)current.constructor.prototype.hasOwnProperty(attributeId)||void 0===current[attributeId]||(void 0===compiledObject[attributeId]?compiledObject[attributeId]=current[attributeId]:(currentValueType=Object.prototype.toString.call(current[attributeId]),(compiledValueType=Object.prototype.toString.call(compiledObject[attributeId])).match(/Object|Array/)&&compiledValueType==currentValueType?CURRENT_SCOPE.util.extend(compiledObject[attributeId],current[attributeId]):compiledObject[attributeId]=current[attributeId]));targetObject||objects.length||(objects.push(compiledObject),targetObject=compiledObject=arguments[0])}return targetObject},CURRENT_SCOPE.util.deepExtend=function(){if(arguments.length<1||"object"!=typeof arguments[0])return!1;if(arguments.length<2)return arguments[0];var target=arguments[0],args,key,val,src,tmpBuf,tmpBuf;return Array.prototype.slice.call(arguments,1).forEach(function(obj){if("object"==typeof obj)for(key in obj)key in obj&&(src=target[key],(val=obj[key])!==target&&("object"!=typeof val||null===val?target[key]=val:window.Buffer&&val instanceof window.Buffer?(tmpBuf=new window.Buffer(val.length),val.copy(tmpBuf),target[key]=tmpBuf):val instanceof Date?target[key]=new Date(val.getTime()):val instanceof RegExp?target[key]=new RegExp(val):(tmpBuf="object"!=typeof src||null===src?Array.isArray(val)?[]:{}:Array.isArray(val)?Array.isArray(src)?src:[]:Array.isArray(src)?{}:src,target[key]=CURRENT_SCOPE.util.deepExtend(tmpBuf,val))))}),target},CURRENT_SCOPE.util.deepExtend=CURRENT_SCOPE.util.extend,CURRENT_SCOPE.util.copy=function(value){if("object"!=typeof value||null===value)return value;var baseObject="[object Array]"==Object.prototype.toString.call(value)?[]:{};return CURRENT_SCOPE.util.extend(baseObject,value),baseObject},CURRENT_SCOPE.util.clone=function(value){return/\[object (?!Array)/.test(Object.prototype.toString.call(value))?(Clone.prototype=value,new Clone):CURRENT_SCOPE.util.copy(value)},CURRENT_SCOPE.util.parseKey=function(key,value,targetScope){if(!key||"string"!=typeof key)throw new Error("Invalid key to be parsed.");if(targetScope&&"object"!=typeof targetScope)throw new Error("Invalid targetScope. It must be an object.");var levels=key.split("."),key=levels[0],output={},currentKey,currentLevel;if(1==levels.length)return targetScope&&(targetScope[key]=value),value;for(;levels.length;)currentKey=levels.shift(),currentLevel=currentLevel||(output={}),levels.length?(currentLevel[currentKey]||(currentLevel[currentKey]={}),currentLevel=currentLevel[currentKey]):currentLevel[currentKey]=value;return targetScope&&CURRENT_SCOPE.util.deepExtend(targetScope,output),output},CURRENT_SCOPE.util.getNamespace=function(declaration,reference){if("string"!=typeof declaration||!/^[A-Za-z_]/.test(declaration))throw new Error("Invalid namespace declaration: "+declaration);if(void 0!==reference&&(!reference||"object"!=typeof reference))throw new Error("Invalid reference object to extract namespace: "+reference+"("+Object.prototype.toString.call(reference)+").");reference=reference||window;var levels=(declaration=declaration.replace(/\[['"](?=[^\]]+)/g,".").replace(/['"]\]/g,"")).split("."),lastLevel=reference,current;for("window"==levels[0]&&1<levels.length&&levels.shift();levels.length;){if(void 0===lastLevel[current=levels.shift()])return;lastLevel=lastLevel[current]}return lastLevel},CURRENT_SCOPE.util.sourceFromStacktrace=function sourceFromStacktrace(offset,exception){if(void 0!==offset&&"number"!=typeof offset)throw new Error("Invalid offset to extract source: "+offset);if(void 0!==exception&&!(exception instanceof Error))throw new Error("Invalid exception to extract source");if(!exception)try{throw new Error}catch(e){exception=e}return extractStacktrace(exception,offset)},CURRENT_SCOPE.util.getCallerLine2=function(){var exception;try{throw new Error("")}catch(err){exception=err}return exception.stack&&exception.stack.split?exception.stack.split("\n")[3]:""},CURRENT_SCOPE.util.getCallerLine=function(){return CURRENT_SCOPE.util.sourceFromStacktrace(-1)||""},funcName="docReady",baseObj=(baseObj=CURRENT_SCOPE.util)||window,readyEventHandlersInstalled=readyFired=!(readyList=[]),baseObj.docReady=function(callback,context){readyFired?setTimeout(function(){callback(context)},1):(readyList.push({fn:callback,ctx:context}),"complete"===document.readyState||!document.attachEvent&&"interactive"===document.readyState?setTimeout(ready,1):readyEventHandlersInstalled||(document.addEventListener?(document.addEventListener("DOMContentLoaded",ready,!1),window.addEventListener("load",ready,!1)):(document.attachEvent("onreadystatechange",readyStateChange),window.attachEvent("onload",ready)),readyEventHandlersInstalled=!0))},function(){function triggerCallbacks(){for(ready=!0,timer=timer&&clearInterval(timer);callbacks.length;)try{(current=callbacks.shift()).call(window)}catch(e){window.console&&"object"==typeof window.console&&("function"==typeof window.console.error?window.console.error("Exception triggering DOMContentLoaded callback: "+e.message):"function"==typeof window.console.log&&window.console.log("[ERROR] Exception triggering DOMContentLoaded callback: "+e.message))}}var callbacks=[],current,ready,timer;CURRENT_SCOPE.util.docReady=function(callback){callbacks.push(callback),(ready=!/in/.test(document.readyState))?setTimeout(triggerCallbacks,1):timer||(document.addEventListener?document.addEventListener("DOMContentLoaded",triggerCallbacks,!1):document.attachEvent&&document.attachEvent("onreadystatechange",function readyStateChange(){"complete"===document.readyState&&setTimeout(triggerCallbacks,1)}),timer=setInterval(function(){/in/.test(document.readyState)||(ready=!0,timer=timer&&clearInterval(timer),triggerCallbacks())},330))}}()}(this),function(CURRENT_SCOPE){if(CURRENT_SCOPE.WebLocation)throw new Error("WebLocation already loaded");function normalize(levels){return levels.replace(EXP_TRIM_QUERY,"").replace(EXP_QUERY_SEPARETORS,"|")}function getRemainningLevels(reference,target){var levels=null;return levels=-1<target.indexOf(reference)?target.replace(reference,"").match(EXP_LEVELS_KEYS):levels}function WebLocationParams(){}function WebLocationComponent(){}var CURRENT_SCOPE,WebLocation,STATIC_PRIVATE,chooseReference,EXP_PATH_ABSOLUTE,EXP_PATH_RELATIVE,EXP_PATH_RELATIVE_TO_DOMAIN,EXP_PATH_RELATIVE_TO_SCHEME,EXP_PATH_RELATIVE_TO_CURRENT_DIR,EXP_MATCH_PARENT_DIR,EXP_CURRENT_FILE_DECLARATION,EXP_LAST_DIRECTORY_DECLARATION,EXP_SINGLE_CHAR,EXP_VALID_ASSOCIATIVE_KEY,EXP_TRIM_QUERY,EXP_QUERY_SEPARETORS,EXP_LEVELS_KEYS;CURRENT_SCOPE.WebLocation=((STATIC_PRIVATE={EXP_IS_PATH_ABSOLUTE:/^(https?|file):/,EXP_MATCH_PARAM_TOKENS:/[\?&]([^=]+)(?:=([^&#]*))?/g,EXP_VALID_PARAM_TYPES:/string|number|boolean|null/i,EXP_MATCH_TOKENS:/^((((?:https?|file)\:)\/\/\/?((.+?)(?:\:(\d+?))?))(\/[^\?#]*?([^\/\?]+?(?:\.([^\/]*?))?)?)?(\?.*?)?)&?(#.*)?$/,HREF_TOKEN_NAMES:["href","uri","origin","protocol","host","hostname","port","pathname","filename","extension","search","hash"],keyFilters:{},hostAliases:{},defaultSettings:{href:"",origin:"",uri:"",protocol:"http:",host:"",hostname:"",port:"",pathname:"",filename:"",extension:"",search:"",hash:""},alphabeticalSorter:function(a,b){return a=a.toLowerCase(),(b=b.toLowerCase())<a?1:a<b?-1:0},merge:function(){if(arguments.length<=1)throw new Error('The "merge" function must receive at least two objects as parameters.');for(var targetObject,current,attributeId,currentValueType,compiledValueType,objects=Array.prototype.slice.call(arguments),returnedObjectType=objects[0]instanceof Array?"array":"object",compiledObject="array"==returnedObjectType?[]:{};objects.length;){if("object"!=typeof(current=objects.shift())||"array"==returnedObjectType&&!(current instanceof Array)||"object"==returnedObjectType&&current instanceof Array)throw new Error('You can\'t combine different types of objects together. They must be all "objects" or "arrays".');for(attributeId in current)current.constructor.prototype.hasOwnProperty(attributeId)||void 0===current[attributeId]||(void 0===compiledObject[attributeId]?compiledObject[attributeId]=current[attributeId]:(currentValueType=Object.prototype.toString.call(current[attributeId]),compiledValueType=Object.prototype.toString.call(compiledObject[attributeId]),compiledValueType.match(/Object|Array/)&&compiledValueType==currentValueType?STATIC_PRIVATE.merge(compiledObject[attributeId],current[attributeId]):compiledObject[attributeId]=current[attributeId]));targetObject||objects.length||(objects.push(compiledObject),targetObject=compiledObject=arguments[0])}return targetObject},cloneComponents:function(obj){return WebLocationComponent.prototype=obj,new WebLocationComponent},cloneParams:function(obj){return WebLocationParams.prototype=obj,new WebLocationParams},printValues:function(validKeys,values){for(var current,i,output=[],i=0;i<validKeys.length;i++)current=validKeys[i],output.push(values[current]);return output.join("")},printSearch:function(params){var currentName,currentValue,subValue,output=[];for(currentName in params)if(params.hasOwnProperty(currentName))for(currentValue=params[currentName],currentValue="[object Array]"==Object.prototype.toString.call(currentValue)?Array.prototype.slice.call(currentValue):[currentValue];currentValue.length;)subValue=currentValue.shift(),null===subValue&&(subValue=""),subValue=encodeURIComponent(subValue),output.push(currentName+"="+subValue);return(output=output.sort(STATIC_PRIVATE.alphabeticalSorter)).length?"?"+output.join("&"):""}}).keyFilters.pattern=function(query,params){var output,key,keyTokens,keyTokens,value;if(!(query instanceof RegExp))throw new Error("Invalid query. It must be a RegExp instance");for(key in output={},params)params.hasOwnProperty(key)&&(keyTokens=query.exec(key),keyTokens&&(keyTokens=keyTokens.pop(),value=params[key],output.hasOwnProperty(keyTokens)||(output[keyTokens]=value)));return output},STATIC_PRIVATE.keyFilters.associative=(EXP_VALID_ASSOCIATIVE_KEY=/^([\w-_]+)((\[[\w-_]+\])|\.[\w-_]+)*$/,EXP_TRIM_QUERY=/^(\.|\[)|(\]|\.)$/g,EXP_QUERY_SEPARETORS=/(\.|\]?\[)/g,EXP_LEVELS_KEYS=/[^|]+/g,function(query,params){var piece,key,value,levels,currentLevel,currentKey,output={};if(!EXP_VALID_ASSOCIATIVE_KEY.test(query))throw new Error("Invalid query. The string must be a associative key the matches this pattern:"+EXP_VALID_ASSOCIATIVE_KEY.source);for(key in query=normalize(query),params)if(params.hasOwnProperty(key)&&(value=params[key],key=normalize(key),levels=getRemainningLevels(query,key))){for(currentLevel=piece={};levels.length;)currentKey=levels.shift(),levels.length?(currentLevel[currentKey]={},currentLevel=currentLevel[currentKey]):currentLevel[currentKey]=value;STATIC_PRIVATE.merge(output,piece)}return output}),STATIC_PRIVATE.convertPathToAbsolute=(EXP_PATH_ABSOLUTE=/^(https?:\/\/|file:)/,EXP_PATH_RELATIVE=/^\.\.\//,EXP_PATH_RELATIVE_TO_DOMAIN=/^\/[^\/]/,EXP_PATH_RELATIVE_TO_SCHEME=/^\/\//,EXP_PATH_RELATIVE_TO_CURRENT_DIR=/^\.\//,EXP_MATCH_PARENT_DIR=/(\.\.\/)/g,EXP_CURRENT_FILE_DECLARATION=/[^\/]+$/,EXP_LAST_DIRECTORY_DECLARATION=/[^\/]+\/?$/,EXP_SINGLE_CHAR=/./,chooseReference=function(declaration,target){if("string"==typeof declaration)return declaration;if(1==declaration.length)return declaration[0];for(var charactere,index,tokens=target.match(EXP_SINGLE_CHAR),sum=0;tokens.length;)sum+=(charactere=tokens.shift()).charCodeAt(0);return declaration[index=sum%declaration.length]},function(target,reference){if("string"!=typeof target)throw new Error("The method .convertPathToAbsolute() must receive a String representing de include path as first parameter");if(void 0!==reference&&"string"!=typeof reference&&("[object Array]"!=Object.prototype.toString.call(reference)||!reference.length))throw new Error("The method .convertPathToAbsolute() must receive a string or an array of strings representing the reference url(s). If you ommit it, it will be set as the document's location.");if(EXP_PATH_ABSOLUTE.test(target))return target;target=target.replace(EXP_PATH_RELATIVE_TO_CURRENT_DIR,"");try{if(reference=chooseReference(reference||location.href,target),!EXP_PATH_ABSOLUTE.test(reference))throw new Error("Location references must be absolute. Given value: "+reference);reference=new WebLocation(reference)}catch(exception){throw exception.message='Invalid reference to convert path "'+target+'" > '+exception.message,exception}(reference=reference.getAllComponents()).href==reference.origin&&(reference.href+="/");var backDirectories,i,base=reference.href.replace(EXP_CURRENT_FILE_DECLARATION,""),fileRealPath=target.replace(base,"").replace(EXP_MATCH_PARENT_DIR,"");if(EXP_PATH_RELATIVE_TO_DOMAIN.test(target))return"file:"==reference.protocol?reference.protocol+/.+\//.exec(reference.href)[0]+target.replace(EXP_PATH_RELATIVE_TO_DOMAIN,""):reference.protocol+"//"+reference.host+target;if(EXP_PATH_RELATIVE_TO_SCHEME.test(target))return"file:"==reference.protocol?reference.protocol+"/"+target:reference.protocol+target;if(EXP_PATH_RELATIVE.test(target))for(backDirectories=target.match(EXP_MATCH_PARENT_DIR).length,i=0;i<backDirectories;i++)base=base.replace(EXP_LAST_DIRECTORY_DECLARATION,"");return base+fileRealPath}),STATIC_PRIVATE.extractTokens=function(uri){var currentName,values,currentValue,i,tokens={},values=STATIC_PRIVATE.EXP_MATCH_TOKENS.exec(uri);if(!values)throw new Error("Invalid uri. It must be a valid string.");for(i=0;i<values.length;i++)currentValue=values[i],tokens[currentName=STATIC_PRIVATE.HREF_TOKEN_NAMES[i]]=currentValue;return tokens},STATIC_PRIVATE.extractParams=function(search){var tokens,paramDeclaration,paramDeclaration,name,paramDeclaration,params={},foundAny=!1;if(STATIC_PRIVATE.EXP_MATCH_PARAM_TOKENS.compile(STATIC_PRIVATE.EXP_MATCH_PARAM_TOKENS.source,"g"),!(tokens=search.match(STATIC_PRIVATE.EXP_MATCH_PARAM_TOKENS))||!tokens.length)return null;for(STATIC_PRIVATE.EXP_MATCH_PARAM_TOKENS.compile(STATIC_PRIVATE.EXP_MATCH_PARAM_TOKENS.source,"");tokens.length;){if(foundAny=!0,paramDeclaration=tokens.shift(),(paramDeclaration=STATIC_PRIVATE.EXP_MATCH_PARAM_TOKENS.exec(paramDeclaration)).shift(),name=paramDeclaration.shift(),paramDeclaration=paramDeclaration.shift()||null)try{paramDeclaration=decodeURIComponent(paramDeclaration)}catch(e){paramDeclaration=unescape(paramDeclaration)}params[name]?params[name].push?params[name].push(paramDeclaration):params[name]=[params[name],paramDeclaration]:params[name]=paramDeclaration}return foundAny?params:null},STATIC_PRIVATE.getComponents=function(href,referenceHosts){var params,href=STATIC_PRIVATE.convertPathToAbsolute(href,referenceHosts),referenceHosts=STATIC_PRIVATE.extractTokens(href);return referenceHosts.search&&(params=STATIC_PRIVATE.extractParams(referenceHosts.search),referenceHosts.search=STATIC_PRIVATE.printSearch(params),referenceHosts.uri=referenceHosts.origin+referenceHosts.pathname+referenceHosts.search,referenceHosts.href=referenceHosts.uri+(referenceHosts.hash||"")),{values:STATIC_PRIVATE.merge({},STATIC_PRIVATE.defaultSettings,referenceHosts),params:params||null}},STATIC_PRIVATE.getURIID=function(uri){var hostId,match;for(hostId in STATIC_PRIVATE.hostAliases)STATIC_PRIVATE.hostAliases.hasOwnProperty(hostId)&&(match=STATIC_PRIVATE.hostAliases[hostId],uri=uri.replace(match,"${"+hostId+"}"));return uri},(CURRENT_SCOPE=WebLocation=function WebLocation(href,referenceHosts){var PRIVATE={},PUBLIC=this;if(!(this instanceof WebLocation))throw new Error('WebLocation is a constructor. You must use the operator "new".');if(!href||"string"!=typeof href)throw new Error("Invalid href. It must be a valid string.");if(void 0!==referenceHosts&&"string"!=typeof referenceHosts&&("[object Array]"!=Object.prototype.toString.call(referenceHosts)||!referenceHosts.length))throw new Error("Invalid referenceHosts. It must be a valid string or an array of strings.");PRIVATE.originalHREF=href,PRIVATE.components=STATIC_PRIVATE.getComponents(href,referenceHosts),PRIVATE.values=PRIVATE.components.values,PRIVATE.params=PRIVATE.components.params,PRIVATE.updateValues=function(){PRIVATE.values.search=PRIVATE.params?STATIC_PRIVATE.printSearch(PRIVATE.params):"",PRIVATE.values.uri=PRIVATE.values.origin+PRIVATE.values.pathname+PRIVATE.values.search,PRIVATE.values.href=PRIVATE.values.uri+PRIVATE.values.hash},PUBLIC.getComponent=function(name){if(PRIVATE.values.hasOwnProperty(name))return PRIVATE.values[name];throw new Error("The requested WebLocation component doesn't exist: "+name)},PUBLIC.getAllComponents=function(copy){if(void 0!==copy&&"boolean"!=typeof copy)throw new Error('Invalid argument "copy". If you wanna receive a raw copy of all components, pass in "true" {Boolena}. If you ommit it (or pass "false", you will receive a cloned object.');return copy?STATIC_PRIVATE.merge({},PRIVATE.values):STATIC_PRIVATE.cloneComponents(PRIVATE.values)},PUBLIC.getAllParams=function(copy){if(void 0!==copy&&"boolean"!=typeof copy)throw new Error('Invalid argument "copy". If you wanna receive a raw copy of all params, pass in "true" {Boolena}. If you ommit it (or pass "false", you will receive a cloned object.');return PRIVATE.params?copy?STATIC_PRIVATE.merge({},PRIVATE.params):STATIC_PRIVATE.cloneParams(PRIVATE.params):null},PUBLIC.queryParams=function(query,filterID){if(!query||!(query instanceof RegExp)&&"string"!=typeof query)throw new Error("Invalid query. It must be a string or a RegExp instance");if(void 0===filterID)filterID=query instanceof RegExp?"pattern":"associative";else if("string"!=typeof filterID||!STATIC_PRIVATE.keyFilters[filterID])throw new Error("Invalid filterID:"+filterID);return STATIC_PRIVATE.keyFilters[filterID](query,PRIVATE.params)},PUBLIC.listParams=function(filterExp){var output,current;if(!(void 0===filterExp||filterExp&&(filterExp instanceof RegExp||"string"==typeof filterExp)))throw new Error("Invalid filterExp. It must be a string or a RegExp instance");if(output=[],!PRIVATE.params)return output;for(current in"string"==typeof filterExp&&(filterExp=new RegExp("^"+filterExp+"$")),PRIVATE.params)!PRIVATE.params.hasOwnProperty(current)||filterExp&&!filterExp.test(current)||output.push(current);return output},PUBLIC.getParam=function(name,multivalued){if(!name||"string"!=typeof name)throw new TypeError('Invalid param name: "'+name+'".');var currentValue=PRIVATE.params?PRIVATE.params[name]:void 0;if(void 0!==multivalued){if("boolean"!=typeof multivalued)throw new Error("Invalid definition for second paremeter. You should declare it as a boolean to define if you expected a multivalued param or not. Passed argument was "+name);if(multivalued&&"[object Array]"!=Object.prototype.toString.call(currentValue))throw new Error('Param "'+name+'" is not valid because it is NOT MULTIVALUED: '+currentValue);if(!multivalued&&"[object Array]"==Object.prototype.toString.call(currentValue))throw new Error('Param "'+name+'" is not valid because it IS MULTIVALUED: ['+currentValue.join(", ")+"].")}return currentValue},PUBLIC.setParams=function(newParams){if("[object Object]"!=Object.prototype.toString.call(newParams))throw new Error("Invalid params. It must be an object.");var newParamName,newParamValue;for(newParamName in newParams)if(newParams.hasOwnProperty(newParamName)&&(newParamValue=newParams[newParamName],!STATIC_PRIVATE.EXP_VALID_PARAM_TYPES.test(Object.prototype.toString.call(newParamValue))||"[object Array]"==Object.prototype.toString.call(newParamValue)))throw new Error('Invalid value for "'+newParamName+'".');return PRIVATE.params=newParams,PRIVATE.updateValues(),PUBLIC},PUBLIC.mergeParams=function(newParams){if("[object Object]"!=Object.prototype.toString.call(newParams))throw new Error("Invalid params. It must be an object.");var newParamName,newParamValue;for(newParamName in newParams)if(newParams.hasOwnProperty(newParamName)&&(newParamValue=newParams[newParamName],!STATIC_PRIVATE.EXP_VALID_PARAM_TYPES.test(Object.prototype.toString.call(newParamValue))||"[object Array]"==Object.prototype.toString.call(newParamValue)))throw new Error('Invalid value for "'+newParamName+'".');return PRIVATE.params=STATIC_PRIVATE.merge({},PRIVATE.params,newParams),PRIVATE.updateValues(),PUBLIC},PUBLIC.addParams=function(newParams){if("[object Object]"!=Object.prototype.toString.call(newParams))throw new Error("Invalid params. It must be an object.");var currentParamValue,newParamName,newParamValue,currentValues=STATIC_PRIVATE.merge({},PRIVATE.params||{});for(newParamName in newParams)if(newParams.hasOwnProperty(newParamName)&&(newParamValue=newParams[newParamName],currentParamValue=currentValues[newParamName],void 0!==newParamValue))if(currentParamValue)if("[object Array]"!=Object.prototype.toString.call(currentParamValue)&&(currentParamValue=currentValues[newParamName]=[currentParamValue]),STATIC_PRIVATE.EXP_VALID_PARAM_TYPES.test(Object.prototype.toString.call(newParamValue)))currentValues[newParamName].push(newParamValue);else{if("[object Array]"!=Object.prototype.toString.call(newParamValue))throw new Error('Invalid value for "'+newParamName+'".');currentValues[newParamName]=currentParamValue.concat(newParamValue)}else currentValues[newParamName]=newParamValue;return PRIVATE.params=currentValues,PRIVATE.updateValues(),PUBLIC},PUBLIC.removeParams=function(paramsList){if("string"==typeof paramsList&&(paramsList=[paramsList]),PRIVATE.params&&"[object Array]"==Object.prototype.toString.call(paramsList))for(;paramsList.length;)delete PRIVATE.params[paramsList.shift()];else PRIVATE.params=null;return PRIVATE.updateValues(),PUBLIC},PUBLIC.unsetParams=function(){return PRIVATE.params=null,PRIVATE.updateValues(),PUBLIC},PUBLIC.getID=function(){return STATIC_PRIVATE.getURIID(PRIVATE.components.values.uri)}}).convertPathToAbsolute=STATIC_PRIVATE.convertPathToAbsolute,CURRENT_SCOPE.setHostAlias=function(id,pattern){if("string"!=typeof id||!/^\w+/.test(id))throw new Error("Invalid host id. It must be a string.");if(!(pattern instanceof RegExp))throw new Error("Invalid alias expression. It must be an instance of RegExp.");STATIC_PRIVATE.hostAliases[id]=pattern},CURRENT_SCOPE)}(this),this.Promise=window.Promise,function(){function t(){}function i(t,n){for(var e=t.length;e--;)if(t[e].listener===n)return e;return-1}function n(e){return function(){return this[e].apply(this,arguments)}}var e=t.prototype,r=this,s=r.EventEmitter;e.getListeners=function(n){var r,e,t=this._getEvents();if(n instanceof RegExp)for(e in r={},t)t.hasOwnProperty(e)&&n.test(e)&&(r[e]=t[e]);else r=t[n]||(t[n]=[]);return r},e.flattenListeners=function(t){for(var e,n=[],e=0;e<t.length;e+=1)n.push(t[e].listener);return n},e.getListenersAsObject=function(n){var e,t=this.getListeners(n);return t instanceof Array&&((e={})[n]=t),e||t},e.addListener=function(r,e){var t,n=this.getListenersAsObject(r),s="object"==typeof e;for(t in n)n.hasOwnProperty(t)&&-1===i(n[t],e)&&n[t].push(s?e:{listener:e,once:!1});return this},e.on=n("addListener"),e.addOnceListener=function(e,t){return this.addListener(e,{listener:t,once:!0})},e.once=n("addOnceListener"),e.defineEvent=function(e){return this.getListeners(e),this},e.defineEvents=function(t){for(var e=0;e<t.length;e+=1)this.defineEvent(t[e]);return this},e.removeListener=function(r,s){var n,e,t=this.getListenersAsObject(r);for(e in t)t.hasOwnProperty(e)&&(n=i(t[e],s),-1!==n&&t[e].splice(n,1));return this},e.off=n("removeListener"),e.addListeners=function(e,t){return this.manipulateListeners(!1,e,t)},e.removeListeners=function(e,t){return this.manipulateListeners(!0,e,t)},e.manipulateListeners=function(r,t,i){var e,n,s=r?this.removeListener:this.addListener,o=r?this.removeListeners:this.addListeners;if("object"!=typeof t||t instanceof RegExp)for(e=i.length;e--;)s.call(this,t,i[e]);else for(e in t)t.hasOwnProperty(e)&&(n=t[e])&&("function"==typeof n?s:o).call(this,e,n);return this},e.removeEvent=function(e){var t,r=typeof e,n=this._getEvents();if("string"==r)delete n[e];else if(e instanceof RegExp)for(t in n)n.hasOwnProperty(t)&&e.test(t)&&delete n[t];else delete this._events;return this},e.removeAllListeners=n("removeEvent"),e.emitEvent=function(r,o){var e,i,t,s,n=this.getListenersAsObject(r);for(t in n)if(n.hasOwnProperty(t))for(i=n[t].length;i--;)e=n[t][i],!0===e.once&&this.removeListener(r,e.listener),s=e.listener.apply(this,o||[]),s===this._getOnceReturnValue()&&this.removeListener(r,e.listener);return this},e.trigger=n("emitEvent"),e.emit=function(e){var t=Array.prototype.slice.call(arguments,1);return this.emitEvent(e,t)},e.setOnceReturnValue=function(e){return this._onceReturnValue=e,this},e._getOnceReturnValue=function(){return!this.hasOwnProperty("_onceReturnValue")||this._onceReturnValue},e._getEvents=function(){return this._events||(this._events={})},t.noConflict=function(){return r.EventEmitter=s,t},"function"==typeof define&&define.amd?define(function(){return t}):"object"==typeof module&&module.exports?module.exports=t:r.EventEmitter=t}.call(this),CURRENT_SCOPE=this,window.fetch?CURRENT_SCOPE.fetch=window.fetch.bind(window):(Headers.prototype.append=function(name,value){name=normalizeName(name),value=normalizeValue(value);var list=this.map[name];list||(this.map[name]=list=[]),list.push(value)},Headers.prototype.delete=function(name){delete this.map[normalizeName(name)]},Headers.prototype.get=function(name){var name=this.map[normalizeName(name)];return name?name[0]:null},Headers.prototype.getAll=function(name){return this.map[normalizeName(name)]||[]},Headers.prototype.has=function(name){return this.map.hasOwnProperty(normalizeName(name))},Headers.prototype.set=function(name,value){this.map[normalizeName(name)]=[normalizeValue(value)]},Headers.prototype.forEach=function(callback){var self=this;Object.getOwnPropertyNames(this.map).forEach(function(name){callback(name,self.map[name])})},support={blob:"FileReader"in CURRENT_SCOPE&&"Blob"in CURRENT_SCOPE&&function(){try{return new Blob,!0}catch(e){return!1}}(),formData:"FormData"in window,XDomainRequest:"XDomainRequest"in window},methods=["DELETE","GET","HEAD","OPTIONS","POST","PUT"],Request.prototype.fetch=function(){var self=this;return new Promise(function(resolve,reject){function responseURL(){return"responseURL"in xhr?xhr.responseURL:/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())?xhr.getResponseHeader("X-Request-URL"):void 0}var legacyCors=!1,origin,xhr=(support.XDomainRequest&&(origin=location.protocol+"//"+location.host,/^\/[^\/]/.test(self.url)||(legacyCors=/msie 8/i.test(navigator.userAgent)||(/^\/\//.test(self.url)?location.protocol+self.url:self.url).substring(0,origin.length)!==origin)),new(legacyCors?XDomainRequest:XMLHttpRequest)),arg=(legacyCors?xhr.getAllResponseHeaders=function(){return"Content-Type: "+xhr.contentType}:"cors"===self.credentials&&(xhr.withCredentials=!0),xhr.onload=function(){var status=1223===xhr.status?204:xhr.status,status,body;(status=legacyCors?200:status)<100||599<status?reject(new TypeError("Network request failed")):(status={status:status,statusText:xhr.statusText,headers:headers(xhr),url:responseURL()},body="response"in xhr?xhr.response:xhr.responseText,resolve(new Response(body,status)))},xhr.onerror=function(){reject(new TypeError("Network request failed"))},xhr.open(self.method,self.url,!0),"responseType"in xhr&&support.blob&&(xhr.responseType="blob"),self.headers.forEach(function(name,values){values.forEach(function(value){xhr.setRequestHeader(name,value)})}),void 0===self._bodyInit?null:self._bodyInit),origin=function(){xhr.send.call(xhr,arg)};legacyCors?(xhr.onprogress=xhr.onprogress||function(){},xhr.ontimeout=xhr.ontimeout||function(){},setTimeout(origin,0)):origin()})},Body.call(Request.prototype),Body.call(Response.prototype),CURRENT_SCOPE.Headers=Headers,CURRENT_SCOPE.Request=Request,CURRENT_SCOPE.Response=Response,CURRENT_SCOPE.fetch=function(url,options){return new Request(url,options).fetch()},CURRENT_SCOPE.fetch.polyfill=!0)}if(CURRENT_SCOPE.Includer)throw new Error("Includer already loaded");var PKG,STATIC_PRIVATE,PROTECTED,STATIC_PUBLIC,Includer,SUPER_PRIVATE,PRIVATE,CURRENT_SCOPE,urls;CURRENT_SCOPE.Includer=(PKG=new Package,PROTECTED={},(STATIC_PRIVATE={}).version="1.1.0",STATIC_PRIVATE.tpn=/tpn=1/.test(location.search),STATIC_PRIVATE.runningAtCompileTime=!0,STATIC_PRIVATE.isLegacyBrowser=/MSIE [5-8]/.test(navigator.userAgent),STATIC_PRIVATE.events=new PKG.EventEmitter,STATIC_PRIVATE.console=new PKG.Log("Includer"),STATIC_PRIVATE.log=STATIC_PRIVATE.console.write,STATIC_PRIVATE.documentLocation=new PKG.WebLocation(location.href),STATIC_PRIVATE.instances={},STATIC_PRIVATE.includes={},STATIC_PRIVATE.activeDependencies={},STATIC_PRIVATE.aliases={},STATIC_PRIVATE.foreignLoaders=null,STATIC_PRIVATE.foreignImporters=null,STATIC_PRIVATE.EXP_EXTRACT_EXTENSION=/\S+\.(\w+)|\!(\w+)$/,STATIC_PRIVATE.EXP_COMPONENT_ID=/[A-Za-z]\S*/,STATIC_PRIVATE.FORMATS_PREFERENCE_ORDER=["zaz","system","amd","global"],STATIC_PRIVATE.settings={log:2,bypassFetch:!1,injection:!0,queryRequestSpan:300,operationTimeout:(STATIC_PRIVATE.isLegacyBrowser,12e4),singleRequestTimeout:45e3,parsingSpan:STATIC_PRIVATE.isLegacyBrowser?120:60,reject:[]},PROTECTED.pkg=PKG,PROTECTED.events=STATIC_PRIVATE.events,PROTECTED.log=STATIC_PRIVATE.log,PROTECTED.pkg.handleFetchResponse=function(response){var message="",message;if(200<=response.status&&response.status<300)return response.text();throw response.statusText&&(message=response.statusText),"string"==typeof response.responseText&&(message+=" > "+response.responseText.replace(/<.+?>/g,"")),(message=new Error(message)).response=response,message},STATIC_PRIVATE.EndPoint=function(SUPER_STATIC_PRIVATE,SUPER_PROTECTED){var STATIC_PRIVATE={},STATIC_PUBLIC,EndPoint,html,country;return STATIC_PRIVATE.log=SUPER_STATIC_PRIVATE.log,STATIC_PRIVATE.documentLocation=SUPER_STATIC_PRIVATE.documentLocation,STATIC_PRIVATE.environments={DSV:{service:"https://dsv-cengine03-mia.dev.terra.com:80/xact/includer/include".replace(/\:80/,function(match){var targetPort=/port=(\d+)/.exec(location.search);return targetPort?":"+targetPort[1]:""})},HLG:{service:"https://api-hlg.tpn.terra.com/includer/include"},PREV:{service:"https://api-hlg.tpn.terra.com/includer/include"},PROD:{service:"https://p1-cloud.trrsf.com.br/api/includer/include"}},STATIC_PRIVATE.defaultParams={env:"prod",format:"json",country:(html=(html=document.getElementsByTagName("html")[0]).getAttribute("lang")||html.getAttribute("xml:lang"),((html=/(?:\w\w\-)+([a-zA-Z]{2})/g.exec(html))?html[1]:"BR").toLowerCase()),group:pkg.context.platform.get("type")},STATIC_PUBLIC=EndPoint=function EndPoint(){var PUBLIC=this,PRIVATE={};PRIVATE.queryRequestSpan=SUPER_STATIC_PRIVATE.settings.queryRequestSpan,PRIVATE.queue=[],PRIVATE.requisitionTimer=null,PRIVATE.environment=STATIC_PRIVATE.environments.PROD,PRIVATE.serviceWebLocation=new PKG.WebLocation(PRIVATE.environment.service).addParams(STATIC_PRIVATE.defaultParams);for(var pageScripts=document.scripts||document.getElementsByTagName("script"),totalPageScripts=pageScripts.length,importerPathEXP=/zaz(\.inline|\-includer\-fe)?([\.\-]min)?\.js/,importerScript,serviceParams,customParams,forcedEnv,inlineParams,i,current,i=0;i<totalPageScripts;i++)if(current=(current=pageScripts[i]).src||current.getAttribute("data-includerInjection"),importerPathEXP.test(current))try{(inlineParams=(importerScript=new PKG.WebLocation(current)).getAllParams(!0))&&STATIC_PRIVATE.log("Inline params defined by importer include: "+current)}catch(e){STATIC_PRIVATE.log.warn("Exception detecting inline params of "+current+" >> "+e.message)}finally{break}"https:"!=location.protocol&&"https:"!=importerScript.getComponent("protocol")||((inlineParams=inlineParams&&"object"==typeof inlineParams?inlineParams:{}).scheme="https"),inlineParams&&"object"==typeof inlineParams&&PRIVATE.serviceWebLocation.mergeParams(inlineParams),1==STATIC_PRIVATE.documentLocation.getParam("tpn")&&(serviceParams=STATIC_PRIVATE.documentLocation.queryParams("includer"),customParams=STATIC_PRIVATE.documentLocation.queryParams(/^(?!includer|tpn|format|env).+/),serviceParams.env&&(serviceParams.env=serviceParams.env.toUpperCase(),STATIC_PRIVATE.environments[serviceParams.env]&&(PRIVATE.environment=STATIC_PRIVATE.environments[serviceParams.env],PRIVATE.serviceWebLocation=new PKG.WebLocation(PRIVATE.environment.service).addParams(STATIC_PRIVATE.defaultParams),inlineParams&&"object"==typeof inlineParams&&PRIVATE.serviceWebLocation.mergeParams(inlineParams))),"component"in customParams&&delete customParams.component,PRIVATE.serviceWebLocation.mergeParams(customParams)),PRIVATE.requestComponents=function(){var requestSettings=new PKG.WebLocation(PRIVATE.serviceWebLocation.getComponent("uri")),requestedComponents=[],request,current;for(PRIVATE.requisitionTimer=null;PRIVATE.queue.length;)current=PRIVATE.queue.shift(),new SUPER_STATIC_PRIVATE.Dependency(current).isSpecified()?STATIC_PRIVATE.log('Dependency "'+current+'" registered at runtime. Discarding from query list.'):-1==requestedComponents.indexOf(current)&&requestedComponents.push(current);if(SUPER_STATIC_PRIVATE.tpn&&SUPER_STATIC_PRIVATE.settings.bypassFetch){for(var exception=new Error('Component fetching prevented by special queryString param "includer.bypassFetch".'),id,dependency,i,i=0;i<requestedComponents.length;i++)id=requestedComponents[i],(dependency=new SUPER_STATIC_PRIVATE.Dependency(id)).refuse(exception);PRIVATE.activeQuery.reject(exception)}else{if(!requestedComponents.length)return STATIC_PRIVATE.log("There are no pending components to query IncluderBE. They were all resolved at runtime."),void PRIVATE.activeQuery.resolve(null);SUPER_STATIC_PRIVATE.log("Consulting IncluderBE for components not specified at runtime: "+requestedComponents.join(", ")),requestSettings.addParams({component:requestedComponents}),SUPER_PROTECTED.pkg.fetch(requestSettings.getComponent("uri")).then(SUPER_PROTECTED.pkg.handleFetchResponse).then(function(data){var includes,invalidComponents,i,id,dependency,file,definition;try{if(data=JSON.parse(data),SUPER_STATIC_PRIVATE.log("Query to IncluderBE complete",data),requestedComponents=data.components,invalidComponents=data.invalid_components,"[object Object]"!=Object.prototype.toString.call(requestedComponents))throw new Error('Attribute "components" invalid or missing.');for(id in requestedComponents)requestedComponents.hasOwnProperty(id)&&(definition=requestedComponents[id],dependency=new SUPER_STATIC_PRIVATE.Dependency(id),definition.includes=definition.includes?PKG.util.copy(definition.includes):[],dependency.isSpecified()||dependency.specify({registryOrigin:"IncluderBE",selectedEnvironment:definition.selectedEnvironment||"prod",events:definition.events||null,includes:definition.includes,imports:definition.depends,exports:definition.delivers}));for(;invalidComponents.length;)id=invalidComponents.shift(),dependency=new SUPER_STATIC_PRIVATE.Dependency(id),/resolved|rejected/.test(dependency.getStatus())||dependency.refuse(new Error('Component "'+id+"\" not found on Includers' registry."))}catch(e){for(e.message="Error parsing response from Includer WS: "+e.message,STATIC_PRIVATE.log(e,requestSettings.getAllComponents()),i=0;i<requestedComponents.length;i++)id=requestedComponents[i],dependency=new SUPER_STATIC_PRIVATE.Dependency(current),/resolved|rejected/.test(dependency.getStatus())||dependency.refuse(e)}finally{PRIVATE.activeQuery.resolve(data)}}).catch(function(exception){var i,id,dependency;for(exception.message="Error requesting Includer's WS: "+exception.message+"\n@ "+requestSettings.getComponent("uri"),SUPER_STATIC_PRIVATE.log(exception,requestSettings.getAllComponents()),i=0;i<requestedComponents.length;i++)id=requestedComponents[i],dependency=new SUPER_STATIC_PRIVATE.Dependency(id).refuse(exception);PRIVATE.activeQuery.reject(exception)})}},this.query=function(components){var handlers={};for("string"==typeof components&&(components=[components]);components.length;)PRIVATE.queue.push(components.shift());return PRIVATE.requisitionTimer||(PRIVATE.requisitionTimer=setTimeout(PRIVATE.requestComponents,PRIVATE.queryRequestSpan),PRIVATE.activeQuery=new PKG.Promise(function(resolve,reject){handlers.resolve=resolve,handlers.reject=reject}),PRIVATE.activeQuery.resolve=handlers.resolve,PRIVATE.activeQuery.reject=handlers.reject),PRIVATE.activeQuery}}}(STATIC_PRIVATE,PROTECTED),STATIC_PRIVATE.Loaders=(SUPER_PRIVATE=STATIC_PRIVATE,CURRENT_SCOPE={},(PRIVATE={}).loaders={},CURRENT_SCOPE.set=function(extensions,handler){var current;if(!extensions||"string"!=typeof extensions&&"[object Array]"!=Object.prototype.toString.call(extensions))throw new Error("Invalid resource loader. The first param must be a valid list of extensions.");if("function"!=typeof handler)throw new Error("Invalid loader. The second param must be a function.");for("string"==typeof extensions&&(extensions=[extensions]);extensions.length;){if(current=extensions.shift(),PRIVATE.loaders[current])throw new Error('Loader already defined for extension "'+current+'".');PRIVATE.loaders[current]=handler}},CURRENT_SCOPE.bind=function(declaration,target){return new PKG.Promise(function(resolve,reject,emit){var args=Array.prototype.slice.call(arguments),loader,timer,loaderID,loaderOutput,current;resolve=function(value){value instanceof STATIC_PRIVATE.ResourceDeclaration?declaration=value:declaration.exports=value,args[0](declaration)},reject=function(value){(value=value instanceof Error?value:new Error(value)).message="Error requesting "+(declaration.extension||"file")+" @ "+declaration.webLocation.getComponent("href")+" >> "+value.message,args[1](value,declaration)};try{if(STATIC_PRIVATE.foreignLoaders)for(loaderID in STATIC_PRIVATE.foreignLoaders)if(STATIC_PRIVATE.foreignLoaders.hasOwnProperty(loaderID)&&(!(current=STATIC_PRIVATE.foreignLoaders[loaderID]).filter||current.filter.test(declaration.url)))try{if(STATIC_PRIVATE.log('Fetching "'+declaration.url+'" from foreign loader: '+loaderID),void 0!==(loaderOutput=current.handler(declaration,{Promise:PKG.Promise,WebLocation:PKG.WebLocation,getNamespace:PKG.util.getNamespace,DOMReady:PKG.util.DOMReady})))return void(loaderOutput instanceof PKG.Promise?(loaderOutput.then(function(value){return timer=timer&&clearTimeout(timer),STATIC_PRIVATE.log('Foreign loader "'+loaderID+'" delivered "'+declaration.url+'".'),declaration.exports=value||null,resolve(declaration),PKG.Promise.resolve(declaration)}).then(null,function(e){return timer=timer&&clearTimeout(timer),e instanceof Error||(e=new Error('Foreign loader "'+loaderID+'" was not able to resolve this url: '+declaration.url+". Reason: "+(e||"not given"))),reject(e),PKG.Promise.reject(e)}),timer=setTimeout(function(){reject(new Error('Fetching operation from foreign loader "'+loaderID+'" rejected by timeout ('+STATIC_PRIVATE.settings.singleRequestTimeout+"ms)"))},STATIC_PRIVATE.settings.singleRequestTimeout)):(STATIC_PRIVATE.log('Foreign loader "'+loaderID+'" delivered "'+declaration.url+'".'),declaration.exports=loaderOutput||null,resolve(declaration)));STATIC_PRIVATE.log('Foreign loader "'+loaderID+'" dismissed '+declaration.url)}catch(e){timer=timer&&clearTimeout(timer),e.message="Foreign loader raised an exception: "+e.message,e.message+=" >> falling back to Includer's registry.",STATIC_PRIVATE.log(e)}if(!(loader=STATIC_PRIVATE.Loaders.get(declaration.extension)))throw new Error("Unknown loader for "+declaration.extension);loader.apply({declaration:declaration,target:target,settings:STATIC_PRIVATE.settings},[resolve,reject,emit])}catch(e){e.message="[Exception on "+declaration.extension+" extension] > "+e.message,reject(e)}})},CURRENT_SCOPE.get=function(extension){return PRIVATE.loaders[extension]},CURRENT_SCOPE.list=function(extension){var output=[],current;for(current in PRIVATE.loaders)PRIVATE.loaders.hasOwnProperty(current)&&-1==output.join("|").indexOf(current)&&output.push(current);return output},CURRENT_SCOPE),STATIC_PRIVATE.Resource=function(SUPER_STATIC_PRIVATE){var STATIC_PRIVATE={},STATIC_PUBLIC,Resource,STATIC_PUBLIC;return Resource=function Resource(declaration,target){var PUBLIC=this,PRIVATE={};PRIVATE.promise=SUPER_STATIC_PRIVATE.Loaders.bind(declaration,target),this.type=declaration.extension,this.promise=PRIVATE.promise}}(STATIC_PRIVATE),STATIC_PRIVATE.ResourceDeclaration=function ResourceDeclaration(value,baseURL,settings){var PUBLIC=this,PRIVATE={};if(!value||"string"!=typeof value&&"[object Object]"!==Object.prototype.toString.call(value))throw new Error("It must a valid string or an object >> "+value);if("string"==typeof value)this.extension=STATIC_PRIVATE.EXP_EXTRACT_EXTENSION.exec(value),PRIVATE.path=value.replace(/\!.*$/,""),this.extension=this.type=this.extension?this.extension[2]||this.extension[1]:void 0,this.force=!1,this.namespaces=null,this.attributes=null,this.events=null,this.corsAllowed=/trrsf\.com/.test(value);else{if(this.type=this.extension=value.extension||value.type,PRIVATE.path=value.path,void 0!==value.force&&"boolean"!=typeof value.force)throw new Error('If defined, property "force" must be a boolean.');this.force=!!value.force,this.timeout=value.timeout,this.namespaces=value.namespaces||null,this.attributes=value.attributes||null,this.events=value.events||null,this.corsAllowed=!value.hasOwnProperty("corsAllowed")||value.corsAllowed}if(this.timeout=this.timeout||STATIC_PRIVATE.settings.singleRequestTimeout,!/^\d+$/.test(this.timeout))throw new Error("Invalid value for resource request timeout: "+this.timeout,this);this.webLocation=new PKG.WebLocation(PRIVATE.path,baseURL),this.targetDocument=settings.targetDocument,this.url=this.webLocation.getComponent("uri"),this.targetDocument.location.host==this.webLocation.getComponent("host")?this.corsAllowed=!0:window.XDomainRequest&&this.targetDocument.location.protocol!=this.webLocation.getComponent("protocol")&&(this.corsAllowed=!1)},STATIC_PRIVATE.sanitizeDeclarations=function(resources,baseURL,settings){if("string"!=typeof resources&&"[object Array]"!=Object.prototype.toString.call(resources))throw new Error("Invalid path. It must be a string or and array of strings.");if("string"!=typeof baseURL&&"[object Array]"!=Object.prototype.toString.call(baseURL))throw new Error("Invalid baseURL. It must be a string or and array of strings.");var declarations=[],invalid=[],declaration,current;for(resources=[].concat(resources="string"==typeof resources?[resources]:resources);resources.length;)if("object"!=typeof(current=resources.shift())||!/template|manifest/i.test(current.type))try{current=new STATIC_PRIVATE.ResourceDeclaration(current,baseURL,settings),declarations.push(current)}catch(exception){exception.message=current+" > "+exception.message,invalid.push(exception.message)}if(invalid.length)throw new Error("Invalid resource declarations: "+invalid.join("\n\n>> "));return declarations},STATIC_PRIVATE.Dependency=function(SUPER_STATIC_PRIVATE,SUPER_STATIC_PUBLIC){var STATIC_PRIVATE={instances:{},Specification:function Specification(id,value){if(!id||!SUPER_STATIC_PRIVATE.EXP_COMPONENT_ID.test(id))throw new Error("Invalid ID to specify a component: "+id+".");if(!value||!/\[object (Object|Function)\]/.test(Object.prototype.toString.call(value)))throw new Error("Invalid definition for component "+id+'. It must be an object or a factory function (instead of "'+value+'")');if("function"==typeof value)this.exports=value;else{if(value.hasOwnProperty("imports")&&!Array.isArray(value.imports))throw new Error("Invalid imports property");if(value.hasOwnProperty("exports")&&!Array.isArray(value.exports)&&"function"!=typeof value.exports)throw new Error("Invalid exports property");PKG.util.extend(this,{exports:[],imports:[],registryOrigin:"RunTime"},value)}}},STATIC_PUBLIC,STATIC_PUBLIC=function Dependency(declaration){var PRIVATE={},PUBLIC=this;if(PRIVATE.id=declaration,SUPER_STATIC_PRIVATE.aliases[PRIVATE.id]&&(PRIVATE.id=SUPER_STATIC_PRIVATE.aliases[PRIVATE.id]),STATIC_PRIVATE.instances[PRIVATE.id])return STATIC_PRIVATE.instances[PRIVATE.id];PRIVATE.status="called",PRIVATE.definition=null,PUBLIC.id=PRIVATE.id,PRIVATE.specified=!1,PRIVATE.specification=null,PRIVATE.definitionOverwritten=!1,PUBLIC.isSpecified=function(definition){return PRIVATE.specified},PUBLIC.listDependencies=function(){return PRIVATE.specification&&PRIVATE.specification.imports?[].concat(PRIVATE.specification.imports):[]},PUBLIC.specify=function(definition){if(PRIVATE.definition&&("resolved"==PRIVATE.status||"rejected"==PRIVATE.status))throw new PKG.ApplicationError("Component already defined and "+PRIVATE.status+": "+PRIVATE.id);definition.registryOrigin=definition.registryOrigin||"RunTime";try{PRIVATE.specification&&"IncluderBE"!=definition.registryOrigin&&(PRIVATE.definitionOverwritten=!0),definition=new STATIC_PRIVATE.Specification(PRIVATE.id,definition),PRIVATE.specification=definition,PRIVATE.definition&&PRIVATE.definition.resolve&&!PRIVATE.definitionOverwritten?PRIVATE.definition.resolve(PRIVATE.specification):PRIVATE.definition=PKG.Promise.resolve(PRIVATE.specification),PRIVATE.status="specified",PRIVATE.specified=!0,PRIVATE.definitionOverwritten||(SUPER_STATIC_PRIVATE.log('Component "'+PUBLIC.id+'" specified.'),SUPER_STATIC_PRIVATE.events.emit("COMPONENT_DEFINED",definition),SUPER_STATIC_PRIVATE.events.emit("COMPONENT_DEFINED:"+PRIVATE.id,definition)),SUPER_STATIC_PRIVATE.events.emit("COMPONENT_SPECIFIED",definition),SUPER_STATIC_PRIVATE.events.emit("COMPONENT_SPECIFIED:"+PRIVATE.id,definition)}catch(e){!0===PRIVATE.definitionOverwritten&&(PRIVATE.definitionOverwritten=!1,SUPER_STATIC_PRIVATE.log.warn('Invalid attempt to overwrite definition of "'+PUBLIC.id+'": '+e.message)),PRIVATE.specified?SUPER_STATIC_PRIVATE.log('Keeping last definition of "'+PUBLIC.id+'".'):(PUBLIC.refuse(e),PRIVATE.specification=null)}},PUBLIC.refuse=function(motive){"string"==typeof motive&&(motive=new Error(motive)),/rejected|resolved/.test(PRIVATE.status)?SUPER_STATIC_PRIVATE.log.warn('Component "'+PRIVATE.id+'" can\'t be reject at this point because it is already settled as "'+PRIVATE.status+'".'):(PRIVATE.definition?PRIVATE.definition.reject(motive):PRIVATE.definition=PKG.Promise.reject(motive),PRIVATE.status="rejected",PRIVATE.factory=PRIVATE.component=PKG.Promise.reject(motive))},PUBLIC.getSpecification=function(){return PRIVATE.specification||null},PUBLIC.fetch=function(){var handlers={};return PRIVATE.definition||(PRIVATE.status="fetching",PRIVATE.definition=new PKG.Promise(function(resolve,reject){var definition,importerID,importerOutput,current,timer;if(handlers.resolve=function(definition){if(timer=timer&&clearTimeout(timer),!PRIVATE.specified&&"rejected"!=PRIVATE.status)try{definition=new STATIC_PRIVATE.Specification(PRIVATE.id,definition),PRIVATE.specification=definition,resolve(definition)}catch(e){handlers.reject(e)}},handlers.reject=function(reason){timer=timer&&clearTimeout(timer),PRIVATE.specified||"rejected"==PRIVATE.status||((reason=reason&&"string"!=typeof reason?reason:new Error(reason||"Component rejected by an unknown reason"))instanceof Error||(reason=new Error("Component reject with: "+reason+". Does it mean something to you?")),reject(reason))},SUPER_STATIC_PRIVATE.foreignImporters)for(importerID in SUPER_STATIC_PRIVATE.foreignImporters)if(SUPER_STATIC_PRIVATE.foreignImporters.hasOwnProperty(importerID)&&(!(current=SUPER_STATIC_PRIVATE.foreignImporters[importerID]).filter||current.filter.test(PRIVATE.id)))try{if(SUPER_STATIC_PRIVATE.log('Fetching "'+PRIVATE.id+'" from foreign importer: '+importerID),void 0!==(importerOutput=current.handler(PRIVATE.id,{Promise:PKG.Promise,WebLocation:PKG.WebLocation,getNamespace:PKG.util.getNamespace,DOMReady:PKG.util.DOMReady}))){if(importerOutput instanceof PKG.Promise)importerOutput.then(function(value){return SUPER_STATIC_PRIVATE.log('Foreign importer "'+importerID+'" delivered "'+PRIVATE.id+'".'),handlers.resolve(value),value}).then(null,function(e){return e.nessage='Foreign importer "'+importerID+'" reject "'+PRIVATE.id+'": '+e.message,handlers.reject(e),e}),timer=setTimeout(function(){handlers.reject(new Error("Fetching operation from foreign importer rejected by timeout ("+SUPER_STATIC_PRIVATE.settings.singleRequestTimeout+"ms)"))},SUPER_STATIC_PRIVATE.settings.singleRequestTimeout);else{if("function"!=typeof importerOutput&&"[object Object]"!=Object.prototype.toString.call(importerOutput)){SUPER_STATIC_PRIVATE.log.warn('Foreign importer failed on fetching "'+PRIVATE.id+'". It returned "'+importerOutput+'" ('+Object.prototype.toString.call(importerOutput)+")  >> falling back to Includer's registry.");break}SUPER_STATIC_PRIVATE.log('Foreign importer "'+importerID+'" delivered "'+PRIVATE.id+'".'),handlers.resolve(importerOutput)}return}}catch(e){timer=clearTimeout(timer),e.message="Foreign importer raised an exception: "+e.message,e.message+=" >> falling back to Includer's registry.",SUPER_STATIC_PRIVATE.log(e)}SUPER_STATIC_PRIVATE.ws.query(PRIVATE.id)}).then(function(definition){return PRIVATE.specified=!0,PRIVATE.status="fetched",definition}),PRIVATE.definition.resolve=handlers.resolve,PRIVATE.definition.reject=handlers.reject),PRIVATE.definition},PRIVATE.loadDependencies=function(){return new PKG.Promise(function(resolve,reject){PUBLIC.fetch().then(function(definition){var factory={},includes=[],dependencies=[],current,i,dependenciesPromise,filesPromise;if(factory.id=PRIVATE.id,factory.imports=Array.isArray(definition.imports)?definition.imports:[],factory.exports=Array.isArray(definition.exports)?definition.exports:[],factory.imports.length){if(-1<factory.imports.indexOf(PRIVATE.id))return void reject(new Error('Circular reference detected. Component "'+factory.id+"\" dependends on itself. Sounds cool, but it's a big inception problem."));for(i=0;i<factory.imports.length;i++)if((current=new SUPER_STATIC_PRIVATE.Dependency(factory.imports[i])).isSpecified()&&-1<current.listDependencies().indexOf(factory.id))return void reject(new Error('Circular reference detected between "'+factory.id+'" and "'+factory.imports[i]+'".'));dependencies.push(SUPER_STATIC_PRIVATE.getFactory(factory.imports))}else dependencies.push(PKG.Promise.resolve([]));factory.exports.length&&factory.exports.join("")!=PRIVATE.id?dependencies.push(SUPER_STATIC_PRIVATE.getFactory(factory.exports)):dependencies.push(PKG.Promise.resolve([])),(dependenciesPromise=PKG.Promise.all(dependencies)).then(function(){return definition.includes&&definition.includes.length?SUPER_STATIC_PRIVATE.getFiles(definition.includes):[]}).then(function(resources){var definition=PRIVATE.specification,exports,i,current;if("function"==typeof definition.exports)factory.payload=definition.exports;else if(Array.isArray(definition.exports)&&definition.exports.length&&definition.exports.join("")!=PRIVATE.id)factory.payload=null;else{for(exports=[],i=0;i<resources.length;i++)"js"==(current=resources[i]).type&&("function"==typeof current.exports&&/zaz\:/.test("function"==typeof current.namespaces.join?current.namespaces.join(""):current.namespaces)&&(current.exports.zazLoader=!0),exports.push(current.exports));1==exports.length&&(exports=exports.pop()),factory.payload="function"==typeof exports&&exports.zazLoader?exports:function(){return exports}}setTimeout(function(){resolve(factory)},SUPER_STATIC_PRIVATE.settings.parsingSpan)}).then(null,reject)}).then(null,reject)})},PUBLIC.load=function(){var handlers={};return PRIVATE.factory||(PRIVATE.status="loading",PRIVATE.factory=new PKG.Promise(function(resolve,reject){handlers.resolve=resolve,handlers.reject=reject,PRIVATE.loadDependencies().then(function(factory){return PRIVATE.definitionOverwritten?(SUPER_STATIC_PRIVATE.log('Definition for "'+PRIVATE.id+'" updated at RunTime. Revaluating dependencies.'),PRIVATE.definitionOverwritten=!1,PRIVATE.loadDependencies()):factory}).then(function(factory){return resolve(factory),PRIVATE.status="loaded",factory}).then(null,function(e){var e=new Error('Error loading up component "'+PRIVATE.id+'" > '+e.message);SUPER_STATIC_PRIVATE.log.warn(e.message),reject(e)})}),PRIVATE.factory.resolve=handlers.resolve,PRIVATE.factory.reject=handlers.reject),PRIVATE.factory},PUBLIC.require=function(){return PRIVATE.component||(PRIVATE.status="exporting",PRIVATE.component=new PKG.Promise(function(resolve,reject){setTimeout(function(){PUBLIC.load().then(function(factory){return Array.isArray(factory.imports)&&factory.imports.length?new PKG.Promise(function(resolve,reject){SUPER_STATIC_PRIVATE.getComponent(factory.imports,function(){var definition=PRIVATE.specification,pkg={},current,i;for(factory.imports=Array.prototype.slice.call(arguments),i=0;i<definition.imports.length;i++)current=definition.imports[i],current=SUPER_STATIC_PRIVATE.aliases[current]||current,PKG.util.parseKey(current,factory.imports[i],pkg);return factory.pkg=pkg,resolve(factory)}).then(null,reject)}):factory}).then(function(factory){return!factory.payload&&Array.isArray(factory.exports)&&factory.exports.length?SUPER_STATIC_PRIVATE.getComponent(factory.exports).then(function(pkg){return factory.payload=function(){return pkg},factory}):factory}).then(function(factory){var definition=PRIVATE.specification;if(definition.events&&"object"==typeof definition.events&&definition.events.onBeforeExport){if("function"!=typeof definition.events.onBeforeExport&&"string"!=typeof definition.events.onBeforeExport)throw new Error("Invalid onBeforeExport event. It must be a function or a string to evaluate");try{"string"==typeof definition.events.onBeforeExport&&(definition.events.onBeforeExport=new Function("definition",definition.events.onBeforeExport)),definition.events.onBeforeExport.call(definition,definition,PRIVATE.factory)}catch(evtException){throw evtException.message='Error triggering "onBeforeExport" event > '+evtException.message,evtException}}return factory}).then(function(factory){var definition=PRIVATE.specification,component;try{component="function"==typeof factory.payload?factory.payload.apply(factory.pkg,factory.imports):factory.payload}catch(e){throw e.message='[Exception on the factory of component "'+PRIVATE.id+'"] : '+e.message,e}return component instanceof PKG.Promise?new PKG.Promise(function(resolve,reject){var done=!1,timer=setTimeout(function(){done=!0,reject(new Error("Factory resolution aborted by timeout ("+SUPER_STATIC_PRIVATE.settings.operationTimeout+"ms"))},SUPER_STATIC_PRIVATE.settings.operationTimeout);component.then(function(component){done||(timer=timer&&clearTimeout(timer),resolve(component))},function(exception){done||(timer=timer&&clearTimeout(timer),exception.message='[Exception on the factory of component "'+PRIVATE.id+'"] : '+exception.message,reject(exception))})}):component}).then(function(component){var definition;if("function"==typeof PRIVATE.factory.payload&&void 0===component)throw new Error("Factory returned undefined.");if((definition=PRIVATE.specification).events&&"object"==typeof definition.events&&definition.events.onAfterExport){if("function"!=typeof definition.events.onAfterExport&&"string"!=typeof definition.events.onAfterExport)throw new Error("Invalid onAfterExport event. It must be a function or a string to evaluate");try{"string"==typeof definition.events.onAfterExport&&(definition.events.onAfterExport=new Function("component",definition.events.onAfterExport)),definition.events.onAfterExport.call(definition,component,PRIVATE.factory)}catch(evtException){throw evtException.message='Error triggering "onAfterExport" event > '+evtException.message,evtException}}return component}).then(function(component){SUPER_STATIC_PRIVATE.log('Component "'+PRIVATE.id+'" marked as resolved.'),PRIVATE.status="resolved",resolve(component)},function(e){var e=new PKG.ApplicationError('Component "'+PRIVATE.id+'" marked as REJECTED: '+e.message);PRIVATE.status="rejected",reject(e)})},SUPER_STATIC_PRIVATE.isLegacyBrowser?120:0)})),PRIVATE.component},PUBLIC.getStatus=function(){return PRIVATE.status},STATIC_PRIVATE.instances[PRIVATE.id]=PUBLIC};return STATIC_PUBLIC.list=function(){return Object.keys(STATIC_PRIVATE.instances)},STATIC_PUBLIC.get=function(id){return STATIC_PRIVATE.instances[id]||null},STATIC_PUBLIC}(STATIC_PRIVATE,void 0),STATIC_PRIVATE.processOrder=function(dependencies,calleddBy){var order=[],command=this.command,operationDependencies=[].concat(dependencies),dependency,procedure,current,exception;if(!(dependencies="string"==typeof dependencies?[dependencies]:dependencies)||!Array.isArray(dependencies))return exception=new PKG.ApplicationError("[Invalid "+command+" order] : "+dependencies),STATIC_PRIVATE.log(exception,dependencies),PKG.Promise.reject(exception);for(dependencies=[].concat(dependencies);dependencies.length;){dependency=dependencies.shift();try{STATIC_PRIVATE.aliases[dependency]&&(STATIC_PRIVATE.log.warn('Using component "'+STATIC_PRIVATE.aliases[dependency]+'" as dependency. ("'+dependency+'" is an alias)'),dependency=STATIC_PRIVATE.aliases[dependency]),dependency=new STATIC_PRIVATE.Dependency(dependency)}catch(e){return e.message="[Invalid "+command+' order] : Error registering dependency "'+dependency+'" > '+e.message+calleddBy,exception=new PKG.ApplicationError(e),STATIC_PRIVATE.log(e,dependencies),PKG.Promise.reject(e)}(current=dependency[command]()).id=dependency.id,order.push(dependency[command]())}return procedure=new PKG.Promise(function(resolve,reject){var done=!1,timer;STATIC_PRIVATE.settings.operationTimeout&&STATIC_PRIVATE.settings.operationTimeout!==1/0&&(timer=setTimeout(function(){var exception;done=!(timer=null),exception=new PKG.ApplicationError(command+" operation for "+operationDependencies.join(", ")+" REJECTED by timeout ("+STATIC_PRIVATE.settings.operationTimeout+"ms)."+(calleddBy||"")),reject(exception)},STATIC_PRIVATE.settings.operationTimeout)),PKG.Promise.all(order).then(function(args){if(!done){var pkg={},currentPromise,currentValue;for(timer=timer&&clearTimeout(timer);order.length;)currentPromise=order.shift(),currentValue=args.shift(),pkg[currentPromise.id]=currentValue;resolve(pkg)}}).then(null,function(e){if(!done)throw timer=timer&&clearTimeout(timer),reject(e),e})})},STATIC_PRIVATE.IncluderSettings=function IncluderSettings(customSettings,currentInstance){if(!(currentInstance instanceof Includer))throw new Error('Includer is a class. You must use the operator "new".');if(void 0!==customSettings&&"[object Object]"!=Object.prototype.toString.call(customSettings))throw new Error("Invalid settings. You must pass an object as first parameter.");if(void 0!==(customSettings=customSettings||{}).targetDocument&&9!=customSettings.targetDocument.nodeType)throw new Error("Invalid targetDocument. It must be a pointer to an HTMLDocument.");if(void 0!==customSettings.baseURL&&("string"!=typeof customSettings.baseURL||!customSettings.baseURL))throw new Error("Invalid baseURL. It must be an URI string.");this.targetDocument=customSettings.targetDocument||document,this.baseURL=customSettings.baseURL||this.targetDocument.location.href.replace(/[^\/]+$/,"")},(STATIC_PUBLIC=Includer=function Includer(customSettings){var PRIVATE={},PUBLIC=this,customSettings;PRIVATE.settings=new STATIC_PRIVATE.IncluderSettings(customSettings,this),PRIVATE.includes={},PRIVATE.monitorInclude=function(include){return new PKG.Promise(function(resolve,reject){include.then(resolve,reject)})},PRIVATE.getIncludes=function(resources){var promises=[],current,uri,uri;for(resources=Array.prototype.slice.call(resources);resources.length;)uri=(current=resources.shift()).webLocation.getComponent("uri"),uri=PRIVATE.includes[uri],uri=new STATIC_PRIVATE.Resource(current,PRIVATE.settings.targetDocument),promises.push(uri.promise);return promises},STATIC_PRIVATE.getDefinition=STATIC_PRIVATE.processOrder.bind({command:"fetch",returns:"definition"}),STATIC_PRIVATE.getFactory=STATIC_PRIVATE.processOrder.bind({command:"load",returns:"factory"}),STATIC_PRIVATE.getComponent=function(dependencies,success,error,callerLine){var components=[],includes=[],originalDependencies=[],invalidDependencies=[],dependenciesValuesList=[],operation,current;if(!dependencies||"string"!=typeof dependencies&&"[object Array]"!=Object.prototype.toString.call(dependencies))throw new PKG.ApplicationError("Invalid dependencies");if("string"==typeof dependencies&&(dependencies=[dependencies]),void 0!==success&&"function"!=typeof success)throw new PKG.ApplicationError("Invalid success callback. If declared, the second parameter must be a function.");if(void 0!==error&&(void 0===success||"function"!=typeof error))throw new PKG.ApplicationError("Invalid error callback. If declared, the third parameter must be a function.");for(dependencies=Array.prototype.slice.call(dependencies);dependencies.length;)(current=dependencies.shift())&&"string"==typeof current&&STATIC_PRIVATE.EXP_COMPONENT_ID.test(current)?(current=current.replace(/^\s*|\s*$/gm,""),originalDependencies.push(current),(/^https?:/.test(current)?includes:components).push(current)):invalidDependencies.push(current);if(invalidDependencies.length)throw new PKG.ApplicationError("There are invalid dependencies required. Passed values are: "+invalidDependencies.join(","));return operation=STATIC_PRIVATE.processOrder.call({command:"require",returns:"component"},components,callerLine).then(function(pkg){return includes.length?PUBLIC.include(includes).then(function(resources){return pkg.resources=resources,pkg}):pkg}).then(null,function(e){if(callerLine&&(e.message+=callerLine),error)try{error(e)}catch(callbackException){e.message+=" >> Exception found on the error callback of a rejected require operation: "+callbackException.message,STATIC_PRIVATE.log.error(e)}return PKG.Promise.reject(e)}).then(function(pkg){var output={},current,resourceIndex;for(pkg=PKG.util.copy(pkg);originalDependencies.length;)current=originalDependencies.shift(),pkg.resources&&/^https?:/.test(current)?(resourceIndex=includes.indexOf(current),dependenciesValuesList.push(-1<resourceIndex?pkg.resources[resourceIndex]:null)):(current=STATIC_PRIVATE.aliases[current]||current,dependenciesValuesList.push(pkg[current]));for(current in pkg)pkg.hasOwnProperty(current)&&"resources"!=current&&(current=STATIC_PRIVATE.aliases[current]||current,PKG.util.parseKey(current,pkg[current],output));return output}).then(function(pkg){if(success)try{success.apply(pkg,dependenciesValuesList)}catch(successException){if(successException.message="A require operation was successfully resolved but its callback raised an exception: "+successException.message,callerLine&&(successException.message+=callerLine),error)try{error(successException)}catch(errorException){successException.message+=" :: Exception also found on the error callback of a rejected require operation: "+errorException.message,STATIC_PRIVATE.log.error(successException),window.console.error(successException)}else successException.message+=" >> No error callback was defined to deal with it.",STATIC_PRIVATE.log.error(successException),window.console.error(successException)}return pkg})},PUBLIC.register=function(id,settings){if("string"!=typeof id||!STATIC_PRIVATE.EXP_COMPONENT_ID.test(id))throw new PKG.ApplicationError('Invalid component id: "'+id+'". It must be a string.');if("[object Object]"!=Object.prototype.toString.call(settings))throw new PKG.ApplicationError('Invalid settings to register component "'+id+'": '+Object.prototype.toString.call(settings));STATIC_PRIVATE.aliases[id]&&(STATIC_PRIVATE.log.warn('Registering component "'+id+'" as "'+STATIC_PRIVATE.aliases[id]+'" (given value is an alias)'),id=STATIC_PRIVATE.aliases[id]);var dependency=new STATIC_PRIVATE.Dependency(id),status=dependency.getStatus();if(/resolved|rejected/.test(status))throw new PKG.ApplicationError('Component "'+id+" already specifyed and "+status);dependency.specify(settings)},PUBLIC.define=function(){var args=Array.prototype.slice.call(arguments),id=args[0],dependencies=3<=args.length&&args[1]instanceof Array?args[1]:null,args=dependencies?args[2]:args[1],invalidDependencies=[],imports=[],includes=[],current;if(!id||"string"!=typeof id||dependencies&&!(dependencies instanceof Array)||"function"!=typeof args)throw new Error("Invalid definition aproach. You must pass the component id as first parameter, an array of dependencies as the second one (optional) and the the factory function as the final one.");if(dependencies&&dependencies.length){for(dependencies=[].concat(dependencies);dependencies.length;)((current=dependencies.shift())&&"string"==typeof current&&STATIC_PRIVATE.EXP_COMPONENT_ID.test(current)?/^https?:/.test(current)?includes:imports:invalidDependencies).push(current);if(invalidDependencies.length)throw new Error('There are invalid dependencies on the definition of "'+id+'". Passed values are: '+invalidDependencies.join(","))}PUBLIC.register(id,{includes:includes,imports:imports||[],exports:args})},PUBLIC.include=STATIC_PRIVATE.getFiles=function(dependencies,success,error){if(!dependencies||"string"!=typeof dependencies&&"[object Array]"!=Object.prototype.toString.call(dependencies))throw new PKG.ApplicationError("Invalid dependencies");if(!(dependencies="string"==typeof dependencies?[dependencies]:dependencies).length)throw new PKG.ApplicationError("Invalid dependencies list. You must declare, at least, on valid dependency to justify this requre() operation.");if(void 0!==success&&"function"!=typeof success)throw new PKG.ApplicationError("Invalid success callback. If declared, the second parameter must be a function.");if(void 0!==error&&(void 0===success||"function"!=typeof error))throw new PKG.ApplicationError("Invalid error callback. If declared, the third parameter must be a function.");var baseURL=PRIVATE.settings.baseURL,declarations,includes;try{declarations=STATIC_PRIVATE.sanitizeDeclarations(dependencies,baseURL,PRIVATE.settings),includes=PRIVATE.getIncludes(declarations)}catch(e){return PKG.Promise.reject(e)}return PKG.Promise.all(includes).then(function(values){if(STATIC_PRIVATE.log("Resources included.",values),success)try{success.apply(includes,values)}catch(e){return e.message="The include operation was successfull, but an exception was found on the callback function: "+e.message,STATIC_PRIVATE.log(e),PKG.Promise.reject(e)}return values}).then(null,function(reason){if(reason.message="[Including procedure failed] » "+reason.message,STATIC_PRIVATE.log(reason),error)try{error.call(includes,reason)}catch(e){return e.message="The include operation was rejected and an exception was ALSO found on the callback function for errors: "+e.message,STATIC_PRIVATE.log(e),PKG.Promise.reject(e)}return PKG.Promise.reject(reason)})},PUBLIC.fetch=STATIC_PRIVATE.getDefinition,PUBLIC.load=function(dependencies){var callerLine;if((dependencies=dependencies&&"string"==typeof dependencies?[dependencies]:dependencies)&&Array.isArray(dependencies))return callerLine=(callerLine=STATIC_PRIVATE.tpn?PKG.util.getCallerLine():null)?" >> called by "+callerLine.replace(/.*?at/,""):"",STATIC_PRIVATE.getFactory(dependencies,callerLine).then(function(factories){return STATIC_PRIVATE.log("Load operation resolved for "+dependencies.join(" ,")+"."),factories}).then(null,function(e){return STATIC_PRIVATE.log.error(e.message),PKG.Promise.reject(e)});throw new PKG.ApplicationError("Invalid dependencies list to load: "+dependencies)},PUBLIC.require=function(dependencies,success,error){var callerLine;if(!(dependencies=dependencies&&"string"==typeof dependencies?[dependencies]:dependencies)||!Array.isArray(dependencies))throw new PKG.ApplicationError("Invalid dependencies list to load: "+dependencies);if(void 0!==success&&"function"!=typeof success)throw new PKG.ApplicationError("Invalid success callback. If declared, the second parameter must be a function.");if(void 0===error||void 0!==success&&"function"==typeof error)return STATIC_PRIVATE.log("New require operation: "+dependencies.join(", ")),callerLine=(callerLine=STATIC_PRIVATE.tpn?PKG.util.getCallerLine():null)?" >> called by "+callerLine.replace(/.*?at/,""):"",STATIC_PRIVATE.getComponent(dependencies,success,error,callerLine).then(function(factories){return STATIC_PRIVATE.log("Require operation resolved for "+dependencies.join(" ,")+"."),factories}).then(null,function(e){return STATIC_PRIVATE.log.error(e.message),PKG.Promise.reject(e)});throw new PKG.ApplicationError("Invalid error callback. If declared, the third parameter must be a function.")},PUBLIC.registerFetchedComponents=function(components){if("[object Object]"!=Object.prototype.toString.call(components))throw new Error("Invalid components to register. It must be an object.");var id,dependency,definition;for(id in"object"!=typeof components&&STATIC_PRIVATE.log.error(new Error("Invalid object passed to resolveComponents().")),components)if(components.hasOwnProperty(id)&&(definition=components[id],!(dependency=new STATIC_PRIVATE.Dependency(id)).isSpecified()))try{dependency.specify({registryOrigin:"IncluderBE",events:definition.events||null,includes:definition.includes,imports:definition.depends,exports:definition.delivers||[]})}catch(e){e.message='Error registering prefetched component "'+id+'": '+e.message,STATIC_PRIVATE.log.error(e)}},PUBLIC.rejectComponents=function(list,motive){var current;if(!list||"string"!=typeof list&&!Array.isArray(list))return STATIC_PRIVATE.log.warn("Invalid list of component ids to reject. Ignoring operation."),PUBLIC;for(void 0===motive||motive&&(motive instanceof Error||"string"==typeof motive)||(STATIC_PRIVATE.log.warn("Invalid motive to reject components. Usign default message."),motive="Component rejected at RunTime."),"string"==typeof list&&(list=[list]),motive instanceof Error&&(motive=motive.message);list.length;)(current=list.shift())&&"string"==typeof current?(current=new STATIC_PRIVATE.Dependency(current)).refuse(motive):STATIC_PRIVATE.log.warn("Invalid component id to reject. Ignoring it.")},PUBLIC.getTargetDocument=function(){return PRIVATE.settings.targetDocument},PUBLIC.events={on:STATIC_PRIVATE.events.on.bind(STATIC_PRIVATE.events),off:STATIC_PRIVATE.events.off.bind(STATIC_PRIVATE.events),once:STATIC_PRIVATE.events.once.bind(STATIC_PRIVATE.events)},((customSettings=STATIC_PRIVATE.settings.reject)&&"string"==typeof customSettings||Array.isArray(customSettings))&&(customSettings="string"==typeof customSettings?[customSettings]:customSettings).length&&PUBLIC.rejectComponents(customSettings,'Component rejected by special queryString param "includer.reject".')}).init=function(){var customSettings,current,submodule;if(STATIC_PRIVATE.log("Starting up."),1==STATIC_PRIVATE.documentLocation.getParam("tpn")){if(customSettings=STATIC_PRIVATE.documentLocation.queryParams("includer"))for(current in customSettings)customSettings.hasOwnProperty(current)&&void 0!==STATIC_PRIVATE.settings[current]&&(STATIC_PRIVATE.log.warn("Forcing custom setting: "+current+" = "+customSettings[current]+" (instead of "+STATIC_PRIVATE.settings[current]+")"),/^\d+$/.test(customSettings[current])?customSettings[current]=parseInt(customSettings[current],10):/true|false/.test(customSettings[current])&&(customSettings[current]=/true/.test(customSettings[current])),STATIC_PRIVATE.settings[current]=customSettings[current]);(void 0!==STATIC_PRIVATE.settings.log&&"boolean"==typeof STATIC_PRIVATE.settings.log||"number"==typeof STATIC_PRIVATE.settings.log)&&STATIC_PRIVATE.console.setVerbosityLevel(STATIC_PRIVATE.settings.log)}if(window.fetch&&!/mod\.fetch\.env=[^&]+/.test(location.search))try{(submodule=new STATIC_PRIVATE.Dependency("mod.fetch")).specify({exports:function(){return STATIC_PRIVATE.log('Delivering "mod.fetch" as submodule of IncluderFE.'),window.fetch}})}catch(e){STATIC_PRIVATE.log.warn('Exception registering submodule "mod.fetch" as a component: '+e.message)}STATIC_PRIVATE.ws=new STATIC_PRIVATE.EndPoint,setTimeout(function(){STATIC_PRIVATE.runningAtCompileTime=!1},0),delete STATIC_PUBLIC.init},STATIC_PUBLIC.version=STATIC_PRIVATE.version,STATIC_PUBLIC.setVerbosityLevel=STATIC_PRIVATE.console.setVerbosityLevel,STATIC_PUBLIC.setHostAlias=PKG.WebLocation.setHostAlias,STATIC_PUBLIC.setComponentAliases=function(definitions){if("[object Object]"!=Object.prototype.toString.call(definitions))throw new Error("Invalid definitions for aliases. It must be an object.");var errors=[],current,id,component,aliases;for(id in definitions)if(definitions.hasOwnProperty(id))if(id&&"string"==typeof id)if(aliases=definitions[id],Array.isArray(aliases)&&aliases.length)for(aliases=[].concat(aliases);aliases.length;)(current=aliases.shift())&&!STATIC_PRIVATE.aliases[current]&&(STATIC_PRIVATE.aliases[current]=id);else errors.push("Invalid list of aliases: "+Object.prototype.toString.call(aliases));else errors.push("Invalid ID: "+id);return errors.length&&STATIC_PRIVATE.log.warn("Error defining some aliases >>> "+errors.join(" \n")),STATIC_PUBLIC},STATIC_PUBLIC.setForeignLoader=function(id,handler,filter){if(!STATIC_PRIVATE.EXP_COMPONENT_ID.test(id))throw new PKG.ApplicationError("Invalid id for a foreign loader: "+id+".");if("function"!=typeof handler)throw new PKG.ApplicationError("Invalid handler for a foreign loader. It must be a function.");if(void 0!==filter&&!(filter instanceof RegExp))throw new PKG.ApplicationError("Invalid filter for a foreign loader. It must be an instance of RegExp.");if(STATIC_PRIVATE.foreignLoaders||(STATIC_PRIVATE.foreignLoaders={}),STATIC_PRIVATE.foreignLoaders[id])throw new PKG.ApplicationError("This foreign loader is already defined: "+id+".");STATIC_PRIVATE.foreignLoaders[id]={filter:filter,handler:handler},STATIC_PRIVATE.log.warn("New foreign loader defined: "+id)},STATIC_PUBLIC.setForeignImporter=function(id,handler,filter){if(!STATIC_PRIVATE.EXP_COMPONENT_ID.test(id))throw new PKG.ApplicationError("Invalid id for a foreign importer: "+id+".");if("function"!=typeof handler)throw new PKG.ApplicationError("Invalid handler for a foreign importer. It must be a function.");if(void 0!==filter&&!(filter instanceof RegExp))throw new PKG.ApplicationError("Invalid filter for a foreign importer. It must be an instance of RegExp.");if(STATIC_PRIVATE.foreignImporters||(STATIC_PRIVATE.foreignImporters={}),STATIC_PRIVATE.foreignImporters[id])throw new PKG.ApplicationError("This foreign importer is already defined: "+id+".");return STATIC_PRIVATE.foreignImporters[id]={filter:filter,handler:handler},STATIC_PRIVATE.log.warn("New foreign importer defined: "+id),STATIC_PUBLIC},STATIC_PUBLIC.getDependency=function(id){if(STATIC_PRIVATE.EXP_COMPONENT_ID.test(id))return STATIC_PRIVATE.Dependency.get(id);throw new PKG.ApplicationError('Invalid id: "'+id+'"')},STATIC_PUBLIC.listDependencies=function(){return STATIC_PRIVATE.Dependency.list()},STATIC_PUBLIC.getEnvironmentOf=function(id){var output;if(STATIC_PRIVATE.EXP_COMPONENT_ID.test(id))return(output=STATIC_PRIVATE.Dependency.get(id))&&(output=output.getSpecification())&&output.selectedEnvironment||null;throw new PKG.ApplicationError('Invalid component id: "'+id+'"')},STATIC_PUBLIC.getRequestsList=(urls=[],STATIC_PRIVATE.events.on("RESOURCE_REQUESTED",function(url){urls.push(url)}),function(filter){var output=[],i;if(void 0!==filter&&!(filter instanceof RegExp))throw new Error("Invalid filter. If defined, it must be an instance of RegExp");if(void 0===filter)return Array.prototype.slice.call(urls);for(i=0;i<urls.length;i++)filter.test(urls[i])&&output.push(urls[i]);return output}),STATIC_PUBLIC.open=function(scopeHandler){if(STATIC_PRIVATE.locked)throw new Error('This namespace is locked. You can only "use" this namespace by the corresponding method now.');if("function"!=typeof scopeHandler)throw new Error("Invalid scopeHandler. It must be a function.");try{STATIC_PRIVATE.log("Openning namespace.."),scopeHandler.call(STATIC_PUBLIC,STATIC_PRIVATE,PROTECTED,STATIC_PUBLIC)}catch(e){e.message="[Exception caught on extension] > "+e.message,STATIC_PRIVATE.log(e)}return STATIC_PUBLIC},STATIC_PUBLIC.use=function(scopeHandler){if("function"!=typeof scopeHandler)throw new Error("Invalid scopeHandler. It must be a function.");try{STATIC_PRIVATE.log("Using namespace..."),scopeHandler.call(STATIC_PUBLIC,PKG.util.clone(STATIC_PRIVATE.PROTECTED),PKG.util.clone(STATIC_PUBLIC))}catch(e){e.message="[Exception caught using this namespace] > "+e.message,STATIC_PRIVATE.log(e)}return STATIC_PUBLIC},STATIC_PUBLIC.lockNamespace=function(){return STATIC_PRIVATE.locked=!0,STATIC_PUBLIC},STATIC_PUBLIC.getInstances=function(){var instances=[],current;for(current in STATIC_PRIVATE.instances)STATIC_PRIVATE.instances.hasOwnProperty(current)&&instances.push(current);return instances},STATIC_PUBLIC.reset=function(deconstruct){var current;if(deconstruct)STATIC_PRIVATE.instances={};else for(current in STATIC_PRIVATE.instances)if(STATIC_PRIVATE.instances.hasOwnProperty(current))try{"function"==typeof(current=STATIC_PRIVATE.instances[current]).destroy&&current.destroy()}catch(error){error.message="[Exception caught destrying Includer instance] >> "+error.message,STATIC_PRIVATE.log(error)}finally{delete STATIC_PRIVATE.instances[current]}},STATIC_PUBLIC.getVersion=function(){return STATIC_PRIVATE.version},STATIC_PUBLIC.toString=function(){return"[constructor Includer]"},STATIC_PUBLIC.setLogHandler=STATIC_PRIVATE.console.setHandler,STATIC_PUBLIC)}(this),(CURRENT_SCOPE=this).Includer.open(function(SUPER_PRIVATE,SUPER_PROTECTED,SUPER_PUBLIC){var PKG=SUPER_PROTECTED.pkg,log=SUPER_PROTECTED.log,events=SUPER_PROTECTED.events,settings=SUPER_PRIVATE.settings,STATIC_PRIVATE={};function fetch(link,retries,interval,resolve,reject){for(var doc=link.ownerDocument,i=doc.styleSheets.length,current;i;)if((current=doc.styleSheets[--i])&&(current.href==link.href||(current.ownerNode||current.owningElement).innerHTML==link.innerHTML))return void resolve(current);retries?(retries--,setTimeout(function(){fetch(link,retries,interval,resolve,reject)},interval)):reject(new Error("Stylesheet computation rejected by timeout ("+interval+"ns)."))}STATIC_PRIVATE.includes={},STATIC_PRIVATE.query=function(url,doc,timeout,corsAllowed){var cache=STATIC_PRIVATE.includes[doc.location.href],cache;return(cache=cache||(STATIC_PRIVATE.includes[doc.location.href]={}))[url]||(cache[url]=new PKG.Promise(function(resolve,reject){var include=STATIC_PRIVATE.getFromDOM(url,doc);include?resolve(include):settings.injection&&corsAllowed?STATIC_PRIVATE.injectStyle(url,doc,timeout,resolve,reject):STATIC_PRIVATE.createLink(url,doc,timeout,resolve,reject)}).then(STATIC_PRIVATE.validateStyleComputation).then(STATIC_PRIVATE.validateSource).then(function(r){return events.emit("RESOURCE_RESOLVED",url),r},function(e){throw events.emit("RESOURCE_REJECTED",url),e})),cache[url]},STATIC_PRIVATE.getFromDOM=function(url,doc){for(var links=doc.getElementsByTagName("link"),targetID=new PKG.WebLocation(url).getID(),i=0,currentLink,currentID;i<links.length;i++)if(((currentLink=links[i])["data-includerID"]||/stylesheet/i.test(currentLink.rel)&&currentLink.href)&&(currentID=(currentID=currentLink.getAttribute("data-includerID"))||(currentLink["data-includerID"]=new PKG.WebLocation(currentLink.href).getID()))==targetID)return currentLink},STATIC_PRIVATE.injectStyle=function(url,doc,timeout,success,error){var done=!1,requisitionTimer,requisitionTimer=setTimeout(function(){done=!0,requisitionTimer=null,error(new Error("Timeout: "+timeout+"ms."))},timeout);PKG.fetch(url).then(function(response){return requisitionTimer=requisitionTimer&&clearTimeout(requisitionTimer),PKG.handleFetchResponse(response)}).then(function(data){done=!0;var element=document.createElement("style"),target=doc.head||doc.getElementsByTagName("head")[0];timeout=timeout||0,element.rel="stylesheet",element.type="text/css",element["data-includerInjection"]=url,element["data-includerID"]=new PKG.WebLocation(url).getID(),data=data.replace(/url\(['"]?(?!data)([^'"]+?)['"]?\)/g,function(wholeMatch,path){var uri;return/^(https?|\/\/)/.test(path)?'url("'+path+'")':'url("'+(uri=PKG.WebLocation.convertPathToAbsolute(path,url))+'")'}),data+="\n/*# sourceURL="+url+" */";try{element.styleSheet?(SUPER_PRIVATE.isLegacyBrowser&&(data=data.replace(/src:[^ ,]+?\.eot"\);\n?/gi,function(match){return""})),element.styleSheet.cssText=data):element.appendChild(document.createTextNode(data)),target.appendChild(element),success(element)}catch(e){e.message="[InjectionError] : "+e.message,error(e)}}).catch(function(e){requisitionTimer=requisitionTimer&&clearTimeout(requisitionTimer),done||(e instanceof Error&&(e.message="[RequestError] : "+e.message.substr(0,140)),error(e)),done=!0}),events.emit("RESOURCE_REQUESTED",url)},STATIC_PRIVATE.createLink=function(url,doc,timeout,success,error){var link=doc.createElement("link"),doc=doc.head||doc.getElementsByTagName("head")[0],timer;return timeout=timeout||0,link.rel="stylesheet",link.type="text/css",link.readyState?link.onreadystatechange=function(){var tag=this;"loaded"!=tag.readyState&&"complete"!=tag.readyState||(tag.onreadystatechange=null,setTimeout(function(){success(tag)},0))}:link.onload=function(data){var tag=this;timer=timer&&clearTimeout(timer),setTimeout(function(){success(tag)},0)},link.onerror=function(e){timer=timer&&clearTimeout(timer),e instanceof Error||(e=new Error("Request error.")),error(e)},link.href=url,doc.appendChild(link),events.emit("RESOURCE_REQUESTED",url),timer=setTimeout(function(){link.onload=link.onreadystatechange=null,error(new Error("Include rejected by request timeout ("+timeout+"ms)."))},timeout),link},STATIC_PRIVATE.validateSource=function(link){var rules;if(STATIC_PRIVATE.skipValidation)return!0;try{link.cssRules||link.rules?rules=link.cssRules||link.rules:link.sheet&&link.sheet.rules?rules=link.sheet.rules:link.styleSheet&&link.styleSheet.cssText?rules=link.styleSheet.cssText:link.innerHTML&&link.innerHTML&&(rules=link.innerHTML),rules&&rules.length||log.warn("No rules found in this styleSheet: "+link.href)}catch(e){STATIC_PRIVATE.skipValidation=!0,log.warn("This browser do not allow rule detection for CSS validation. Reading styleSheet: "+link.href)}},STATIC_PRIVATE.validateStyleComputation=function(link){return new PKG.Promise(function(resolve,reject){var retries=5,interval=100;fetch(link,5,100,resolve,reject)})},SUPER_PRIVATE.Loaders.set("css",function(resolve,reject){var declaration=this.declaration,url=declaration.webLocation.getComponent("uri"),timeout=declaration.timeout;STATIC_PRIVATE.query(url,declaration.targetDocument,timeout,declaration.corsAllowed).then(resolve,reject)})}),function(CURRENT_SCOPE){CURRENT_SCOPE.Includer.open(function(SUPER_PRIVATE,SUPER_PROTECTED,SUPER_PUBLIC){var PKG=SUPER_PROTECTED.pkg,events=SUPER_PROTECTED.events,log=SUPER_PROTECTED.log,includer=new SUPER_PUBLIC,STATIC_PRIVATE={FORMATS_PREFERENCE_ORDER:["zaz","system","amd","global"]};STATIC_PRIVATE.PREFERED_EVALUATING_METHOD=/file/.test(location.protocol)?"appendScript":"xhr",STATIC_PRIVATE.EXP_TRIM=/^\s*|\s*$/gm,STATIC_PRIVATE.EXP_STRIP_COMMENTS=/\/\*[\s\S]*?\*\/|\/\/.*?\n/g,STATIC_PRIVATE.EXP_EXPLICIT_FORMAT_DECLARATION=/^(?:\(\s*function\s+\(.*?\)\s*\{\s*)?['"]format\s+(amd|es6|global|cjs)/,STATIC_PRIVATE.EXP_STRICT_CODE=/^(?:\(\s*function\s+\(.*?\)\s*\{\s*)?['"]use\s+strict/i,STATIC_PRIVATE.DEFAUTL_REQUEST_TIMEOUT=5e3,STATIC_PRIVATE.NOOP=function NOOP(){},STATIC_PRIVATE.includes={},STATIC_PRIVATE.scopeFilter=function(id){return function(pkg){return pkg[id]}},STATIC_PRIVATE.getNamespaces=function(namespaces){var promises=[],list=namespaces,current,tokens,scope,id;for(Array.isArray(list)||(list=[list]),list=Array.prototype.slice.call(list);list.length;)scope=(tokens=(current=list.shift()).split(":"))[0],id=tokens[1],1==tokens.length&&(scope=/^window\./.test(tokens[0])?"global":"includer",id=tokens[0]),/global|polyfill/.test(scope)?promises.push(PKG.Promise.resolve(STATIC_PRIVATE.getGlobalNamespace(id,window))):"zaz"===scope?promises.push(STATIC_PRIVATE.getZazNamespace(id)):promises.push(includer.require(id).then(STATIC_PRIVATE.scopeFilter(id)));return PKG.Promise.all(promises).then(function(values){if(1==values.length)return values;for(var output={},list=Array.prototype.slice.call(namespaces),i,current,i=0;i<list.length;i++)output[current=(current=list[i]).replace(/.+?\:/,"")]?log.warn("Namespace duplicated on file definition: "+current+". Keeping the first one declared."):output[current]=values[i];return output})},STATIC_PRIVATE.getZazNamespace=function(declaration,targetWindow){return new PKG.Promise(function(resolve,reject){"object"!=typeof window.zaz||"function"!=typeof window.zaz.use?reject(new Error("ZAZ framework not loaded.")):window.zaz.use(function(pkg){function specficationListener(spec){timer=timer&&clearTimeout(timer),done?log.warn("Factory defined after rejected by timeout: "+declaration):(done=!0,resolve(component.require))}var timer,done,component,component=new SUPER_PRIVATE.Dependency(declaration);component.isSpecified()&&"IncluderBE"!==component.getSpecification().registryOrigin?resolve(component.require):(timer=setTimeout(function(){done=!0,timer=null,reject(new Error('Timeout reached for definition of namespace "'+declaration+'" ('+STATIC_PRIVATE.DEFAUTL_REQUEST_TIMEOUT+"ms).")),pkg.includer.events.off("COMPONENT_SPECIFIED:"+declaration,specficationListener)},STATIC_PRIVATE.DEFAUTL_REQUEST_TIMEOUT),pkg.includer.events.once("COMPONENT_SPECIFIED:"+declaration,specficationListener))})})},STATIC_PRIVATE.getGlobalNamespace=function(declaration,targetWindow){for(var levels=(declaration=declaration.replace(/\[['"](?=[^\]]+)/g,".").replace(/['"]\]/g,"")).split("."),lastLevel=targetWindow,current;levels.length;){if(void 0===lastLevel[current=levels.shift()])return;lastLevel=lastLevel[current]}return log("Global namespace found: "+declaration),lastLevel},STATIC_PRIVATE.getNormalizedException=function(exceptionsList){var output=[],scope,current;for(exceptionsList=[].concat(exceptionsList);exceptionsList.length;){current=exceptionsList.shift();try{output.push(current.scope+" "+current.type+": "+current.data[1])}catch(e){output.push("object"==typeof current&&current instanceof Error?current.message:"No details...")}}return output=new Error(output.join(" >>> "))},STATIC_PRIVATE.evaluate=function(data,format){return new PKG.Promise(function(resolve,reject){var adapters={},definedNamespaces=[],oldGlobalErrorHandler=window.onerror,errorMonitor,globalException,wrapper;switch(format){case"zaz":"object"==typeof window.zaz&&"function"==typeof window.zaz.use?(wrapper=new Function("zaz",data),window.zaz.use(function Includer(pkg){var errors=[],exception;STATIC_PRIVATE.zazConsole||(STATIC_PRIVATE.zazConsole=window.zaz.console||{on:STATIC_PRIVATE.NOOP,off:STATIC_PRIVATE.NOOP}),errorMonitor=function(err){errors.push(err)};try{STATIC_PRIVATE.zazConsole.on("error",errorMonitor),wrapper.call(window,window.zaz)}catch(e){errors.push(e)}finally{try{STATIC_PRIVATE.zazConsole.off("error",errorMonitor)}catch(e){}errors.length?((exception=STATIC_PRIVATE.getNormalizedException(errors)).message=errors.length+" expection(s) caught on evaluation: "+exception.message,reject(exception)):resolve(definedNamespaces)}})):reject(new Error("ZAZ cerebro is not present."));break;case"amd":adapters.define=function(id){try{includer.define.apply(window,Array.prototype.slice.call(arguments)),definedNamespaces.push("amd:"+id)}catch(e){e.message='Exception defining "'+id+'": '+e.message,reject(e)}};try{adapters.require=includer.require,(wrapper=new Function("define","require",data)).call(window,adapters.define,adapters.require),resolve(definedNamespaces)}catch(exc){reject(exc)}return;default:window.onerror=function(message,fileName,lineNumber){return(globalException=new Error(message)).description=message,globalException.fileName=fileName,globalException.lineNumber=lineNumber,!0};try{data&&data.replace(STATIC_PRIVATE.EXP_TRIM,"")&&(window.execScript||function(data){window.eval.call(window,data)})(data),resolve(definedNamespaces)}catch(e){reject(globalException=globalException||e)}finally{window.onerror=oldGlobalErrorHandler}}})},STATIC_PRIVATE.detectFormat=function(code){var code,explicitFormat,code=(code=code.substring(0,500)).replace(STATIC_PRIVATE.EXP_STRIP_COMMENTS,"").replace(STATIC_PRIVATE.EXP_TRIM,""),explicitFormat=STATIC_PRIVATE.EXP_EXPLICIT_FORMAT_DECLARATION.exec(code);return explicitFormat?explicitFormat[1]:/zaz\.use\(/.test(code)?"zaz":"global"},STATIC_PRIVATE.query=function(definition){var url=definition.url,doc=definition.targetDocument,timeout=0<definition.timeout?definition.timeout:STATIC_PRIVATE.DEFAUTL_REQUEST_TIMEOUT,cache=STATIC_PRIVATE.includes[doc.location.href],cache;return(cache=cache||(STATIC_PRIVATE.includes[doc.location.href]={}))[url]||(cache[url]=new PKG.Promise(function(resolve,reject){var done=!1,include,namespaces,DOMisReady,request,current,fallbackTimer,activeLoadListeners,activeErrorListeners;if(definition.events&&"object"==typeof definition.events&&void 0!==definition.events.onBeforeLoad){if(!definition.events.onBeforeLoad||"string"!=typeof definition.events.onBeforeLoad&&"function"!=typeof definition.events.onBeforeLoad)return void reject(new Error('Invalid event handler for "onBeforeLoad".'));"string"==typeof definition.events.onBeforeLoad&&(definition.events.onBeforeLoad=new Function("definition",definition.events.onBeforeLoad));try{definition.events.onBeforeLoad(definition)}catch(e){return e.message='Exception on event "onBeforeLoad" of this include: '+e.message,void reject(e)}}if(definition.namespaces){if("string"!=typeof definition.namespaces&&!Array.isArray(definition.namespaces))return reject(new Error("Invalid namespaces definition: "+definition.namespaces));for("string"==typeof definition.namespaces&&(definition.namespaces=[definition.namespaces]),namespaces=Array.prototype.slice.call(definition.namespaces);namespaces.length;)if(current=namespaces.shift(),(current=/polyfill:(\S+)/i.exec(current))&&(current=current[1],void 0!==(current=STATIC_PRIVATE.getGlobalNamespace(current,window))))return definition.exports=current,void resolve(current)}if(include=STATIC_PRIVATE.getIncluded(url,doc),DOMisReady=!/in/.test(document.readyState),!include&&/zaz\-includer\-fe\/.+?reporter/.test(url))return definition.HTMLElement=null,definition.source="/* no preload definition */",void resolve(definition);if(include){if((definition.HTMLElement=include).getAttribute("data-includerInjection"))return definition.source=include.innerHTML,void resolve(definition);!DOMisReady||include.getAttribute("async")||include.getAttribute("defer")?SUPER_PRIVATE.settings.injection&&definition.corsAllowed?(fallbackTimer=setTimeout(function(){done=!0,fallbackTimer=null,reject(new Error("Request timeout: "+(timeout+300)+"ms."))},timeout+300),PKG.fetch(url).then(function(response){return fallbackTimer=fallbackTimer&&clearTimeout(fallbackTimer),PKG.handleFetchResponse(response)}).then(function(data){done=!0,definition.source=data,resolve(definition)}).catch(function(e){fallbackTimer=fallbackTimer&&clearTimeout(fallbackTimer),done||(e instanceof Error?(e.message="[RequestError] : "+e.message.substr(0,140),reject(e)):console.error(e))})):(activeLoadListeners=include.onload||include.onreadystatechange,include.onload=include.onreadystatechange=function(){if(!this.readyState||"loaded"==this.readyState||"complete"==this.readyState){this.onload=this.onreadystatechange=null,fallbackTimer=fallbackTimer&&clearTimeout(fallbackTimer);try{"function"==typeof activeLoadListeners&&activeLoadListeners.call(include)}catch(e){throw e.message="[foreign onload listener exception] : "+e.message,e}finally{resolve(definition)}}},include.onerror=function(){activeErrorListeners=include.onerror,fallbackTimer=fallbackTimer&&clearTimeout(fallbackTimer);try{"function"==typeof activeErrorListeners&&activeErrorListeners.call(include)}catch(e){throw e.message="[foreign onerror listener exception] : "+e.message,e}finally{reject(new Error("[RequestError] : "+include.src))}},fallbackTimer=window.setTimeout(function(){resolve(definition)},1e3)):resolve(definition)}else STATIC_PRIVATE.getScript(definition,resolve,reject)}).then(function(definition){return definition.namespaces?STATIC_PRIVATE.getNamespaces(definition.namespaces).then(function(exports){return 1==exports.length&&(exports=exports.pop()),definition.exports=exports,definition},function(exception){throw exception.message="Exception fetching namespaces: "+definition.namespaces+": "+exception.message,exception}):(definition.exports=null,definition)}).then(function(definition){if(definition.events&&"object"==typeof definition.events&&void 0!==definition.events.onAfterLoad){if(!definition.events.onAfterLoad||"string"!=typeof definition.events.onAfterLoad&&"function"!=typeof definition.events.onAfterLoad)return PKG.Promise.reject(new Error('Invalid event handler for "onAfterLoad".'));"string"==typeof definition.events.onAfterLoad&&(definition.events.onAfterLoad=new Function("definition",definition.events.onAfterLoad));try{definition.events.onAfterLoad(definition)}catch(e){return e.message='Exception on event "onAfterLoad" of this include: '+e.message,PKG.Promise.reject(e)}}return definition}).then(function(definition){return events.emit("RESOURCE_RESOLVED",definition),definition},function(e){throw events.emit("RESOURCE_REJECTED",definition),e})),cache[url]},STATIC_PRIVATE.getScript=function(definition,success,error){var doc=definition.targetDocument,url=definition.url,timeout=definition.timeout,head,doc,requisitionTimer,request,details,detectedNamespaces,exception,done;SUPER_PRIVATE.settings.injection&&definition.corsAllowed?(requisitionTimer=setTimeout(function(){done=!0,requisitionTimer=null,error(new Error("Timeout: "+timeout+"ms."))},timeout),PKG.fetch(url).then(function(response){return requisitionTimer=requisitionTimer&&clearTimeout(requisitionTimer),PKG.handleFetchResponse(response)}).then(function(data){var format;done=!0;try{format=definition.format||STATIC_PRIVATE.detectFormat(data),data=data+"\n//# sourceURL="+url,void 0!==definition.format&&definition.format!=format&&log({name:"warn",message:"Defined resource format ("+definition.format+") difers from auto-detected one: "+definition.format}),STATIC_PRIVATE.evaluate(data,format).then(function(detectedNamespaces){log(definition.url+" >> evaluated as "+format),definition.namespaces||(detectedNamespaces?definition.namespaces=detectedNamespaces:log.warn("No namespaces were defined or autodetected for this file.")),success(definition)},function(e){e.message="Evaluation error: "+e.message,error(e)})}catch(e){(exception=(exception=e)instanceof Error?exception:new Error("Uncaught exception found on file "+definition.url)).message="[RuntimeError] : "+exception.message,log(exception),error(exception)}}).catch(function(e){requisitionTimer=requisitionTimer&&clearTimeout(requisitionTimer),done||(e instanceof Error?(e.message="[RequestError] : "+e.message,error(e)):console.error(e)),done=!0})):(head=doc.getElementsByTagName("head")[0],doc=doc.createElement("script"),(definition.HTMLElement=doc).type="text/javascript",doc.charset="utf-8",doc.onload=doc.onreadystatechange=function(){var details;this.readyState&&"loaded"!=this.readyState&&"complete"!=this.readyState||(this.onload=this.onreadystatechange=null,details={src:this.src},setTimeout(function(){try{success.call(definition,definition)}catch(exception){exception.message="[Exceptions caught triggering the success handler for request "+details.src+" » "+exception.message,details.exception=exception,error.call(definition,exception)}},0))},error&&(doc.addEventListener?doc.addEventListener("error",function(event){details={src:url,evt:event},error.call(details,details)},!0):error&&doc.attachEvent&&doc.attachEvent("onerror",function(event){details={src:url,evt:event},error.call(details,details)},!0)),doc.src=url,head.appendChild(doc)),events.emit("RESOURCE_REQUESTED",url)},STATIC_PRIVATE.getIncluded=function(url,doc){for(var links=doc.getElementsByTagName("script"),targetID=new PKG.WebLocation(url).getID(),i=0,currentScript,currentID;i<links.length;i++)if(!(currentScript=links[i]).src&&currentScript.getAttribute("data-includerInjection")&&(currentID=currentScript["data-includerID"]=new PKG.WebLocation(currentScript.getAttribute("data-includerInjection")).getID()),(currentID=currentScript.src?(currentID=currentScript.getAttribute("data-includerID"))||(currentScript["data-includerID"]=new PKG.WebLocation(currentScript.src).getID()):currentID)&&(currentID==targetID||/zaz(\.inline)?([\.\-]min)?\.js/.test(currentID)&&/zaz(\.inline)?([\.\-]min)?\.js/.test(targetID)))return currentScript},SUPER_PRIVATE.Loaders.set("js",function(resolve,reject){var declaration=this.declaration,url=declaration.webLocation.getComponent("uri"),timeout=declaration.timeout;STATIC_PRIVATE.query(this.declaration).then(function(resource){return resolve(resource),resource},reject)})})}(this),this.Includer.init(),this.Includer.lockNamespace()}.call(scope)}catch(e){console.error("context:including includerjs failed!",e)}if(scope.Includer.setHostAlias("trrsf:static",/https?:\/\/s\d\.trrsf\.com(\.br)?(\/update\-\d+)?(?=\/)/),debugging&&pkg.console.check(!0,'Host alias "trrsf" defined for static files.'),scope.Includer.setHostAlias("trrsf:dynamic",/https?:\/\/p\d\.trrsf\.com(\.br)?(\/update\-\d+)?(?=\/)/),debugging&&pkg.console.check(!0,'Host alias "trrsf" defined for dynamic files.'),scope.Includer.setHostAlias("dsv-fe",/https?:\/\/dsv-fe(0\d-mia\.dev)?.terra\.com/),debugging&&pkg.console.check(!0,'Host alias "dsv-fe" defined for static files.'),scope.Includer.setHostAlias("preview-fe",/https?:\/\/.*?preview-fe(\.tpn)?.terra\.com/),debugging&&pkg.console.check(!0,'Host alias "preview-fe" defined for static files.'),scope.Includer.setComponentAliases({"mod.jQuery":["jQuery"],"mod.jQuery.trrListCarousel":["jQuery.trrListCarousel"],"mod.globalStorage":["GlobalStorage"],"mod.realtime":["Realtime"],"mod.googlePlus":["GooglePlus"],"mod.facebook":["Facebook"],"mod.xRequest":["Request"],"mod.xact":["XAct"],"mod.datetime":["Datetime"],"mod.nunjucks":["nunjucks"],"mod.terraTV":["TerraTV"],"mod.viewable":["zaz-mod-viewable"],"mod.adManager":["mod.manager"]}),debugging&&pkg.console.check(!0,"Component aliases defined."),window.modMan&&"object"==typeof window.modMan&&"object"==typeof window.modMan.require&&"function"==typeof window.modMan.require.extensionAt&&"function"==typeof window.modMan.require.extensions&&"object"==typeof window.modMan.library&&"object"==typeof window.modMan.library.extensions&&"function"==typeof window.modMan.library.extensions.get)try{scope.Includer.setForeignLoader("modMan",function(resource,tools){var modManExtensionName;if("object"==typeof resource&&resource.url&&(modManExtensionName=window.modMan.require.extensionAt(resource.url.replace(/=$/,""))))return pkg.console.warn('Delegating request of "'+resource.url+'" to modMan (as extension "'+modManExtensionName+'").'),new tools.Promise(function(resolve,reject){window.modMan.require.extensions({dependencies:[modManExtensionName],success:function(){var extensionDetails=window.modMan.library.extensions.get(modManExtensionName),exports;extensionDetails&&"object"==typeof extensionDetails&&"string"==typeof extensionDetails.namespace?void 0===(exports=tools.getNamespace(extensionDetails.namespace,window))?reject(new Error("Namespace declared by modMan ("+extensionDetails.namespace+') for extension "'+modManExtensionName+'" could not be found.')):resolve(exports):resolve(null)},error:function(e){(e=e instanceof Error?e:new Error(e&&"string"==typeof e?e:"No details...")).message="modMan found an error loading "+resource.url+" >> "+e.message,reject(e)}})})}),pkg.console.log("modMan just defined as a foreign loader.")}catch(e){e.message="Error setting modMan as a foreign loader: "+e.message,pkg.console.error(e)}includer=instance=new scope.Includer,(/tpn=1/.test(location.search)||/zaz-(cerebro|includer(\-fe)?)\/tests/.test(location.pathname))&&(window.Includer=scope.Includer),/includer\.log=(1|true)/.test(location.search)&&scope.Includer.setLogHandler("BROWSERS_CONSOLE"),includer.define("JSON",function(){return window.JSON}),includer.define("mod.nunjucks",function(){return window.nunjucks}),includer.define("Promise",function(){return window.Promise}),includer.define("WebLocation",function(){return pkg.utils.WebLocation});for(var scripts=document.scripts||document.getElementsByTagName("script"),styles=document.styleSheets,EXP_CEREBRO_PATH=/zaz([\.\-](inline|scope))?([\.\-]min)?\.js(\?.*|$)/,EXP_MORPH_PATH=/zaz([\.\-]?(legacy|mob|tab|standalone))?([\.\-]min)?\.css(\?.*|$)/,i,current,total,total=scripts.length,i=0;i<total;i++)if(current=(current=scripts[i])&&current.getAttribute&&current.getAttribute("data-includerInjection")?current.getAttribute("data-includerInjection"):current.src,EXP_CEREBRO_PATH.test(current)){includer.registerFetchedComponents({cerebro:{depends:[],includes:[{path:current,corsAllowed:!0,namespaces:"global:zaz",type:"js"}]}});break}for(total=styles.length,i=0;i<total;i++){if(current=(current=styles[i])&&current.getAttribute&&current.getAttribute("data-includerInjection")?current.getAttribute("data-includerInjection"):current.href,EXP_MORPH_PATH.test(current)){includer.registerFetchedComponents({morph:{depends:[],includes:[{path:current,corsAllowed:!1,type:"css"}]}});break}if(/\/zaz-ui-t360\//.test(current)){includer.registerFetchedComponents({"ui.t360.style":{depends:[],includes:[{path:current,corsAllowed:!1,type:"css"}]}});break}}includer.registerFetchedComponents({zaz:{depends:["cerebro","morph"]},"mod.jQuery":{depends:[],includes:[{path:targetProtocol+(/MSIE [876]/.test(navigator.userAgent)?"//s1.trrsf.com/update-1414603519/fe/zaz-3rd/jquery/jquery-1.11.0.min.js":"//s1.trrsf.com/update-1582053940/fe/zaz-3rd/jquery/jquery-3.4.1.min.js"),namespaces:"global:jQuery",attributes:{charset:"utf-8"},events:{onAfterLoad:"try { window.jQuery.noConflict(true); } catch (e) {e.message = 'Exception triggering noConflict() for jQuery: ' + e.message; if (window.console) {window.console.error(e); } }"},corsAllowed:!1,type:"js"}]},"mod.googlePlus":{depends:[],events:{onBeforeExport:"if (!window.___gcfg) { window.___gcfg = { lang: '"+html+"' }; }"},includes:[{namespaces:"window.gapi",path:"https://apis.google.com/js/client:plusone.js?explicit",corsAllowed:!1,type:"js",events:null}]},"mod.jQuery.trrListCarousel":{depends:["mod.jQuery","zaz"],includes:[{namespaces:"zaz:mod.jQuery.trrListCarousel",path:targetProtocol+"//s1.trrsf.com/update-1414603519/fe/zaz-3rd/jquery.trrlistcarousel/jquery.trrListCarousel-1.0.0.js",corsAllowed:!0,type:"js",events:null}]},"mod.html5":{depends:[],includes:[{namespaces:"global:html5",path:targetProtocol+"//s1.trrsf.com/update-1414603519/fe/zaz-3rd/html5shiv/html5shiv.js",corsAllowed:!0,type:"js",events:null}]},"mod.facebook":{depends:[],meta:null,delivers:[],events:null,includes:[{namespaces:"window.FB",path:targetProtocol+"//connect.facebook.net/"+("BR"==defaultCountries?"pt_BR":"es_LA")+"/sdk.js",corsAllowed:!1,type:"js",events:null}]},"mod.facebookSDK":{depends:[],meta:null,delivers:[],events:null,includes:[{namespaces:"window.FB",path:targetProtocol+"//connect.facebook.net/"+("BR"==defaultCountries?"pt_BR":"es_LA")+"/sdk.js",corsAllowed:!1,type:"js",events:null}]},"mod.promise":{depends:[],meta:null,delivers:[],events:null,includes:[{namespaces:"polyfill:Promise",path:targetProtocol+"//s1.trrsf.com/update-1417451355095/fe/zaz-3rd/promise/promise-2.0.0.min.js",corsAllowed:!0,type:"js",events:null}]}}),pkg.set("includer",instance),pkg.extend(function define(PROTECTED){return instance.define}),pkg.extend(function require(PROTECTED){return instance.require})}),console.check(!0,"Loaded extension includer.js"),zaz.use(function(pkg){"use strict";pkg.define("mod.dictionaryManager",function(){var PRIVATE={},PUBLIC={};return PRIVATE.tmpDictCounter=0,PUBLIC.createDictionary=function(matter){var dictionary=!1,dicts=matter&&matter.dictionaries||[],tmpDict=!1;return(dicts=Array.isArray(dicts)?dicts:[dicts]).forEach(function(cur){"string"==typeof cur?(0!==cur.indexOf("dict.")&&(cur="dict."+cur),dictionary?pkg.getComponent(cur)&&dictionary.extend((new(pkg.getComponent(cur).component())).langs):dictionary=new((dictionary=pkg.getComponent(cur).component)?dictionary():PUBLIC.createNewDictionary(cur,matter))):cur&&!Array.isArray(cur)&&(tmpDict=new(PUBLIC.createNewDictionary(cur,matter)),dictionary?dictionary.extend(tmpDict.langs):dictionary=tmpDict)}),dictionary=!1===dictionary?new(PUBLIC.createNewDictionary(null,matter)):dictionary},PUBLIC.createNewDictionary=function(dictData,matter){return dictData&&dictData.name||(PRIVATE.tmpDictCounter++,dictData={name:matter.name+"-tmpDict-"+PRIVATE.tmpDictCounter,version:"0.0.0",langs:dictData||{pt:{},en:{},es:{}}}),!!Object.keys(dictData).length&&new(pkg.getComponent("dictFactory").component())({name:dictData.name,version:dictData.version,langs:dictData.dictionaries||dictData.langs})},PUBLIC})}),console.check(!0,"Loaded extension dictionary-manager.js"),zaz.use(function templateManagerSetUp(pkg){"use strict";var console=pkg.console;pkg.define("mod.templateManager",["mod.nunjucks","mod.promise"],function(nun,Promise){var nunTags={tags:{blockStart:"{%",blockEnd:"%}",variableStart:"{{",variableEnd:"}}",commentStart:"\x3c!--",commentEnd:"--\x3e"}},NunLoader=nun.Loader.extend({async:!1,getSource:function(name){return{src:name}}}),replaceAccents=function(str){return str.replace(/[áàãâä]/gi,"a").replace(/[éèêë]/gi,"e").replace(/[íìîï]/gi,"i").replace(/[óòõôö]/gi,"o").replace(/[úùûü]/gi,"u").replace(/[ç]/gi,"c")};return function TemplateManager(dict,ref){function fixVariableSyntax(str){return str.replace(/\$\{([^\}]*)\}/gi,"{{$1}}")}var templates={},rawTemplates={},dictionary=dict,env=new nun.Environment(new NunLoader,nunTags);return this.ref=ref,this.dictionary=dictionary,this.setReferences=function(n,e,d){var filters=this.filters,i;for(i in nun=n,env=e,dictionary=d,this.engine=env,filters)"function"==typeof filters[i]&&this.engine.addFilter(i,filters[i])},this.create=function(tplName,tplString){var i=null,tpl=null;if("string"==typeof tplName)tplString=fixVariableSyntax(tplString),templates[tplName]=new nun.Template(tplString,env,env),rawTemplates[tplName]=tplString;else for(i in tplName)"string"==typeof tplName[i]?(tplName[i]=fixVariableSyntax(tplName[i]),templates[i]=new nun.Template(tplName[i],env,env),rawTemplates[i]=tplName[i]):(templates[i]=tplName[i],rawTemplates[i]=tplName[i].tmplStr)},this.get=function(tplName){return templates[tplName]||!1},this.render=function(tplName,tplBinding){var tpl=templates[tplName]||tplName,tplStr=rawTemplates[tplName],df,wc,tmpName="",TmpComp=null,tmpParams=[],params={},componentList={},componentsPromises=[],callback="function"==typeof arguments[arguments.length-1]?arguments[arguments.length-1]:null,tpl=env.render(tplStr,pkg.utils.merge({},rawTemplates,tplBinding||{}));return this.ref&&this.ref.l10n?tpl=this.ref.l10n.format(tpl):this.dictionary&&(tpl=this.dictionary.format(tpl)),(wc=pkg.context.browser.get("legacy")?(wc=[],tpl=tpl.replace(/<x\-([a-z0-9\-_]+)([^(\/?)>]+)/gi,"<span data-x-tag-name='$1'$2").replace(/<\/x\-([a-z0-9\-_]+)>/gi,"</span>"),df=pkg.utils.strToDOM(tpl),Array.toArray(df.querySelectorAll("[data-x-tag-name]"))):(df=pkg.utils.strToDOM(tpl),Array.toArray(df.querySelectorAll("x-"+pkg.ui.components.list().join(",x-"))||[]))).length&&wc.forEach(function(cur,i){var matchingID;params={},tmpName=cur.getAttribute("data-x-tag-name")||cur.tagName.toLowerCase().replace("x-",""),cur.removeAttribute("data-x-tag-name"),matchingID=(matchingID=new RegExp("<x\\-("+tmpName+")","i").exec(tplStr)||[tmpName]).pop(),componentsPromises.push(new Promise(function(resolve,reject){pkg.require(["comp."+matchingID],function(TmpComp){(tmpParams=Array.toArray(cur.attributes)).forEach(function(key){params[key.name]=key.value}),params.innerHTML=cur.innerHTML;try{TmpComp=new TmpComp(params),componentList[TmpComp.name]||(componentList[TmpComp.name]=[]),TmpComp.API.DOMRef=TmpComp.DOM.firstChild,cur.parentNode.replaceChild(TmpComp.DOM,cur),TmpComp.DOM=TmpComp.API.DOMRef,componentList[TmpComp.name].push(TmpComp)}catch(e){e.message='Component "'+matchingID+'" triggered an error on its initialization!\n'+e.message,console.error(e)}finally{resolve({componentName:matchingID,error:null,container:TmpComp&&"object"==typeof TmpComp.DOM&&TmpComp.DOM?TmpComp.DOM:cur})}},function(e){resolve({componentName:matchingID,error:e.message,container:cur})})})),"function"==typeof callback&&Promise.all(componentsPromises).then(callback)}),df.getComponents=function(comp){if(void 0!==comp&&"string"!=typeof comp)throw new Error("Invalid argument passed to getComponents. It must be an id (string) or undefined que get the whole list.");return componentsPromises.length?Promise.all(componentsPromises).then(function(){return comp?componentList[comp]?Promise.resolve(componentList[comp]):Promise.reject("Component not embeded as a comp (x-tag): "+comp):Promise.resolve(componentList)}):comp?componentList[comp]?Promise.resolve(componentList[comp]):Promise.reject("Component not embeded as a comp (x-tag): "+comp):Promise.resolve(componentList)},df},this.list=function(){return templates},this.filters={maxLen:function(str,len){return str.substring(0,len||void 0)},ceil:function(val){return Math.ceil(val)},floor:function(val){return Math.floor(val)},numberFormat:function(value,decPlaces,thouSeparator,decSeparator){var value=value,decPlaces=isNaN(decPlaces=Math.abs(decPlaces))?2:decPlaces,decSeparator=null==decSeparator?".":decSeparator,thouSeparator=null==thouSeparator?",":thouSeparator,sign=value<0?"-":"",i=parseInt(value=Math.abs(+value||0).toFixed(decPlaces))+"",j=3<(j=i.length)?j%3:0;return sign+(j?i.substr(0,j)+thouSeparator:"")+i.substr(j).replace(/(\d{3})(?=\d)/g,"$1"+thouSeparator)+(decPlaces?decSeparator+Math.abs(value-i).toFixed(decPlaces).slice(2):"")},pad:function(value,length,placeholder,type){return(""+value).pad(length,placeholder,type)},focus:function(value,match){var strValue=replaceAccents(value),strMatch=replaceAccents(match),strValue=strValue.toLowerCase().indexOf(strMatch.toLowerCase()),strMatch;if(0<=strValue)return strMatch=match.length,value.replace(value.substr(strValue,strMatch),"<strong>"+value.substr(strValue,strMatch)+"</strong>")}},env&&this.setReferences(nun,env),this}})}),console.check(!0,"Loaded extension template-manager.js"),console.check(!0,"Loaded extension library.js"),zaz.use(function exampleBuilder(pkg,__privileged){"use strict";var scopePrivate="value";__privileged.extend(function ExampleFactory(__protected){function Example(){return this.value="Example text",this.someNumber=Math.ceil(1e3*Math.random()),this}var self=this;return new Example}),__privileged.example=new __privileged.ExampleFactory,pkg.extend(function eg(__protected){return __privileged.example})}),zaz.use(function testingTheExample(pkg,__privileged){"use strict"}),zaz.use(function testingTheExample(pkg){"use strict"}),zaz.use(function extendingExample(pkg,__privileged){"use strict";__privileged.ExampleFactory.extend(function FooBar(__protected){return this.foo="bar",this}),pkg.eg.extend(function fooBar(){return __privileged.ExampleFactory.fooBar})}),zaz.use(function extendingExample(pkg,__privileged){"use strict"}),zaz.use(function extendingExample(pkg){"use strict"}),console.check(!0,"Loaded extension example.js"),zaz.use(function factoryManager(pkg,__privileged){"use strict";var console=pkg.console,MainFactory=function(){var factories={},createClass=function(conf,parent){var kinds={},funcName=(parent=parent.replace(/\W/g,""),conf.factoryName),idPascal=conf.id[0].toUpperCase()+conf.id.substring(1);return eval("funcName= function "+funcName+"(matter) { return "+parent+".apply(this, [matter]) }; //# sourceURL="+funcName+".js"),funcName.prototype=pkg.utils.merge(funcName.prototype,conf.implement.prototype||{}),funcName.prototype.factoryId=conf.id,funcName.prototype.factoryName=funcName,funcName["get"+idPascal+"s"]=function(){return kinds},funcName.create=function(conf){var inst=new funcName(conf);return kinds[conf.id||conf.name]||(kinds[conf.id||conf.name]=inst),inst},funcName.destroy=function(){MainFactory.destroy(conf.id)},pkg.schemas.create(conf.id,conf),funcName},Factory=function(matter){var valid=pkg.schemas.validate(this.factoryId,matter),tmp,md=MainFactory.getMetadata(this.factoryId);if(matter.factoryName=md.factoryName,!0!==valid)throw new Error("Invalid schema passed to "+matter.factoryName+(matter.name?", from "+matter.name:"!")+"\n"+valid);matter.factoryCreationName=md.id+"."+(matter.name||matter.id||(new Date).getTime()+Math.ceil(1e4*Math.random()));var FuncName=md.name.replace(/\W/g,"");if(eval("FuncName= function "+FuncName+"(){} //# sourceURL="+FuncName+"Factory.js"),FuncName.dependencies=matter.dependencies,FuncName.prototype=matter,"function"==typeof md.firstUse)try{tmp=md.firstUse(FuncName,matter),void 0!==tmp&&(FuncName=tmp)}catch(e){throw new Error("Failed instantiating "+md.name+"!\n"+e.message)}return FuncName};return{create:function(conf){var id=null;if(factories[conf.id])throw new Error("Tried to create a factory with the id of an existing factory ("+conf.id+")!");if(!conf.id||"string"!=typeof conf.id)throw new Error("The id of your factory must be a string, "+conf.id+"given");id=conf.id,conf.factoryName=conf.id.replace(/\W/g,"")+"Factory";var Factory=createClass(conf,"Factory"),defVal=(conf.factoryName=conf.factoryName,function(){return Factory.dependencies=[].slice.call(arguments),Factory});pkg.define(conf.id+"Factory",conf.dependencies||[],defVal),factories[conf.id]={klass:Factory,metaData:conf};try{"function"==typeof conf.onFactoryCreate&&conf.onFactoryCreate(factories[conf.id].klass,factories[conf.id].metaData)}catch(e){console.error("Failed trying to call firstUse event on factory "+this.factoryName,e.message,e)}return pkg.introduce(conf.id+"Factory",defVal),factories[conf.id].klass},list:function(detailed){return Object.keys(factories)},get:function(factory){return!!factories[factory]&&factories[factory].klass},getMetadata:function(factory){return!!factories[factory]&&factories[factory].metaData},isInstanceOf:function(who,factory){return"factory"==factory?who instanceof Factory:!!factories[factory]&&who instanceof factories[factory].klass},exists:function(who){return!!factories[who]},destroy:function(factoryId){var tmp=factories[factoryId];if(tmp){if("function"==typeof tmp.metaData.onFactoryDestroy)try{tmp.metaData.onFactoryDestroy()}catch(e){console.error("Failed executing onFactoryDestroy function for "+tmp.metaData.id,e.message,e)}factories[factoryId]=void 0,delete factories[factoryId]}}}}();__privileged.mainFactory=MainFactory,pkg.factoryManager=__privileged.mainFactory}),zaz.use(function DictFactory(pkg,__privileged){"use strict";var console=pkg.console,validStates=["deprecated","inactive","loading","ready","failed","canceled"];__privileged.mainFactory.create({id:"dict",name:"Dictionary",docs:"http://github.tpn.terra.com/Terra/zaz/wiki/Dictionaries",tests:"--",source:"http://github.tpn.terra.com/Terra/zaz-cerebro",implement:{properties:{name:{type:"string",required:!0},version:{type:"string",required:!0,format:/^((\d+\.){2})\d+((\:\d+)?)$/,description:"Must match something like 1.2.3:4 or at least 1.2.3",value:"0.0.1:1"},extends:{type:"array",required:!1},langs:{type:"object",required:!0,properties:{pt:{type:"object",required:!0},en:{type:"object",required:!0},es:{type:"object",required:!0}}}}},dependencies:[],firstUse:function(DicConstructor,matter){function Dictionary(matter){function getRootLang(lang){var l=lang,l;return l=(l=lang.split("-"))[0]}this.vocabulary={};var _this=this;this.langs=pkg.utils.deepMerge({},this.langs||this.__factoryData),this.setLang=function(lang){var l=getRootLang(lang);_this.currentLang=lang,this.vocabulary=pkg.utils.deepMerge(_this.langs.global||{},_this.langs[l]||{},_this.langs[lang]||{})},this.setLang(pkg.context.page.get("locale")),this.get=function(key,lang,num){var val=!1,tmp;return isNaN(lang)||(num=lang),(lang=lang&&isNaN(lang)?lang:this.currentLang)==this.currentLang?val=this.vocabulary[key]:(tmp=this.currentLang,this.setLang(lang),val=this.vocabulary[key],this.setLang(tmp)),void 0===val&&(console.warn('The key "'+key+'" was not found on '+this.name+"("+(lang||_this.currentLang)+") dictionary!"),val=key),val=Array.isArray(val)?1===parseInt(num,10)?val[1]:val[0]:val},this.getVocabulary=function(){return _this.vocabulary},this.format=function(s){var args=null,that=this,val="";if("string"!=typeof s)throw new Error("dictionary.format require a string as first argument! "+typeof s+" given");var rx=/(([^\\]|^)%(\()?([a-zA-Z0-9\-_]+))(\([\d\.]+\))?\)?/,replaceRx=new RegExp,tmp=s.match(rx),num=0;if(1<arguments.length)args=Array.prototype.slice.call(arguments),s=args.shift(),args.forEach(function(cur){cur=(cur=that.get(cur)).replace(/%/g,"\\%"),replaceRx.compile("([^\\\\]|^)%([a-zA-Z0-9\\-_])"),s=s.replace(replaceRx,"$1"+cur)});else for(;tmp;)num=0,tmp&&tmp.pop&&(num=tmp.pop(),tmp=tmp.pop(),num=num&&num.replace(/[^\d\.]/g,"")),val=(val=that.get(tmp,num||void 0)).replace(/%/g,"\\%"),replaceRx.compile("([^\\\\]|^)%(\\()?"+tmp+"(\\([\\d\\.]+\\))?\\)?",""),tmp=(s=s.replace(replaceRx,"$1"+val)).match(rx);return s.replace(/\\%/g,"%")},this.extend=function(data){if("object"!=typeof data)throw new Error("Extending a dictionary demands an object as argument");this.langs=pkg.utils.deepMerge(this.langs,data),this.setLang(this.currentLang)},this.__factoryData=null,delete this.__factoryData}var exts;return matter.extends&&matter.extends.length&&(exts=("dict."+matter.extends.join("|dict.")).split("|")||[],matter.extends=exts),Dictionary.prototype={__factoryData:pkg.utils.merge({},matter.langs)},Dictionary.prototype.name=matter.name,Dictionary.prototype.version=matter.version,Dictionary.prototype.kind=matter.kind="Dictionary",pkg.define(matter.factoryCreationName,exts||[],function(matter){var ret=function(){var extensions,extensions;return arguments.length&&(extensions=(extensions=Array.prototype.slice.call(arguments)).map(function(Cur){return(Cur=new Cur).langs}),Dictionary.prototype.langs=pkg.utils.deepMerge.apply(null,extensions.concat([matter.langs]))),Dictionary};return pkg.introduce(matter.factoryCreationName,ret,{observable:!1}),ret}(matter)),Dictionary},onFactoryCreate:function(dicFactoryConstructor,metaData){return dicFactoryConstructor},onFactoryDestroy:function(){}})}),!function(){var modules={},ArrayProto,ObjProto,escapeMap,escapeRegex,lookupEscape,exports,nodes,sym,globals,nunjucks;function extend(cls,name,props){var F=function(){},prototype=(F.prototype=cls.prototype,new F),fnTest=/xyz/.test(function(){xyz})?/\bparent\b/:/.*/,k;for(k in props=props||{}){var src=props[k],parent=prototype[k];"function"==typeof parent&&"function"==typeof src&&fnTest.test(src)?prototype[k]=function(src,parent){return function(){var tmp=this.parent,res=(this.parent=parent,src.apply(this,arguments));return this.parent=tmp,res}}(src,parent):prototype[k]=src}prototype.typename=name;var new_cls=function(){prototype.init&&prototype.init.apply(this,arguments)};return new_cls.prototype=prototype,(new_cls.prototype.constructor=new_cls).extend=function(name,props){return"object"==typeof name&&(props=name,name="anonymous"),extend(new_cls,name,props)},new_cls}function gensym(){return"hole_"+sym++}function mapCOW(arr,func){for(var res=null,i=0;i<arr.length;i++){var item=func(arr[i]);item!==arr[i]&&((res=res||arr.slice())[i]=item)}return res||arr}function walk(ast,func,depthFirst){if(!(ast instanceof nodes.Node))return ast;if(!depthFirst){var astT=func(ast);if(astT&&astT!==ast)return astT}var astT,astT,contentArgs,astT,contentArgs;return ast instanceof nodes.NodeList?(astT=mapCOW(ast.children,function(node){return walk(node,func,depthFirst)}))!==ast.children&&(ast=new nodes[ast.typename](ast.lineno,ast.colno,astT)):ast instanceof nodes.CallExtension?(astT=walk(ast.args,func,depthFirst),contentArgs=mapCOW(ast.contentArgs,function(node){return walk(node,func,depthFirst)}),astT===ast.args&&contentArgs===ast.contentArgs||(ast=new nodes[ast.typename](ast.extName,ast.prop,astT,contentArgs))):(contentArgs=mapCOW(astT=ast.fields.map(function(field){return ast[field]}),function(prop){return walk(prop,func,depthFirst)}))!==astT&&(ast=new nodes[ast.typename](ast.lineno,ast.colno),contentArgs.forEach(function(prop,i){ast[ast.fields[i]]=prop})),depthFirst&&func(ast)||ast}function depthWalk(ast,func){return walk(ast,func,!0)}function _liftFilters(node,asyncFilters,prop){var children=[],walked=depthWalk(prop?node[prop]:node,function(node){return node instanceof nodes.Block?node:node instanceof nodes.Filter&&-1!==asyncFilters.indexOf(node.name.value)||node instanceof nodes.CallExtensionAsync?(symbol=new nodes.Symbol(node.lineno,node.colno,gensym()),children.push(new nodes.FilterAsync(node.lineno,node.colno,node.name,node.args,symbol)),symbol):void 0;var symbol});return prop?node[prop]=walked:node=walked,children.length?(children.push(node),new nodes.NodeList(node.lineno,node.colno,children)):node}function liftFilters(ast,asyncFilters){return depthWalk(ast,function(node){return node instanceof nodes.Output?_liftFilters(node,asyncFilters):node instanceof nodes.For?_liftFilters(node,asyncFilters,"arr"):node instanceof nodes.If?_liftFilters(node,asyncFilters,"cond"):node instanceof nodes.CallExtension?_liftFilters(node,asyncFilters,"args"):void 0})}function liftSuper(ast){return walk(ast,function(blockNode){var hasSuper,symbol;blockNode instanceof nodes.Block&&(hasSuper=!1,symbol=gensym(),blockNode.body=walk(blockNode.body,function(node){if(node instanceof nodes.FunCall&&"super"==node.name.value)return hasSuper=!0,new nodes.Symbol(node.lineno,node.colno,symbol)}),hasSuper&&blockNode.body.children.unshift(new nodes.Super(0,0,blockNode.name,new nodes.Symbol(0,0,symbol))))})}function convertStatements(ast){return depthWalk(ast,function(node){var async;if(node instanceof nodes.If||node instanceof nodes.For)return async=!1,walk(node,function(node){if(node instanceof nodes.FilterAsync||node instanceof nodes.IfAsync||node instanceof nodes.AsyncEach||node instanceof nodes.AsyncAll||node instanceof nodes.CallExtensionAsync)return async=!0,node}),async?node instanceof nodes.If?new nodes.IfAsync(node.lineno,node.colno,node.cond,node.body,node.else_):node instanceof nodes.For?new nodes.AsyncEach(node.lineno,node.colno,node.arr,node.name,node.body):void 0:void 0})}function cps(ast,asyncFilters){return convertStatements(liftSuper(liftFilters(ast,asyncFilters)))}function transform(ast,asyncFilters,name){return cps(ast,asyncFilters||[])}function cycler(items){var index=-1,current=null;return{reset:function(){index=-1,current=null},next:function(){return++index>=items.length&&(index=0),current=items[index]}}}function joiner(sep){sep=sep||",";var first=!0;return function(){var val=first?"":sep;return first=!1,val}}modules.object=extend(Object,"Object",{}),ArrayProto=Array.prototype,ObjProto=Object.prototype,escapeMap={"&":"&amp;",'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;"},escapeRegex=/[&"'<>]/g,lookupEscape=function(ch){return escapeMap[ch]},(exports=modules.lib={withPrettyErrors:function(path,withInternals,func){try{return func()}catch(e){var func;throw(e=e.Update?e:new exports.TemplateError(e)).Update(path),withInternals||(func=e,(e=new Error(func.message)).name=func.name),e}},TemplateError:function(message,lineno,colno){var err=this;return message instanceof Error?message=(err=message).name+": "+message.message:Error.captureStackTrace&&Error.captureStackTrace(err),err.name="Template render error",err.message=message,err.lineno=lineno,err.colno=colno,err.firstUpdate=!0,err.Update=function(path){var path="("+(path||"unknown path")+")";return this.firstUpdate&&(this.lineno&&this.colno?path+=" [Line "+this.lineno+", Column "+this.colno+"]":this.lineno&&(path+=" [Line "+this.lineno+"]")),path+="\n ",this.firstUpdate&&(path+=" "),this.message=path+(this.message||""),this.firstUpdate=!1,this},err}}).TemplateError.prototype=Error.prototype,exports.escape=function(val){return val.replace(escapeRegex,lookupEscape)},exports.isFunction=function(obj){return"[object Function]"==ObjProto.toString.call(obj)},exports.isArray=Array.isArray||function(obj){return"[object Array]"==ObjProto.toString.call(obj)},exports.isString=function(obj){return"[object String]"==ObjProto.toString.call(obj)},exports.isObject=function(obj){return"[object Object]"==ObjProto.toString.call(obj)},exports.groupBy=function(obj,val){for(var result={},iterator=exports.isFunction(val)?val:function(obj){return obj[val]},i=0;i<obj.length;i++){var value=obj[i],key=iterator(value,i);(result[key]||(result[key]=[])).push(value)}return result},exports.toArray=function(obj){return Array.prototype.slice.call(obj)},exports.without=function(array){var result=[];if(!array)return result;for(var index=-1,length=array.length,contains=exports.toArray(arguments).slice(1);++index<length;)-1===contains.indexOf(array[index])&&result.push(array[index]);return result},exports.extend=function(obj,obj2){for(var k in obj2)obj[k]=obj2[k];return obj},exports.repeat=function(char_,n){for(var str="",i=0;i<n;i++)str+=char_;return str},exports.each=function(obj,func,context){if(null!=obj)if(ArrayProto.each&&obj.each==ArrayProto.each)obj.forEach(func,context);else if(obj.length===+obj.length)for(var i=0,l=obj.length;i<l;i++)func.call(context,obj[i],i,obj)},exports.map=function(obj,func){var results=[];if(null==obj)return results;if(ArrayProto.map&&obj.map===ArrayProto.map)return obj.map(func);for(var i=0;i<obj.length;i++)results[results.length]=func(obj[i],i);return obj.length===+obj.length&&(results.length=obj.length),results},exports.asyncIter=function(arr,iter,cb){function next(){++i<arr.length?iter(arr[i],i,next,cb):cb()}var i=-1;next()},exports.asyncFor=function(obj,iter,cb){function next(){var k=keys[++i];i<len?iter(k,obj[k],i,len,next):cb()}var keys=exports.keys(obj),len=keys.length,i=-1;next()},Array.prototype.indexOf||(Array.prototype.indexOf=function(array,searchElement){if(null==array)throw new TypeError;var t=Object(array),len=t.length>>>0;if(0==len)return-1;var n=0;if(2<arguments.length&&((n=Number(arguments[2]))!=n?n=0:0!=n&&n!=1/0&&n!=-1/0&&(n=(0<n||-1)*Math.floor(Math.abs(n)))),len<=n)return-1;for(var k=0<=n?n:Math.max(len-Math.abs(n),0);k<len;k++)if(k in t&&t[k]===searchElement)return k;return-1}),Array.prototype.map||(Array.prototype.map=function(){throw new Error("map is unimplemented for this js engine")}),exports.keys=function(obj){if(Object.prototype.keys)return obj.keys();var keys=[],k;for(k in obj)obj.hasOwnProperty(k)&&keys.push(k);return keys},function(){function traverseAndCheck(obj,type,results){obj instanceof type&&results.push(obj),obj instanceof Node&&obj.findAll(type,results)}function printNodes(node,indent){function print(str,indent,inline){for(var lines=str.split("\n"),i=0;i<lines.length;i++){if(lines[i]&&(inline&&0<i||!inline))for(var j=0;j<indent;j++)util.print(" ");i===lines.length-1?util.print(lines[i]):util.puts(lines[i])}}if(indent=indent||0,print(node.typename+": ",indent),node instanceof NodeList)print("\n"),lib.each(node.children,function(n){printNodes(n,indent+2)});else if(node instanceof CallExtension)print(node.extName+"."+node.prop),print("\n"),node.args&&printNodes(node.args,indent+2),node.contentArgs&&lib.each(node.contentArgs,function(n){printNodes(n,indent+2)});else{var nodes=null,props=null;if(node.iterFields(function(val,field){val instanceof Node?(nodes=nodes||{})[field]=val:(props=props||{})[field]=val}),props?print(util.inspect(props,!0,null)+"\n",null,!0):print("\n"),nodes)for(var k in nodes)printNodes(nodes[k],indent+2)}}var util=modules.util,lib=modules.lib,Object,Node=modules.object.extend("Node",{init:function(lineno,colno){this.lineno=lineno,this.colno=colno;for(var fields=this.fields,i=0,l=fields.length;i<l;i++){var field=fields[i],val=arguments[i+2];void 0===val&&(val=null),this[field]=val}},findAll:function(type,results){if(results=results||[],this instanceof NodeList)for(var children=this.children,i=0,l=children.length;i<l;i++)traverseAndCheck(children[i],type,results);else for(var fields=this.fields,i=0,l=fields.length;i<l;i++)traverseAndCheck(this[fields[i]],type,results);return results},iterFields:function(func){lib.each(this.fields,function(field){func(this[field],field)},this)}}),Value=Node.extend("Value",{fields:["value"]}),NodeList=Node.extend("NodeList",{fields:["children"],init:function(lineno,colno,nodes){this.parent(lineno,colno,nodes||[])},addChild:function(node){this.children.push(node)}}),Root=NodeList.extend("Root"),Literal=Value.extend("Literal"),Symbol=Value.extend("Symbol"),Group=NodeList.extend("Group"),Array=NodeList.extend("Array"),Pair=Node.extend("Pair",{fields:["key","value"]}),Dict=NodeList.extend("Dict"),LookupVal=Node.extend("LookupVal",{fields:["target","val"]}),If=Node.extend("If",{fields:["cond","body","else_"]}),IfAsync=If.extend("IfAsync"),InlineIf=Node.extend("InlineIf",{fields:["cond","body","else_"]}),For=Node.extend("For",{fields:["arr","name","body"]}),AsyncEach=For.extend("AsyncEach"),AsyncAll=For.extend("AsyncAll"),Macro=Node.extend("Macro",{fields:["name","args","body"]}),Import=Node.extend("Import",{fields:["template","target"]}),FromImport=Node.extend("FromImport",{fields:["template","names"],init:function(lineno,colno,template,names){this.parent(lineno,colno,template,names||new NodeList)}}),FunCall=Node.extend("FunCall",{fields:["name","args"]}),Filter=FunCall.extend("Filter"),FilterAsync=Filter.extend("FilterAsync",{fields:["name","args","symbol"]}),KeywordArgs=Dict.extend("KeywordArgs"),Block=Node.extend("Block",{fields:["name","body"]}),Super=Node.extend("Super",{fields:["blockName","symbol"]}),TemplateRef=Node.extend("TemplateRef",{fields:["template"]}),Extends=TemplateRef.extend("Extends"),TemplateRef=TemplateRef.extend("Include"),Set=Node.extend("Set",{fields:["targets","value"]}),Output=NodeList.extend("Output"),TemplateData=Literal.extend("TemplateData"),UnaryOp=Node.extend("UnaryOp",{fields:["target"]}),BinOp=Node.extend("BinOp",{fields:["left","right"]}),Or=BinOp.extend("Or"),And=BinOp.extend("And"),Not=UnaryOp.extend("Not"),Add=BinOp.extend("Add"),Sub=BinOp.extend("Sub"),Mul=BinOp.extend("Mul"),Div=BinOp.extend("Div"),FloorDiv=BinOp.extend("FloorDiv"),Mod=BinOp.extend("Mod"),Pow=BinOp.extend("Pow"),Neg=UnaryOp.extend("Neg"),UnaryOp=UnaryOp.extend("Pos"),Compare=Node.extend("Compare",{fields:["expr","ops"]}),CompareOperand=Node.extend("CompareOperand",{fields:["expr","type"]}),CustomTag=Node.extend("CustomTag",{init:function(lineno,colno,name){this.lineno=lineno,this.colno=colno,this.name=name}}),CallExtension=Node.extend("CallExtension",{fields:["extName","prop","args","contentArgs"],init:function(ext,prop,args,contentArgs){this.extName=ext._name||ext,this.prop=prop,this.args=args||new NodeList,this.contentArgs=contentArgs||[],this.autoescape=ext.autoescape}}),CallExtensionAsync=CallExtension.extend("CallExtensionAsync");modules.nodes={Node:Node,Root:Root,NodeList:NodeList,Value:Value,Literal:Literal,Symbol:Symbol,Group:Group,Array:Array,Pair:Pair,Dict:Dict,Output:Output,TemplateData:TemplateData,If:If,IfAsync:IfAsync,InlineIf:InlineIf,For:For,AsyncEach:AsyncEach,AsyncAll:AsyncAll,Macro:Macro,Import:Import,FromImport:FromImport,FunCall:FunCall,Filter:Filter,FilterAsync:FilterAsync,KeywordArgs:KeywordArgs,Block:Block,Super:Super,Extends:Extends,Include:TemplateRef,Set:Set,LookupVal:LookupVal,BinOp:BinOp,Or:Or,And:And,Not:Not,Add:Add,Sub:Sub,Mul:Mul,Div:Div,FloorDiv:FloorDiv,Mod:Mod,Pow:Pow,Neg:Neg,Pos:UnaryOp,Compare:Compare,CompareOperand:CompareOperand,CallExtension:CallExtension,CallExtensionAsync:CallExtensionAsync,printNodes:printNodes}}(),function(){function makeMacro(argNames,kwargNames,func){return function(){var argCount=numArgs(arguments),args,kwargs=getKeywordArgs(arguments);if(argCount>argNames.length){for(var args=Array.prototype.slice.call(arguments,0,argNames.length),vals=Array.prototype.slice.call(arguments,args.length,argCount),i=0;i<vals.length;i++)i<kwargNames.length&&(kwargs[kwargNames[i]]=vals[i]);args.push(kwargs)}else if(argCount<argNames.length){args=Array.prototype.slice.call(arguments,0,argCount);for(var i=argCount;i<argNames.length;i++){var arg=argNames[i];args.push(kwargs[arg]),delete kwargs[arg]}args.push(kwargs)}else args=arguments;return func.apply(this,args)}}function makeKeywordArgs(obj){return obj.__keywords=!0,obj}function getKeywordArgs(args){var len=args.length;if(len){var args=args[len-1];if(args&&args.hasOwnProperty("__keywords"))return args}return{}}function numArgs(args){var len=args.length;if(0===len)return 0;var args=args[len-1];return args&&args.hasOwnProperty("__keywords")?len-1:len}function SafeString(val){if("string"!=typeof val)return val;this.val=val}function copySafeness(dest,target){return dest instanceof SafeString?new SafeString(target):target.toString()}function markSafe(val){var type=typeof val;return"string"==type?new SafeString(val):"function"!=type?val:function(){var ret=val.apply(this,arguments);return"string"==typeof ret?new SafeString(ret):ret}}function suppressValue(val,autoescape){return val=null!=val?val:"",val=autoescape&&"string"==typeof val?lib.escape(val):val}function memberLookup(obj,val){return"function"==typeof(obj=obj||{})[val]?function(){return obj[val].apply(obj,arguments)}:obj[val]}function callWrap(obj,name,args){if(!obj)throw new Error("Unable to call `"+name+"`, which is undefined or falsey");if("function"!=typeof obj)throw new Error("Unable to call `"+name+"`, which is not a function");return obj.apply(this,args)}function contextOrFrameLookup(context,frame,name){var frame=frame.lookup(name);return null!=frame?frame:context.lookup(name)}function handleError(error,lineno,colno){return error.lineno?error:new lib.TemplateError(error,lineno,colno)}function asyncEach(arr,dimen,iter,cb){var len;lib.isArray(arr)?(len=arr.length,lib.asyncIter(arr,function(item,i,next){switch(dimen){case 1:iter(item,i,len,next);break;case 2:iter(item[0],item[1],i,len,next);break;case 3:iter(item[0],item[1],item[2],i,len,next);break;default:item.push(i,next),iter.apply(this,item)}},cb)):lib.asyncFor(arr,function(key,val,i,len,next){iter(key,val,i,len,next)},cb)}function asyncAll(arr,dimen,func,cb){function done(i,output){finished++,outputArr[i]=output,finished==len&&cb(null,outputArr.join(""))}var finished=0,len,outputArr;if(lib.isArray(arr))if(len=arr.length,outputArr=new Array(len),0==len)cb(null,"");else for(var i=0;i<arr.length;i++){var item=arr[i];switch(dimen){case 1:func(item,i,len,done);break;case 2:func(item[0],item[1],i,len,done);break;case 3:func(item[0],item[1],item[2],i,len,done);break;default:item.push(i,done),func.apply(this,item)}}else{var keys=lib.keys(arr),len=keys.length,outputArr=new Array(len);if(0==len)cb(null,"");else for(var i=0;i<keys.length;i++){var k=keys[i];func(k,arr[k],i,len,done)}}}var lib=modules.lib,Obj,Frame=modules.object.extend({init:function(parent){this.variables={},this.parent=parent},set:function(name,val){for(var parts=name.split("."),obj=this.variables,i=0;i<parts.length-1;i++){var id=parts[i];obj[id]||(obj[id]={}),obj=obj[id]}obj[parts[parts.length-1]]=val},get:function(name){var name=this.variables[name];return null!=name?name:null},lookup:function(name){var p=this.parent,val=this.variables[name];return null!=val?val:p&&p.lookup(name)},push:function(){return new Frame(this)},pop:function(){return this.parent}});SafeString.prototype=Object.create(String.prototype),SafeString.prototype.valueOf=function(){return this.val},SafeString.prototype.toString=function(){return this.val},modules.runtime={Frame:Frame,makeMacro:makeMacro,makeKeywordArgs:makeKeywordArgs,numArgs:numArgs,suppressValue:suppressValue,memberLookup:memberLookup,contextOrFrameLookup:contextOrFrameLookup,callWrap:callWrap,handleError:handleError,isArray:lib.isArray,keys:lib.keys,SafeString:SafeString,copySafeness:copySafeness,markSafe:markSafe,asyncEach:asyncEach,asyncAll:asyncAll}}(),function(){function token(type,value,lineno,colno){return{type:type,value:value,lineno:lineno,colno:colno}}function Tokenizer(str){this.str=str,this.index=0,this.len=str.length,this.lineno=0,this.colno=0,this.in_code=!1}var lib=modules.lib,whitespaceChars=" \n\t\r",delimChars="()[]{}%*-+/#,:|.<>=!",intChars="0123456789",BLOCK_START="{%",BLOCK_END="%}",VARIABLE_START="{{",VARIABLE_END="}}",COMMENT_START="{#",COMMENT_END="#}",TOKEN_STRING="string",TOKEN_WHITESPACE="whitespace",TOKEN_DATA="data",TOKEN_BLOCK_START="block-start",TOKEN_BLOCK_END="block-end",TOKEN_VARIABLE_START="variable-start",TOKEN_VARIABLE_END="variable-end",TOKEN_COMMENT="comment",TOKEN_LEFT_PAREN="left-paren",TOKEN_RIGHT_PAREN="right-paren",TOKEN_LEFT_BRACKET="left-bracket",TOKEN_RIGHT_BRACKET="right-bracket",TOKEN_LEFT_CURLY="left-curly",TOKEN_RIGHT_CURLY="right-curly",TOKEN_OPERATOR="operator",TOKEN_COMMA="comma",TOKEN_COLON="colon",TOKEN_PIPE="pipe",TOKEN_INT="int",TOKEN_FLOAT="float",TOKEN_BOOLEAN="boolean",TOKEN_SYMBOL="symbol",TOKEN_SPECIAL="special";Tokenizer.prototype.nextToken=function(){var lineno=this.lineno,colno=this.colno;if(this.in_code){var cur=this.current(),tok,dec;if(this.is_finished())return null;if('"'==cur||"'"==cur)return token("string",this.parseString(cur),lineno,colno);if(tok=this._extract(" \n\t\r"))return token("whitespace",tok,lineno,colno);if((tok=this._extractString(BLOCK_END))||(tok=this._extractString("-"+BLOCK_END)))return this.in_code=!1,token("block-end",tok,lineno,colno);if(tok=this._extractString(VARIABLE_END))return this.in_code=!1,token("variable-end",tok,lineno,colno);if(-1!=delimChars.indexOf(cur)){this.forward();var complexOps=["==","!=","<=",">=","//","**"],curComplex=cur+this.current(),type;switch(-1!==complexOps.indexOf(curComplex)&&(this.forward(),cur=curComplex),cur){case"(":type="left-paren";break;case")":type="right-paren";break;case"[":type="left-bracket";break;case"]":type="right-bracket";break;case"{":type="left-curly";break;case"}":type="right-curly";break;case",":type="comma";break;case":":type="colon";break;case"|":type="pipe";break;default:type="operator"}return token(type,cur,lineno,colno)}if((tok=this._extractUntil(" \n\t\r"+delimChars)).match(/^[-+]?[0-9]+$/))return"."==this.current()?(this.forward(),token("float",tok+"."+this._extract(intChars),lineno,colno)):token("int",tok,lineno,colno);if(tok.match(/^(true|false)$/))return token("boolean",tok,lineno,colno);if(tok)return token("symbol",tok,lineno,colno);throw new Error("Unexpected value while parsing: "+tok)}var beginChars=BLOCK_START.charAt(0)+VARIABLE_START.charAt(0)+COMMENT_START.charAt(0)+COMMENT_END.charAt(0),tok;if(this.is_finished())return null;if((tok=this._extractString(BLOCK_START+"-"))||(tok=this._extractString(BLOCK_START)))return this.in_code=!0,token("block-start",tok,lineno,colno);if(tok=this._extractString(VARIABLE_START))return this.in_code=!0,token("variable-start",tok,lineno,colno);var data,in_comment=(tok="",!1);for(this._matches(COMMENT_START)&&(in_comment=!0,tok=this._extractString(COMMENT_START));null!==(data=this._extractUntil(beginChars))&&(tok+=data,!(this._matches(BLOCK_START)||this._matches(VARIABLE_START)||this._matches(COMMENT_START))||in_comment);){if(this._matches(COMMENT_END)){if(!in_comment)throw new Error("unexpected end of comment");tok+=this._extractString(COMMENT_END);break}tok+=this.current(),this.forward()}if(null===data&&in_comment)throw new Error("expected end of comment, got end of file");return token(in_comment?"comment":"data",tok,lineno,colno)},Tokenizer.prototype.parseString=function(delimiter){this.forward();for(var lineno=this.lineno,colno=this.colno,str="";!this.is_finished()&&this.current()!=delimiter;){var cur=this.current();if("\\"==cur){switch(this.forward(),this.current()){case"n":str+="\n";break;case"t":str+="\t";break;case"r":str+="\r";break;default:str+=this.current()}this.forward()}else str+=cur,this.forward()}return this.forward(),str},Tokenizer.prototype._matches=function(str){return this.index+str.length>this.length?null:this.str.slice(this.index,this.index+str.length)==str;var m},Tokenizer.prototype._extractString=function(str){return this._matches(str)?(this.index+=str.length,str):null},Tokenizer.prototype._extractUntil=function(charString){return this._extractMatching(!0,charString||"")},Tokenizer.prototype._extract=function(charString){return this._extractMatching(!1,charString)},Tokenizer.prototype._extractMatching=function(breakOnMatch,charString){if(this.is_finished())return null;var first=charString.indexOf(this.current());if(breakOnMatch&&-1==first||!breakOnMatch&&-1!=first){for(var t=this.current(),idx=(this.forward(),charString.indexOf(this.current()));(breakOnMatch&&-1==idx||!breakOnMatch&&-1!=idx)&&!this.is_finished();)t+=this.current(),this.forward(),idx=charString.indexOf(this.current());return t}return""},Tokenizer.prototype.is_finished=function(){return this.index>=this.len},Tokenizer.prototype.forwardN=function(n){for(var i=0;i<n;i++)this.forward()},Tokenizer.prototype.forward=function(){this.index++,"\n"==this.previous()?(this.lineno++,this.colno=0):this.colno++},Tokenizer.prototype.back=function(){var idx;this.index--,"\n"==this.current()?(this.lineno--,idx=this.src.lastIndexOf("\n",this.index-1),this.colno=-1==idx?this.index:this.index-idx):this.colno--},Tokenizer.prototype.current=function(){return this.is_finished()?"":this.str.charAt(this.index)},Tokenizer.prototype.previous=function(){return this.str.charAt(this.index-1)},modules.lexer={lex:function(src){return new Tokenizer(src)},setTags:function(tags){BLOCK_START=tags.blockStart||BLOCK_START,BLOCK_END=tags.blockEnd||BLOCK_END,VARIABLE_START=tags.variableStart||VARIABLE_START,VARIABLE_END=tags.variableEnd||VARIABLE_END,COMMENT_START=tags.commentStart||COMMENT_START,COMMENT_END=tags.commentEnd||COMMENT_END},TOKEN_STRING:"string",TOKEN_WHITESPACE:"whitespace",TOKEN_DATA:"data",TOKEN_BLOCK_START:"block-start",TOKEN_BLOCK_END:"block-end",TOKEN_VARIABLE_START:"variable-start",TOKEN_VARIABLE_END:"variable-end",TOKEN_COMMENT:"comment",TOKEN_LEFT_PAREN:"left-paren",TOKEN_RIGHT_PAREN:"right-paren",TOKEN_LEFT_BRACKET:"left-bracket",TOKEN_RIGHT_BRACKET:"right-bracket",TOKEN_LEFT_CURLY:"left-curly",TOKEN_RIGHT_CURLY:"right-curly",TOKEN_OPERATOR:"operator",TOKEN_COMMA:"comma",TOKEN_COLON:"colon",TOKEN_PIPE:"pipe",TOKEN_INT:"int",TOKEN_FLOAT:"float",TOKEN_BOOLEAN:"boolean",TOKEN_SYMBOL:"symbol",TOKEN_SPECIAL:"special"}}(),function(){var lexer=modules.lexer,nodes=modules.nodes,Object=modules.object,lib=modules.lib,Parser=Object.extend({init:function(tokens){this.tokens=tokens,this.peeked=null,this.breakOnBlocks=null,this.dropLeadingWhitespace=!1,this.extensions=[]},nextToken:function(withWhitespace){var tok;if(this.peeked){if(withWhitespace||this.peeked.type!=lexer.TOKEN_WHITESPACE)return tok=this.peeked,this.peeked=null,tok;this.peeked=null}if(tok=this.tokens.nextToken(),!withWhitespace)for(;tok&&tok.type==lexer.TOKEN_WHITESPACE;)tok=this.tokens.nextToken();return tok},peekToken:function(){return this.peeked=this.peeked||this.nextToken(),this.peeked},pushToken:function(tok){if(this.peeked)throw new Error("pushToken: can only push one token on between reads");this.peeked=tok},fail:function(msg,lineno,colno){var tok;throw void 0!==lineno&&void 0!==colno||!this.peekToken()||(lineno=(tok=this.peekToken()).lineno,colno=tok.colno),void 0!==lineno&&(lineno+=1),void 0!==colno&&(colno+=1),new lib.TemplateError(msg,lineno,colno)},skip:function(type){var tok=this.nextToken();return!(!tok||tok.type!=type)||(this.pushToken(tok),!1)},expect:function(type){var tok=this.nextToken();return tok.type!==type&&this.fail("expected "+type+", got "+tok.type,tok.lineno,tok.colno),tok},skipValue:function(type,val){var tok=this.nextToken();return!(!tok||tok.type!=type||tok.value!=val)||(this.pushToken(tok),!1)},skipWhitespace:function(){return this.skip(lexer.TOKEN_WHITESPACE)},skipSymbol:function(val){return this.skipValue(lexer.TOKEN_SYMBOL,val)},advanceAfterBlockEnd:function(name){var tok,tok=(name||((tok=this.peekToken())||this.fail("unexpected end of file"),tok.type!=lexer.TOKEN_SYMBOL&&this.fail("advanceAfterBlockEnd: expected symbol token or explicit name to be passed"),name=this.nextToken().value),this.nextToken());tok&&tok.type==lexer.TOKEN_BLOCK_END?"-"===tok.value.charAt(0)&&(this.dropLeadingWhitespace=!0):this.fail("expected block end in "+name+" statement")},advanceAfterVariableEnd:function(){this.skip(lexer.TOKEN_VARIABLE_END)||this.fail("expected variable end")},parseFor:function(){var forTok=this.peekToken(),node,endBlock,type;if(this.skipSymbol("for")?(node=new nodes.For(forTok.lineno,forTok.colno),endBlock="endfor"):this.skipSymbol("asyncEach")?(node=new nodes.AsyncEach(forTok.lineno,forTok.colno),endBlock="endeach"):this.skipSymbol("asyncAll")?(node=new nodes.AsyncAll(forTok.lineno,forTok.colno),endBlock="endall"):this.fail("parseFor: expected for{Async}",forTok.lineno,forTok.colno),node.name=this.parsePrimary(),node.name instanceof nodes.Symbol||this.fail("parseFor: variable name expected for loop"),this.peekToken().type==lexer.TOKEN_COMMA){var key=node.name;for(node.name=new nodes.Array(key.lineno,key.colno),node.name.addChild(key);this.skip(lexer.TOKEN_COMMA);){var prim=this.parsePrimary();node.name.addChild(prim)}}return this.skipSymbol("in")||this.fail('parseFor: expected "in" keyword for loop',forTok.lineno,forTok.colno),node.arr=this.parseExpression(),this.advanceAfterBlockEnd(forTok.value),node.body=this.parseUntilBlocks(endBlock),this.advanceAfterBlockEnd(),node},parseMacro:function(){var macroTok=this.peekToken(),name=(this.skipSymbol("macro")||this.fail("expected macro"),this.parsePrimary(!0)),args=this.parseSignature(),name=new nodes.Macro(macroTok.lineno,macroTok.colno,name,args);return this.advanceAfterBlockEnd(macroTok.value),name.body=this.parseUntilBlocks("endmacro"),this.advanceAfterBlockEnd(),name},parseImport:function(){var importTok=this.peekToken(),template=(this.skipSymbol("import")||this.fail("parseImport: expected import",importTok.lineno,importTok.colno),this.parsePrimary()),target=(this.skipSymbol("as")||this.fail('parseImport: expected "as" keyword',importTok.lineno,importTok.colno),this.parsePrimary()),template=new nodes.Import(importTok.lineno,importTok.colno,template,target);return this.advanceAfterBlockEnd(importTok.value),template},parseFrom:function(){for(var fromTok=this.peekToken(),template=(this.skipSymbol("from")||this.fail("parseFrom: expected from"),this.parsePrimary()),template=new nodes.FromImport(fromTok.lineno,fromTok.colno,template,new nodes.NodeList),names=(this.skipSymbol("import")||this.fail("parseFrom: expected import",fromTok.lineno,fromTok.colno),template.names);;){var nextTok=this.peekToken();if(nextTok.type==lexer.TOKEN_BLOCK_END){names.children.length||this.fail("parseFrom: Expected at least one import name",fromTok.lineno,fromTok.colno),"-"==nextTok.value.charAt(0)&&(this.dropLeadingWhitespace=!0),this.nextToken();break}0<names.children.length&&!this.skip(lexer.TOKEN_COMMA)&&this.fail("parseFrom: expected comma",fromTok.lineno,fromTok.colno);var nextTok=this.parsePrimary(),alias;"_"==nextTok.value.charAt(0)&&this.fail("parseFrom: names starting with an underscore cannot be imported",nextTok.lineno,nextTok.colno),this.skipSymbol("as")?(alias=this.parsePrimary(),names.addChild(new nodes.Pair(nextTok.lineno,nextTok.colno,nextTok,alias))):names.addChild(nextTok)}return template},parseBlock:function(){var tag=this.peekToken(),node=(this.skipSymbol("block")||this.fail("parseBlock: expected block",tag.lineno,tag.colno),new nodes.Block(tag.lineno,tag.colno));return node.name=this.parsePrimary(),node.name instanceof nodes.Symbol||this.fail("parseBlock: variable name expected",tag.lineno,tag.colno),this.advanceAfterBlockEnd(tag.value),node.body=this.parseUntilBlocks("endblock"),this.peekToken()||this.fail("parseBlock: expected endblock, got end of file"),this.advanceAfterBlockEnd(),node},parseTemplateRef:function(tagName,nodeType){var tag=this.peekToken(),tagName=(this.skipSymbol(tagName)||this.fail("parseTemplateRef: expected "+tagName),new nodeType(tag.lineno,tag.colno));return tagName.template=this.parsePrimary(),this.advanceAfterBlockEnd(tag.value),tagName},parseExtends:function(){return this.parseTemplateRef("extends",nodes.Extends)},parseInclude:function(){return this.parseTemplateRef("include",nodes.Include)},parseIf:function(){var tag=this.peekToken(),node,tag=(this.skipSymbol("if")||this.skipSymbol("elif")?node=new nodes.If(tag.lineno,tag.colno):this.skipSymbol("ifAsync")?node=new nodes.IfAsync(tag.lineno,tag.colno):this.fail("parseIf: expected if or elif",tag.lineno,tag.colno),node.cond=this.parseExpression(),this.advanceAfterBlockEnd(tag.value),node.body=this.parseUntilBlocks("elif","else","endif"),this.peekToken());switch(tag&&tag.value){case"elif":node.else_=this.parseIf();break;case"else":this.advanceAfterBlockEnd(),node.else_=this.parseUntilBlocks("endif"),this.advanceAfterBlockEnd();break;case"endif":node.else_=null,this.advanceAfterBlockEnd();break;default:this.fail("parseIf: expected endif, else, or endif, got end of file")}return node},parseSet:function(){for(var tag=this.peekToken(),node=(this.skipSymbol("set")||this.fail("parseSet: expected set",tag.lineno,tag.colno),new nodes.Set(tag.lineno,tag.colno,[])),target;(target=this.parsePrimary())&&(node.targets.push(target),this.skip(lexer.TOKEN_COMMA)););return this.skipValue(lexer.TOKEN_OPERATOR,"=")||this.fail("parseSet: expected = in set tag",tag.lineno,tag.colno),node.value=this.parseExpression(),this.advanceAfterBlockEnd(tag.value),node},parseStatement:function(){var tok=this.peekToken(),node;if(tok.type!=lexer.TOKEN_SYMBOL&&this.fail("tag name expected",tok.lineno,tok.colno),this.breakOnBlocks&&-1!==this.breakOnBlocks.indexOf(tok.value))return null;switch(tok.value){case"raw":return this.parseRaw();case"if":case"ifAsync":return this.parseIf();case"for":case"asyncEach":case"asyncAll":return this.parseFor();case"block":return this.parseBlock();case"extends":return this.parseExtends();case"include":return this.parseInclude();case"set":return this.parseSet();case"macro":return this.parseMacro();case"import":return this.parseImport();case"from":return this.parseFrom();default:if(this.extensions.length)for(var i=0;i<this.extensions.length;i++){var ext=this.extensions[i];if(-1!==(ext.tags||[]).indexOf(tok.value))return ext.parse(this,nodes,lexer)}this.fail("unknown block tag: "+tok.value,tok.lineno,tok.colno)}return node},parseRaw:function(){this.advanceAfterBlockEnd();for(var str="",begun=this.peekToken(),output;;){var tok=this.nextToken(!0);if(tok||this.fail("expected endraw, got end of file"),tok.type==lexer.TOKEN_BLOCK_START){var ws=null,name=this.nextToken(!0);if(name.type==lexer.TOKEN_WHITESPACE&&(ws=name,name=this.nextToken()),name.type==lexer.TOKEN_SYMBOL&&"endraw"==name.value){this.advanceAfterBlockEnd(name.value);break}str+=tok.value,ws&&(str+=ws.value),str+=name.value}else tok.type===lexer.TOKEN_STRING?str+='"'+tok.value+'"':str+=tok.value}return new nodes.Output(begun.lineno,begun.colno,[new nodes.TemplateData(begun.lineno,begun.colno,str)])},parsePostfix:function(node){for(var tok=this.peekToken(),lookup;tok;){if(tok.type==lexer.TOKEN_LEFT_PAREN)node=new nodes.FunCall(tok.lineno,tok.colno,node,this.parseSignature());else if(tok.type==lexer.TOKEN_LEFT_BRACKET){1<(lookup=this.parseAggregate()).children.length&&this.fail("invalid index"),node=new nodes.LookupVal(tok.lineno,tok.colno,node,lookup.children[0])}else{if(tok.type!=lexer.TOKEN_OPERATOR||"."!=tok.value)break;this.nextToken();var val=this.nextToken(),lookup=(val.type!=lexer.TOKEN_SYMBOL&&this.fail("expected name as lookup value, got "+val.value,val.lineno,val.colno),new nodes.Literal(val.lineno,val.colno,val.value));node=new nodes.LookupVal(tok.lineno,tok.colno,node,lookup)}tok=this.peekToken()}return node},parseExpression:function(){var node;return this.parseInlineIf()},parseInlineIf:function(){var node=this.parseOr(),cond_node,body_node,node;return this.skipSymbol("if")&&(cond_node=this.parseOr(),body_node=node,(node=new nodes.InlineIf(node.lineno,node.colno)).body=body_node,node.cond=cond_node,this.skipSymbol("else")?node.else_=this.parseOr():node.else_=null),node},parseOr:function(){for(var node=this.parseAnd();this.skipSymbol("or");)var node2=this.parseAnd(),node=new nodes.Or(node.lineno,node.colno,node,node2);return node},parseAnd:function(){for(var node=this.parseNot();this.skipSymbol("and");)var node2=this.parseNot(),node=new nodes.And(node.lineno,node.colno,node,node2);return node},parseNot:function(){var tok=this.peekToken();return this.skipSymbol("not")?new nodes.Not(tok.lineno,tok.colno,this.parseNot()):this.parseCompare()},parseCompare:function(){for(var compareOps=["==","!=","<",">","<=",">="],expr=this.parseAdd(),ops=[];;){var tok=this.nextToken();if(!tok)break;if(-1!==compareOps.indexOf(tok.value))ops.push(new nodes.CompareOperand(tok.lineno,tok.colno,this.parseAdd(),tok.value));else if(tok.type==lexer.TOKEN_SYMBOL&&"in"==tok.value)ops.push(new nodes.CompareOperand(tok.lineno,tok.colno,this.parseAdd(),"in"));else{if(tok.type!=lexer.TOKEN_SYMBOL||"not"!=tok.value||!this.skipSymbol("in")){this.pushToken(tok);break}ops.push(new nodes.CompareOperand(tok.lineno,tok.colno,this.parseAdd(),"notin"))}}return ops.length?new nodes.Compare(ops[0].lineno,ops[0].colno,expr,ops):expr},parseAdd:function(){for(var node=this.parseSub();this.skipValue(lexer.TOKEN_OPERATOR,"+");)var node2=this.parseSub(),node=new nodes.Add(node.lineno,node.colno,node,node2);return node},parseSub:function(){for(var node=this.parseMul();this.skipValue(lexer.TOKEN_OPERATOR,"-");)var node2=this.parseMul(),node=new nodes.Sub(node.lineno,node.colno,node,node2);return node},parseMul:function(){for(var node=this.parseDiv();this.skipValue(lexer.TOKEN_OPERATOR,"*");)var node2=this.parseDiv(),node=new nodes.Mul(node.lineno,node.colno,node,node2);return node},parseDiv:function(){for(var node=this.parseFloorDiv();this.skipValue(lexer.TOKEN_OPERATOR,"/");)var node2=this.parseFloorDiv(),node=new nodes.Div(node.lineno,node.colno,node,node2);return node},parseFloorDiv:function(){for(var node=this.parseMod();this.skipValue(lexer.TOKEN_OPERATOR,"//");)var node2=this.parseMod(),node=new nodes.FloorDiv(node.lineno,node.colno,node,node2);return node},parseMod:function(){for(var node=this.parsePow();this.skipValue(lexer.TOKEN_OPERATOR,"%");)var node2=this.parsePow(),node=new nodes.Mod(node.lineno,node.colno,node,node2);return node},parsePow:function(){for(var node=this.parseUnary();this.skipValue(lexer.TOKEN_OPERATOR,"**");)var node2=this.parseUnary(),node=new nodes.Pow(node.lineno,node.colno,node,node2);return node},parseUnary:function(noFilters){var tok=this.peekToken(),tok,tok=this.skipValue(lexer.TOKEN_OPERATOR,"-")?new nodes.Neg(tok.lineno,tok.colno,this.parseUnary(!0)):this.skipValue(lexer.TOKEN_OPERATOR,"+")?new nodes.Pos(tok.lineno,tok.colno,this.parseUnary(!0)):this.parsePrimary();return tok=noFilters?tok:this.parseFilter(tok)},parsePrimary:function(noPostfix){var tok=this.nextToken(),val=null,node=null;if(tok?tok.type==lexer.TOKEN_STRING?val=tok.value:tok.type==lexer.TOKEN_INT?val=parseInt(tok.value,10):tok.type==lexer.TOKEN_FLOAT?val=parseFloat(tok.value):tok.type==lexer.TOKEN_BOOLEAN&&("true"==tok.value?val=!0:"false"==tok.value?val=!1:this.fail("invalid boolean: "+tok.val,tok.lineno,tok.colno)):this.fail("expected expression, got end of file"),null!==val?node=new nodes.Literal(tok.lineno,tok.colno,val):tok.type==lexer.TOKEN_SYMBOL?(node=new nodes.Symbol(tok.lineno,tok.colno,tok.value),noPostfix||(node=this.parsePostfix(node))):(this.pushToken(tok),node=this.parseAggregate()),node)return node;this.fail("unexpected token: "+tok.value,tok.lineno,tok.colno)},parseFilter:function(node){for(;this.skip(lexer.TOKEN_PIPE);){for(var tok=this.expect(lexer.TOKEN_SYMBOL),name=tok.value,tok;this.skipValue(lexer.TOKEN_OPERATOR,".");)name+="."+this.expect(lexer.TOKEN_SYMBOL).value;node=new nodes.Filter(tok.lineno,tok.colno,new nodes.Symbol(tok.lineno,tok.colno,name),new nodes.NodeList(tok.lineno,tok.colno,[node])),this.peekToken().type==lexer.TOKEN_LEFT_PAREN&&(tok=this.parsePostfix(node),node.args.children=node.args.children.concat(tok.args.children))}return node},parseAggregate:function(){var tok=this.nextToken(),node;switch(tok.type){case lexer.TOKEN_LEFT_PAREN:node=new nodes.Group(tok.lineno,tok.colno);break;case lexer.TOKEN_LEFT_BRACKET:node=new nodes.Array(tok.lineno,tok.colno);break;case lexer.TOKEN_LEFT_CURLY:node=new nodes.Dict(tok.lineno,tok.colno);break;default:return null}for(;;){var type=this.peekToken().type,type,value,type;if(type==lexer.TOKEN_RIGHT_PAREN||type==lexer.TOKEN_RIGHT_BRACKET||type==lexer.TOKEN_RIGHT_CURLY){this.nextToken();break}0<node.children.length&&(this.skip(lexer.TOKEN_COMMA)||this.fail("parseAggregate: expected comma after expression",tok.lineno,tok.colno)),node instanceof nodes.Dict?(type=this.parsePrimary(),this.skip(lexer.TOKEN_COLON)||this.fail("parseAggregate: expected colon after dict key",tok.lineno,tok.colno),value=this.parseExpression(),node.addChild(new nodes.Pair(type.lineno,type.colno,type,value))):(type=this.parseExpression(),node.addChild(type))}return node},parseSignature:function(tolerant,noParens){var tok=this.peekToken();if(!noParens&&tok.type!=lexer.TOKEN_LEFT_PAREN){if(tolerant)return null;this.fail("expected arguments",tok.lineno,tok.colno)}tok.type==lexer.TOKEN_LEFT_PAREN&&(tok=this.nextToken());for(var args=new nodes.NodeList(tok.lineno,tok.colno),kwargs=new nodes.KeywordArgs(tok.lineno,tok.colno),kwnames=[],checkComma=!1,arg;;){if(tok=this.peekToken(),!noParens&&tok.type==lexer.TOKEN_RIGHT_PAREN){this.nextToken();break}if(noParens&&tok.type==lexer.TOKEN_BLOCK_END)break;checkComma&&!this.skip(lexer.TOKEN_COMMA)?this.fail("parseSignature: expected comma after expression",tok.lineno,tok.colno):(arg=this.parseExpression(),this.skipValue(lexer.TOKEN_OPERATOR,"=")?kwargs.addChild(new nodes.Pair(arg.lineno,arg.colno,arg,this.parseExpression())):args.addChild(arg)),checkComma=!0}return kwargs.children.length&&args.addChild(kwargs),args},parseUntilBlocks:function(){var prev=this.breakOnBlocks,ret=(this.breakOnBlocks=lib.toArray(arguments),this.parse());return this.breakOnBlocks=prev,ret},parseNodes:function(){for(var tok,buf=[],nextVal;tok=this.nextToken();)if(tok.type==lexer.TOKEN_DATA){var data=tok.value,nextToken=this.peekToken(),nextVal=nextToken&&nextToken.value;this.dropLeadingWhitespace&&(data=data.replace(/^\s*/,""),this.dropLeadingWhitespace=!1),nextToken&&nextToken.type==lexer.TOKEN_BLOCK_START&&"-"==nextVal.charAt(nextVal.length-1)&&(data=data.replace(/\s*$/,"")),buf.push(new nodes.Output(tok.lineno,tok.colno,[new nodes.TemplateData(tok.lineno,tok.colno,data)]))}else if(tok.type==lexer.TOKEN_BLOCK_START){var nextToken=this.parseStatement();if(!nextToken)break;buf.push(nextToken)}else{tok.type==lexer.TOKEN_VARIABLE_START?(nextVal=this.parseExpression(),this.advanceAfterVariableEnd(),buf.push(new nodes.Output(tok.lineno,tok.colno,[nextVal]))):tok.type!=lexer.TOKEN_COMMENT&&this.fail("Unexpected token at top-level: "+tok.type,tok.lineno,tok.colno)}return buf},parse:function(){return new nodes.NodeList(0,0,this.parseNodes())},parseAsRoot:function(){return new nodes.Root(0,0,this.parseNodes())}});modules.parser={parse:function(src,extensions){var src=new Parser(lexer.lex(src));return void 0!==extensions&&(src.extensions=extensions),src.parseAsRoot()}}}(),nodes=modules.nodes,sym=0,modules.transformer={transform:transform},function(){function binOpEmitter(str){return function(node,frame){this.compile(node.left,frame),this.emit(str),this.compile(node.right,frame)}}function quotedArray(arr){lib.map(arr,function(x){return'"'+x+'"'})}var lib=modules.lib,parser=modules.parser,transformer=modules.transformer,nodes=modules.nodes,Object=modules.object,Frame=modules.runtime.Frame,compareOps={"==":"==","!=":"!=","<":"<",">":">","<=":"<=",">=":">="},Compiler=Object.extend({init:function(){this.codebuf=[],this.lastId=0,this.buffer=null,this.bufferStack=[],this.isChild=!1,this.scopeClosers=""},fail:function(msg,lineno,colno){throw void 0!==lineno&&(lineno+=1),void 0!==colno&&(colno+=1),new lib.TemplateError(msg,lineno,colno)},pushBufferId:function(id){this.bufferStack.push(this.buffer),this.buffer=id,this.emit("var "+this.buffer+' = "";')},popBufferId:function(){this.buffer=this.bufferStack.pop()},emit:function(code){this.codebuf.push(code)},emitLine:function(code){this.emit(code+"\n")},emitLines:function(){lib.each(lib.toArray(arguments),function(line){this.emitLine(line)},this)},emitFuncBegin:function(name){this.buffer="output",this.scopeClosers="",this.emitLine("function "+name+"(env, context, frame, runtime, cb) {"),this.emitLine("var lineno = null;"),this.emitLine("var colno = null;"),this.emitLine("var "+this.buffer+' = "";'),this.emitLine("try {")},emitFuncEnd:function(noReturn){noReturn||this.emitLine("cb(null, "+this.buffer+");"),this.closeScopeLevels(),this.emitLine("} catch (e) {"),this.emitLine("  cb(runtime.handleError(e, lineno, colno));"),this.emitLine("}"),this.emitLine("}"),this.buffer=null},addScopeLevel:function(){this.scopeClosers+="})"},closeScopeLevels:function(){this.emitLine(this.scopeClosers+";"),this.scopeClosers=""},withScopedSyntax:function(func){var scopeClosers=this.scopeClosers;this.scopeClosers="",func.call(this),this.closeScopeLevels(),this.scopeClosers=scopeClosers},makeCallback:function(res){var err=this.tmpid();return"function("+err+(res?","+res:"")+") {\nif("+err+") { cb("+err+"); return; }"},tmpid:function(){return this.lastId++,"t_"+this.lastId},_bufferAppend:function(func){this.emit(this.buffer+" += runtime.suppressValue("),func.call(this),this.emit(", env.autoesc);\n")},_compileChildren:function(node,frame){for(var children=node.children,i=0,l=children.length;i<l;i++)this.compile(children[i],frame)},_compileAggregate:function(node,frame,startChar,endChar){startChar&&this.emit(startChar);for(var i=0;i<node.children.length;i++)0<i&&this.emit(","),this.compile(node.children[i],frame);endChar&&this.emit(endChar)},_compileExpression:function(node,frame){this.assertType(node,nodes.Literal,nodes.Symbol,nodes.Group,nodes.Array,nodes.Dict,nodes.FunCall,nodes.Filter,nodes.LookupVal,nodes.Compare,nodes.InlineIf,nodes.And,nodes.Or,nodes.Not,nodes.Add,nodes.Sub,nodes.Mul,nodes.Div,nodes.FloorDiv,nodes.Mod,nodes.Pow,nodes.Neg,nodes.Pos,nodes.Compare,nodes.NodeList),this.compile(node,frame)},assertType:function(node){for(var types=lib.toArray(arguments).slice(1),success=!1,i=0;i<types.length;i++)node instanceof types[i]&&(success=!0);success||this.fail("assertType: invalid type: "+node.typename,node.lineno,node.colno)},compileCallExtension:function(node,frame,async){var name=node.extName,args=node.args,contentArgs=node.contentArgs,autoescape="boolean"!=typeof node.autoescape||node.autoescape,transformedArgs=[],node;async||this.emit(this.buffer+" += runtime.suppressValue("),this.emit('env.getExtension("'+node.extName+'")["'+node.prop+'"]('),this.emit("context"),(args||contentArgs)&&this.emit(","),args&&(args instanceof nodes.NodeList||this.fail("compileCallExtension: arguments must be a NodeList, use `parser.parseSignature`"),lib.each(args.children,function(arg,i){this._compileExpression(arg,frame),i==args.children.length-1&&!contentArgs.length||this.emit(",")},this)),contentArgs.length&&lib.each(contentArgs,function(arg,i){var id;0<i&&this.emit(","),arg?(id=this.tmpid(),this.emitLine("function(cb) {"),this.emitLine("if(!cb) { cb = function(err) { if(err) { throw err; }}}"),this.pushBufferId(id),this.withScopedSyntax(function(){this.compile(arg,frame),this.emitLine("cb(null, "+id+");")}),this.popBufferId(),this.emitLine("return "+id+";"),this.emitLine("}")):this.emit("null")},this),async?(node=this.tmpid(),this.emitLine(", "+this.makeCallback(node)),this.emitLine(this.buffer+" += runtime.suppressValue("+node+", "+autoescape+" && env.autoesc);"),this.addScopeLevel()):(this.emit(")"),this.emit(", "+autoescape+" && env.autoesc);\n"))},compileCallExtensionAsync:function(node,frame){this.compileCallExtension(node,frame,!0)},compileNodeList:function(node,frame){this._compileChildren(node,frame)},compileLiteral:function(node,frame){var val,val;"string"==typeof node.value?(val=(val=(val=(val=(val=node.value.replace(/\\/g,"\\\\")).replace(/"/g,'\\"')).replace(/\n/g,"\\n")).replace(/\r/g,"\\r")).replace(/\t/g,"\\t"),this.emit('"'+val+'"')):this.emit(node.value.toString())},compileSymbol:function(node,frame){var node=node.value,frame;(frame=frame.lookup(node))?this.emit(frame):this.emit('runtime.contextOrFrameLookup(context, frame, "'+node+'")')},compileGroup:function(node,frame){this._compileAggregate(node,frame,"(",")")},compileArray:function(node,frame){this._compileAggregate(node,frame,"[","]")},compileDict:function(node,frame){this._compileAggregate(node,frame,"{","}")},compilePair:function(node,frame){var key=node.key,node=node.value;key instanceof nodes.Symbol?key=new nodes.Literal(key.lineno,key.colno,key.value):key instanceof nodes.Literal&&"string"==typeof key.value||this.fail("compilePair: Dict keys must be strings or names",key.lineno,key.colno),this.compile(key,frame),this.emit(": "),this._compileExpression(node,frame)},compileInlineIf:function(node,frame){this.emit("("),this.compile(node.cond,frame),this.emit("?"),this.compile(node.body,frame),this.emit(":"),null!==node.else_?this.compile(node.else_,frame):this.emit('""'),this.emit(")")},compileOr:binOpEmitter(" || "),compileAnd:binOpEmitter(" && "),compileAdd:binOpEmitter(" + "),compileSub:binOpEmitter(" - "),compileMul:binOpEmitter(" * "),compileDiv:binOpEmitter(" / "),compileMod:binOpEmitter(" % "),compileNot:function(node,frame){this.emit("!"),this.compile(node.target,frame)},compileFloorDiv:function(node,frame){this.emit("Math.floor("),this.compile(node.left,frame),this.emit(" / "),this.compile(node.right,frame),this.emit(")")},compilePow:function(node,frame){this.emit("Math.pow("),this.compile(node.left,frame),this.emit(", "),this.compile(node.right,frame),this.emit(")")},compileNeg:function(node,frame){this.emit("-"),this.compile(node.target,frame)},compilePos:function(node,frame){this.emit("+"),this.compile(node.target,frame)},compileCompare:function(node,frame){this.compile(node.expr,frame);for(var i=0;i<node.ops.length;i++){var n=node.ops[i];this.emit(" "+compareOps[n.type]+" "),this.compile(n.expr,frame)}},compileLookupVal:function(node,frame){this.emit("runtime.memberLookup(("),this._compileExpression(node.target,frame),this.emit("),"),this._compileExpression(node.val,frame),this.emit(", env.autoesc)")},_getNodeName:function(node){switch(node.typename){case"Symbol":return node.value;case"FunCall":return"the return value of ("+this._getNodeName(node.name)+")";case"LookupVal":return this._getNodeName(node.target)+'["'+this._getNodeName(node.val)+'"]';case"Literal":return node.value.toString().substr(0,10);default:return"--expression--"}},compileFunCall:function(node,frame){this.emit("(lineno = "+node.lineno+", colno = "+node.colno+", "),this.emit("runtime.callWrap("),this._compileExpression(node.name,frame),this.emit(', "'+this._getNodeName(node.name).replace(/"/g,'\\"')+'", '),this._compileAggregate(node.args,frame,"[","])"),this.emit(")")},compileFilter:function(node,frame){var name=node.name;this.assertType(name,nodes.Symbol),this.emit('env.getFilter("'+name.value+'").call(context, '),this._compileAggregate(node.args,frame),this.emit(")")},compileFilterAsync:function(node,frame){var name=node.name,symbol=(this.assertType(name,nodes.Symbol),node.symbol.value);frame.set(symbol,symbol),this.emit('env.getFilter("'+name.value+'").call(context, '),this._compileAggregate(node.args,frame),this.emitLine(", "+this.makeCallback(symbol)),this.addScopeLevel()},compileKeywordArgs:function(node,frame){var names=[];lib.each(node.children,function(pair){names.push(pair.key.value)}),this.emit("runtime.makeKeywordArgs("),this.compileDict(node,frame),this.emit(")")},compileSet:function(node,frame){var ids=[];lib.each(node.targets,function(target){var target=target.value,target=frame.get(target);null===target&&(target=this.tmpid(),this.emitLine("var "+target+";")),ids.push(target)},this),this.emit(ids.join(" = ")+" = "),this._compileExpression(node.value,frame),this.emitLine(";"),lib.each(node.targets,function(target,i){var i=ids[i],target=target.value;this.emitLine('frame.set("'+target+'", '+i+");"),null===frame.get(target)&&frame.set(target,i),this.emitLine("if(!frame.parent) {"),this.emitLine('context.setVariable("'+target+'", '+i+");"),"_"!=target.charAt(0)&&this.emitLine('context.addExport("'+target+'");'),this.emitLine("}")},this)},compileIf:function(node,frame,async){this.emit("if("),this._compileExpression(node.cond,frame),this.emitLine(") {"),this.withScopedSyntax(function(){this.compile(node.body,frame),async&&this.emit("cb()")}),node.else_?(this.emitLine("}\nelse {"),this.withScopedSyntax(function(){this.compile(node.else_,frame),async&&this.emit("cb()")})):async&&(this.emitLine("}\nelse {"),this.emit("cb()")),this.emitLine("}")},compileIfAsync:function(node,frame){this.emit("(function(cb) {"),this.compileIf(node,frame,!0),this.emit("})(function() {"),this.addScopeLevel()},scanLoop:function(node){var loopUses={};return node.iterFields(function(field){var field=field.findAll(nodes.LookupVal);lib.each(field,function(lookup){lookup.target instanceof nodes.Symbol&&"loop"==lookup.target.value&&lookup.val instanceof nodes.Literal&&(loopUses[lookup.val.value]=!0)})}),loopUses},emitLoopBindings:function(node,loopUses,arr,i,len){var bindings={index:i+" + 1",index0:i,revindex:(len=len||arr+".length")+" - "+i,revindex0:len+" - "+i+" - 1",first:i+" === 0",last:i+" === "+len+" - 1",length:len},name;for(name in bindings)name in loopUses&&this.emitLine('frame.set("loop.'+name+'", '+bindings[name]+");")},compileFor:function(node,frame){var i=this.tmpid(),len=this.tmpid(),arr=this.tmpid(),loopUses=this.scanLoop(node);if(frame=frame.push(),this.emitLine("frame = frame.push();"),this.emit("var "+arr+" = "),this._compileExpression(node.arr,frame),this.emitLine(";"),this.emit("if("+arr+") {"),node.name instanceof nodes.Array){this.emitLine("var "+i+";"),this.emitLine("if(runtime.isArray("+arr+")) {"),this.emitLine("for("+i+"=0; "+i+" < "+arr+".length; "+i+"++) {");for(var u=0;u<node.name.children.length;u++){var tid=this.tmpid();this.emitLine("var "+tid+" = "+arr+"["+i+"]["+u+"]"),this.emitLine('frame.set("'+node.name.children[u].value+'", '+arr+"["+i+"]["+u+"]);"),frame.set(node.name.children[u].value,tid)}this.emitLoopBindings(node,loopUses,arr,i),this.withScopedSyntax(function(){this.compile(node.body,frame)}),this.emitLine("}"),this.emitLine("} else {");var key=node.name.children[0],val=node.name.children[1],k=this.tmpid(),v=this.tmpid();frame.set(key.value,k),frame.set(val.value,v),this.emitLine(i+" = -1;"),(loopUses.revindex||loopUses.revindex0||loopUses.last||loopUses.length)&&this.emitLine("var "+len+" = runtime.keys("+arr+").length;"),this.emitLine("for(var "+k+" in "+arr+") {"),this.emitLine(i+"++;"),this.emitLine("var "+v+" = "+arr+"["+k+"];"),this.emitLine('frame.set("'+key.value+'", '+k+");"),this.emitLine('frame.set("'+val.value+'", '+v+");"),this.emitLoopBindings(node,loopUses,arr,i,len),this.withScopedSyntax(function(){this.compile(node.body,frame)}),this.emitLine("}"),this.emitLine("}")}else{var v=this.tmpid();frame.set(node.name.value,v),this.emitLine("for(var "+i+"=0; "+i+" < "+arr+".length; "+i+"++) {"),this.emitLine("var "+v+" = "+arr+"["+i+"];"),this.emitLine('frame.set("'+node.name.value+'", '+v+");"),this.emitLoopBindings(node,loopUses,arr,i),this.withScopedSyntax(function(){this.compile(node.body,frame)}),this.emitLine("}")}this.emitLine("}"),this.emitLine("frame = frame.pop();")},_compileAsyncLoop:function(node,frame,parallel){var i=this.tmpid(),len=this.tmpid(),arr=this.tmpid(),loopUses=this.scanLoop(node),asyncMethod=parallel?"asyncAll":"asyncEach",id,asyncMethod=(frame=frame.push(),this.emitLine("frame = frame.push();"),this.emit("var "+arr+" = "),this._compileExpression(node.arr,frame),this.emitLine(";"),node.name instanceof nodes.Array?(this.emit("runtime."+asyncMethod+"("+arr+", "+node.name.children.length+", function("),lib.each(node.name.children,function(name){this.emit(name.value+",")},this),this.emit(i+","+len+",next) {"),lib.each(node.name.children,function(name){var name=name.value;frame.set(name,name),this.emitLine('frame.set("'+name+'", '+name+");")},this)):(id=node.name.value,this.emitLine("runtime."+asyncMethod+"("+arr+", 1, function("+id+", "+i+", "+len+",next) {"),this.emitLine('frame.set("'+id+'", '+id+");"),frame.set(id,id)),this.emitLoopBindings(node,loopUses,arr,i,len),this.withScopedSyntax(function(){var buf;parallel&&(buf=this.tmpid(),this.pushBufferId(buf)),this.compile(node.body,frame),this.emitLine("next("+i+(buf?","+buf:"")+");"),parallel&&this.popBufferId()}),this.tmpid());this.emitLine("}, "+this.makeCallback(asyncMethod)),this.addScopeLevel(),parallel&&this.emitLine(this.buffer+" += "+asyncMethod+";"),this.emitLine("frame = frame.pop();")},compileAsyncEach:function(node,frame){this._compileAsyncLoop(node,frame)},compileAsyncAll:function(node,frame){this._compileAsyncLoop(node,frame,!0)},_emitMacroBegin:function(node,frame){var args=[],kwargs=null,funcId="macro_"+this.tmpid(),realNames=(lib.each(node.args.children,function(arg,i){i===node.args.children.length-1&&arg instanceof nodes.Dict?kwargs=arg:(this.assertType(arg,nodes.Symbol),args.push(arg))},this),lib.map(args,function(n){return"l_"+n.value})),argNames=(realNames.push("kwargs"),lib.map(args,function(n){return'"'+n.value+'"'})),kwargNames=lib.map(kwargs&&kwargs.children||[],function(n){return'"'+n.key.value+'"'});return this.emitLines("var "+funcId+" = runtime.makeMacro(","["+argNames.join(", ")+"], ","["+kwargNames.join(", ")+"], ","function ("+realNames.join(", ")+") {","frame = frame.push();","kwargs = kwargs || {};"),lib.each(args,function(arg){this.emitLine('frame.set("'+arg.value+'", l_'+arg.value+");"),frame.set(arg.value,"l_"+arg.value)},this),kwargs&&lib.each(kwargs.children,function(pair){var name=pair.key.value;this.emit('frame.set("'+name+'", kwargs.hasOwnProperty("'+name+'") ? kwargs["'+name+'"] : '),this._compileExpression(pair.value,frame),this.emitLine(");")},this),funcId},_emitMacroEnd:function(){this.emitLine("frame = frame.pop();"),this.emitLine("return new runtime.SafeString("+this.buffer+");"),this.emitLine("});")},compileMacro:function(node,frame){frame=frame.push();var funcId=this._emitMacroBegin(node,frame),prevBuffer=this.buffer,prevBuffer=(this.buffer="output",this.emitLine("var "+this.buffer+'= "";'),this.compile(node.body,frame),this._emitMacroEnd(),this.buffer=prevBuffer,node.name.value);(frame=frame.pop()).set(prevBuffer,funcId),frame.parent?this.emitLine('frame.set("'+prevBuffer+'", '+funcId+");"):("_"!=node.name.value.charAt(0)&&this.emitLine('context.addExport("'+prevBuffer+'");'),this.emitLine('context.setVariable("'+prevBuffer+'", '+funcId+");"))},compileImport:function(node,frame){var id=this.tmpid(),target=node.target.value;this.emit("env.getTemplate("),this._compileExpression(node.template,frame),this.emitLine(", "+this.makeCallback(id)),this.addScopeLevel(),this.emitLine(id+".getExported("+this.makeCallback(id)),this.addScopeLevel(),frame.set(target,id),frame.parent?this.emitLine('frame.set("'+target+'", '+id+");"):this.emitLine('context.setVariable("'+target+'", '+id+");")},compileFromImport:function(node,frame){var importedId=this.tmpid();this.emit("env.getTemplate("),this._compileExpression(node.template,frame),this.emitLine(", "+this.makeCallback(importedId)),this.addScopeLevel(),this.emitLine(importedId+".getExported("+this.makeCallback(importedId)),this.addScopeLevel(),lib.each(node.names.children,function(nameNode){var name,nameNode,id=this.tmpid(),nameNode=nameNode instanceof nodes.Pair?(name=nameNode.key.value,nameNode.value.value):name=nameNode.value;this.emitLine("if("+importedId+'.hasOwnProperty("'+name+'")) {'),this.emitLine("var "+id+" = "+importedId+"."+name+";"),this.emitLine("} else {"),this.emitLine("cb(new Error(\"cannot import '"+name+"'\")); return;"),this.emitLine("}"),frame.set(nameNode,id),frame.parent?this.emitLine('frame.set("'+nameNode+'", '+id+");"):this.emitLine('context.setVariable("'+nameNode+'", '+id+");")},this)},compileBlock:function(node,frame){var id;this.isChild||(id=this.tmpid(),this.emitLine('context.getBlock("'+node.name.value+'")(env, context, frame, runtime, '+this.makeCallback(id)),this.emitLine(this.buffer+" += "+id+";"),this.addScopeLevel())},compileSuper:function(node,frame){var name=node.blockName.value,node=node.symbol.value;this.emitLine('context.getSuper(env, "'+name+'", b_'+name+", frame, runtime, "+this.makeCallback(node)),this.emitLine(node+" = runtime.markSafe("+node+");"),this.addScopeLevel(),frame.set(node,node)},compileExtends:function(node,frame){this.isChild&&this.fail("compileExtends: cannot extend multiple times",node.template.lineno,node.template.colno);var k=this.tmpid();this.emit("env.getTemplate("),this._compileExpression(node.template,frame),this.emitLine(", true, "+this.makeCallback("parentTemplate")),this.emitLine("for(var "+k+" in parentTemplate.blocks) {"),this.emitLine("context.addBlock("+k+", parentTemplate.blocks["+k+"]);"),this.emitLine("}"),this.addScopeLevel(),this.isChild=!0},compileInclude:function(node,frame){var id=this.tmpid(),id2=this.tmpid();this.emit("env.getTemplate("),this._compileExpression(node.template,frame),this.emitLine(", "+this.makeCallback(id)),this.addScopeLevel(),this.emitLine(id+".render(context.getVariables(), frame.push(), "+this.makeCallback(id2)),this.emitLine(this.buffer+" += "+id2),this.addScopeLevel()},compileTemplateData:function(node,frame){this.compileLiteral(node,frame)},compileOutput:function(node,frame){for(var children=node.children,i=0,l=children.length;i<l;i++)children[i]instanceof nodes.TemplateData?children[i].value&&(this.emit(this.buffer+" += "),this.compileLiteral(children[i],frame),this.emitLine(";")):(this.emit(this.buffer+" += runtime.suppressValue("),this.compile(children[i],frame),this.emit(", env.autoesc);\n"))},compileRoot:function(node,frame){frame&&this.fail("compileRoot: root node can't have frame"),frame=new Frame,this.emitFuncBegin("root"),this._compileChildren(node,frame),this.isChild&&this.emitLine("parentTemplate.rootRenderFunc(env, context, frame, runtime, cb);"),this.emitFuncEnd(this.isChild),this.isChild=!1;for(var blocks=node.findAll(nodes.Block),i=0;i<blocks.length;i++){var block,name=(block=blocks[i]).name.value,tmpFrame=(this.emitFuncBegin("b_"+name),new Frame);this.compile(block.body,tmpFrame),this.emitFuncEnd()}this.emitLine("return {");for(var i=0;i<blocks.length;i++){var block,name="b_"+(block=blocks[i]).name.value;this.emitLine(name+": "+name+",")}this.emitLine("root: root\n};")},compile:function(node,frame){var _compile=this["compile"+node.typename];_compile?_compile.call(this,node,frame):this.fail("compile: Cannot compile node: "+node.typename,node.lineno,node.colno)},getCode:function(){return this.codebuf.join("")}});modules.compiler={compile:function(src,asyncFilters,extensions,name){var c=new Compiler;if(extensions&&extensions.length)for(var i=0;i<extensions.length;i++)"preprocess"in extensions[i]&&(src=extensions[i].preprocess(src,name));return c.compile(transformer.transform(parser.parse(src,extensions),asyncFilters,name)),c.getCode()},Compiler:Compiler}}(),function(){var lib=modules.lib,r=modules.runtime,filters={abs:function(n){return Math.abs(n)},batch:function(arr,linecount,fill_with){for(var res=[],tmp=[],i=0;i<arr.length;i++)i%linecount==0&&tmp.length&&(res.push(tmp),tmp=[]),tmp.push(arr[i]);if(tmp.length){if(fill_with)for(var i=tmp.length;i<linecount;i++)tmp.push(fill_with);res.push(tmp)}return res},capitalize:function(str){var ret=str.toLowerCase();return r.copySafeness(str,ret.charAt(0).toUpperCase()+ret.slice(1))},center:function(str,width){if(str.length>=(width=width||80))return str;var width=width-str.length,pre=lib.repeat(" ",width/2-width%2),width=lib.repeat(" ",width/2);return r.copySafeness(str,pre+str+width)},default:function(val,def){return val||def},dictsort:function(val,case_sensitive,by){if(!lib.isObject(val))throw new lib.TemplateError("dictsort filter: val must be an object");var array=[],k,si;for(k in val)array.push([k,val[k]]);if(void 0===by||"key"===by)si=0;else{if("value"!==by)throw new lib.TemplateError("dictsort filter: You can only sort by either key or value");si=1}return array.sort(function(t1,t2){var t1=t1[si],t2=t2[si];return case_sensitive||(lib.isString(t1)&&(t1=t1.toUpperCase()),lib.isString(t2)&&(t2=t2.toUpperCase())),t2<t1?1:t1==t2?0:-1}),array},escape:function(str){return"string"==typeof str||str instanceof r.SafeString?lib.escape(str):str},safe:function(str){return r.markSafe(str)},first:function(arr){return arr[0]},groupby:function(arr,attr){return lib.groupBy(arr,attr)},indent:function(str,width,indentfirst){width=width||4;for(var res="",lines=str.split("\n"),sp=lib.repeat(" ",width),i=0;i<lines.length;i++)res+=0!=i||indentfirst?sp+lines[i]+"\n":lines[i]+"\n";return r.copySafeness(str,res)},join:function(arr,del,attr){return del=del||"",(arr=attr?lib.map(arr,function(v){return v[attr]}):arr).join(del)},last:function(arr){return arr[arr.length-1]},length:function(arr){return arr.length},list:function(val){if(lib.isString(val))return val.split("");if(lib.isObject(val)){var keys=[];if(Object.keys)keys=Object.keys(val);else for(var k in val)keys.push(k);return lib.map(keys,function(k){return{key:k,value:val[k]}})}throw new lib.TemplateError("list filter: type not iterable")},lower:function(str){return str.toLowerCase()},random:function(arr){return arr[Math.floor(Math.random()*arr.length)]},replace:function(str,old,new_,maxCount){for(var res,last=res=str,count=1,res=res.replace(old,new_);last!=res&&!(maxCount<=count);)res=(last=res).replace(old,new_),count++;return r.copySafeness(str,res)},reverse:function(val){var arr,arr=lib.isString(val)?filters.list(val):lib.map(val,function(v){return v});return arr.reverse(),lib.isString(val)?r.copySafeness(val,arr.join("")):arr},round:function(val,precision,method){precision=precision||0;var precision=Math.pow(10,precision),method,method="ceil"==method?Math.ceil:"floor"==method?Math.floor:Math.round;return method(val*precision)/precision},slice:function(arr,slices,fillWith){for(var sliceLength=Math.floor(arr.length/slices),extra=arr.length%slices,offset=0,res=[],i=0;i<slices;i++){var start=offset+i*sliceLength;i<extra&&offset++;var end,start=arr.slice(start,offset+(i+1)*sliceLength);fillWith&&extra<=i&&start.push(fillWith),res.push(start)}return res},sort:function(arr,reverse,caseSens,attr){return(arr=lib.map(arr,function(v){return v})).sort(function(a,b){var x,a,a=attr?(x=a[attr],b[attr]):(x=a,b);return!caseSens&&lib.isString(x)&&lib.isString(a)&&(x=x.toLowerCase(),a=a.toLowerCase()),x<a?reverse?1:-1:a<x?reverse?-1:1:0}),arr},string:function(obj){return r.copySafeness(obj,obj)},title:function(str){for(var words=str.split(" "),i=0;i<words.length;i++)words[i]=filters.capitalize(words[i]);return r.copySafeness(str,words.join(" "))},trim:function(str){return r.copySafeness(str,str.replace(/^\s*|\s*$/g,""))},truncate:function(input,length,killwords,end){var orig=input,killwords;return input.length<=(length=length||255)?input:(input=killwords?input.substring(0,length):(-1===(killwords=input.lastIndexOf(" ",length))&&(killwords=length),input.substring(0,killwords)),r.copySafeness(orig,input+=null!=end?end:"..."))},upper:function(str){return str.toUpperCase()},urlencode:function(obj){var enc=encodeURIComponent,parts;if(lib.isString(obj))return enc(obj);if(lib.isArray(obj))parts=obj.map(function(item){return enc(item[0])+"="+enc(item[1])});else for(var k in parts=[],obj)obj.hasOwnProperty(k)&&parts.push(enc(k)+"="+enc(obj[k]));return parts.join("&")},urlize:function(str,length,nofollow){isNaN(length)&&(length=1/0);var noFollowAttr=!0===nofollow?' rel="nofollow"':"",puncRE=/^(?:\(|<|&lt;)?(.*?)(?:\.|,|\)|\n|&gt;)?$/,emailRE=/^[\w.!#$%&'*+\-\/=?\^`{|}~]+@[a-z\d\-]+(\.[a-z\d\-]+)+$/i,httpHttpsRE=/^https?:\/\/.*$/,wwwRE=/^www\./,tldRE=/\.(?:org|net|com)(?:\:|\/|$)/,words;return str.split(/\s+/).filter(function(word){return word&&word.length}).map(function(word){var matches=word.match(puncRE),matches=matches&&matches[1]||word;return httpHttpsRE.test(matches)?'<a href="'+matches+'"'+noFollowAttr+">"+matches.substr(0,length)+"</a>":wwwRE.test(matches)?'<a href="http://'+matches+'"'+noFollowAttr+">"+matches.substr(0,length)+"</a>":emailRE.test(matches)?'<a href="mailto:'+matches+'">'+matches+"</a>":tldRE.test(matches)?'<a href="http://'+matches+'"'+noFollowAttr+">"+matches.substr(0,length)+"</a>":matches}).join(" ")},wordcount:function(str){return str.match(/\w+/g).length},float:function(val,def){var val=parseFloat(val);return isNaN(val)?def:val},int:function(val,def){var val=parseInt(val,10);return isNaN(val)?def:val}};filters.d=filters.default,filters.e=filters.escape,modules.filters=filters}(),modules.globals={range:function(start,stop,step){step=stop?step||1:(stop=start,start=0,1);for(var arr=[],i=start;i<stop;i+=step)arr.push(i);return arr},cycler:function(){return cycler(Array.prototype.slice.call(arguments))},joiner:joiner},function(){var Obj=modules.object,lib=modules.lib,Obj=Obj.extend({on:function(name,func){this.listeners=this.listeners||{},this.listeners[name]=this.listeners[name]||[],this.listeners[name].push(func)},emit:function(name){var args=Array.prototype.slice.call(arguments,1);this.listeners&&this.listeners[name]&&lib.each(this.listeners[name],function(listener){listener.apply(null,args)})}});modules.loader=Obj}(),function(){var Loader,WebLoader=modules.loader.extend({init:function(baseURL,neverUpdate){this.precompiled=window.nunjucksPrecompiled||{},this.baseURL=baseURL||"",this.neverUpdate=neverUpdate},getSource:function(name){var src;return this.precompiled[name]?{src:{type:"code",obj:this.precompiled[name]},path:name}:(src=this.fetch(this.baseURL+"/"+name))?{src:src,path:name,noCache:!this.neverUpdate}:null},fetch:function(url,callback){var ajax,loading=!0,src;return window.XMLHttpRequest?ajax=new XMLHttpRequest:window.ActiveXObject&&(ajax=new ActiveXObject("Microsoft.XMLHTTP")),ajax.onreadystatechange=function(){4!==ajax.readyState||0!==ajax.status&&200!==ajax.status||!loading||(loading=!1,src=ajax.responseText)},url+=(-1===url.indexOf("?")?"?":"&")+"s="+(new Date).getTime(),ajax.open("GET",url,!1),ajax.send(),src}});modules["web-loaders"]={WebLoader:WebLoader}}(),"undefined"==typeof window?modules.loaders=modules["node-loaders"]:modules.loaders=modules["web-loaders"],function(){var lib=modules.lib,Obj=modules.object,lexer=modules.lexer,compiler=modules.compiler,builtin_filters=modules.filters,builtin_loaders=modules.loaders,runtime=modules.runtime,globals=modules.globals,Frame=runtime.Frame,Environment=Obj.extend({init:function(loaders,opts){for(var name in this.dev=!!(opts=opts||{}).dev,this.autoesc=!!opts.autoescape,loaders?this.loaders=lib.isArray(loaders)?loaders:[loaders]:builtin_loaders.FileSystemLoader?this.loaders=[new builtin_loaders.FileSystemLoader("views")]:this.loaders=[new builtin_loaders.WebLoader("/views")],this.initCache(),this.filters={},this.asyncFilters=[],this.extensions={},this.extensionsList=[],opts.tags&&lexer.setTags(opts.tags),builtin_filters)this.addFilter(name,builtin_filters[name])},initCache:function(){var cache={};lib.each(this.loaders,function(loader){"function"==typeof loader.on&&loader.on("update",function(template){cache[template]=null})}),this.cache=cache},addExtension:function(name,extension){extension._name=name,this.extensions[name]=extension,this.extensionsList.push(extension)},getExtension:function(name){return this.extensions[name]},addFilter:function(name,func,async){var func=func;async&&this.asyncFilters.push(name),this.filters[name]=func},getFilter:function(name){if(this.filters[name])return this.filters[name];throw new Error("filter not found: "+name)},getTemplate:function(name,eagerCompile,cb){if(name&&name.raw&&(name=name.raw),lib.isFunction(eagerCompile)&&(cb=eagerCompile,eagerCompile=!1),"string"!=typeof name)throw new Error("template names must be a string: "+name);var tmpl=this.cache[name],syncResult;return tmpl?(eagerCompile&&tmpl.compile(),cb?void cb(null,tmpl):tmpl):(lib.asyncIter(this.loaders,function(loader,i,next,done){function handle(src){src?done(src):next()}loader.async?loader.getSource(name,function(err,src){if(err)throw err;handle(src)}):handle(loader.getSource(name))},function(info){if(info){var tmpl=new Template(info.src,this,info.path,eagerCompile);info.noCache||(this.cache[name]=tmpl),cb?cb(null,tmpl):syncResult=tmpl}else{var info=new Error("template not found: "+name);if(!cb)throw info;cb(info)}}.bind(this)),syncResult)},express:function(app){function NunjucksView(name,opts){this.name=name,this.path=name}var env=this;NunjucksView.prototype.render=function(opts,cb){env.render(this.name,opts,cb)},app.set("view",NunjucksView)},render:function(name,ctx,cb){lib.isFunction(ctx)&&(cb=ctx,ctx=null);var syncResult=null;return this.getTemplate(name,function(err,tmpl){if(err&&cb)cb(err);else{if(err)throw err;tmpl.render(ctx,cb||function(err,res){if(err)throw err;syncResult=res})}}),syncResult},renderString:function(src,ctx,cb){var tmpl;return new Template(src,this).render(ctx,cb)}}),Context=Obj.extend({init:function(ctx,blocks){for(var name in this.ctx=ctx,this.blocks={},this.exported=[],blocks)this.addBlock(name,blocks[name])},lookup:function(name){return(name in globals&&!(name in this.ctx)?globals:this.ctx)[name]},setVariable:function(name,val){this.ctx[name]=val},getVariables:function(){return this.ctx},addBlock:function(name,block){this.blocks[name]=this.blocks[name]||[],this.blocks[name].push(block)},getBlock:function(name){if(this.blocks[name])return this.blocks[name][0];throw new Error('unknown block "'+name+'"')},getSuper:function(env,name,block,frame,runtime,cb){var block=(this.blocks[name]||[]).indexOf(block),blk=this.blocks[name][block+1],context=this;if(-1==block||!blk)throw new Error('no super block available for "'+name+'"');blk(env,this,frame,runtime,cb)},addExport:function(name){this.exported.push(name)},getExported:function(){for(var exported={},i=0;i<this.exported.length;i++){var name=this.exported[i];exported[name]=this.ctx[name]}return exported}}),Template=Obj.extend({init:function(src,env,path,eagerCompile){if(this.env=env||new Environment,lib.isObject(src))switch(src.type){case"code":this.tmplProps=src.obj;break;case"string":this.tmplStr=src.obj}else{if(!lib.isString(src))throw new Error("src must be a string or an object describing the source");this.tmplStr=src}this.path=path,eagerCompile?lib.withPrettyErrors(this.path,this.env.dev,this._compile.bind(this)):this.compiled=!1},render:function(ctx,frame,cb){return"function"==typeof ctx?(cb=ctx,ctx={}):"function"==typeof frame&&(cb=frame,frame=null),lib.withPrettyErrors(this.path,this.env.dev,function(){this.compile();var context=new Context(ctx||{},this.blocks),syncResult=null;return this.rootRenderFunc(this.env,context,frame||new Frame,runtime,cb||function(err,res){if(err)throw err;syncResult=res}),syncResult}.bind(this))},getExported:function(cb){this.compile();var context=new Context({},this.blocks);this.rootRenderFunc(this.env,context,new Frame,runtime,function(){cb(null,context.getExported())})},compile:function(){this.compiled||this._compile()},_compile:function(){var source,source,func,source;source=this.tmplProps||(source=compiler.compile(this.tmplStr,this.env.asyncFilters,this.env.extensionsList,this.path),new Function(source)()),this.blocks=this._getBlocks(source),this.rootRenderFunc=source.root,this.compiled=!0},_getBlocks:function(props){var blocks={},k;for(k in props)"b_"==k.slice(0,2)&&(blocks[k.slice(2)]=props[k]);return blocks}});modules.environment={Environment:Environment,Template:Template}}();var lib=modules.lib,env=modules.environment,compiler=modules.compiler,parser=modules.parser,lexer=modules.lexer,runtime=modules.runtime,Loader=modules.loader,loaders=modules.loaders,precompile=modules.precompile,e,nunjucks;(nunjucks={}).Environment=env.Environment,nunjucks.Template=env.Template,nunjucks.Loader=Loader,nunjucks.FileSystemLoader=loaders.FileSystemLoader,nunjucks.WebLoader=loaders.WebLoader,nunjucks.compiler=compiler,nunjucks.parser=parser,nunjucks.lexer=lexer,nunjucks.runtime=runtime,nunjucks.configure=function(templatesPath,opts){opts=opts||{},lib.isObject(templatesPath)&&(opts=templatesPath,templatesPath=null);var noWatch="watch"in opts&&!opts.watch,loader=loaders.FileSystemLoader||loaders.WebLoader;return e=new env.Environment(new loader(templatesPath,noWatch),opts),opts&&opts.express&&e.express(opts.express),e},nunjucks.compile=function(src,env,path,eagerCompile){return e||nunjucks.configure(),new nunjucks.Template(src,env,path,eagerCompile)},nunjucks.render=function(name,ctx,cb){return e||nunjucks.configure(),e.render(name,ctx,cb)},nunjucks.renderString=function(src,ctx,cb){return e||nunjucks.configure(),e.renderString(src,ctx,cb)},precompile&&(nunjucks.precompile=precompile.precompile,nunjucks.precompileString=precompile.precompileString),nunjucks.require=function(name){return modules[name]},"function"==typeof define&&define.amd?define(function(){return nunjucks}):window.nunjucks=nunjucks}(),zaz.use(function AppFactory(pkg,__privileged){"use strict";var console=pkg.console,validStates=["not-started","ok","loading","building","ready","active","disabled","idle","deprecated","failed"];__privileged.mainFactory.create({id:"app",name:"Application",docs:"http://github.tpn.terra.com/Terra/zaz-cerebro/wiki/Apps",description:"This factory will help you with validation, templates, localization, among other features.\nApps are supposed to cover features with DOM integrations, CSS and JavaScript behavior.",tests:"--",source:"http://github.tpn.terra.com/Terra/zaz-cerebro",version:"1.0.0",implement:{properties:{name:{type:"string",required:!0,format:/^[a-z0-9\-_\.]+$/i},version:{type:"string",required:!0,format:/^((\d+\.){2})\d+((\:\d+)?)$/,description:"Must match something like 1.2.3:4 or at least 1.2.3",value:"0.0.1:1"},setup:{type:"function",required:!0},init:{type:"function",required:!0},teardown:{type:"function",required:!0},state:{type:"string",required:!1,enum:validStates,description:"Must be one of: "+validStates},dictionaries:{type:"mixed",required:!1},templates:{type:"mixed",required:!1},description:{type:"string",required:!1,description:"A brief description about the App"},error:{type:"function",required:!1,description:"Triggered whenever there is an error in your app's script"},docs:{type:"string",required:!1,format:/((http|https):\/\/(\w+:{0,1}\w*@)?(\S+)|)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/,description:"Must be a valid URL to the documentation of your factory's product"},source:{type:"string",required:!1,format:/((http|https):\/\/(\w+:{0,1}\w*@)?(\S+)|)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/,description:"Must be a valid URL to the project's source on Github"},tests:{type:"string",required:!1,format:/((http|https):\/\/(\w+:{0,1}\w*@)?(\S+)|)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/,description:"Must be a valid URL to the project's test page"},extendable:{type:"mixed",required:!1},extend:{type:"function",required:!1,description:"Used to extend the App"},dependencies:{type:"array",required:!1},expects:{type:"object",required:!1}},dependencies:["mod.nunjucks"]},dependencies:["dictFactory","mod.promise"],firstUse:function(AppConstructor,matter){var appInstances=[],Promise=window.Promise;return matter.state&&"deprecated"==matter.state&&console.warn("CAREFUL, you are trying to use a deprecated App!\n"+matter.name+"Has been marked for termination and is with the deprecated status.\nPlease get in touch to the maintainers of this app to see how to proceed.\nThis means this app may no longer work, or even stop working with no previous warning besides this."),function(AppConstructor,matter){return new Promise(function(resolve,reject){function rejected(data){var opts=Application.globalOptions||{};return!data||(!opts.reject||opts.rejectPlugins&&opts.rejectPlugins.length||(opts.rejectPlugins=opts.reject),opts&&opts.rejectPlugins&&opts.rejectPlugins.indexOf&&0<=Application.globalOptions.rejectPlugins.indexOf(data.name)&&(console.log(data.name+" was rejected globaly by the application"),1))}function evaluateCondition(condition,value){var i=0,l=0,ret=!1;if("all"==condition||"any"==condition)return 1;if("function"==typeof condition)return condition(value);if(Array.isArray(condition)){for(l=condition.length;i<l;i++)if("string"==typeof condition[i]&&"!"===condition[i][0]){if(condition[i].substring(1).toLowerCase()==value)return;ret=!0}else condition[i]==value&&(ret=!0);return ret}return"string"==typeof condition&&"!"==condition[0]?condition!=value:condition==value}function matchConditions(data,prop,val,cb){function verifyNextCondition(conditions){if(conditions.length){var cond=conditions.pop();if("validate"==cond)if("function"==typeof data.conditions.validate)try{data.conditions.validate()?verifyNextCondition(conditions):cb(!1)}catch(e){e.message='Validate method triggered an error in "'+data.name+'"\n'+e.message,console.error(e),verifyNextCondition(conditions)}else verifyNextCondition(conditions);else ctx[cond]?ctx[cond].list(function(curList){curList[prop]=val,validator(curList,data.conditions[cond])?verifyNextCondition(conditions):cb(!1)}):verifyNextCondition(conditions)}else cb(!0)}var ctx=pkg.context,condition=null,conditions=data.conditions||{},curList=[];if("disabled"==data.status)cb(!1);else if(data.conditions){var condList=[];for(condition in conditions)conditions.hasOwnProperty(condition)&&condList.push(condition);verifyNextCondition(condList)}else cb(!0)}function announceError(e,mName,dName,msg){if(msg)return console.error(msg,e);console.error("Failed executing the turnOn or turnOff method of "+mName+" for extension "+dName+"\n"+e.message)}function tryAndCall(fn,target,data,errorMsg){if(fn&&"function"==typeof fn)try{fn.apply(target,data)}catch(e){throw new Error(errorMsg,e.message,e)}}function turnItOnOrOff(cond,val,ext,force){var tmp=null;if("disabled"==ext.data.status)return!1;if(!0===force||!1!==force&&evaluateCondition(cond,val))if(ext.data.initialized)try{"function"==typeof ext.matter.turnOn&&ext.matter.turnOn(ext.data,sharedData),"function"==typeof ext.data.turnOn&&ext.data.turnOn(ext.data,sharedData),Application.trigger(ext.data.name,"on")}catch(e){tryAndCall(matter.error,Application,"Failed running error function from Application description!"),announceError(e,ext.matter.name,ext.data.name,"Failed turning "+ext.matter.name+" on in "+matter.name+"!\n"+e.message)}else ext.matter.extend.call(Application.prototype,ext.data,sharedData);else try{"function"==typeof ext.matter.turnOff&&ext.data.initialized&&(tmp=ext.matter.turnOff(ext.data,sharedData)),"function"==typeof ext.data.turnOff&&ext.data.turnOff(ext.data,sharedData),Application.trigger(ext.data.name,"off")}catch(e){tryAndCall(matter.error,Application,"Failed running error function from Application description!"),announceError(e,ext.matter.name,ext.data.name,"Failed turning "+ext.matter.name+" off in "+matter.name+"!\n"+e.message)}}function listenForConditions(ext){var ctx=pkg.context,conditions=ext.data.conditions||[],condition=null,curCondition=null,item="";for(condition in conditions)if(conditions.hasOwnProperty(condition)&&"validate"!=condition&&ctx[condition])for(item in curCondition=conditions[condition])curCondition.hasOwnProperty(item)&&ctx[condition].on(item,function(cond,item){return function(val){matchConditions(ext.data,item,val,function(conditionsMet){conditionsMet?Application.turnOn(ext.data.name):Application&&Application.turnOff(ext.data.name)})}}(curCondition[item],item))}function seekAndDestroy(what,why){if(what.isInstance||what.data&&what.data.isExtension){if(what&&"function"==typeof what.teardown)try{what.teardown.apply(what,[why,what,Application.prototype,sharedData]),appInstances.splice(appInstances.indexOf(what),1)}catch(e){e.message="Failed destroying "+(what.name||"target")+"!\n"+e.message,console.error(e)}}else{what.getExtensionList().forEach(function(ext){if("function"==typeof(ext=what.extensions[ext].data).teardown)try{if("active"!=ext.state)return;ext.teardown.apply(ext,[why,Application,Application.prototype,sharedData])}catch(e){e.message="Failed destroying "+ext+" in "+matter.name+"\n"+e.message,console.error(e)}}),what.extensions={},what.getInstances().forEach(function(inst){if("function"==typeof inst.teardown)try{inst.teardown.apply(inst,[why,Application,Application.prototype,sharedData])}catch(e){e.message="Failed destroying an instance of "+matter.name+"\n"+e.message,console.error(e)}}),appInstances=[];try{matter.teardown.apply(Application,[why,Application,Application.prototype,sharedData])}catch(e){e.message="Failed destroying the app "+(matter.name||"target")+"!\n"+e.message,console.error(e)}}}var Application,sharedData={},validator=function(curList,conditions){var list=curList,item=null;for(item in conditions)if(conditions.hasOwnProperty(item)&&!evaluateCondition(conditions[item],list[item]))return!1;return!0},Application=function(){var args=Array.prototype.slice.call(arguments),valid,details,err,that=this,errMsg,err,details,valid;if(matter.expects&&"object"==typeof matter.expects&&!0!==(valid=pkg.schemas.validate(matter.expects,args[0],matter.name)))throw err="Invalid data passed to "+matter.factoryCreationName,details='\nThe argument passed to "new '+matter.factoryCreationName+'(...)" must match these rules:\n',console.warn(err+details,matter.expects,"\nBut the given object was:\n",args[0],"\nThe validator message was: "+valid),new Error("Check for more information in the previous warning messages");var err=Application.get("state");if("deprecated"==err||"failed"==err||"disabled"==err)return console.warn('Application "'+matter.name+'" was called, but it is inactive, disabled or deprecated!'),!1;1<args.length?(args.push(sharedData),console.warn('Application "'+matter.name+'" called with multiple parameters. Some of with will probably be ignored by the constructor')):args=[void 0!==args[0]?args[0]:null,sharedData],this.destroy=function(why){seekAndDestroy(that,"Method destroy called in instance"+(why?"\n Reason: "+why:"")),Application.trigger("destroyed",this)},this.teardown=function(){console.log("Teardown method not implemented in an instance of "+matter.name)},pkg.utils.make.observable(this,{recursive:!1,useStatic:!0});var details=null;try{details=this.init.apply(this,args)}catch(e){return e.message="Error in application "+matter.name+"!\n"+e.message,console.error(e),!1}return this.isInstance=!0,appInstances.push(details||this),"active"!=Application.get("state")&&Application.set("state","active"),Application.trigger("instanced",details||this),details||this},innerPKG,dictionary,extSchema,ret;if(Application.prototype=new AppConstructor,Application.globalOptions={},Application.globalOptions.rejectPlugins=[],Application.prototype.kind=matter.kind="Application",Application.setGlobalOptions=Application.prototype.setGlobalOptions=function(att,val){if(val||"object"!=typeof att)Application.globalOptions[att]=val;else for(var i in att)att.hasOwnProperty(i)&&Application.setGlobalOptions(i,att[i]);return Application},Application.getOptions=Application.prototype.getGlobalOptions=function(attr){return attr?Application.globalOptions[attr]:Application.globalOptions},pkg.utils.make.observable(Application,{recursive:!1,useStatic:!0}),Application.dispatch=function(eventId,value){var i,current;if(appInstances&&"object"==typeof appInstances&&appInstances.length)for(i=0;i<appInstances.length;i++)if((current=appInstances[i])&&"object"==typeof current&&"function"==typeof current.trigger)try{current.trigger(eventId,value)}catch(dispatchException){console.error('Exception triggering event "'+eventId+'" for an instance of '+matter.name+": "+dispatchException.message)}},Application.getInstances=function(){return appInstances},matter.dependencies=matter.dependencies||[],matter.dependencies.unshift("mod.nunjucks"),Array.isArray(matter.dictionaries)&&matter.dictionaries.length)for(var i=0;i<matter.dictionaries.length;i++)matter.dictionaries[i]&&"string"==typeof matter.dictionaries[i]&&-1===matter.dependencies.indexOf("dict."+matter.dictionaries[i])&&matter.dependencies.push("dict."+matter.dictionaries[i]);function processExtension(ext){!rejected((ext=Application.extensions[ext]).data)&&ext.matter&&matchConditions(ext.data,void 0,void 0,function(conditionsMet){conditionsMet&&!ext.data.initialized&&ext.matter.extend.call(Application.prototype,ext.data,sharedData)})}function startExtensions(state){if("active"==state){var ext=null;for(ext in Application.off("state",startExtensions),Application.extensions)Application.extensions.hasOwnProperty(ext)&&!Application.extensions[ext].setupDone&&processExtension(ext)}}return Array.isArray(matter.dictionaries)&&matter.dictionaries.length&&!/mod\.templateManager/.test(matter.dependencies.join(""))&&matter.dependencies.push("mod.dictionaryManager"),matter.templates&&!/mod\.templateManager/.test(matter.dependencies.join(""))&&matter.dependencies.push("mod.templateManager"),pkg.includer.register(matter.factoryCreationName,{imports:matter.dependencies,exports:(extSchema={properties:{name:{type:"string",required:!0},version:{type:"string",required:!0,format:/^((\d+\.){2})\d+((\:\d+)?)$/},target:{type:"string",required:!0,enum:Object.keys(matter.extendable||{}),descriptions:"Define a target within the app, where your extensions should be applied to"},setup:{type:"function",required:!0,description:"The extension constructor"}}},ret=function(){function Extension(data){pkg.utils.merge(this,data),dictionary&&innerPKG&&innerPKG.mod.templateManager&&"function"==typeof innerPKG.mod.templateManager&&(this.templates=new innerPKG.mod.templateManager(dictionary,this)),matter.templates&&"object"==typeof matter.templates&&"function"==typeof matter.templates.list&&(data.templates=pkg.utils.merge({},matter.templates.list()||{},data.templates||{})),data.templates&&"object"==typeof data.templates&&this.templates.create(data.templates),dictionary&&innerPKG&&innerPKG.mod&&innerPKG.mod.dictionaryManager&&(this.l10n=new innerPKG.mod.dictionaryManager.createDictionary(data),matter.l10n&&matter.l10n.langs&&this.l10n.extend(matter.l10n.langs))}for(var required,innerPKG=required=this,i,current,required=pkg.utils.merge({},this),i=0;i<matter.dependencies.length;i++)required[current=matter.dependencies[i]]=arguments[i];Application.set("state","building"),sharedData.dependencies=required,innerPKG&&innerPKG.mod.dictionaryManager&&"object"==typeof innerPKG.mod.dictionaryManager&&(dictionary=innerPKG.mod.dictionaryManager.createDictionary(matter),Application.prototype.l10n=dictionary),matter.templates&&"object"==typeof matter.templates&&innerPKG&&innerPKG.mod.templateManager&&"function"==typeof innerPKG.mod.templateManager&&(Application.prototype.templates=new innerPKG.mod.templateManager(dictionary,this),Application.prototype.templates.create(matter.templates));var originalMatterExtend=matter.extend||function(){},appP=Application.prototype;return matter.templates=appP.templates,matter.l10n=appP.l10n,matter.getClass=function(){return Application},matter.extend=function(data){var extension,valid,extension=new Extension(data),valid=pkg.schemas.validate(pkg.utils.deepMerge(extSchema,matter.extendable[data.target]||{}),data,matter.name);if(!0!==valid)throw new Error(valid);extension.processed=originalMatterExtend.apply(Application.prototype,[extension,Application,Application.prototype,sharedData]),!1===extension||Application.extensions[extension.name].setupDone||(pkg.require(data.dependencies||[],function(){extension.dependencies=pkg.utils.arraysToObject(extension.dependencies,Array.toArray(arguments));try{data.setup.apply(extension,[extension.processed,sharedData]),Application.extensions[extension.name].setupDone=!0}catch(err){err.message='Failed extending "'+matter.factoryCreationName+'" with "'+(data.name||"not-named")+'"!\n'+err.message,console.error(err)}},function(e){if(e.message='"'+data.name+'", extending "'+matter.name+'", triggered an error due to one of its dependencies!\n'+e.message,console.error(e),data.error)try{data.error(e)}catch(err){console.error('Exception triggered in error callback for the extension "'+data.name+'", extending "'+matter.name,err.message)}}),Application.trigger(data.name,"on"),data.initialized=!0,console.check(!0,'Extended "'+matter.factoryCreationName+'" with "'+data.name+'"'))},Application.extensions={},Application.extend=function(data){var ext;if(!data)return!1;if(!matter.extendable)return console.warn("App "+matter.name+" does not allow extensions"),!1;var valid=pkg.schemas.validate(pkg.utils.merge(extSchema,matter.extendable[data.target]||{}),data);if(!0!==valid)return console.error("Invalid data passed to extend method of "+matter.name+"\n"+valid),!1;if(Application.extensions[data.name])throw new Error("There is already another extension with the same name("+data.name+")!");if(rejected(data))return!1;data.isExtension=!0,Application.extensions[data.name]={data:data,matter:matter},listenForConditions(Application.extensions[data.name]),matchConditions(data,void 0,void 0,function(conditionsMet){if(conditionsMet){var conditionsMet=Application.get("state");if("deprecated"!=conditionsMet&&"failed"!=conditionsMet&&!1!==data.autoStart)try{data.initialized||matter.extend.call(Application.prototype,data,sharedData)}catch(e){var conditionsMet="Failed executing the extend method from the app "+matter.name+"!\nError: "+e.message;data.error&&"function"==typeof data.error?tryAndCall(data.error,data,[conditionsMet,e],conditionsMet):(e.message=conditionsMet,console.error(e))}}})},Application.allowExtensions=function(){return!!matter.extendable},Application.hasExtension=function(ext){return!!Application.extensions[ext]},Application.getExtensionList=function(){return Object.keys(Application.extensions)},Application.getExtension=function(ext){return Application.extensions[ext]&&Application.extensions[ext].data||!1},Application.turnOff=function(ext){return!(!Application.extensions[ext]||"disabled"==Application.extensions[ext].status)&&turnItOnOrOff(!1,!1,Application.extensions[ext],!1)},Application.turnOn=function(ext){return!(!Application.extensions[ext]||"disabled"==Application.extensions[ext].status)&&turnItOnOrOff(!1,!1,Application.extensions[ext],!0)},Application.kill=function(ext,why){Application.extensions[ext]&&(Application.turnOff(ext),seekAndDestroy(Application.extensions[ext],"Method destroy called in Application, target the extension "+ext),Application.extensions[ext].data.status="disabled")},Application.destroy=function(why){seekAndDestroy(Application,"Method destroy called in Application (destroy all instances)"),Application.set("state","ready")},Application.disable=function(){Application.set("state","disabled"),Application.trigger("disabled",!0)},Application.enable=function(){Application.set("state","ready"),Application.trigger("enabled",!0)},matter.conditions?Application.set("state","disabled"):matchConditions(matter,void 0,void 0,function(conditionsMet){if(conditionsMet){Application.prototype.name||pkg.utils.merge(Application.prototype,matter);try{matter.setup.apply(Application.prototype,[Application,Application.prototype,sharedData]),Application.set("state","ready"),console.check(!0,'Application "'+matter.name+'" is ready')}catch(e){Application.set("state","failed"),console.check(!1,"["+matter.name+'] :: Application "'+matter.name+'" failed!\n'+e.message),matter.error?tryAndCall(matter.error,Application,"Failed running error function from Application description!"):(e.message='Error on application "'+matter.name+'" !\n'+e.message,console.error(e))}}else Application.set("state","disabled")}),Application},Application.on("state",startExtensions),Application.set("state","idle"),pkg.introduce(matter.factoryCreationName,Application,{observable:!1,defined:!0}),ret)}),resolve(AppConstructor),AppConstructor})}(AppConstructor,matter)},onFactoryCreate:function(appFactoryConstructor,metaData){return appFactoryConstructor},onFactoryDestroy:function(){}}),pkg.context=__privileged.context}),zaz.use(function AppFactory(pkg,__privileged){"use strict";var console=pkg.console,validStates=["not-started","ok","loading","building","ready","active","idle","deprecated","failed"];__privileged.mainFactory.create({id:"mod",name:"Module",docs:"http://github.tpn.terra.com/Terra/zaz/wiki/Modules",tests:"--",source:"http://github.tpn.terra.com/Terra/zaz-cerebro",implement:{properties:{name:{type:"string",required:!0},version:{type:"string",required:!0,format:/^((\d+\.){2})\d+((\:\d+)?)$/,description:"Must match something like 1.2.3:4 or at least 1.2.3",value:"0.0.1:1"},setup:{type:"function",required:!0},teardown:{type:"function",required:!0},state:{type:"string",required:!1,enum:validStates,description:"Must be one of: "+validStates},docs:{type:"string",required:!1,format:/((http|https):\/\/(\w+:{0,1}\w*@)?(\S+)|)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/,description:"Must be a valid URL to the documentation of your factory's product"},description:{type:"string",required:!1,description:"A brief description about the Module"},tests:{type:"string",required:!1,format:/((http|https):\/\/(\w+:{0,1}\w*@)?(\S+)|)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/,description:"Must be a valid URL to the project's test page"},source:{type:"string",required:!1,format:/((http|https):\/\/(\w+:{0,1}\w*@)?(\S+)|)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/,description:"Must be a valid URL to the project's source on Github"},dependencies:{type:"array",required:!1}}},dependencies:[],firstUse:function(ModConstructor,matter){var instance=new ModConstructor(matter);return matter.dependencies=matter.dependencies||[],pkg.define(matter.factoryCreationName,matter.dependencies,function(){for(var __shared={},args=[],Module=!1,required,i,current,required=pkg.utils.merge({},this),i=0;i<matter.dependencies.length;i++)required[current=matter.dependencies[i]]=arguments[i];__shared.dependencies=required,args.push(__shared);try{return matter.validate=pkg.schemas.validate,"object"!=typeof(Module=instance.setup.apply(matter,args)||matter)&&"function"!=typeof Module||(Module.destroy=function(reason){try{matter.teardown(reason),Module.set("state","ok")}catch(e){console.error("Failed trying to destroy the module "+matter.name+"!\n"+e.message),Module.set("state","failed")}},Module.kind=matter.kind="Module",Module.prototype&&(Module.prototype.kind="Module",Module.prototype.help=Module.help),"object"==typeof Module&&(pkg.utils.make.observable(Module,{recursive:!1,useStatic:!0}),Module.set("state","ready"))),pkg.introduce(matter.factoryCreationName,Module,{observable:!1,defined:!0}),Module}catch(e){e.message="Failed trying to setup the module "+matter.name+"!\n"+e.message,console.error(e),(Module=Module||{}).set?Module.set("state","failed"):Module.state="failed"}return!1}),ModConstructor},onFactoryCreate:function(modFactoryConstructor,metaData){return modFactoryConstructor},onFactoryDestroy:function(){}})}),zaz.use(function AppFactory(pkg,__privileged){"use strict";var console=pkg.console,validStates=["not-started","ok","loading","building","ready","active","idle","deprecated","failed"];__privileged.mainFactory.create({id:"comp",name:"Component",docs:"http://github.tpn.terra.com/Terra/zaz/wiki/Component",tests:"--",source:"",implement:{properties:{name:{type:"string",required:!0,format:/[a-z\-0-9]/},version:{type:"string",required:!0,format:/^((\d+\.){2})\d+((\:\d+)?)$/,description:"Must match something like 1.2.3:4 or at least 1.2.3",value:"0.0.1:1"},init:{type:"function",required:!0,description:"Must return an object with DOM and API properties, representing the component's DOM element and an API to manipulate it."},state:{type:"string",required:!1,enum:validStates,description:"Must be one of: "+validStates},dictionaries:{type:"mixed",required:!1},templates:{type:"mixed",required:!1},docs:{type:"string",required:!1,format:/((http|https):\/\/(\w+:{0,1}\w*@)?(\S+)|)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/,description:"Must be a valid URL to the documentation of your factory's product"},description:{type:"string",required:!1,description:"A brief description about the Module"},tests:{type:"string",required:!1,format:/((http|https):\/\/(\w+:{0,1}\w*@)?(\S+)|)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/,description:"Must be a valid URL to the project's test page"},source:{type:"string",required:!1,format:/((http|https):\/\/(\w+:{0,1}\w*@)?(\S+)|)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/,description:"Must be a valid URL to the project's source on Github"},dependencies:{type:"array",required:!1}}},dependencies:[],firstUse:function(CompConstructor,matter){function Component(data){var valid=pkg.schemas.validate(this.expects||{},data);if(!0!==valid)console.error(valid);else try{if((valid=this.init.call(this,data))&&"object"==typeof valid&&valid.DOM&&valid.DOM.nodeType&&valid.API&&"object"==typeof valid.API)return valid.API.DOMRef=valid.DOM,valid.name=this.name,valid;throw new TypeError("Components MUST return an object with properties DOM(a DOM Element) and API(an Object), in its init method.")}catch(e){e.message='Component "'+matter.name+'" failed!\n'+e.message,console.error(e)}}return Component.prototype.name=matter.name,Component.prototype.includerID="comp"+matter.name,Component.prototype.version=matter.version,Component.prototype.init=matter.init,Component.prototype.expects=matter.expects,Component.prototype.kind=matter.kind="Component",matter.dependencies=matter.dependencies||[],Array.isArray(matter.dictionaries)&&matter.dictionaries.length&&!/mod\.templateManager/.test(matter.dependencies.join(""))&&matter.dependencies.push("mod.dictionaryManager"),matter.templates&&!/mod\.templateManager/.test(matter.dependencies.join(""))&&matter.dependencies.push("mod.templateManager"),Component.prototype.dependencies=matter.dependencies,pkg.includer.register(matter.factoryCreationName,{imports:matter.dependencies,exports:function(){var innerPKG=this,dictionary;return this&&this.mod&&(this.mod.dictionaryManager&&"object"==typeof this.mod.dictionaryManager&&(dictionary=this.mod.dictionaryManager.createDictionary(matter),Component.prototype.l10n=dictionary),"function"==typeof this.mod.templateManager&&(Component.prototype.templates=new this.mod.templateManager(dictionary),Component.prototype.templates.create(matter.templates))),Component}}),pkg.introduce(matter.factoryCreationName,Component,{observable:!1,defined:!0}),pkg.ui.components.add(matter.name.toLowerCase(),Component),console.check(!0,'Component "'+matter.name+'" is ready'),Component},onFactoryCreate:function(compFactoryConstructor,metaData){return compFactoryConstructor},onFactoryDestroy:function(){}})}),console.check(!0,"Loaded extension factory-manager.js"),!function(scope){"use strict";var PRIVATE={initialized:!1,isAMPPageview:0<location.href.indexOf("pvamp.html"),GA4enabled:0<=["www.terra.com.br","p1.trrsf.com","hlg-montador.tpn.terra.com","hlg-cms.hlg-montador.tpn.terra.com"].indexOf(window.location.hostname),GA4measurementId:"G-FJBH1GT2QG",GA4measurementIdLogados:"G-6PE2SMXCWW",GA4userId:null,GA4userLoginType:null,trackerName:"terraAnalytics",terraUA:"***********-1",init:function(){var fpd=PRIVATE.getFPDData();PRIVATE.setGA4LoginType(fpd),window.dataLayer=window.dataLayer||[],window.gtag=function gtag(){window.dataLayer.push(arguments)},window.gtag("js",new Date),PRIVATE.setConfigGA4(),zaz.use(function(pkg){pkg.require(["mod.analytics4"])}),PRIVATE.initialized=!0}},applyDarkMode=function(value){window.osThemeColor="light",value&&(window.osThemeColor="dark")},darkModeMediaQuery;window.matchMedia&&"not all"!==window.matchMedia("(prefers-color-scheme)").media?((darkModeMediaQuery=window.matchMedia("(prefers-color-scheme: dark)")).addListener(function(e){applyDarkMode(e.matches)}),applyDarkMode(darkModeMediaQuery.matches)):window.osThemeColor="unsupport",PRIVATE.setGA4LoginType=function(fpd){fpd&&(fpd.emailhash&&""!=fpd.emailhash?(PRIVATE.GA4userId=fpd.emailhash,PRIVATE.GA4userLoginType=1):fpd.mdivoCPID&&""!=fpd.mdivoCPID&&"None"!=fpd.mdivoCPID?(PRIVATE.GA4userId=fpd.mdivoCPID,PRIVATE.GA4userLoginType=2):fpd.mdivoID&&""!=fpd.mdivoID&&"None"!=fpd.mdivoID?(PRIVATE.GA4userId=fpd.mdivoID,PRIVATE.GA4userLoginType=3):fpd.terraUserInfoEMH&&""!=fpd.terraUserInfoEMH&&(PRIVATE.GA4userId=fpd.terraUserInfoEMH,PRIVATE.GA4userLoginType=4))},PRIVATE.setCustomDimensions=function(customDimensons){var CD=customDimensons||{},customDimensons=[],displayMode="default",pageTheme="light",reTheme,userTheme=/user_theme=(\w+)/.exec(document.cookie),abTest="default",userTheme,customDimensons,userTheme=("terra360"==window.terraVersion&&(abTest="terra360"),userTheme&&1<userTheme.length&&"auto"==(pageTheme=userTheme[1])&&(pageTheme=window.osThemeColor),window.info_path?customDimensons=window.info_path:window.terra_info_channel&&(customDimensons={channel:(userTheme=window.terra_info_channel.replace("br.","").split("."))[0]||"",subchannel:userTheme[1]||"",channeldetail:window.terra_info_channeldetail||"",breadcrumb:""},window.info_path&&window.info_path.breadcrumb&&(customDimensons.breadcrumb=window.info_path.breadcrumb)),void 0===CD.dimension1&&""!==customDimensons.channel&&(CD.dimension1=customDimensons.channel),void 0===CD.dimension2&&""!==customDimensons.subchannel&&(CD.dimension2=customDimensons.subchannel),void 0===CD.dimension3&&""!==customDimensons.channeldetail&&(CD.dimension3=customDimensons.channeldetail),CD.dimension4||(CD.dimension4="contenido_digital"),void 0===CD.dimension5&&""!==customDimensons.breadcrumb&&(CD.dimension5=customDimensons.breadcrumb),!CD.dimension6&&window.terra_info_id&&(CD.dimension6=window.terra_info_id),!CD.dimension7&&window.terra_info_type&&(CD.dimension7=window.terra_info_type.toLowerCase()),CD.dimension9||(CD.dimension9="terra"),!CD.dimension10&&window.osThemeColor&&(CD.dimension10=window.osThemeColor),!CD.dimension18&&window.terra_info_source&&(CD.dimension18=window.terra_info_source),!CD.dimension19&&window.terra_info_author&&(CD.dimension19=window.terra_info_author),CD.dimension35||navigator&&navigator.userAgent&&navigator.userAgent.match("TerraLauncher")&&(CD.dimension35="TerraLauncher"),CD.dimension48||(CD.dimension48=abTest),CD.dimension49||(PRIVATE.isAMPPageview?CD.dimension49="true":CD.dimension49="false"),CD.dimension52||(CD.dimension52=pageTheme),navigator.connection&&(navigator.connection.type&&!CD.dimension57&&(CD.dimension57=navigator.connection.type),navigator.connection.downlinkMax&&!CD.dimension58&&(CD.dimension58=navigator.connection.downlinkMax),navigator.connection.downlink&&!CD.dimension60&&(CD.dimension60=navigator.connection.downlink),navigator.connection.rtt&&!CD.dimension61&&(CD.dimension61=navigator.connection.rtt),navigator.connection.effectiveType&&!CD.dimension62&&(CD.dimension62=navigator.connection.effectiveType),"saveData"in window.navigator.connection&&!CD.dimension63&&(CD.dimension63=navigator.connection.saveData.toString())),window.matchMedia&&(window.matchMedia("(display-mode: fullscreen)").matches&&(displayMode="fullscreen"),window.matchMedia("(display-mode: standalone)").matches&&(displayMode="standalone"),window.matchMedia("(display-mode: minimal-ui)").matches&&(displayMode="minimal-ui"),window.matchMedia("(display-mode: browser)").matches&&(displayMode="browser")),CD.dimension64=displayMode,!CD.dimension68&&window.terra_source_type&&(CD.dimension68=window.terra_source_type),/t360=(\w+)/.exec(document.cookie)),customDimensons=(CD.dimension72=userTheme&&"new"==userTheme[1]?"t360":"default",zaz.use(function(pkg){!CD.dimension75&&pkg.context.page.get("datePublished")&&(CD.dimension75=pkg.context.page.get("datePublished").substr(0,7))}),!CD.dimension76&&window.terra_size_version&&(CD.dimension76=window.terra_size_version),!CD.dimension78&&window.related_videos_type&&(CD.dimension78=window.related_videos_type),CD.dimension84||navigator&&navigator.deviceMemory&&(CD.dimension84=navigator.deviceMemory),CD.dimension85||window.devicePixelRatio&&(CD.dimension85=window.devicePixelRatio),PRIVATE.getFPDData());return customDimensons&&customDimensons.emailhash&&(CD.dimension86=customDimensons.emailhash,CD.dimension87=customDimensons.emailhash,CD.dimension88=customDimensons.emailhash,customDimensons.sub&&(CD.dimension89=customDimensons.sub),customDimensons.team&&(CD.dimension90=customDimensons.team),customDimensons.zodiac&&(CD.dimension91=customDimensons.zodiac)),CD.dimension92||window.performance&&window.performance.navigation&&(CD.dimension92=window.performance.navigation.type),!CD.dimension94&&window.terra_info_vendor&&(CD.dimension94=window.terra_info_vendor),!CD.dimension95&&window.articleIsPrefetch&&(CD.dimension95=window.articleIsPrefetch),CD.dimension96||(CD.dimension96=window.trr.contextData.summary?"true":"false"),CD},PRIVATE.getFPDData=function(){var fpd=localStorage.getItem("FPD");if(fpd)try{return fpd=JSON.parse(fpd)}catch(e){}return null},PRIVATE.getUserPropertiesGA4=function(CD){var fpd=PRIVATE.getFPDData(),ret={};return(fpd=null===fpd?{}:fpd).emailhash&&""!=fpd.emailhash&&(ret.fpd_user_email=fpd.emailhash),fpd.sub&&""!=fpd.sub&&(ret.fpd_user_sub=fpd.sub),fpd.team&&""!=fpd.team&&(ret.fpd_user_team=fpd.team),fpd.zodiac&&""!=fpd.zodiac&&(ret.fpd_user_zodiac=fpd.zodiac),PRIVATE.GA4userLoginType&&""!=PRIVATE.GA4userLoginType&&(ret.fpd_user_login_type=PRIVATE.GA4userLoginType),fpd.mdivoID&&""!=fpd.mdivoID&&"None"!=fpd.mdivoID&&(ret.fpd_user_customer_ms=fpd.mdivoID),fpd.mdivoCPID&&""!=fpd.mdivoCPID&&"None"!=fpd.mdivoCPID&&(ret.fpd_user_customer_cp=fpd.mdivoCPID),ret.fpd_user_customer_v=!(!fpd.mdivoID||""==fpd.mdivoID||"None"==fpd.mdivoID),fpd.terraUserInfoEMH&&""!=fpd.terraUserInfoEMH&&(ret.fpd_user_email_terra=fpd.terraUserInfoEMH),fpd.terraUserInfoIPERMH&&""!=fpd.terraUserInfoIPERMH&&(ret.fpd_user_idperm=fpd.terraUserInfoIPERMH),ret.fpd_user_customer_terra=!(!fpd.terraUserInfoIPERMH||""==fpd.terraUserInfoIPERMH),ret.fpd_user_terra_premium=window.terraPremium||!1,localStorage.getItem("mdivo-segmento")&&(ret.fpd_customer_vivo_segm=localStorage.getItem("mdivo-segmento")),sessionStorage.getItem("fpd_user_customer_terra_services")&&(ret.fpd_user_customer_terra_services=sessionStorage.getItem("fpd_user_customer_terra_services")),ret},PRIVATE.convertCDtoGA4=function(CD){var is_special_article=window.terra_info_special_article||!1,is_special_article={content_id:CD.dimension6,content_type:CD.dimension7,os_theme:CD.dimension10,service_provider_name:CD.dimension15,content_source:CD.dimension18,content_author:CD.dimension19,video_display_mode:CD.dimension20,video_id_title:CD.dimension27,video_content_type:CD.dimension28,video_source:CD.dimension30,video_autoplay:CD.dimension32,video_player:CD.dimension33,phone_type:CD.dimension35,amp:CD.dimension49,page_theme:CD.dimension52,network_info_type:CD.dimension57,network_downlink_max:CD.dimension58,network_downlink:CD.dimension60,network_rtt:CD.dimension61,network_effective_type:CD.dimension62,network_data_saver:CD.dimension63,display_mode:CD.dimension64,partner_type:CD.dimension68,published_date:CD.dimension75,video_category:CD.dimension77,video_recommend:CD.dimension78,device_memory:CD.dimension84,device_pixel_ratio:CD.dimension85,navigation_type:CD.dimension92,content_provider:CD.dimension94,prefetch:CD.dimension95,summary:CD.dimension96,product:"portal",article_special:is_special_article,scroll:CD.dimension14,table_position:CD.dimension80};return CD.dimension1&&(is_special_article.channel=CD.dimension1),CD.dimension2&&(is_special_article.subchannel=CD.dimension2),CD.dimension3&&(is_special_article.channeldetail=CD.dimension3),CD.dimension5&&(is_special_article.breadcrumb=CD.dimension5),is_special_article},PRIVATE.addGA4OnlyCDs=function(CDGA4){CDGA4&&(void 0===CDGA4.paid_content&&"terra_info_sponsored_content"in window&&(CDGA4.paid_content=window.terra_info_sponsored_content),window.zaz.use(function(pkg){pkg.context.page.get("abTest")&&""!=pkg.context.page.get("abTest")&&(CDGA4.ab_test=pkg.context.page.get("abTest"))}))},PRIVATE.setConfigGA4=function(){var userProperties=PRIVATE.getUserPropertiesGA4();zaz.use(function(pkg){var pkg=pkg.context.page.get("query"),urlPV,pkg;pkg&&(urlPV=pkg.urlPV,(pkg=pkg.contentTitle)&&urlPV&&(window.gtag("set","page_location",urlPV),window.gtag("set","page_title",pkg)))}),window.gtag("set","send_page_view",!1),window.gtag("set","cookie_flags","secure;samesite=none"),window.gtag("set","user_properties",userProperties),PRIVATE.GA4userId&&window.gtag("set","user_id",PRIVATE.GA4userId)},PRIVATE.convertArgumentToGA4=function(_args){return{item_id:_args[1].id,item_name:_args[1].name,item_list_name:_args[1].dimension70,item_category:_args[1].dimension69,item_category2:_args[1].dimension65,item_category3:_args[1].dimension66,item_category4:_args[1].dimension83,item_category5:_args[1].dimension67,item_brand:_args[1].dimension51,item_variant:_args[1].dimension71,index:_args[1].dimension81}},PRIVATE.sendGA4=function(_arguments,CD,sendTo){var CDGA4;"event"!=_arguments[1]&&"pageview"!=_arguments[1]||(CDGA4=PRIVATE.convertCDtoGA4(CD),PRIVATE.addGA4OnlyCDs(CDGA4),CDGA4.send_to=sendTo,"pageview"!=_arguments[1]||"CAP"==window.terra_info_type||CD.dimension14?3<_arguments.length&&"event"==_arguments[1]&&(CDGA4.event_category=_arguments[2],4<_arguments.length&&"object"!=typeof _arguments[4]&&(CDGA4.event_label=_arguments[4]),5<_arguments.length&&"object"!=typeof _arguments[5]&&(CDGA4.value=_arguments[5]),window.gtag("event",_arguments[3],CDGA4)):window.gtag("event","page_view",CDGA4))},window.tga={},window.tga.send=function(){if(!PRIVATE.GA4enabled)return!1;var CD,CDGA4,_arguments;PRIVATE.initialized||PRIVATE.init(),arguments&&0<arguments.length&&(CDGA4=CD=null,(_arguments=Array.prototype.slice.call(arguments))[0]=PRIVATE.trackerName+"."+_arguments[0],"event"!=_arguments[1]&&"pageview"!=_arguments[1]||("object"!=typeof _arguments[_arguments.length-1]?(CD=PRIVATE.setCustomDimensions(),_arguments[_arguments.length]=CD):(CD=PRIVATE.setCustomDimensions(_arguments[_arguments.length-1]),_arguments[_arguments.length-1]=CD)),PRIVATE.isAMPPageview||PRIVATE.sendGA4(arguments,CD,PRIVATE.GA4measurementId),PRIVATE.GA4userId&&PRIVATE.sendGA4(arguments,CD,PRIVATE.GA4measurementIdLogados))},window.gaTerra=window.tga.send,window.tga.event=function(category,action,elemts,cds){function sendMetric(event){var event=event.currentTarget||event.target;event&&event.htmlDataset("eventLabel")&&window.tga.send("send","event",category,action,event.htmlDataset("eventLabel"),cds)}var items;if(!(items=elemts?void 0===elemts.length?[elemts]:elemts:items))return!1;for(var i=0;i<items.length;i++)items[i]&&items[i].addEventListener&&items[i].addEventListener("click",sendMetric)},window.tga.sendGA4=function(type,params){if(!PRIVATE.GA4enabled)return!1;PRIVATE.initialized||PRIVATE.init();var CD=PRIVATE.setCustomDimensions(),CD=PRIVATE.convertCDtoGA4(CD),params;PRIVATE.addGA4OnlyCDs(CD),PRIVATE.isAMPPageview||("page_view"==type?(CD.send_to=PRIVATE.GA4measurementId,window.gtag("event",type,CD)):(params.send_to=PRIVATE.GA4measurementId,window.gtag("event",type,params)),PRIVATE.GA4userId&&"page_view"==type&&((params=Object.assign({},CD)).send_to=PRIVATE.GA4measurementIdLogados,window.gtag("event",type,params)))},scope.tga=window.tga}(this),console.check(!0,"Loaded extension metrics.js"),window.zaz.init(),window.zaz&&window.performance&&"function"==typeof window.performance.mark)try{window.performance.mark("ZAZ_CEREBRO_LOADED")}catch(perfException){window.console&&"object"==typeof window.console&&("function"==typeof window.console.warn?window.console.warn("Error registering performance metric ZAZ_CEREBRO_LOADED. "+perfException.message):"function"==typeof window.console.log&&window.console.error("[ZaZ Warning] Error registering performance metric ZAZ_CEREBRO_LOADED. "+perfException.message))}