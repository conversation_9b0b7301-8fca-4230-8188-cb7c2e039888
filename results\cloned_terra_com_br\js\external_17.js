/*! zaz-app-t360-stories - v1.0.0 - 27/05/2024 -- 11:44am */

zaz.use(function appT360Stories(pkg){"use strict";var console=pkg.console,appFactory,STATIC_PUBLIC=null,STATIC_PRIVATE={};pkg.factoryManager.get("app").create({name:"t360.stories",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/t360-stories",source:"http://github.tpn.terra.com/Terra/t360-stories",description:"Just another app",tests:"http://s1.trrsf.com/fe/t360-stories/tests/index.htm?zaz[env]=tests",dependencies:[],dictionaries:[],templates:{stories:'{% set centerCarousel = "" %}{% set arrow = "" %}{% set lazyLoadImageStart = 9 %}{% set qtdItemsStories = card[\'items\']|length %}{% if context[\'device\'] == \'mob\' %}{% set lazyLoadImageStart = 3 %}{% if qtdItemsStories < 4 %}{% set centerCarousel = "center-carousel" %}{% endif %}{% else %}{% if qtdItemsStories > 9 %}{% set arrow = "active-carousel" %}{% else %}{% set centerCarousel = "center-carousel" %}{% endif %}{% endif %}{% set storiesSize = \'\' %}{% if qtdItemsStories < 5 %}{% set storiesSize = \'sm\' %}{% elif qtdItemsStories <= 9 %}{% set storiesSize = \'md\' %}{% else %}{% set storiesSize = \'lg\' %}{% endif %}{% set stories_border_color = \'default\' %}{% if context.isHome == \'False\' %}{% set stories_border_color = context.color_page %}{% endif %}{% set image_size = [106, 106] %}{% set image_crop = \'s\' %}<div class="t360-stories table-news table-range-flex loading-app" data-stories-size="{{storiesSize}}" data-type="NEWS" data-position="{{ tableInfo.tableSequence }}" data-area="AMP-STORIES" id="table-{{ card.id }}" data-id="{{ card.id }}" data-app-name="{{ card.name }}" data-table="no-card"><div class="t360-stories__wrapper"><div class="t360-stories__control--prev t360-stories__control--button {{ arrow }}" data-control="prev"><span id="stories-left" class="icon icon-solid icon-color-black icon-24 icon-chevron-left"></span></div><div class="t360-stories__content"><div class="t360-stories__carousel {{ centerCarousel }}">{% for item in card[\'items\'] %}{% if item[\'items\'][0] %}{% set channelInfo = item|get_cms_path %}{% set color = {\'url\' : item[\'items\'][0][\'url\']}|get_channel_color_name %}<a class="t360-stories--story metric-item" id="card-{{item[\'items\'][0][\'id\']}}" data-source-name="{{ item[\'items\'][0][\'source\'][\'publicName\'] }}" data-source-types="{{ item[\'items\'][0][\'source\'][\'types\']|join(\',\') if item[\'items\'][0][\'source\'][\'types\'] else \'\'}}" data-type="{{ item[\'type\'] }}" data-reason="e" data-size="card-story" data-channel-info="{{ channelInfo }}" data-url="{{ item[\'items\'][0][\'url\'] }}" data-color="{{ color }}" title="{{ item[\'items\'][0][\'title\']|e }}" href="{{ item[\'items\'][0][\'url\'] }}">{% if item[\'items\'][0][\'image\'] %}<div class="t360-stories--story__border-gradient"></div><img class="t360-stories--story__image paradasp" src="{{item[\'items\'][0][\'image\']|get_card_image(image_crop, image_size[0], image_size[1])}}" {% if loop.index > lazyLoadImageStart %}loading="lazy" decoding="async"{% endif %}>{% endif %}<span class="t360-stories--story__title">{{ item[\'items\'][0][\'title\'] }}</span></a>{% endif %}{% endfor %}</div></div><div class="t360-stories__control--next t360-stories__control--button {{ arrow }}" data-control="next"><span id="stories-right" class="icon icon-solid icon-color-black icon-24 icon-chevron-right"></span></div></div></div><div class="t360-stories-embed closed loading-app"><amp-story-player class="t360-stories-embed__player i-amphtml-story-player-loaded"><script type="application/json">{"behavior": {"pageScroll": false,"autoplay": false},"controls": [{"name": "close","position": "start"},{"name": "skip-to-next"}]}<\/script>{% for item in card[\'items\'] %}{% if item[\'items\'][0] %}<a href="{{item[\'items\'][0][\'url\']}}" loading="lazy"></a>{% endif %}{% endfor %}</amp-story-player></div>'},setup:function(STATIC_PUBLIC,PUBLIC,__shared){},init:function(data,__shared){var PRIVATE={},PUBLIC=this;return PRIVATE.createStoriesIfNull=function(){var stories;data.container.innerHTML.trim()||(stories=PUBLIC.templates.render("stories",{card:data.data}),data.container.appendChild(stories))},PRIVATE.createCarousel=function(){PRIVATE.storiesButtons=data.container.querySelectorAll(".t360-stories__control--button"),PRIVATE.storiesButtonsPrev=PRIVATE.containerStories.querySelector(".t360-stories__control--prev"),PRIVATE.storiesButtonsNext=PRIVATE.containerStories.querySelector(".t360-stories__control--next"),pkg.require("mod.t360.carouselRanges",function(T360CarouselRanges){PRIVATE.carousel=new T360CarouselRanges({containerCarousel:".t360-stories__content",itemsCarousel:".t360-stories--story",dividerScrollCarousel:1,previousButtonCarousel:".t360-stories__control--prev",nextButtonCarousel:".t360-stories__control--next",automaticMarginResize:!0,minimumMarginInPixels:0,marginProperty:"marginLeft"}),PRIVATE.carousel.arrow(),window.addEventListener("resize",function(){PRIVATE.carousel.arrow(),PRIVATE.carousel.updateStep()}),PRIVATE.storiesButtonsPrev.addEventListener("click",function(){PRIVATE.storiesButtonsPrev.classList.contains("arrow-disable")||window.tga.send("send","event","AMP-STORY","pagination","prev")}),PRIVATE.storiesButtonsNext.addEventListener("click",function(){PRIVATE.storiesButtonsNext.classList.contains("arrow-disable")||window.tga.send("send","event","AMP-STORY","pagination","next")})})},PRIVATE.closePlayer=function(){PRIVATE.tagHTML[0].style.overflow="",PRIVATE.player.pause(),PRIVATE.containerStoriesEmbed.classList.add("closed"),PRIVATE.containerStoriesEmbed.classList.add("loading-app"),PRIVATE.containerStoriesCard.forEach(function(card){card.classList.remove("hidden")})},PRIVATE.setHistory=function(index){window.addEventListener("popstate",function(){history.replaceState({data:"firstView",url:PRIVATE.firstView},"",PRIVATE.firstView),PRIVATE.closePlayer()}),history.pushState({data:"first-story",url:PRIVATE.stories[index].href},"","/"+PRIVATE.stories[index].href.split(".br/")[1])},PRIVATE.startPlayer=function(el,index){PRIVATE.firstView=window.location.href,PRIVATE.containerStoriesEmbed.classList.remove("closed"),PRIVATE.player.show(PRIVATE.stories[index].href,null,{animate:!1}).catch(function(erro){console.log("Erro na promisse show : "+erro)}).finally(function(el){PRIVATE.containerStoriesEmbed.classList.remove("loading-app")}),el.classList.add("hidden"),PRIVATE.tagHTML=document.getElementsByTagName("html"),PRIVATE.tagHTML[0].style.overflowY="hidden",PRIVATE.setHistory(index),PRIVATE.containerStoriesEmbed.style.transform="translate3d(0, 0, 0) scale3d(1, 1, 1)",PRIVATE.player.play(),el.querySelector(".t360-stories--story__image").classList.remove("border-color-"+el.dataset.color),el.querySelector(".t360-stories--story__image").classList.remove("border-color-default"),el.querySelector(".t360-stories--story__image").classList.add("border-color-visited"),document.querySelector(".channel-parada")&&(el.querySelector(".t360-stories--story__image").classList.remove("paradasp"),el.querySelector(".t360-stories--story__border-gradient").remove()),document.querySelector(".channel-parada-home")&&(el.querySelector(".t360-stories--story__image").classList.remove("paradasp"),el.querySelector(".t360-stories--story__border-gradient").remove()),PRIVATE.containerStories.classList.remove("loading-app")},PRIVATE.createStoriesIfNull(),PRIVATE.containerStories=data.container,PRIVATE.containerStories.classList.remove("loading-app"),PRIVATE.containerStoriesCard=PRIVATE.containerStories.querySelectorAll(".t360-stories--story"),PRIVATE.containerStoriesEmbed=document.body.querySelector(".t360-stories-embed"),PRIVATE.player=document.body.querySelector(".t360-stories-embed__player"),PRIVATE.playerIsReady=0,PRIVATE.tablePosition=PRIVATE.containerStories.htmlDataset("position"),PRIVATE.tablePosition&&1==PRIVATE.tablePosition.toString().length&&(PRIVATE.tablePosition="0"+PRIVATE.tablePosition),"mob"!==pkg.context.platform.get("type")&&PRIVATE.createCarousel(),PRIVATE.containerStoriesCard.forEach(function(el,index){el.addEventListener("click",function(e){e.preventDefault(),PRIVATE.containerStories.classList.add("loading-app"),2==PRIVATE.playerIsReady?PRIVATE.startPlayer(el,index):0==PRIVATE.playerIsReady&&(pkg.require("mod.ampStoryPlayer",function(){}).then(function(){PRIVATE.stories=PRIVATE.player.getStories(),PRIVATE.startPlayer(el,index),PRIVATE.playerIsReady=2}),PRIVATE.playerIsReady=1),window.tga.send("send","event","card-story","click",el.title,{dimension14:"Scroll AMP-STORY",dimension80:PRIVATE.tablePosition})})}),PRIVATE.player.addEventListener("navigation",function(event){history.replaceState({data:"story-player",url:PRIVATE.stories[event.detail.index].href},"story navigation","/"+PRIVATE.stories[event.detail.index].href.split(".br/")[1])}),PRIVATE.player.addEventListener("amp-story-player-close",function(){history.replaceState({},"go back to firstView",PRIVATE.firstView),PRIVATE.closePlayer()}),PUBLIC},teardown:function(why,__static,__proto,__shared){}})});