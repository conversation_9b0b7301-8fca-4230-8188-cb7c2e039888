/*! zaz-app-t360-ticker - v1.0.0 - 05/12/2024 -- 7:04pm */

zaz.use(function(pkg){"use strict";var console=pkg.console,dictFactory;pkg.factoryManager.get("dict").create({name:"t360.ticker",version:"1.0.0",state:"ok",extends:[],langs:{global:{gameTime1:"Previa",gameTime2:"1° Tiempo",gameTime3:"Entretiempo",gameTime4:"2° Tiempo",gameTime5:"1° T Prórroga",gameTime6:"2° T Prórroga",gameTime7:"Penales",gameTime8:"Fin de partido",gameTime9:"Partido paralizado",liveEvents:"eventos en vivo",liveEvent:"evento en vivo"},pt:{gameTime1:"Pré-jogo",gameTime2:"1° Tempo",gameTime3:"Intervalo",gameTime4:"2° Tempo",gameTime5:"1° T Prorrogação",gameTime6:"2° T Prorrogação",gameTime7:"P<PERSON><PERSON><PERSON>",gameTime8:"Fim de jogo",gameTime9:"Partida paralizada",liveEvents:"eventos ao vivo",liveEvent:"evento ao vivo"},es:{},en:{}}})}),zaz.use(function appT360Ticker(pkg){"use strict";var console=pkg.console,appFactory,STATIC_PUBLIC=null,STATIC_PRIVATE={};pkg.factoryManager.get("app").create({name:"t360.ticker",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/zaz-app-t360-ticker",source:"http://github.tpn.terra.com/pages/terra/zaz-app-t360-ticker",description:"Just another app",tests:"http://s1.trrsf.com/fe/zaz-t360-ticker/tests/index.htm?zaz[env]=tests",dependencies:[],dictionaries:["t360.ticker"],templates:{wrapper:'{% set eventsLength = events|length %}{% set sportsLength = eventsSports|length %}{% set destaquesLength = eventsDestaques|length %}{% set currentEventID = 0 %}{% set hasCurrentEvent = {\'flag\': False} %}{% if context.isHome == \'True\' %}{% set filteredEvents = [] %}{% for event in events %}{% if event.coverageOrderBar < 100 or event.prioritized == 1 %}{% set temp = filteredEvents.append(event) %}{% endif %}{% endfor %}{% set events = filteredEvents %}{% endif %}{% if context and context.currentEventID %}{% set currentEventID = context.currentEventID|int %}{% for event in events %}{% if event.id == currentEventID %}{% if hasCurrentEvent.update({\'flag\': True}) %} {% endif %}{% endif %}{% endfor %}{% endif %}{% if hasCurrentEvent.flag %}{% set eventsLength = eventsLength - 1 %}{% endif %}{% set realtimeID = \'\' %}{% if live_feed and live_feed.LIVE and live_feed.LIVE.REALTIME %}{% set realtimeID = live_feed.LIVE.REALTIME.LASTESTID %}{% endif %}{% set tooltipText = \'\' %}{% if context and context.countryLive %}{% if context.countryLive == \'br\' and eventsLength == 1 %}{% set tooltipText = \'evento ao vivo\' %}{% elif context.countryLive == \'br\' and eventsLength > 1 %}{% set tooltipText = \'eventos ao vivo\' %}{% elif context.countryLive != \'br\' and eventsLength == 1 %}{% set tooltipText = \'evento en vivo\' %}{% elif context.countryLive != \'br\' and eventsLength > 1 %}{% set tooltipText = \'eventos en vivo\' %}{% endif %}{% endif %}<div class="zaz-app-t360-ticker--background"><div class="zaz-app-t360-ticker total-events-count-{{eventsLength}}" id="zaz-app-t360-ticker--container" data-keep-index="10" data-realtime-id="{{ realtimeID }}"><div class="zaz-app-t360-ticker--tooltip" {% if sportsLength == 0 or destaquesLength == 0 %}style="display: none"{% endif %}><div class="zaz-app-t360-ticker--tooltip__itemLeft"><p class="zaz-app-t360-ticker--tooltip__itemLeft--name">Esportes</p><span class="zaz-app-t360-ticker--tooltip--quantity zaz-app-t360-ticker--tooltip__itemLeft--quantity" {{\'style="visibility:hidden"\' if sportsLength == 0 else \'\'}}>{{sportsLength}}</span></div><div class="zaz-app-t360-ticker--tooltip__itemRight"><p class="zaz-app-t360-ticker--tooltip__itemRight--name">Destaques</p><span class="zaz-app-t360-ticker--tooltip--quantity zaz-app-t360-ticker--tooltip__itemRight--quantity" {{\'style="visibility:hidden"\' if destaquesLength == 0 else \'\'}}>{{destaquesLength}}</span></div></div><nav class="grid-view"><div class="container-carousel container-carousel-esportes"><div class="page-nav previous esportes disabled"><span class="item-icon icon-solid icon-color-default icon-24 icon-chevron-left"></span></div><ul class="container"><div class="slide-esportes events-count-{{sportsLength}}">{% for event in eventsSports %}{% if currentEventID != event.id %}{% if event.template == \'itemLive\' %}{% include itemLive %}{% elif event.template == \'itemMatch\' %}{% include itemMatch %}{% else %}{% include itemLiveBlog %}{% endif %}{% endif %}{% endfor %}</div></ul><div class="page-nav next esportes"><span class="item-icon icon-solid icon-color-default icon-24 icon-chevron-right"></span></div></div><div class="container-carousel container-carousel-destaques"><div class="page-nav previous destaques disabled"><span class="item-icon icon-solid icon-color-default icon-24 icon-chevron-left"></span></div><ul class="container"><div class="slide-destaques events-count-{{destaquesLength}}">{% for event in eventsDestaques %}{% if currentEventID != event.id %}{% if event.template == \'itemLive\' %}{% include itemLive %}{% elif event.template == \'itemMatch\' %}{% include itemMatch %}{% else %}{% include itemLiveBlog %}{% endif %}{% endif %}{% endfor %}</div></ul><div class="page-nav next destaques"><span class="item-icon icon-solid icon-color-default icon-24 icon-chevron-right"></span></div></div></nav><button class="zaz-app-t360-ticker--veja-mais">Exibir mais</button></div></div>',modalMobile:"{% set eventsLength = events|length %}{% set sportsLength = eventsSports|length %}{% set destaquesLength = eventsDestaques|length %}{% set currentEventID = 0 %}{% set hasCurrentEvent = {'flag': False} %}{% if context and context.currentEventID %}{% set currentEventID = context.currentEventID|int %}{% for event in events %}{% if event.id == currentEventID %}{% if hasCurrentEvent.update({'flag': True}) %} {% endif %}{% endif %}{% endfor %}{% endif %}{% if hasCurrentEvent.flag %}{% set eventsLength = eventsLength - 1 %}{% endif %}{% set realtimeID = '' %}{% if live_feed and live_feed.LIVE and live_feed.LIVE.REALTIME %}{% set realtimeID = live_feed.LIVE.REALTIME.LASTESTID %}{% endif %}{% set tooltipText = '' %}{% if context and context.countryLive %}{% if context.countryLive == 'br' and eventsLength == 1 %}{% set tooltipText = 'evento ao vivo' %}{% elif context.countryLive == 'br' and eventsLength > 1 %}{% set tooltipText = 'eventos ao vivo' %}{% elif context.countryLive != 'br' and eventsLength == 1 %}{% set tooltipText = 'evento en vivo' %}{% elif context.countryLive != 'br' and eventsLength > 1 %}{% set tooltipText = 'eventos en vivo' %}{% endif %}{% endif %}<div class=\"zaz-app-t360-ticker--modal-mobile\" style=\"display:none;\"><div class=\"zaz-app-t360-ticker--modal-mobile--background\"></div><div class=\"zaz-app-t360-ticker--modal-mobile--container\"><div class=\"zaz-app-t360-ticker--modal-mobile--top\"><div class=\"zaz-app-t360-ticker--tooltip\"><div class=\"zaz-app-t360-ticker--tooltip__itemLeft {{ 'zaz-app-t360-ticker--tooltip__inactiveTab' if sportsLength == 0 else '' }}\"><p class=\"zaz-app-t360-ticker--tooltip__itemLeft--name\">Esportes</p><span class=\"zaz-app-t360-ticker--tooltip--quantity zaz-app-t360-ticker--tooltip__itemLeft--quantity\">{{sportsLength}}</span></div><div class=\"zaz-app-t360-ticker--tooltip__itemRight {{ 'zaz-app-t360-ticker--tooltip__inactiveTab' if destaquesLength == 0 else '' }}\"><p class=\"zaz-app-t360-ticker--tooltip__itemRight--name\">Destaques</p><span class=\"zaz-app-t360-ticker--tooltip--quantity zaz-app-t360-ticker--tooltip__itemRight--quantity\">{{destaquesLength}}</span></div></div><span class=\"icon icon-solid icon-color-black icon-16 icon-times\"></span></div><div class=\"zaz-app-t360-ticker--modal-mobile--list\"><ul class=\"container container-carousel-esportes\">{% for event in eventsSports %}{% if currentEventID != event.id %}{% if event.template == 'itemLive' %}{% include itemLive %}{% elif event.template == 'itemMatch' %}{% include itemMatch %}{% else %}{% include itemLiveBlog %}{% endif %}{% endif %}{% endfor %}</ul><ul class=\"container container-carousel-destaques\">{% for event in eventsDestaques %}{% if currentEventID != event.id %}{% if event.template == 'itemLive' %}{% include itemLive %}{% elif event.template == 'itemMatch' %}{% include itemMatch %}{% else %}{% include itemLiveBlog %}{% endif %}{% endif %}{% endfor %}</ul></div></div></div>",itemMatch:'{% set imgShield1 = event.match.teams.team1.shield %}{% set imgShield2 = event.match.teams.team2.shield %}{% set statusDictionary = {\'1\':"Pré-jogo",\'2\':"1° Tempo",\'3\':"Intervalo",\'4\':"2° Tempo",\'5\':"1° T Prorrogação",\'6\':"2° T Prorrogação",\'7\':"Pênaltis",\'8\':"Fim de jogo",\'9\':"Partida paralizada"} %}{% if event.match.teams.team1.shieldSource %}{% set imgShield1 = \'https://p2.trrsf.com/image/fget/cf/51/51/filters:quality(100)/\' + event.match.teams.team1.shieldSource|replace(\'http://\',\'\') %}{% endif %}{% if event.match.teams.team2.shieldSource %}{% set imgShield2 = \'https://p2.trrsf.com/image/fget/cf/51/51/filters:quality(100)/\' + event.match.teams.team2.shieldSource|replace(\'http://\',\'\') %}{% endif %}{% set hasPenalty = event.match.gameTime.id == 7 or (event.match.gameTime.id > 7 and (event.match.teams.team1.score.penalty + event.match.teams.team2.score.penalty) > 0) %}{% set horaPartida = {\'data\':"",\'hora\':""} %}{% set timeStamp = \'\' %}{% if not event.horaPartida %}{% set horaPartida = event.dateTime|parse_soccer_match_time %}{% set timeStamp = event.dateTime %}{% else %}{% set horaPartida = event.horaPartida %}{% set timeStamp = event.timestamp %}{% endif %}<li class="event match{{ \' data-manager\' if event.match.gameTime.id == 1 and event.horaPartida else \'\' }}" id="ticker-event-{{ event.id }}" data-date="{{ event.dateTime }}" data-id="{{ event.id }}" data-teams-id="{{ event.match.teams.team1.providerClubId }},{{ event.match.teams.team2.providerClubId }}" data-ga-action="futebol" data-ga-label="{{ event.coverage|lower }}"><a href="{{ event.url }}" title="{{ event.match.teams.team1.name }} x {{ event.match.teams.team2.name }}"><div class="zaz-app-t360-animation-goal"><span class="zaz-app-t360-zoom-goal">Goool!</span></div><div class="zaz-app-t360-ticker--match"><div class="zaz-app-t360-ticker--match--info"><img src="{{ imgShield1 }}" alt="{{ event.match.teams.team1.name }}" class="zaz-app-t360-ticker--match--info--shield"><div class="name zaz-app-t360-ticker--match--info--teamName"><span class="short">{{ event.match.teams.team1.nameAbbr }}</span><span class="big">{{ event.match.teams.team1.name }}</span></div></div><div class="zaz-app-t360-ticker--match--info"><span class="zaz-app-t360-ticker--match--info--coverage">{{ event.coverage }}</span><div class="zaz-app-t360-ticker--match--info--date">{% if event.match.gameTime.id == 1 %}{{ horaPartida[\'data\'] }}{% elif event.match.gameTime.id > 1 and event.match.gameTime.id <= 8 %}{{ statusDictionary[event.match.gameTime.id|string] }}{% endif %}</div>{% if event.match.gameTime.id == 1 %}<span class="zaz-app-t360-ticker--match--info--time">{{horaPartida[\'hora\']}}</span>{% else %}<span class="zaz-app-t360-ticker--match--info--time">\x3c!-- Tem que ficar todos na mesma linha, senão zoa o layout entre backend e frontend --\x3e<span class="goal team1">{{event.match.teams.team1.score.points}}</span><span class="penalty team1">{{\'(\' + event.match.teams.team1.score.penalty|string + \')\' if hasPenalty else \'\'}}</span><span class="icon icon-solid icon-12 icon-times"></span><span class="penalty team2">{{\'(\' + event.match.teams.team2.score.penalty|string + \')\' if hasPenalty else \'\'}}</span><span class="goal team2">{{event.match.teams.team2.score.points}}</span></span>{% endif %}</div><div class="zaz-app-t360-ticker--match--info"><img src="{{ imgShield2 }}" alt="{{ event.match.teams.team2.name }}" class="zaz-app-t360-ticker--match--info--shield"><div class="name zaz-app-t360-ticker--match--info--teamName"><span class="short">{{ event.match.teams.team2.nameAbbr }}</span><span class="big">{{ event.match.teams.team2.name }}</span></div></div></div></a></li>',itemLive:'{% set videoTitle = \'\' %}{% if event.title %}{% set videoTitle = event.title %}{% elif event.videoTitle %}{% set videoTitle = event.videoTitle %}{% endif %}<li class="event live zaz-app-t360-ticker--live" id="ticker-event-{{ event.id }}" data-id="{{ event.id }}" data-ga-action="live" data-ga-label="{{ videoTitle|e }}"><a href="{{ event.url }}" class="item">{% if event.thumbSource %}<div class="thumb"><img src="{{ \'https://p2.trrsf.com/image/fget/cf/129/72/\' + event.thumbSource|replace(\'http://\',\'\') }}" alt="{{ videoTitle|e }}"><span class="icon icon-solid icon-color-white icon-play"></span><p class="title-live {% if event.rerun == 0 %}live-event{% endif %}">{% if event.rerun == 1 %}REPRISE{% elif event.rerun == 2 %}ESPECIAL{% elif event.rerun == 3 %}EXCLUSIVO{% else %}AO VIVO{% endif %}</p></div>{% elif event.thumb %}<div class="thumb"><img src="{{ event.thumb }}" alt="{{ videoTitle|e }}"><span class="icon icon-solid icon-color-white icon-24 icon-play"></span></div>{% endif %}<div class="description"><div class="sub-desc">{{ videoTitle }}</div></div></a></li>',itemLiveBlog:'<li class="event live zaz-app-t360-ticker--live" id="ticker-event-{{ event.id }}" data-id="{{ event.id }}" data-ga-action="live-blog" data-ga-label="{{ event.title|e }}"><a href="{{ event.url }}" class="item">{% if event.thumbSource %}<div class="thumb teste"><img src="{{ \'https://p2.trrsf.com/image/fget/cf/129/72/\' + event.thumbSource|replace(\'http://\',\'\') }}" alt="{{ event.title|e }}"><span class="icon icon-24 icon-live-white"></span></div>{% elif event.thumb %}<div class="thumb"><img src="{{ event.thumb }}" alt="{{ event.title|e }}"><span class="icon icon-24 icon-live-white"></span></div>{% endif %}<div class="description"><div class="title">{{ event.title }}</div></div></a></li>'},expects:{properties:{placeholder:{type:"object",required:!0},defaultTabName:{type:"string",required:!1,default:"esportes"}}},setup:function(STATIC_PUBLIC,PUBLIC,__shared){},init:function(queryStrings,__shared){var PRIVATE={},PUBLIC=this;PRIVATE.device=pkg.context.platform.get("type"),PRIVATE.acceptEventsType=[1,2,3,7],PRIVATE.liveEventsType={0:"AO VIVO",1:"REPRISE",2:"ESPECIAL",3:"EXCLUSIVO"},PRIVATE.pageEventId=pkg.context.page.get("eventID"),PRIVATE.eventStatus=2,PRIVATE.events=[],PRIVATE.esportesLength=0,PRIVATE.destaquesLength=0,PRIVATE.data=queryStrings,PRIVATE.resolutionRange="",PRIVATE.realtimeChannel={channel:"cms-live",lastID:0,type:"M"},PRIVATE.carouselEsportes=null,PRIVATE.carouselDestaques=null,PRIVATE.newRT=!1;var queryStrings=pkg.context.page.get("query"),eventsEsportes=[],eventsDestaques=[],alreadySetupEsportesNavigationEvent,alreadySetupDestaquesNavigationEvent,alreadySetupCloseEvent;return queryStrings&&queryStrings.tickerStatus&&(PRIVATE.eventStatus=parseInt(queryStrings.tickerStatus)),PRIVATE.loadDateTime=function(elements){function fixDates(elemDateList){function generateClosure(x){return function(hourGame){var dateHR=new Date(hourGame.ISO8601_datetime),weekIndex,dayOfWeek,dateGame=["DOM","SEG","TER","QUA","QUI","SEX","SAB"][dateHR.getDay()]+" - "+dateHR.getDate()+"/"+(dateHR.getMonth()+1),hourGame=dateHR.getMinutes();hourGame<10&&(hourGame="0"+hourGame);var dateHR=dateHR.getHours();dateHR<10&&(dateHR="0"+dateHR);var hourGame=dateHR+":"+hourGame;x.querySelector(".zaz-app-t360-ticker--match--info--date").innerHTML=dateGame,x.querySelector(".zaz-app-t360-ticker--match--info--time").innerHTML=hourGame}}var x,objDateTime,dateFormatted,dateFormatted,closures=[],element,dateFormatted;for(x in elemDateList){!elemDateList.hasOwnProperty(x)||(dateFormatted=(element=elemDateList[x]).htmlDataset("date"))&&(closures[x]=generateClosure(element),dateFormatted=/(\d{9,10})H(\d{2})M/.test(dateFormatted)?dateFormatted.replace(/(\d{4})(\d{2})(\d{2})(\d{1,2})H(\d{2})M/,"$1-$2-$3T$4:$5:00"):/(\d{4})\-(\d{2})\-(\d{2}) (\d{2}):(\d{2}) (?:GMT|UTC)(Z|[+\-])(\d{2})(\d{2}).+/.test(dateFormatted)?dateFormatted.replace(/(\d{4})\-(\d{2})\-(\d{2}) (\d{2}):(\d{2}) (?:GMT|UTC)(Z|[+\-])(\d{2})(\d{2}).+/,"$1-$2-$3T$4:$5:00"):dateFormatted,(objDateTime=new PRIVATE.dateTime({input:{timestamp:dateFormatted,timezone:"UTC"},output:{format:"TIME",timezone:pkg.context.page.get("country").toUpperCase(),locale:pkg.context.page.get("locale")}})).compute(closures[x]))}}PRIVATE.dateTime?fixDates(elements):pkg.require(["mod.datetime"],function(ModDateTime){PRIVATE.dateTime=ModDateTime,fixDates(elements)})},PRIVATE.selectorElements=function(){PRIVATE.elements={},PRIVATE.elements.wrapper=PRIVATE.data.placeholder.querySelector("#zaz-app-t360-ticker--container"),PRIVATE.elements.slide_sport=PRIVATE.data.placeholder.querySelector(".slide-esportes"),PRIVATE.elements.slide_destaques=PRIVATE.data.placeholder.querySelector(".slide-destaques"),PRIVATE.elements.tooltip_sports=PRIVATE.data.placeholder.querySelector(".zaz-app-t360-ticker--tooltip__itemLeft"),PRIVATE.elements.tooltip_destaques=PRIVATE.data.placeholder.querySelector(".zaz-app-t360-ticker--tooltip__itemRight"),PRIVATE.elements.tooltip_holder=PRIVATE.data.placeholder.querySelector(".zaz-app-t360-ticker--tooltip"),PRIVATE.elements.modalMobile=document.querySelector(".zaz-app-t360-ticker--modal-mobile"),PRIVATE.elements.items=PRIVATE.elements.wrapper.querySelector(".event"),PRIVATE.elements.backendWrapper=PRIVATE.data.placeholder,PRIVATE.elements.modalMobile&&(PRIVATE.elements.slide_sport_mobile=PRIVATE.elements.modalMobile.querySelector(".container-carousel-esportes"),PRIVATE.elements.slide_destaques_mobile=PRIVATE.elements.modalMobile.querySelector(".container-carousel-destaques"),PRIVATE.elements.tooltip_sports_mobile=PRIVATE.elements.modalMobile.querySelector(".zaz-app-t360-ticker--tooltip__itemLeft"),PRIVATE.elements.tooltip_destaques_mobile=PRIVATE.elements.modalMobile.querySelector(".zaz-app-t360-ticker--tooltip__itemRight"))},PRIVATE.getActiveEvents=function(liveFeed){var isAutoplay,queryStrings=pkg.context.page.get("query");if(liveFeed&&liveFeed.LIVE&&liveFeed.LIVE.GROUPS.GROUP){var groups=liveFeed.LIVE.GROUPS.GROUP,currentGroup,events,event,lastEvent,tmpEnabledHeaders=null;"object"!=typeof groups||groups instanceof window.Array||(groups=[groups]);for(var l=0;l<groups.length;l++)if((currentGroup=groups[l]).CONTENT){"object"!=typeof(events=currentGroup.CONTENT.EVENT)||events instanceof window.Array||(events=[events]);for(var j=0;j<events.length;j++)event=events[j],isAutoplay=!1,"content"!==pkg.context.publisher.get("template")&&1===event.CONFIGURATION.VIDEO_HOME&&""!==pkg.context.page.get("idItemMenu")&&0<=event.VIDEO_HOME_CONFIG.indexOf(pkg.context.page.get("idItemMenu"))&&(isAutoplay=!0,PRIVATE.eventAutoplayId!=event.ID&&(isAutoplay=!1)),event.STATUS===PRIVATE.eventStatus&&PRIVATE.inArray(PRIVATE.acceptEventsType,event.EVENT_TYPE)&&PRIVATE.pageEventId!==event.ID&&!isAutoplay&&(event.HIDE_TICKER&&1==event.HIDE_TICKER||(0===PRIVATE.events.length||event.ORDER_BAR>=lastEvent.ORDER_BAR?PRIVATE.events.push(PRIVATE.normalizeData(event)):PRIVATE.events.unshift(PRIVATE.normalizeData(event)),lastEvent=event))}}PRIVATE.renderHTML(),PRIVATE.reorderEventsTeam(),PRIVATE.activeCarousels(),PRIVATE.loadChannelRealtime(),PRIVATE.calcNavigation()},PRIVATE.activeCarousels=function(){"mob"!==PRIVATE.device&&pkg.require(["mod.t360.carouselRanges"],function(T360CarouselRanges){0<PRIVATE.esportesLength&&!PRIVATE.carouselEsportes&&(PRIVATE.carouselEsportes=new T360CarouselRanges({containerCarousel:".slide-esportes",itemsCarousel:".slide-esportes > .event",dividerScrollCarousel:1,previousButtonCarousel:".container-carousel-esportes-previous",nextButtonCarousel:".container-carousel-esportes-next"})),0<PRIVATE.destaquesLength&&!PRIVATE.carouselDestaques&&(PRIVATE.carouselDestaques=new T360CarouselRanges({containerCarousel:".slide-destaques",itemsCarousel:".slide-destaques > .event",dividerScrollCarousel:1,previousButtonCarousel:".container-carousel-destaques-previous",nextButtonCarousel:".container-carousel-destaques-next"})),PRIVATE.listenerResize||(PRIVATE.listenerResize=!0,window.addEventListener("resize",function(){PRIVATE.carouselEsportes&&PRIVATE.carouselEsportes.updateStep(),PRIVATE.carouselDestaques&&PRIVATE.carouselDestaques.updateStep()}))})},PRIVATE.removeCarousels=function(){0===eventsEsportes.length&&0===eventsDestaques.length&&(document.querySelector(".container-carousel-esportes").style.display="none",document.querySelector(".container-carousel-destaques").style.display="none")},PRIVATE.normalizeData=function(data){var event=null;return 3===data.EVENT_TYPE||7===data.EVENT_TYPE?(1==data.TEAMS.TEAM[1].HOME&&data.TEAMS.TEAM.reverse(),event={template:"itemMatch",eventType:data.EVENT_TYPE,classDateManager:"date-manager",id:data.ID,url:data.URL,coverage:data.COVERAGE,coverageOrderBar:data.COVERAGE_ORDER_BAR,dateTime:data.DATE_TIME,prioritized:+(data.TEAMS.TEAM[0].PRIORITIZED||data.TEAMS.TEAM[1].PRIORITIZED),hideTicker:0,match:{gameTime:{name:PUBLIC.l10n.get("gameTime"+data.GAME_INFO.GAME_TIME.ID),id:data.GAME_INFO.GAME_TIME.ID},teams:{team1:{shield:PRIVATE.fixTeamShield(data.TEAMS.TEAM[0].SHIELD),name:"br"===pkg.context.page.get("country")?data.TEAMS.TEAM[0].NAME_PT:data.TEAMS.TEAM[0].NAME_ES,nameAbbr:"br"===pkg.context.page.get("country")?data.TEAMS.TEAM[0].SHORT_NAME_PT:data.TEAMS.TEAM[0].SHORT_NAME_ES,providerClubId:data.TEAMS.TEAM[0].PROVIDER_CLUB_ID,score:{points:data.TEAMS.TEAM[0].SCORE.GOALS,penalty:data.TEAMS.TEAM[0].SCORE.PENALTY}},team2:{shield:PRIVATE.fixTeamShield(data.TEAMS.TEAM[1].SHIELD),name:"br"===pkg.context.page.get("country")?data.TEAMS.TEAM[1].NAME_PT:data.TEAMS.TEAM[1].NAME_ES,nameAbbr:"br"===pkg.context.page.get("country")?data.TEAMS.TEAM[1].SHORT_NAME_PT:data.TEAMS.TEAM[1].SHORT_NAME_ES,providerClubId:data.TEAMS.TEAM[1].PROVIDER_CLUB_ID,score:{points:data.TEAMS.TEAM[1].SCORE.GOALS,penalty:data.TEAMS.TEAM[1].SCORE.PENALTY}}}}}):1===data.EVENT_TYPE?event={template:"itemLive",eventType:data.EVENT_TYPE,id:data.ID,group:data.TAG,coverageId:data.COVERAGE_ID,coverage:data.COVERAGE,coverageOrderBar:data.COVERAGE_ORDER_BAR,url:data.URL,rerun:data.RERUN,videoHome:data.CONFIGURATION.HOME,videoTitle:data.CONFIGURATION.TITLE,videoSubtitle:data.CONFIGURATION.DESCRIPTION,thumb:data.THUMB,thumbSource:data.THUMB_SOURCE,hideTicker:data.HIDE_TICKER}:2===data.EVENT_TYPE&&(event={template:"itemLiveBlog",eventType:data.EVENT_TYPE,id:data.ID,group:data.TAG,coverageId:data.COVERAGE_ID,coverage:data.COVERAGE,coverageOrderBar:data.COVERAGE_ORDER_BAR,title:data.TITLE,url:data.URL,thumb:data.THUMB,thumbSource:data.THUMB_SOURCE,hideTicker:data.HIDE_TICKER}),event},PRIVATE.renderHTML=function(){for(var hasDate=!1,sportsFilter,referenceNode,elemEvents=document.getElementById("zaz-app-t360-ticker--container"),i=0,referenceNode,referenceNode;i<PRIVATE.events.length;i++)if("itemMatch"===PRIVATE.events[i].template){hasDate=!0;break}referenceNode=pkg.context.page.get("isHome")?(sportsFilter=function(event){return(3===event.eventType||7===event.eventType)&&(event.coverageOrderBar<100||1==event.prioritized)},function(event){return 1===event.eventType||2===event.eventType}):(sportsFilter=function(x){return 3===x.eventType||7===x.eventType},function(x){return 1===x.eventType||2===x.eventType}),eventsEsportes=PRIVATE.events.filter(sportsFilter),eventsDestaques=PRIVATE.events.filter(referenceNode),PRIVATE.esportesLength=eventsEsportes.length,PRIVATE.destaquesLength=eventsDestaques.length,!eventsEsportes.length&&0<eventsDestaques.length?(PRIVATE.data.defaultTabName="destaques",(eventsEsportes=eventsEsportes.map(function(event){return event.horaPartida={hora:"",data:""},event})).sort(function(a,b){return a.coverageOrderBar<b.coverageOrderBar?-1:1}),eventsEsportes.sort(function(a,b){return a.prioritized||b.prioritized?a.prioritized?-1:b.prioritized?1:0:0})):!eventsDestaques.length&&0<eventsEsportes.length&&(PRIVATE.data.defaultTabName="esportes"),elemEvents||(elemEvents=PUBLIC.templates.render("wrapper",{events:PRIVATE.events,eventsSports:eventsEsportes,eventsDestaques:eventsDestaques,device:PRIVATE.device}),(referenceNode=document.getElementById("ticker-events-container"))&&PRIVATE.data.placeholder.removeChild(referenceNode),(referenceNode=document.getElementById("zaz-nb-plugin-open-menu"))?PRIVATE.data.placeholder.insertBefore(elemEvents,referenceNode):PRIVATE.data.placeholder.appendChild(elemEvents)),PRIVATE.selectorElements(),PRIVATE.setNavigation();var elemEvents=PRIVATE.elements.wrapper.querySelectorAll(".event");elemEvents&&PRIVATE.bindEventGA(elemEvents),hasDate&&PRIVATE.loadDateTime(PRIVATE.elements.slide_sport.querySelectorAll(".event.match.data-manager"))},PRIVATE.reorderEventsTeam=function(){var elemEvents=PRIVATE.elements.slide_sport.querySelectorAll(".event");elemEvents&&pkg.context.user.get("myTeam",function(userTeam){var teamsId;if(userTeam&&userTeam.musa_id)for(var ip=0;ip<elemEvents.length;ip++)(teamsId=(teamsId=elemEvents[ip].htmlDataset("teamsId"))&&teamsId.split(","))&&PRIVATE.inArray(teamsId,userTeam.musa_id)&&(PRIVATE.elements.slide_sport.insertBefore(elemEvents[ip],PRIVATE.elements.slide_sport.firstChild),"web"===PRIVATE.device?(PRIVATE.carouselEsportes.reset(),PRIVATE.carouselEsportes.arrow()):PRIVATE.elements.slide_sport.scrollLeft=0)})},PRIVATE.createEvent=function(event){var tempTeam,hasDate=!1,elemEvent,wrapperHTML=document.createElement("div");if(event){switch(3===event.eventType||7===event.eventType?(event.template="itemMatch",hasDate=!0,event.match.teams.team1.home||(tempTeam=event.match.teams.team1,event.match.teams.team1=event.match.teams.team2,event.match.teams.team2=tempTeam),event.match.teams.team1.shield=PRIVATE.fixTeamShield(event.match.teams.team1.shield),event.match.teams.team2.shield=PRIVATE.fixTeamShield(event.match.teams.team2.shield),event.horaPartida={hora:"",data:""},event.dateTime=event.timestamp):1===event.eventType?event.template="itemLive":2===event.eventType&&(event.template="itemLiveBlog"),wrapperHTML=PUBLIC.templates.render(event.template,{event:event}),PRIVATE.events.push(event),PRIVATE.bindEventGA([wrapperHTML]),event.template){case"itemLive":case"itemLiveBlog":eventsDestaques.push(event),PRIVATE.elements.slide_destaques.appendChild(wrapperHTML),PRIVATE.elements.modalMobile&&(elemEvent=PRIVATE.elements.slide_destaques.querySelector("#ticker-event-"+event.id).cloneNode(!0),PRIVATE.elements.modalMobile.querySelector(".container-carousel-destaques .container").appendChild(elemEvent));break;case"itemMatch":eventsEsportes.push(event),PRIVATE.elements.slide_sport.appendChild(wrapperHTML),PRIVATE.elements.modalMobile&&(elemEvent=PRIVATE.elements.slide_sport.querySelector("#ticker-event-"+event.id).cloneNode(!0),PRIVATE.elements.modalMobile.querySelector(".container-carousel-esportes .container").appendChild(elemEvent))}PRIVATE.activeCarousels(),PRIVATE.calcNavigation(),PRIVATE.reorderEventsTeam(),PRIVATE.changeWidthCarousel(),hasDate&&(PRIVATE.loadDateTime(PRIVATE.elements.slide_sport.querySelectorAll("#ticker-event-"+event.id)),PRIVATE.elements.modalMobile&&PRIVATE.loadDateTime(PRIVATE.elements.modalMobile.querySelectorAll("#ticker-event-"+event.id)))}},PRIVATE.removeEvent=function(data){for(var elemMobile,elemParentEvent,whichCarousel,event=data.content,removed=!1,i=0,elemMobile;i<PRIVATE.events.length;i++)if(PRIVATE.events[i].id===event.id){PRIVATE.events.splice(i,1),(elemMobile=PRIVATE.elements.wrapper.querySelector("#ticker-event-"+event.id))&&(whichCarousel=(elemParentEvent=elemMobile.parentElement).className.match(/(esportes|destaques)/)[0],elemParentEvent&&(elemParentEvent.removeChild(elemMobile),removed=!0)),!PRIVATE.elements.modalMobile||(elemMobile=PRIVATE.elements.modalMobile.querySelector("#ticker-event-"+event.id))&&(elemParentEvent=elemMobile.parentElement)&&elemParentEvent.removeChild(elemMobile);break}if(removed){switch(whichCarousel){case"esportes":eventsEsportes=eventsEsportes.filter(function(searchedEvent){return event.id!==searchedEvent.id}),PRIVATE.esportesLength=eventsEsportes.length,0===PRIVATE.esportesLength&&(PRIVATE.data.defaultTabName="destaques",PRIVATE.getActiveTab()),PRIVATE.carouselEsportes&&0<PRIVATE.esportesLength&&PRIVATE.carouselEsportes.arrow();break;case"destaques":eventsDestaques=eventsDestaques.filter(function(searchedEvent){return event.id!==searchedEvent.id}),PRIVATE.destaquesLength=eventsDestaques.length,0===PRIVATE.destaquesLength&&(PRIVATE.data.defaultTabName="esportes",PRIVATE.getActiveTab()),PRIVATE.carouselDestaques&&0<PRIVATE.destaquesLength&&PRIVATE.carouselDestaques.arrow()}PRIVATE.removeCarousels(),PRIVATE.calcNavigation(),PRIVATE.changeWidthCarousel()}},PRIVATE.bindEventGA=function(elemts){for(var i=0,elemtsLength=elemts.length,sendMetrics=function(label){var action=label.currentTarget.htmlDataset("gaAction"),label=label.currentTarget.htmlDataset("gaLabel");action&&label&&window.tga.send("send","event","ticker",action,label)};i<elemtsLength;i++)elemts[i].addEventListener("click",sendMetrics)},PRIVATE.setNavigation=function(){PRIVATE.calcNavigation(),"mob"===pkg.context.platform.get("type")?(document.querySelector(".zaz-app-t360-ticker--veja-mais").addEventListener("click",PRIVATE.displayModal),window.addEventListener("orientationchange",PRIVATE.calcNavigation)):window.addEventListener("resize",function(){PRIVATE.resolutionRange!=pkg.context.page.get("range")&&(PRIVATE.calcNavigation(),PRIVATE.changeWidthCarousel()),"large"==pkg.context.page.get("range")&&(PRIVATE.carouselEsportes&&PRIVATE.carouselEsportes.updateStep(),PRIVATE.carouselDestaques&&PRIVATE.carouselDestaques.updateStep())})},PRIVATE.calcNavigation=function(){(PRIVATE.elements.tooltip_sports||PRIVATE.elements.tooltip_destaques)&&(PRIVATE.elements.tooltip_sportsNumber=PRIVATE.elements.tooltip_sports.querySelector(".zaz-app-t360-ticker--tooltip__itemLeft--quantity"),PRIVATE.elements.tooltip_destaquesNumber=PRIVATE.elements.tooltip_destaques.querySelector(".zaz-app-t360-ticker--tooltip__itemRight--quantity")),PRIVATE.elements.modalMobile&&(PRIVATE.elements.tooltip_sports_mobile||PRIVATE.elements.tooltip_destaques_mobile)&&(PRIVATE.elements.tooltip_sports_mobileNumber=PRIVATE.elements.tooltip_sports_mobile.querySelector(".zaz-app-t360-ticker--tooltip__itemLeft--quantity"),PRIVATE.elements.tooltip_destaques_mobileNumber=PRIVATE.elements.tooltip_destaques_mobile.querySelector(".zaz-app-t360-ticker--tooltip__itemRight--quantity")),eventsEsportes.length&&eventsDestaques.length?PRIVATE.elements.tooltip_holder.style.display="flex":PRIVATE.elements.tooltip_holder.style.display="none",eventsEsportes.length?(alreadySetupEsportesNavigationEvent||(PRIVATE.elements.tooltip_sports.addEventListener("click",function(){PRIVATE.data.defaultTabName="esportes",PRIVATE.getActiveTab(),PRIVATE.carouselEsportes&&PRIVATE.carouselEsportes.updateStep()}),alreadySetupEsportesNavigationEvent=!0),PRIVATE.elements.tooltip_sports.classList.remove("zaz-app-t360-ticker--tooltip__inactiveTab"),PRIVATE.elements.tooltip_sports_mobile&&PRIVATE.elements.tooltip_sports_mobile.classList.remove("zaz-app-t360-ticker--tooltip__inactiveTab"),PRIVATE.elements.tooltip_sportsNumber&&(PRIVATE.elements.tooltip_sportsNumber.style.visibility="",PRIVATE.elements.tooltip_sportsNumber.innerHTML=eventsEsportes.length),PRIVATE.elements.tooltip_sports_mobile&&PRIVATE.elements.tooltip_sports_mobileNumber&&(PRIVATE.elements.tooltip_sports_mobileNumber.style.visibility="",PRIVATE.elements.tooltip_sports_mobileNumber.innerHTML=eventsEsportes.length),PRIVATE.elements.slide_sport&&(PRIVATE.elements.slide_sport.className=PRIVATE.elements.slide_sport.className.replace(/events-count-\d+/,"events-count-"+eventsEsportes.length))):(PRIVATE.elements.tooltip_sports.classList.add("zaz-app-t360-ticker--tooltip__inactiveTab"),PRIVATE.elements.tooltip_sports_mobile&&PRIVATE.elements.tooltip_sports_mobile.classList.add("zaz-app-t360-ticker--tooltip__inactiveTab"),PRIVATE.elements.tooltip_sportsNumber&&(PRIVATE.elements.tooltip_sportsNumber.style.visibility="hidden"),PRIVATE.elements.tooltip_sports_mobileNumber&&(PRIVATE.elements.tooltip_sports_mobileNumber.style.visibility="hidden")),eventsDestaques.length?(alreadySetupDestaquesNavigationEvent||(PRIVATE.elements.tooltip_destaques.addEventListener("click",function(){PRIVATE.data.defaultTabName="destaques",PRIVATE.getActiveTab(),PRIVATE.carouselDestaques&&PRIVATE.carouselDestaques.updateStep()}),alreadySetupDestaquesNavigationEvent=!0),PRIVATE.elements.tooltip_destaques.classList.remove("zaz-app-t360-ticker--tooltip__inactiveTab"),PRIVATE.elements.tooltip_destaques_mobile&&(PRIVATE.elements.tooltip_destaques_mobile.classList.remove("zaz-app-t360-ticker--tooltip__inactiveTab"),PRIVATE.elements.tooltip_destaques_mobileNumber&&(PRIVATE.elements.tooltip_destaques_mobileNumber.style.visibility="",PRIVATE.elements.tooltip_destaques_mobileNumber.innerHTML=eventsDestaques.length)),PRIVATE.elements.tooltip_destaquesNumber&&(PRIVATE.elements.tooltip_destaquesNumber.style.visibility="",PRIVATE.elements.tooltip_destaquesNumber.innerHTML=eventsDestaques.length),PRIVATE.elements.slide_destaques&&(PRIVATE.elements.slide_destaques.className=PRIVATE.elements.slide_destaques.className.replace(/events-count-\d+/,"events-count-"+eventsDestaques.length))):(PRIVATE.elements.tooltip_destaques.classList.add("zaz-app-t360-ticker--tooltip__inactiveTab"),PRIVATE.elements.tooltip_destaques_mobile&&PRIVATE.elements.tooltip_destaques_mobile.classList.add("zaz-app-t360-ticker--tooltip__inactiveTab"),PRIVATE.elements.tooltip_destaquesNumber&&(PRIVATE.elements.tooltip_destaquesNumber.style.visibility="hidden"),PRIVATE.elements.tooltip_destaques_mobileNumber&&(PRIVATE.elements.tooltip_destaques_mobileNumber.style.visibility="hidden"));var totalEvents=eventsDestaques.length+eventsEsportes.length;PRIVATE.elements.wrapper.className=PRIVATE.elements.wrapper.className.replace(/total-events-count-\d+/,"total-events-count-"+totalEvents),PRIVATE.elements.backendWrapper&&(PRIVATE.elements.backendWrapper.className=PRIVATE.elements.backendWrapper.className.replace(/total-events-count-\d+/,"total-events-count-"+totalEvents)),PRIVATE.esportesLength=eventsEsportes.length,PRIVATE.destaquesLength=eventsDestaques.length,!eventsEsportes.length&&0<eventsDestaques.length?PRIVATE.data.defaultTabName="destaques":!eventsDestaques.length&&0<eventsEsportes.length&&(PRIVATE.data.defaultTabName="esportes"),PRIVATE.getActiveTab(),PRIVATE.changeWidthCarousel()},PRIVATE.getActiveTab=function(){var container_carousel_sport=PRIVATE.data.placeholder.querySelector(".container-carousel-esportes"),container_carousel_destaques=PRIVATE.data.placeholder.querySelector(".container-carousel-destaques"),tooltip_esportes=PRIVATE.data.placeholder.querySelector(".zaz-app-t360-ticker--tooltip__itemLeft"),tooltip_destaques=PRIVATE.data.placeholder.querySelector(".zaz-app-t360-ticker--tooltip__itemRight");switch(PRIVATE.data.defaultTabName){case"esportes":0<eventsEsportes.length&&(container_carousel_destaques.style.display="none",tooltip_destaques.classList.remove("zaz-app-t360-ticker--tooltip__activeTab"),container_carousel_sport.style.display="flex",tooltip_esportes.classList.add("zaz-app-t360-ticker--tooltip__activeTab"),PRIVATE.elements.slide_sport_mobile&&(PRIVATE.elements.tooltip_destaques_mobile.classList.remove("zaz-app-t360-ticker--tooltip__activeTab"),PRIVATE.elements.tooltip_sports_mobile.classList.add("zaz-app-t360-ticker--tooltip__activeTab"),PRIVATE.elements.slide_sport_mobile.style.display="block",PRIVATE.elements.slide_destaques_mobile.style.display="none"),PRIVATE.carouselEsportes&&0<PRIVATE.esportesLength&&(PRIVATE.carouselEsportes.updateStep(),PRIVATE.carouselEsportes.arrow()));break;case"destaques":0<eventsDestaques.length&&(container_carousel_sport.style.display="none",tooltip_esportes.classList.remove("zaz-app-t360-ticker--tooltip__activeTab"),container_carousel_destaques.style.display="flex",tooltip_destaques.classList.add("zaz-app-t360-ticker--tooltip__activeTab"),PRIVATE.elements.slide_sport_mobile&&(PRIVATE.elements.tooltip_destaques_mobile.classList.add("zaz-app-t360-ticker--tooltip__activeTab"),PRIVATE.elements.tooltip_sports_mobile.classList.remove("zaz-app-t360-ticker--tooltip__activeTab"),PRIVATE.elements.slide_sport_mobile.style.display="none",PRIVATE.elements.slide_destaques_mobile.style.display="block"),PRIVATE.carouselDestaques&&0<PRIVATE.destaquesLength&&(PRIVATE.carouselDestaques.updateStep(),PRIVATE.carouselDestaques.arrow()))}},PRIVATE.fixTeamShield=function(shield){return"https://p2.trrsf.com/image/fget/cf/51/51/filters:quality(100)/"+(shield=shield.replace("https://p1.trrsf.com/image/get?src=http://","").replace("&amp;o=sc&amp;w=51",""))},PRIVATE.changeWidthCarousel=function(){var slide_sport=PRIVATE.data.placeholder.querySelector(".slide-esportes"),slide_destaques=PRIVATE.data.placeholder.querySelector(".slide-destaques");0<PRIVATE.esportesLength&&(PRIVATE.esportesLength<3&&1200<=window.innerWidth?(slide_sport.classList.add("disable-carousel"),slide_sport.classList.remove("active-carousel")):(slide_sport.classList.remove("disable-carousel"),slide_sport.classList.add("active-carousel"))),0<PRIVATE.destaquesLength&&(PRIVATE.destaquesLength<3&&1200<=window.innerWidth?(slide_destaques.classList.add("disable-carousel"),slide_destaques.classList.remove("active-carousel")):(slide_destaques.classList.remove("disable-carousel"),slide_destaques.classList.add("active-carousel")))},PRIVATE.loadChannelRealtime=function(){PRIVATE.rtSoccerId=parseInt(PRIVATE.elements.wrapper.htmlDataset("rtSoccerId")),PRIVATE.rtCmsId=parseInt(PRIVATE.elements.wrapper.htmlDataset("rtCmsId")),pkg.require(["mod.t360.realtime"],function(ModNewRealtime){PRIVATE.rtSoccerId||PRIVATE.rtCmsId?PRIVATE.realtimeChannel.lastID=PRIVATE.rtSoccerId>PRIVATE.rtCmsId?PRIVATE.rtCmsId:PRIVATE.rtSoccerId:(PRIVATE.realtimeChannel.lastID=1,PRIVATE.realtimeChannel.type="O"),ModNewRealtime.subscribe({channelCode:PRIVATE.realtimeChannel.channel,subscriptionType:PRIVATE.realtimeChannel.type,realtimeId:PRIVATE.realtimeChannel.lastID,dispatcher:PRIVATE.callbackChannel,onError:function(err){console.log("Realtime Subscribe Error",err)}})})},PRIVATE.callbackChannel=function(data){var event,currentRealtimeID;data.payload.content.eventType||(data.payload.content.eventType=1),3===(event=data.payload.content).eventType||7===event.eventType?currentRealtimeID=PRIVATE.rtSoccerId:1!==event.eventType&&2!==event.eventType||(currentRealtimeID=PRIVATE.rtCmsId),data.realtimeId>currentRealtimeID&&PRIVATE.insertMessage(data.payload)},window.__tickerListenerEvent=PRIVATE.insertMessage=function(json){json&&("update"===json.flag||"insert"===json.flag?(2===json.content.eventType&&(json.content.status=2),json.content.status===PRIVATE.eventStatus&&PRIVATE.insertEvent(json)):"remove"===json.flag&&PRIVATE.removeEvent(json))},PRIVATE.insertEvent=function(sponsors){var event=sponsors.content;if(1===event.videoHome&&0<=event.videoHomeConfig.indexOf(pkg.context.page.get("idItemMenu")))return!1;if(event.id==PRIVATE.eventAutoplayId)return!1;if(event.hideTicker&&(1===event.eventType||2===event.eventType))return PRIVATE.removeEvent(sponsors),!1;if(3===event.eventType||7===event.eventType){var sponsors=event.match.teams.team1.prioritized||event.match.teams.team2.prioritized;if(pkg.context.page.get("isHome")&&100<=event.coverage_order_bar&&0==sponsors)return!1;var sponsors=document.querySelector(".zaz-app-t360-ticker--sponsors");sponsors&&"none"===sponsors.style.display&&(sponsors.style.display="flex")}if(PRIVATE.inArray(PRIVATE.acceptEventsType,event.eventType)){for(var i=0;i<PRIVATE.events.length;i++)if(PRIVATE.events[i].id===event.id){if(3===event.eventType||7===event.eventType)return PRIVATE.updateMatchEvent(PRIVATE.events[i],event),!0;if(1===event.eventType||2===event.eventType)return PRIVATE.updateDestaquesEvent(PRIVATE.events[i],event),!0}PRIVATE.pageEventId!==event.id&&PRIVATE.createEvent(event)}},PRIVATE.updateDestaquesEvent=function(currentEvent,updateEvent){var elemEvent=PRIVATE.elements.slide_destaques.querySelector("#ticker-event-"+currentEvent.id),image=elemEvent.querySelector(".thumb img"),title=updateEvent.videoTitle||updateEvent.title;image&&(updateEvent.thumbSource?image.src="https://p2.trrsf.com/image/fget/cf/129/72/"+updateEvent.thumbSource.replace("http://",""):image.src=updateEvent.thumb,image.alt=title),1===currentEvent.eventType?(elemEvent.querySelector(".sub-desc").innerHTML=title,elemEvent.querySelector(".title-live").innerHTML=PRIVATE.liveEventsType[updateEvent.rerun],0==updateEvent.rerun?elemEvent.querySelector(".title-live").classList.add("live-event"):elemEvent.querySelector(".title-live").classList.remove("live-event")):2===currentEvent.eventType&&(elemEvent.querySelector(".title").innerHTML=updateEvent.title)},PRIVATE.updateMatchEvent=function(currentEvent,updateEvent){var team1,team2,hasGol=!1,elemTeam=null,elemAnimate=null,elemEvent,elemEventList,generateGoalAnimationClosure=function(elemAnimate,displayStyle){return function(){elemAnimate.style.display=displayStyle}},generateTeamNameAnimationClosure=function(elemTeam){return function(){elemTeam&&(elemTeam.className+=" animation")}},elemEventList=PRIVATE.elements.modalMobile?[PRIVATE.elements.slide_sport.querySelector("#ticker-event-"+currentEvent.id),PRIVATE.elements.slide_sport_mobile.querySelector("#ticker-event-"+currentEvent.id)]:[PRIVATE.elements.slide_sport.querySelector("#ticker-event-"+currentEvent.id)];if(elemEventList.length){team2=updateEvent.match.teams.team1.home?(team1=updateEvent.match.teams.team1,updateEvent.match.teams.team2):(team1=updateEvent.match.teams.team2,updateEvent.match.teams.team1);for(var i=0;i<elemEventList.length;i++){if(elemEvent=elemEventList[i],1===updateEvent.match.gameTime.id)elemEvent.dataset.date=updateEvent.dateTime||updateEvent.timestamp,PRIVATE.loadDateTime([elemEvent]);else{var gols1=currentEvent.match.teams.team1.score.points||0,gols2=currentEvent.match.teams.team2.score.points||0,penaltis1=currentEvent.match.teams.team1.score.penalty?"("+currentEvent.match.teams.team1.score.penalty+")":"",penaltis2=currentEvent.match.teams.team2.score.penalty?"("+currentEvent.match.teams.team2.score.penalty+")":"";if(elemEvent.querySelector(".zaz-app-t360-ticker--match--info--time").innerHTML='<span class="goal team1">'+gols1+'</span><span class="penalty team1">'+penaltis1+'</span><span class="icon icon-solid icon-12 icon-times"></span><span class="penalty team2">'+penaltis2+'</span><span class="goal team2">'+gols2+"</span>",7===updateEvent.match.gameTime.id){for(var elements=elemEvent.querySelectorAll(".penalty"),j=0;j<elements.length;j++)elements[j].className=elements[j].className.replace(" hide");currentEvent.match.teams.team1.score.penalty!=team1.score.penalty&&0<team1.score.penalty&&(hasGol=!0,currentEvent.match.teams.team1.score.penalty=team1.score.penalty,(elemTeam=elements[0])&&(elemTeam.innerHTML="("+currentEvent.match.teams.team1.score.penalty+")")),currentEvent.match.teams.team2.score.penalty!=team2.score.penalty&&0<team2.score.penalty&&(hasGol=!0,currentEvent.match.teams.team2.score.penalty=team2.score.penalty,(elemTeam=elements[1])&&(elemTeam.innerHTML="("+currentEvent.match.teams.team2.score.penalty+")"))}else currentEvent.match.teams.team1.score.points!=team1.score.points&&0<team1.score.points&&(hasGol=!0,currentEvent.match.teams.team1.score.points=team1.score.points,(elemTeam=elemEvent.querySelector(".goal.team1"))&&(elemTeam.innerHTML=currentEvent.match.teams.team1.score.points)),currentEvent.match.teams.team2.score.points!=team2.score.points&&0<team2.score.points&&(hasGol=!0,currentEvent.match.teams.team2.score.points=team2.score.points,(elemTeam=elemEvent.querySelector(".goal.team2"))&&(elemTeam.innerHTML=currentEvent.match.teams.team2.score.points))}hasGol&&((elemAnimate=elemEvent.querySelector(".zaz-app-t360-animation-goal"))&&("block"===elemAnimate.style.display?(elemAnimate.style.display="none",setTimeout(generateGoalAnimationClosure(elemAnimate,"block"),300)):elemAnimate.style.display="block",elemAnimate.addEventListener("animationend",generateGoalAnimationClosure(elemAnimate,"none"))),elemTeam&&(elemTeam.className=elemTeam.className.replace(" animation",""),setTimeout(generateTeamNameAnimationClosure(elemTeam),300)),"mob"===pkg.context.platform.get("type")&&navigator&&navigator.vibrate&&navigator.vibrate(500)),1!=updateEvent.match.gameTime.id&&(currentEvent.match.gameTime.id=updateEvent.match.gameTime.id,(elemTeam=elemEvent.querySelector(".zaz-app-t360-ticker--match--info--date"))&&(elemTeam.innerHTML=PUBLIC.l10n.get("gameTime"+currentEvent.match.gameTime.id)))}}},PRIVATE.displayModal=function(){var matchesWithDates;alreadySetupCloseEvent||(alreadySetupCloseEvent=!0,PRIVATE.elements.modalMobile=document.querySelector(".zaz-app-t360-ticker--modal-mobile"),PRIVATE.elements.slide_sport_mobile=PRIVATE.elements.modalMobile.querySelector(".container-carousel-esportes"),PRIVATE.elements.slide_destaques_mobile=PRIVATE.elements.modalMobile.querySelector(".container-carousel-destaques"),PRIVATE.elements.modalMobile.querySelector(".icon-times").addEventListener("click",function(){PRIVATE.elements.modalMobile.style.display="none",document.body.style.overflow=""}),eventsDestaques.length&&PRIVATE.elements.modalMobile.querySelector(".zaz-app-t360-ticker--tooltip__itemRight").addEventListener("click",function(){PRIVATE.data.defaultTabName="destaques",PRIVATE.changeActiveTabModal(),PRIVATE.carouselDestaques&&PRIVATE.carouselDestaques.updateStep()}),eventsEsportes.length&&PRIVATE.elements.modalMobile.querySelector(".zaz-app-t360-ticker--tooltip__itemLeft").addEventListener("click",function(){PRIVATE.data.defaultTabName="esportes",PRIVATE.changeActiveTabModal(),PRIVATE.carouselEsportes&&PRIVATE.carouselEsportes.updateStep()}),(matchesWithDates=PRIVATE.elements.modalMobile.querySelectorAll(".event.match.data-manager"))&&PRIVATE.loadDateTime(matchesWithDates)),PRIVATE.changeActiveTabModal(),PRIVATE.elements.modalMobile.style.display="block",document.body.style.overflow="hidden"},PRIVATE.changeActiveTabModal=function(){var container_carousel_sport_mobile=PRIVATE.elements.modalMobile.querySelector(".container-carousel-esportes"),container_carousel_destaques_mobile=PRIVATE.elements.modalMobile.querySelector(".container-carousel-destaques"),tooltip_sports_mobile=PRIVATE.elements.modalMobile.querySelector(".zaz-app-t360-ticker--tooltip__itemLeft"),tooltip_destaques_mobile=PRIVATE.elements.modalMobile.querySelector(".zaz-app-t360-ticker--tooltip__itemRight");switch(PRIVATE.data.defaultTabName){case"esportes":0<eventsEsportes.length&&(container_carousel_destaques_mobile.style.display="none",tooltip_destaques_mobile.classList.remove("zaz-app-t360-ticker--tooltip__activeTab"),container_carousel_sport_mobile.style.display="block",tooltip_sports_mobile.classList.add("zaz-app-t360-ticker--tooltip__activeTab"),PRIVATE.carouselEsportes&&0<PRIVATE.esportesLength&&PRIVATE.carouselEsportes.arrow());break;case"destaques":0<eventsDestaques.length&&(container_carousel_sport_mobile.style.display="none",tooltip_sports_mobile.classList.remove("zaz-app-t360-ticker--tooltip__activeTab"),container_carousel_destaques_mobile.style.display="block",tooltip_destaques_mobile.classList.add("zaz-app-t360-ticker--tooltip__activeTab"),PRIVATE.carouselDestaques&&0<PRIVATE.destaquesLength&&PRIVATE.carouselDestaques.arrow())}},PRIVATE.inArray=function(array,val){for(var i=0,j=array.length;i<j;i++)if(array[i]===val)return!0;return!1},PRIVATE.init=function(){var feed=window.feedLive,elemLive=document.querySelector(".app-t360-live");PRIVATE.eventAutoplayId=0,elemLive&&elemLive.htmlDataset("liveId")&&(PRIVATE.eventAutoplayId=elemLive.htmlDataset("liveId")),feed&&"object"==typeof feed&&PRIVATE.getActiveEvents(feed)},PRIVATE.init(),PUBLIC},teardown:function(why,__static,__proto,__shared){}})});