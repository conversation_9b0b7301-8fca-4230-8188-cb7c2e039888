"""
Redis Client - Sistema de cache distribuído e filas.

Este módulo implementa cache Redis, filas de processamento assíncrono
e pub/sub para comunicação entre serviços.
"""

import asyncio
import json
import pickle
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

import structlog
from redis.asyncio import Redis, ConnectionPool
from redis.exceptions import RedisError

from .config import get_settings
from .metrics import metrics_collector

logger = structlog.get_logger(__name__)


class RedisClient:
    """Cliente Redis assíncrono para cache e filas."""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.settings = get_settings()
        self.redis_url = redis_url or getattr(self.settings, 'redis_url', None) or 'redis://localhost:6379/0'
        
        # Pool de conexões
        self.pool = ConnectionPool.from_url(
            self.redis_url,
            max_connections=20,
            retry_on_timeout=True,
            socket_keepalive=True,
            socket_keepalive_options={},
            health_check_interval=30,
        )
        
        self.redis = Redis(connection_pool=self.pool)
        self._initialized = False
        
        # Prefixos para organização
        self.cache_prefix = "cache:"
        self.queue_prefix = "queue:"
        self.lock_prefix = "lock:"
        self.session_prefix = "session:"
        
        logger.info("Redis client initialized", url=self._mask_url(self.redis_url))
    
    async def initialize(self) -> None:
        """Inicializar conexão Redis."""
        if self._initialized:
            return
        
        try:
            # Testar conexão
            await self.redis.ping()
            
            # Configurar scripts Lua para operações atômicas
            await self._load_lua_scripts()
            
            self._initialized = True
            logger.info("Redis connection established successfully")
            
        except Exception as e:
            logger.error("Failed to initialize Redis connection", error=str(e))
            raise
    
    async def _load_lua_scripts(self) -> None:
        """Carregar scripts Lua para operações atômicas."""
        # Script para operação atômica de cache com TTL
        self.cache_set_script = await self.redis.script_load("""
            local key = KEYS[1]
            local value = ARGV[1]
            local ttl = tonumber(ARGV[2])
            
            redis.call('SET', key, value)
            if ttl > 0 then
                redis.call('EXPIRE', key, ttl)
            end
            return 'OK'
        """)
        
        # Script para operação atômica de fila com prioridade
        self.queue_push_script = await self.redis.script_load("""
            local queue_key = KEYS[1]
            local priority_key = KEYS[2]
            local item = ARGV[1]
            local priority = tonumber(ARGV[2])
            local timestamp = tonumber(ARGV[3])
            
            -- Adicionar item à fila
            redis.call('LPUSH', queue_key, item)
            
            -- Adicionar à fila de prioridade se especificado
            if priority > 0 then
                redis.call('ZADD', priority_key, priority, item)
            end
            
            return redis.call('LLEN', queue_key)
        """)
    
    # ================================
    # Cache Operations
    # ================================
    
    async def cache_get(self, key: str) -> Optional[Any]:
        """Obter valor do cache."""
        try:
            cache_key = f"{self.cache_prefix}{key}"
            value = await self.redis.get(cache_key)
            
            if value is None:
                metrics_collector.storage_operations_total.labels(
                    operation="cache_get",
                    backend="redis",
                    status="miss"
                ).inc()
                return None
            
            # Tentar deserializar JSON primeiro, depois pickle
            try:
                result = json.loads(value)
            except (json.JSONDecodeError, TypeError):
                result = pickle.loads(value)
            
            metrics_collector.storage_operations_total.labels(
                operation="cache_get",
                backend="redis",
                status="hit"
            ).inc()
            
            logger.debug("Cache hit", key=key)
            return result
            
        except Exception as e:
            logger.error("Cache get failed", key=key, error=str(e))
            metrics_collector.record_error("redis", "cache_get_error")
            return None
    
    async def cache_set(
        self,
        key: str,
        value: Any,
        ttl: int = 3600,
        serialize_json: bool = True
    ) -> bool:
        """Definir valor no cache."""
        try:
            cache_key = f"{self.cache_prefix}{key}"
            
            # Serializar valor
            if serialize_json:
                try:
                    serialized_value = json.dumps(value, default=str)
                except (TypeError, ValueError):
                    serialized_value = pickle.dumps(value)
            else:
                serialized_value = pickle.dumps(value)
            
            # Usar script Lua para operação atômica
            await self.redis.evalsha(
                self.cache_set_script,
                1,
                cache_key,
                serialized_value,
                ttl
            )
            
            metrics_collector.storage_operations_total.labels(
                operation="cache_set",
                backend="redis",
                status="success"
            ).inc()
            
            logger.debug("Cache set", key=key, ttl=ttl)
            return True
            
        except Exception as e:
            logger.error("Cache set failed", key=key, error=str(e))
            metrics_collector.record_error("redis", "cache_set_error")
            return False
    
    async def cache_delete(self, key: str) -> bool:
        """Remover valor do cache."""
        try:
            cache_key = f"{self.cache_prefix}{key}"
            result = await self.redis.delete(cache_key)
            
            metrics_collector.storage_operations_total.labels(
                operation="cache_delete",
                backend="redis",
                status="success"
            ).inc()
            
            return result > 0
            
        except Exception as e:
            logger.error("Cache delete failed", key=key, error=str(e))
            metrics_collector.record_error("redis", "cache_delete_error")
            return False
    
    async def cache_exists(self, key: str) -> bool:
        """Verificar se chave existe no cache."""
        try:
            cache_key = f"{self.cache_prefix}{key}"
            return await self.redis.exists(cache_key) > 0
        except Exception as e:
            logger.error("Cache exists check failed", key=key, error=str(e))
            return False
    
    async def cache_clear_pattern(self, pattern: str) -> int:
        """Limpar cache por padrão."""
        try:
            cache_pattern = f"{self.cache_prefix}{pattern}"
            keys = await self.redis.keys(cache_pattern)
            
            if keys:
                deleted = await self.redis.delete(*keys)
                logger.info("Cache cleared by pattern", pattern=pattern, deleted=deleted)
                return deleted
            
            return 0
            
        except Exception as e:
            logger.error("Cache clear pattern failed", pattern=pattern, error=str(e))
            return 0
    
    # ================================
    # Queue Operations
    # ================================
    
    async def queue_push(
        self,
        queue_name: str,
        item: Dict[str, Any],
        priority: int = 0
    ) -> int:
        """Adicionar item à fila."""
        try:
            queue_key = f"{self.queue_prefix}{queue_name}"
            priority_key = f"{self.queue_prefix}{queue_name}:priority"
            
            # Adicionar timestamp
            item_with_timestamp = {
                **item,
                "_queued_at": datetime.utcnow().isoformat(),
                "_priority": priority
            }
            
            serialized_item = json.dumps(item_with_timestamp, default=str)
            
            # Usar script Lua para operação atômica
            queue_length = await self.redis.evalsha(
                self.queue_push_script,
                2,
                queue_key,
                priority_key,
                serialized_item,
                priority,
                int(datetime.utcnow().timestamp())
            )
            
            metrics_collector.storage_operations_total.labels(
                operation="queue_push",
                backend="redis",
                status="success"
            ).inc()
            
            metrics_collector.set_queue_size(queue_name, "pending", queue_length)
            
            logger.debug("Item queued", queue=queue_name, priority=priority, length=queue_length)
            return queue_length
            
        except Exception as e:
            logger.error("Queue push failed", queue=queue_name, error=str(e))
            metrics_collector.record_error("redis", "queue_push_error")
            return 0
    
    async def queue_pop(
        self,
        queue_name: str,
        timeout: int = 10,
        use_priority: bool = False
    ) -> Optional[Dict[str, Any]]:
        """Remover item da fila."""
        try:
            if use_priority:
                # Usar fila de prioridade
                priority_key = f"{self.queue_prefix}{queue_name}:priority"
                result = await self.redis.bzpopmax(priority_key, timeout=timeout)
                
                if result:
                    _, item_json, _ = result
                    item = json.loads(item_json)
                    
                    # Remover também da fila normal
                    queue_key = f"{self.queue_prefix}{queue_name}"
                    await self.redis.lrem(queue_key, 1, item_json)
                    
                    return item
            else:
                # Usar fila FIFO normal
                queue_key = f"{self.queue_prefix}{queue_name}"
                result = await self.redis.brpop(queue_key, timeout=timeout)
                
                if result:
                    _, item_json = result
                    item = json.loads(item_json)
                    
                    # Remover também da fila de prioridade se existir
                    priority_key = f"{self.queue_prefix}{queue_name}:priority"
                    await self.redis.zrem(priority_key, item_json)
                    
                    return item
            
            return None
            
        except Exception as e:
            logger.error("Queue pop failed", queue=queue_name, error=str(e))
            metrics_collector.record_error("redis", "queue_pop_error")
            return None
    
    async def queue_length(self, queue_name: str) -> int:
        """Obter tamanho da fila."""
        try:
            queue_key = f"{self.queue_prefix}{queue_name}"
            length = await self.redis.llen(queue_key)
            
            metrics_collector.set_queue_size(queue_name, "pending", length)
            return length
            
        except Exception as e:
            logger.error("Queue length check failed", queue=queue_name, error=str(e))
            return 0
    
    async def queue_clear(self, queue_name: str) -> bool:
        """Limpar fila."""
        try:
            queue_key = f"{self.queue_prefix}{queue_name}"
            priority_key = f"{self.queue_prefix}{queue_name}:priority"
            
            await self.redis.delete(queue_key, priority_key)
            
            metrics_collector.set_queue_size(queue_name, "pending", 0)
            logger.info("Queue cleared", queue=queue_name)
            return True
            
        except Exception as e:
            logger.error("Queue clear failed", queue=queue_name, error=str(e))
            return False
    
    # ================================
    # Lock Operations
    # ================================
    
    async def acquire_lock(
        self,
        lock_name: str,
        timeout: int = 60,
        blocking_timeout: int = 10
    ) -> Optional[str]:
        """Adquirir lock distribuído."""
        try:
            lock_key = f"{self.lock_prefix}{lock_name}"
            lock_value = f"{datetime.utcnow().isoformat()}:{id(self)}"
            
            # Tentar adquirir lock
            acquired = await self.redis.set(
                lock_key,
                lock_value,
                nx=True,  # Só definir se não existir
                ex=timeout  # TTL
            )
            
            if acquired:
                logger.debug("Lock acquired", lock=lock_name, timeout=timeout)
                return lock_value
            
            # Se não conseguiu, tentar com timeout
            if blocking_timeout > 0:
                for _ in range(blocking_timeout):
                    await asyncio.sleep(1)
                    acquired = await self.redis.set(lock_key, lock_value, nx=True, ex=timeout)
                    if acquired:
                        logger.debug("Lock acquired after wait", lock=lock_name)
                        return lock_value
            
            logger.debug("Failed to acquire lock", lock=lock_name)
            return None
            
        except Exception as e:
            logger.error("Lock acquisition failed", lock=lock_name, error=str(e))
            return None
    
    async def release_lock(self, lock_name: str, lock_value: str) -> bool:
        """Liberar lock distribuído."""
        try:
            lock_key = f"{self.lock_prefix}{lock_name}"
            
            # Script Lua para liberação atômica
            lua_script = """
                if redis.call("GET", KEYS[1]) == ARGV[1] then
                    return redis.call("DEL", KEYS[1])
                else
                    return 0
                end
            """
            
            result = await self.redis.eval(lua_script, 1, lock_key, lock_value)
            
            if result:
                logger.debug("Lock released", lock=lock_name)
                return True
            else:
                logger.warning("Lock release failed - value mismatch", lock=lock_name)
                return False
                
        except Exception as e:
            logger.error("Lock release failed", lock=lock_name, error=str(e))
            return False
    
    # ================================
    # Session Management
    # ================================
    
    async def session_set(self, session_id: str, data: Dict[str, Any], ttl: int = 3600) -> bool:
        """Definir dados de sessão."""
        session_key = f"{self.session_prefix}{session_id}"
        return await self.cache_set(session_key.replace(self.cache_prefix, ""), data, ttl)
    
    async def session_get(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Obter dados de sessão."""
        session_key = f"{self.session_prefix}{session_id}"
        return await self.cache_get(session_key.replace(self.cache_prefix, ""))
    
    async def session_delete(self, session_id: str) -> bool:
        """Remover sessão."""
        session_key = f"{self.session_prefix}{session_id}"
        return await self.cache_delete(session_key.replace(self.cache_prefix, ""))
    
    # ================================
    # Health & Stats
    # ================================
    
    async def health_check(self) -> Dict[str, Any]:
        """Verificar saúde do Redis."""
        try:
            start_time = asyncio.get_event_loop().time()
            
            # Ping test
            await self.redis.ping()
            
            response_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            # Obter informações do servidor
            info = await self.redis.info()
            
            return {
                "status": "healthy",
                "response_time_ms": round(response_time, 2),
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "unknown"),
                "redis_version": info.get("redis_version", "unknown"),
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
            }
    
    async def get_stats(self) -> Dict[str, Any]:
        """Obter estatísticas do Redis."""
        try:
            info = await self.redis.info()
            
            # Contar chaves por prefixo
            cache_keys = len(await self.redis.keys(f"{self.cache_prefix}*"))
            queue_keys = len(await self.redis.keys(f"{self.queue_prefix}*"))
            lock_keys = len(await self.redis.keys(f"{self.lock_prefix}*"))
            session_keys = len(await self.redis.keys(f"{self.session_prefix}*"))
            
            return {
                "redis_info": {
                    "version": info.get("redis_version"),
                    "uptime_seconds": info.get("uptime_in_seconds"),
                    "connected_clients": info.get("connected_clients"),
                    "used_memory": info.get("used_memory"),
                    "used_memory_human": info.get("used_memory_human"),
                    "total_commands_processed": info.get("total_commands_processed"),
                },
                "key_counts": {
                    "cache_keys": cache_keys,
                    "queue_keys": queue_keys,
                    "lock_keys": lock_keys,
                    "session_keys": session_keys,
                    "total_keys": cache_keys + queue_keys + lock_keys + session_keys,
                }
            }
            
        except Exception as e:
            logger.error("Failed to get Redis stats", error=str(e))
            return {"error": str(e)}
    
    async def close(self) -> None:
        """Fechar conexões Redis."""
        try:
            await self.redis.close()
            logger.info("Redis connections closed")
        except Exception as e:
            logger.error("Error closing Redis connections", error=str(e))
    
    def _mask_url(self, url: str) -> str:
        """Mascarar senha na URL para logs."""
        if "://" not in url:
            return url
        
        try:
            from urllib.parse import urlparse, urlunparse
            parsed = urlparse(url)
            if parsed.password:
                netloc = f"{parsed.username}:***@{parsed.hostname}"
                if parsed.port:
                    netloc += f":{parsed.port}"
                masked = parsed._replace(netloc=netloc)
                return urlunparse(masked)
        except Exception:
            pass
        
        return url


# Instância global do cliente Redis
redis_client = RedisClient()


async def init_redis() -> None:
    """Inicializar cliente Redis."""
    await redis_client.initialize()


async def close_redis() -> None:
    """Fechar cliente Redis."""
    await redis_client.close()
