"""
Database - Sistema de banco de dados com SQLAlchemy.

Este módulo gerencia conexões com PostgreSQL, sessões assíncronas
e operações de banco de dados para o WebScraper.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Optional

import structlog
from sqlalchemy import event, text
from sqlalchemy.ext.asyncio import (
    AsyncEngine, AsyncSession, async_sessionmaker, create_async_engine
)
from sqlalchemy.pool import NullPool, QueuePool

from .config import get_settings
from .models import Base

logger = structlog.get_logger(__name__)


class DatabaseManager:
    """Gerenciador de banco de dados assíncrono."""
    
    def __init__(self, database_url: Optional[str] = None):
        self.settings = get_settings()
        self.database_url = database_url or self.settings.database_url
        self.engine: Optional[AsyncEngine] = None
        self.session_factory: Optional[async_sessionmaker] = None
        self._initialized = False
    
    async def initialize(self) -> None:
        """Inicializar conexão com banco de dados."""
        if self._initialized:
            return
        
        logger.info("Initializing database connection", url=self._mask_password(self.database_url))
        
        # Configurar engine baseado no tipo de banco
        if "postgresql" in self.database_url:
            self.engine = await self._create_postgresql_engine()
        elif "sqlite" in self.database_url:
            self.engine = await self._create_sqlite_engine()
        else:
            raise ValueError(f"Unsupported database type: {self.database_url}")
        
        # Configurar factory de sessões
        self.session_factory = async_sessionmaker(
            bind=self.engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=True,
            autocommit=False,
        )
        
        # Testar conexão
        await self._test_connection()
        
        self._initialized = True
        logger.info("Database connection initialized successfully")
    
    async def _create_postgresql_engine(self) -> AsyncEngine:
        """Criar engine para PostgreSQL."""
        engine = create_async_engine(
            self.database_url,
            # Pool settings
            poolclass=QueuePool,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=3600,
            
            # Connection settings
            connect_args={
                "server_settings": {
                    "application_name": "webscraper",
                    "jit": "off",  # Desabilitar JIT para queries simples
                }
            },
            
            # Logging
            echo=self.settings.debug,
            echo_pool=self.settings.debug,
            
            # Performance
            future=True,
        )
        
        # Configurar listeners para otimização
        @event.listens_for(engine.sync_engine, "connect")
        def set_postgresql_pragmas(dbapi_connection, connection_record):
            """Configurar pragmas do PostgreSQL."""
            with dbapi_connection.cursor() as cursor:
                # Configurações de performance
                cursor.execute("SET statement_timeout = '30s'")
                cursor.execute("SET lock_timeout = '10s'")
                cursor.execute("SET idle_in_transaction_session_timeout = '60s'")
        
        return engine
    
    async def _create_sqlite_engine(self) -> AsyncEngine:
        """Criar engine para SQLite."""
        engine = create_async_engine(
            self.database_url,
            # SQLite settings
            poolclass=NullPool,  # SQLite não precisa de pool
            connect_args={
                "check_same_thread": False,
                "timeout": 30,
            },
            
            # Logging
            echo=self.settings.debug,
            
            # Performance
            future=True,
        )
        
        # Configurar pragmas do SQLite
        @event.listens_for(engine.sync_engine, "connect")
        def set_sqlite_pragmas(dbapi_connection, connection_record):
            """Configurar pragmas do SQLite."""
            cursor = dbapi_connection.cursor()
            
            # Performance pragmas
            cursor.execute("PRAGMA journal_mode=WAL")
            cursor.execute("PRAGMA synchronous=NORMAL")
            cursor.execute("PRAGMA cache_size=10000")
            cursor.execute("PRAGMA temp_store=MEMORY")
            cursor.execute("PRAGMA mmap_size=268435456")  # 256MB
            
            # Foreign keys
            cursor.execute("PRAGMA foreign_keys=ON")
            
            cursor.close()
        
        return engine
    
    async def _test_connection(self) -> None:
        """Testar conexão com banco de dados."""
        try:
            async with self.engine.begin() as conn:
                result = await conn.execute(text("SELECT 1"))
                assert result.scalar() == 1
            
            logger.debug("Database connection test successful")
            
        except Exception as e:
            logger.error("Database connection test failed", error=str(e))
            raise
    
    async def create_tables(self) -> None:
        """Criar todas as tabelas."""
        if not self._initialized:
            await self.initialize()
        
        logger.info("Creating database tables")
        
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database tables created successfully")
    
    async def drop_tables(self) -> None:
        """Remover todas as tabelas (cuidado!)."""
        if not self._initialized:
            await self.initialize()
        
        logger.warning("Dropping all database tables")
        
        async with self.engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        
        logger.warning("All database tables dropped")
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Obter sessão de banco de dados."""
        if not self._initialized:
            await self.initialize()
        
        async with self.session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def execute_raw(self, query: str, params: Optional[dict] = None) -> any:
        """Executar query SQL raw."""
        async with self.get_session() as session:
            result = await session.execute(text(query), params or {})
            return result
    
    async def get_stats(self) -> dict:
        """Obter estatísticas do banco de dados."""
        stats = {
            "engine_info": {
                "url": self._mask_password(self.database_url),
                "pool_size": getattr(self.engine.pool, "size", None),
                "checked_out": getattr(self.engine.pool, "checkedout", None),
                "overflow": getattr(self.engine.pool, "overflow", None),
            }
        }
        
        # Estatísticas específicas do PostgreSQL
        if "postgresql" in self.database_url:
            try:
                async with self.get_session() as session:
                    # Conexões ativas
                    result = await session.execute(text("""
                        SELECT count(*) as active_connections
                        FROM pg_stat_activity 
                        WHERE state = 'active'
                    """))
                    stats["active_connections"] = result.scalar()
                    
                    # Tamanho do banco
                    result = await session.execute(text("""
                        SELECT pg_size_pretty(pg_database_size(current_database())) as db_size
                    """))
                    stats["database_size"] = result.scalar()
                    
            except Exception as e:
                logger.debug("Failed to get PostgreSQL stats", error=str(e))
        
        return stats
    
    async def health_check(self) -> dict:
        """Verificar saúde do banco de dados."""
        try:
            start_time = asyncio.get_event_loop().time()
            
            async with self.get_session() as session:
                await session.execute(text("SELECT 1"))
            
            response_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            return {
                "status": "healthy",
                "response_time_ms": round(response_time, 2),
                "database_type": "postgresql" if "postgresql" in self.database_url else "sqlite",
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "database_type": "postgresql" if "postgresql" in self.database_url else "sqlite",
            }
    
    async def close(self) -> None:
        """Fechar conexões com banco de dados."""
        if self.engine:
            await self.engine.dispose()
            logger.info("Database connections closed")
    
    def _mask_password(self, url: str) -> str:
        """Mascarar senha na URL para logs."""
        if "://" not in url:
            return url
        
        try:
            from urllib.parse import urlparse, urlunparse
            parsed = urlparse(url)
            if parsed.password:
                netloc = f"{parsed.username}:***@{parsed.hostname}"
                if parsed.port:
                    netloc += f":{parsed.port}"
                masked = parsed._replace(netloc=netloc)
                return urlunparse(masked)
        except Exception:
            pass
        
        return url


# Instância global do gerenciador de banco
db_manager = DatabaseManager()


def get_db_session():
    """Dependency para obter sessão de banco de dados."""
    return db_manager.get_session()


async def init_database() -> None:
    """Inicializar banco de dados."""
    await db_manager.initialize()


async def create_tables() -> None:
    """Criar tabelas do banco de dados."""
    await db_manager.create_tables()


async def close_database() -> None:
    """Fechar conexões do banco de dados."""
    await db_manager.close()


# Context manager para transações
@asynccontextmanager
async def transaction() -> AsyncGenerator[AsyncSession, None]:
    """Context manager para transações."""
    async with db_manager.get_session() as session:
        async with session.begin():
            yield session
