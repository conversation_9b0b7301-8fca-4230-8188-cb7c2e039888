/*! zaz-app-t360-weather - v1.0.0 - 05/12/2024 -- 12:57pm */
zaz.use(function appT360Weather(pkg){var console=pkg.console,appFactory=pkg.factoryManager.get("app"),STATIC_PUBLIC=null,STATIC_PRIVATE={};appFactory.create({name:"t360.weather",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/t360-weather",source:"http://github.tpn.terra.com/Terra/t360-weather",description:"weather app",tests:"http://s1.trrsf.com/fe/t360-weather/tests/index.htm?zaz[env]=tests",dependencies:[],templates:{"weather-default":'<div class="t360-weather"><div class="t360-weather__main t360-weather__app-screen {{ \'hidden-screen\' if not locationFound }}"><div class="t360-weather__main__header"><a href="https://www.terra.com.br/noticias/climatempo/"><div class="t360-weather__main__header__logo"></div></a><div class="icon icon-16 icon-solid icon-color-black icon-search" title="Trocar de cidade"></div></div><div class="t360-weather__main__info"><div><div class="t360-weather__main__info__city-name">{{ cityName + \' / \' + uf }}</div><div class="t360-weather__main__info__rain-prob">Probabilidade de chuva: <b>{{ rainProb }}</b> {{ rainPrecip }}</div></div><div class="t360-weather__main__info__temperature"><span class="icon icon-64 icon-weather icon-weather_{{ weatherIcon.type }}" title="{{ weatherIcon.label }}"></span><div><div class="t360-weather__main__info__temperature__current"><span>{{ currentTemp }}</span>°</div><div class="t360-weather__main__info__temperature__min-max"><span>{{ minTemp }}</span><span>{{ maxTemp }}</span></div></div></div><div class="t360-weather__main__info__day-icons" title="{{ brief }}"><div title="{{ morningIcon.label }}"><span class="icon icon-weather icon-weather_{{ morningIcon.type }}"></span><span>Manhã</span></div><div title="{{ afternoonIcon.label }}"><span class="icon icon-weather icon-weather_{{ afternoonIcon.type }}"></span><span>Tarde</span></div><div title="{{ eveningIcon.label }}"><span class="icon icon-weather icon-weather_{{ eveningIcon.type }}"></span><span>Noite</span></div></div></div></div>{% set searchInputTitle = (\'Troque\' if locationFound else \'Busque\') + \' por uma cidade do Brasil\' %}<div class="t360-weather__search t360-weather__app-screen {{ \'hidden-screen\' if locationFound }}">{% if locationFound %}<div class="t360-weather__search__return"><span class="icon icon-solid icon-16 icon-color-black icon-share" title="Voltar"></span></div><div class="t360-weather__search__city-name">Cidade atual:<strong>{{ cityName + \' / \' + uf }}</strong></div>{% endif %}<div class="t360-weather__search__input-wrapper"><input class="t360-weather__search__input" type="text" name="input-weather-city-name" id="input-weather-city-name"placeholder="Digite o nome da cidade" autocomplete="off" title="{{ searchInputTitle }}"><span class="icon icon-solid icon-16 icon-times" title="Cancelar busca"></span><div class="t360-weather__search__loader"></div><ul class="t360-weather__search__result-box"></ul></div>{% if enableUseMyLocationButton %}<div class="t360-weather__search__use-my-location" title="Usar minha localização"><span class="icon icon-solid icon-16 icon-map-marker-alt"></span><span class="default-label">usar minha localização</span><span class="warning-permission-label">ative a localização no seu navegador e recarregue a página!</span><span class="waiting-permission-label">aguardando permissão...</span></div>{% endif %}<div class="t360-weather__search__app-logo"><div class="icon icon-weather icon-64 icon-weather_2"></div><div class="t360-weather__search__app-logo__label">Previsão do tempo</div></div></div></div>',"weather-small":'<div class="t360-weather t360-weather--small"><div class="t360-weather__main t360-weather__app-screen"><div class="t360-weather__main__header"><div class="t360-weather__main__header__city-name">{{ cityName + \' / \' + uf if locationFound else \'Sem localização precisa\' }}</div><a href="https://www.terra.com.br/noticias/climatempo/"><div class="t360-weather__main__header__logo"></div></a></div><div class="t360-weather__main__info {{ \'location-found\' if locationFound }}">{% if locationFound %}<div class="t360-weather__main__info__rain-prob">Prob. de chuva: <b>{{ rainProb }}</b> {{ rainPrecip }}</div><div class="t360-weather__main__info__day-icons" title="{{ brief }}"><div title="{{ morningIcon.label }}"><span class="icon icon-weather icon-weather_{{ morningIcon.type }}"></span><span>Manhã</span></div><div title="{{ afternoonIcon.label }}"><span class="icon icon-weather icon-weather_{{ afternoonIcon.type }}"></span><span>Tarde</span></div><div title="{{ eveningIcon.label }}"><span class="icon icon-weather icon-weather_{{ eveningIcon.type }}"></span><span>Noite</span></div></div>{% else %}<div class="t360-weather__main__no-location"><div class="t360-weather__main__no-location__search-link" title="Clique para buscar informações de uma cidade"><span class="icon icon-16 icon-solid icon-search"></span><span>encontrar uma cidade</span></div><div class="t360-weather__main__no-location__use-my-location" title="Clique aqui para usar sua localização atual"><span class="icon icon-solid icon-16 icon-map-marker-alt"></span><span class="default-label">usar minha localização</span><span class="warning-permission-label">ative a localização no seu navegador e recarregue a página!</span><span class="waiting-permission-label">aguardando permissão...</span></div></div>{% endif %}<div class="t360-weather__main__info__temperature-wrapper"><div class="t360-weather__main__info__current-temp">{{ currentTemp if currentTemp else \'--\' }}°</div><div class="t360-weather__main__info__minmax"><span>{{ minTemp if minTemp else \'--°C\' }}</span><b>{{ maxTemp if maxTemp else \'--°C\' }}</b></div></div></div>{% if not locationFound %}<div class="icon icon-weather icon-32 icon-weather_error_light"></div>{% endif %}</div><div class="t360-weather__search t360-weather__app-screen hidden-screen"><div class="t360-weather__search__header"><div class="t360-weather__search__return"><span class="icon icon-solid icon-16 icon-color-black icon-share" title="Voltar"></span></div><div class="icon icon-40 icon-weather icon-weather_2"></div><div class="t360-weather__search__header__title">Previsão do tempo</div></div><div class="t360-weather__search__input-wrapper"><input class="t360-weather__search__input" type="text" name="input-weather-city-name" id="input-weather-city-name"placeholder="Digite o nome da cidade" autocomplete="off" title="{{ searchInputTitle }}"><span class="icon icon-solid icon-16 icon-times" title="Cancelar busca"></span><div class="t360-weather__search__loader"></div><ul class="t360-weather__search__result-box"></ul></div></div></div>'},expects:{expects:{type:"object",properties:{container:{type:"object",required:!0},data:{type:"object",required:!0,properties:{configs:{type:"object",properties:{size:{type:"string",required:!0,default:"default"}}}}}}}},setup:function(STATIC_PUBLIC,PUBLIC,__shared){STATIC_PRIVATE.trrGeo=decodeURI(pkg.utils.getCookie("trrgeo")).split("|"),STATIC_PRIVATE.fallbackCityId=STATIC_PRIVATE.trrGeo[6]||null,STATIC_PRIVATE.fallbackCoordinates=STATIC_PRIVATE.trrGeo[0]&&STATIC_PRIVATE.trrGeo[1]?{latitude:STATIC_PRIVATE.trrGeo[0],longitude:STATIC_PRIVATE.trrGeo[1]}:null,STATIC_PRIVATE.iconLabelDict={1:"Sol","1n":"Noite sem nuvens",2:"Sol com algumas nuvens","2r":"Sol com muitas nuvens","2n":"Noite com algumas nuvens","2rn":"Noite com muitas nuvens",3:"Nublado","3n":"Nublado",4:"Sol e chuva","4r":"Sol com muitas nuvens e chuva","4n":"Noite chuvosa","4rn":"Noite nublada e chuvosa","4t":"Sol entre nuvens e pancadas de chuva, com trovoadas","4tn":"Pancadas de chuva durante a noite",5:"Chuvoso","5n":"Chuvoso",6:"Chuva e trovoadas","6n":"Chuva e trovoadas",7:"Geada","7n":"Geada",8:"Neve",9:"Nevoeiro"},STATIC_PRIVATE.setCookie=function(name,value,days){var expires="",date;days&&((date=new Date).setTime(date.getTime()+24*days*60*60*1e3),expires="; expires="+date.toUTCString()),document.cookie=name+"="+(value||"")+expires+";domain=.terra.com.br;path=/"},STATIC_PRIVATE.validateGeolocPermission=function(){return new Promise(function(resolve,reject){navigator&&navigator.permissions?navigator.permissions.query({name:"geolocation"}).then(function(result){return resolve("granted"===result.state)}).catch(function(e){resolve(!1)}):resolve(!1)})},STATIC_PRIVATE.getUserCoords=function(){return new Promise(function(resolve,reject){var onSuccess,options;navigator.geolocation.getCurrentPosition(function(position){if(!position||!position.coords)throw new Error("Navigator could not retrieve coordinates");resolve({latitude:position.coords.latitude,longitude:position.coords.longitude})},reject,{enableHighAccuracy:!0,timeout:1e4,maximumAge:0})})},STATIC_PRIVATE.getClimatempoAdvisorId=function(cause,useTrrGeo){return new Promise(function(resolve,reject){var initiator;(useTrrGeo?Promise.resolve(STATIC_PRIVATE.fallbackCoordinates):STATIC_PRIVATE.getUserCoords()).then(function(coords){if(null===coords)throw new Error("Coordinates could not be retrieved");fetch("https://p1-cloud.trrsf.com.br/api/weather-api/getlocal?climatempo=true&lat="+coords.latitude+"&lon="+coords.longitude,{signal:window.AbortSignal.timeout(1e4)}).then(function(response){if(response.ok)return response.json();var errorMessage="HTTP "+response.status,errorMessage=(errorMessage+="\nSource: "+response.url)+("\nCoordinates LAT"+coords.latitude+"/LONG"+coords.longitude+" could not return valid location.");throw window.tga.send("send","event","geoloc","error","getlocal"),errorMessage+="\nUsing fallback id: "+!!STATIC_PRIVATE.fallbackCityId,STATIC_PRIVATE.fallbackCityId&&(resolve(STATIC_PRIVATE.fallbackCityId),console.log(errorMessage)),new Error(errorMessage)}).then(function(data){var newTrrGeoValue;if(data&&data.climatempoAdvisorId)return data.climatempoAdvisorId!=STATIC_PRIVATE.fallbackCityId&&((newTrrGeoValue=[]).push(coords.latitude,coords.longitude,data.city,data.state,data.country,1,data.climatempoAdvisorId),newTrrGeoValue=(newTrrGeoValue=encodeURI(newTrrGeoValue.join("|").toUpperCase().replace(/\s+/g,"+"))).normalize("NFD").replace(/[\u0300-\u036f]/g,""),STATIC_PRIVATE.setCookie("trrgeo",newTrrGeoValue,30)),window.tga.send("send","event","geoloc",cause,data.city+" - "+data.state),resolve(data.climatempoAdvisorId);throw window.tga.send("send","event","geoloc","climatempo-error",data),new Error("Location data could not retrieve necessary information")}).catch(function(error){window.tga.send("send","event","geoloc","error",error.message),reject(error)})}).catch(reject)})},STATIC_PRIVATE.getWeatherData=function(climatempoAdvisorId){return new Promise(function(resolve,reject){fetch("https://p1-cloud.trrsf.com.br/api/weather-api/buscatempo?id_climatempo_advisor="+climatempoAdvisorId,{signal:window.AbortSignal.timeout(1e4)}).then(function(response){if(response.ok)return response.json();var errorMessage="HTTP "+response.status,errorMessage=(errorMessage+="\nSource: "+response.url)+("\nCould not fetch weather data for id "+climatempoAdvisorId);throw new Error(errorMessage)}).then(function(data){if(data&&data.climatempo)return resolve(Object.assign(data.climatempo[0],data.climatempo[1]));throw new Error("Weather data could not retrieve necessary information")}).catch(reject)})}},init:function(data,__shared){var PRIVATE={},PUBLIC=this;return PRIVATE.configs=data.data&&data.data.configs,PRIVATE.container=data.container,PRIVATE.enableUseMyLocationButton=!0,PRIVATE.myLocationId=PRIVATE.currentCityId=null,PRIVATE.configs&&"small"==PRIVATE.configs.size||(PRIVATE.configs={size:"default"}),PRIVATE.debounce=function(func,timeout){var timer;return void 0===timeout&&(timeout=300),function(){var args=arguments,context=this;clearTimeout(timer),timer=setTimeout(function(){func.apply(context,args)},timeout)}},PRIVATE.throttle=function(func,limit){var lastCall=0;return function(){var now=Date.now();limit<=now-lastCall&&(lastCall=now,func.apply(this,arguments))}},PRIVATE.storeElements=function(){PRIVATE.appScreenTemperature=PRIVATE.appHtml.querySelector(".t360-weather__main"),PRIVATE.appScreenSearch=PRIVATE.appHtml.querySelector(".t360-weather__search"),PRIVATE.appSearchLoader=PRIVATE.appScreenSearch.querySelector(".t360-weather__search__loader"),PRIVATE.appResultBox=PRIVATE.appScreenSearch.querySelector(".t360-weather__search__result-box"),PRIVATE.appResultBoxItems=PRIVATE.appResultBox.querySelectorAll(".t360-weather__search__result-box__item"),PRIVATE.appIconReturn=PRIVATE.appScreenSearch.querySelector(".icon-share"),PRIVATE.appIconEraseSearch=PRIVATE.appScreenSearch.querySelector(".icon-times"),PRIVATE.appInputSearch=PRIVATE.appScreenSearch.querySelector('[name="input-weather-city-name"]'),"default"===PRIVATE.configs.size&&(PRIVATE.appLinkToSearchScreen=PRIVATE.appScreenTemperature.querySelector(".icon-search"),PRIVATE.appUseMyLocationButton=PRIVATE.appScreenSearch.querySelector(".t360-weather__search__use-my-location")),"small"===PRIVATE.configs.size&&(PRIVATE.appLinkToSearchScreen=PRIVATE.appScreenTemperature.querySelector(".t360-weather__main__no-location__search-link"),PRIVATE.appUseMyLocationButton=PRIVATE.appScreenTemperature.querySelector(".t360-weather__main__no-location__use-my-location"))},PRIVATE.bindEvents=function(){PRIVATE.appInputSearch.addEventListener("keyup",PRIVATE.debounce(PRIVATE.inputSearchKeyupHandler,750)),PRIVATE.appIconEraseSearch.addEventListener("click",function(evt){PRIVATE.appInputSearch.value="",PRIVATE.appResultBox.classList.remove("active"),PRIVATE.appResultBox.innerHTML=""}),PRIVATE.appLinkToSearchScreen&&PRIVATE.appLinkToSearchScreen.addEventListener("click",function(evt){PRIVATE.appScreenSearch.classList.remove("hidden-screen"),PRIVATE.appScreenTemperature.classList.add("hidden-screen")}),PRIVATE.appUseMyLocationButton&&PRIVATE.appUseMyLocationButton.addEventListener("click",PRIVATE.useMyLocationClickHandler),PRIVATE.appIconReturn&&PRIVATE.appIconReturn.addEventListener("click",function(evt){PRIVATE.appScreenTemperature.classList.remove("hidden-screen"),PRIVATE.appScreenSearch.classList.add("hidden-screen")})},PRIVATE.changeInputSearch=function(action){PRIVATE.appInputSearch.placeholder="enable"===action?"Digite o nome da cidade":"Aguarde um instante...",PRIVATE.appInputSearch.disabled="disable"===action},PRIVATE.inputSearchKeyupHandler=function(evt){evt.target.value.length<4?PRIVATE.appResultBox.classList.remove("active"):(PRIVATE.appSearchLoader.classList.add("active"),PRIVATE.appResultBox.classList.remove("active"),fetch("https://p1-cloud.trrsf.com.br/api/weather-api/buscaidclimatempo?cidade="+evt.target.value,{signal:window.AbortSignal.timeout(1e4)}).then(function(response){return response.json()}).then(function(data){data.ids&&(data.ids,0!==data.ids.length)?(PRIVATE.appResultBox.innerHTML="",data.ids.forEach(function(city){var li;city.score<.75||((li=document.createElement("li")).className="t360-weather__search__result-box__item",li.innerText=city.city+" / "+city.state,li.dataset.id=city.climatempoAdvisorId,li.addEventListener("click",PRIVATE.searchResultItemClickHandler),PRIVATE.appResultBox.insertAdjacentElement("beforeend",li))})):(PRIVATE.appSearchLoader.classList.remove("active"),PRIVATE.appResultBox.innerHTML="<strong>Nenhuma cidade encontrada</strong>"),PRIVATE.appResultBox.classList.add("active")}).catch(function(error){console.log("Something went wrong searching for cities",error)}).finally(function(){PRIVATE.appSearchLoader.classList.remove("active")}))},PRIVATE.useMyLocationClickHandler=function(evt){window.tga.send("send","event","geoloc","click","request"),PRIVATE.appSearchLoader.classList.add("active"),navigator&&navigator.permissions&&navigator.permissions.query({name:"geolocation"}).then(function(result){"denied"===result.state?(PRIVATE.enableUseMyLocationButton=!1,PRIVATE.appUseMyLocationButton.classList.add("warning-permission"),setTimeout(function(){PRIVATE.appUseMyLocationButton.remove()},6e3)):(PRIVATE.changeInputSearch("disable"),"prompt"===result.state&&PRIVATE.appUseMyLocationButton.classList.add("waiting-permission"),STATIC_PRIVATE.getClimatempoAdvisorId("click").then(function(climatempoAdvisorId){return PRIVATE.enableUseMyLocationButton=!1,PRIVATE.myLocationId=climatempoAdvisorId,STATIC_PRIVATE.getWeatherData(climatempoAdvisorId)}).then(function(weatherData){PRIVATE.renderTemplate(weatherData)}).catch(function(error){console.log('Feature "use my location" did not work properly',error),PRIVATE.appUseMyLocationButton.remove()}).finally(function(){PRIVATE.appUseMyLocationButton&&PRIVATE.appUseMyLocationButton.classList.remove("waiting-permission"),PRIVATE.appSearchLoader.classList.remove("active"),PRIVATE.changeInputSearch("enable")}))}).finally(function(){PRIVATE.appSearchLoader.classList.remove("active")})},PRIVATE.searchResultItemClickHandler=function(evt){PRIVATE.appInputSearch.value=evt.target.innerText,PRIVATE.appResultBox.classList.remove("active"),PRIVATE.appSearchLoader.classList.add("active"),PRIVATE.changeInputSearch("disable"),STATIC_PRIVATE.getWeatherData(evt.target.dataset.id).then(function(weatherData){PRIVATE.renderTemplate(weatherData)}).catch(function(error){console.log("Something went wrong retrieving weather data",error)}).finally(function(){PRIVATE.appSearchLoader.classList.remove("active"),PRIVATE.changeInputSearch("enable")})},PRIVATE.renderTemplate=function(appData){var templateData={device:pkg.context.platform.type,locationFound:!1,enableUseMyLocationButton:PRIVATE.enableUseMyLocationButton},todaysForecast,rightNowData,templateData,nextDaysData;appData&&(todaysForecast=appData.previsao[0].detalhe,rightNowData=appData.momento[0],templateData=Object.assign(templateData,{cityName:appData.cidade,uf:appData.estado,weatherIcon:{type:rightNowData.icone,label:STATIC_PRIVATE.iconLabelDict[rightNowData.icone]},currentTemp:rightNowData.temp,minTemp:todaysForecast.min,maxTemp:todaysForecast.max,brief:todaysForecast.frase,briefShort:rightNowData.condicao,rainProb:todaysForecast.prob,rainPrecip:todaysForecast.prec,morningIcon:{type:todaysForecast.icomanha,label:STATIC_PRIVATE.iconLabelDict[todaysForecast.icomanha]},afternoonIcon:{type:todaysForecast.icotarde,label:STATIC_PRIVATE.iconLabelDict[todaysForecast.icotarde]},eveningIcon:{type:todaysForecast.iconoite,label:STATIC_PRIVATE.iconLabelDict[todaysForecast.iconoite]}}),nextDaysData=[],appData.previsao.slice(1).forEach(function(nextDay){nextDaysData.push({weatherIcon:nextDay.detalhe.icone,dayShort:nextDay.nomedia.slice(0,3),minTemp:nextDay.detalhe.min.slice(0,-1),maxTemp:nextDay.detalhe.max.slice(0,-1)})}),templateData.nextDaysData=nextDaysData,templateData.locationFound=!0,null!==PRIVATE.currentCityId&&PRIVATE.currentCityId!==appData.id&&(templateData.enableUseMyLocationButton=!0),null!==PRIVATE.myLocationId&&PRIVATE.myLocationId===appData.id&&(templateData.enableUseMyLocationButton=!1),PRIVATE.currentCityId=appData.id),PRIVATE.appHtml=PUBLIC.templates.render("weather-"+PRIVATE.configs.size,templateData),PRIVATE.storeElements(),PRIVATE.bindEvents(),PRIVATE.container.replaceChildren(PRIVATE.appHtml)},STATIC_PRIVATE.validateGeolocPermission().then(function(hasGeolocPermission){return PRIVATE.enableUseMyLocationButton=hasGeolocPermission,STATIC_PRIVATE.getClimatempoAdvisorId("automatic",!hasGeolocPermission)}).then(function(climatempoAdvisorId){return PRIVATE.enableUseMyLocationButton=!1,STATIC_PRIVATE.getWeatherData(climatempoAdvisorId)}).then(function(weatherData){PRIVATE.renderTemplate(weatherData)}).catch(function(error){console.log(error),PRIVATE.renderTemplate()}).finally(function(){PRIVATE.container.classList.remove("loading-app")}),PUBLIC},teardown:function(why,__static,__proto,__shared){}})});