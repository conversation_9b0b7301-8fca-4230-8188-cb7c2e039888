apiVersion: apps/v1
kind: Deployment
metadata:
  name: webscraper
  namespace: webscraper
  labels:
    app.kubernetes.io/name: webscraper
    app.kubernetes.io/component: application
    app.kubernetes.io/version: "3.0.0"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: webscraper
  template:
    metadata:
      labels:
        app.kubernetes.io/name: webscraper
        app.kubernetes.io/component: application
        app.kubernetes.io/version: "3.0.0"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: webscraper
        image: webscraper:3.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8000
          name: metrics
          protocol: TCP
        - containerPort: 8080
          name: api
          protocol: TCP
        env:
        # Configuration from ConfigMap
        - name: WEBSCRAPER_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: webscraper-config
              key: WEBSCRAPER_ENVIRONMENT
        - name: WEBSCRAPER_LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: webscraper-config
              key: WEBSCRAPER_LOG_LEVEL
        - name: WEBSCRAPER_DEBUG
          valueFrom:
            configMapKeyRef:
              name: webscraper-config
              key: WEBSCRAPER_DEBUG
        - name: WEBSCRAPER_REDIS_URL
          valueFrom:
            configMapKeyRef:
              name: webscraper-config
              key: WEBSCRAPER_REDIS_URL
        - name: WEBSCRAPER_ELASTICSEARCH_URL
          valueFrom:
            configMapKeyRef:
              name: webscraper-config
              key: WEBSCRAPER_ELASTICSEARCH_URL
        - name: WEBSCRAPER_METRICS_ENABLED
          valueFrom:
            configMapKeyRef:
              name: webscraper-config
              key: WEBSCRAPER_METRICS_ENABLED
        - name: WEBSCRAPER_ALERTS_ENABLED
          valueFrom:
            configMapKeyRef:
              name: webscraper-config
              key: WEBSCRAPER_ALERTS_ENABLED
        
        # Secrets
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: webscraper-secrets
              key: POSTGRES_PASSWORD
        - name: WEBSCRAPER_S3_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: webscraper-secrets
              key: WEBSCRAPER_S3_ACCESS_KEY
        - name: WEBSCRAPER_S3_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: webscraper-secrets
              key: WEBSCRAPER_S3_SECRET_KEY
        - name: WEBSCRAPER_WEBHOOK_URL
          valueFrom:
            secretKeyRef:
              name: webscraper-secrets
              key: WEBSCRAPER_WEBHOOK_URL
        
        # Computed environment variables
        - name: WEBSCRAPER_DATABASE_URL
          value: "postgresql+asyncpg://webscraper:$(POSTGRES_PASSWORD)@postgres:5432/webscraper"
        
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        - name: cache-volume
          mountPath: /app/cache
        - name: logs-volume
          mountPath: /app/logs
      
      volumes:
      - name: data-volume
        emptyDir: {}
      - name: cache-volume
        emptyDir: {}
      - name: logs-volume
        emptyDir: {}
      
      # Security context
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      
      # Restart policy
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: webscraper
  namespace: webscraper
  labels:
    app.kubernetes.io/name: webscraper
    app.kubernetes.io/component: application
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: metrics
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: api
  selector:
    app.kubernetes.io/name: webscraper

---
apiVersion: v1
kind: Service
metadata:
  name: webscraper-external
  namespace: webscraper
  labels:
    app.kubernetes.io/name: webscraper
    app.kubernetes.io/component: application
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: api
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: metrics
  selector:
    app.kubernetes.io/name: webscraper

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: webscraper-hpa
  namespace: webscraper
  labels:
    app.kubernetes.io/name: webscraper
    app.kubernetes.io/component: autoscaler
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: webscraper
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
