<!DOCTYPE html>

<html data-theme="light" lang="pt-BR">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width,initial-scale=1" name="viewport"/>
<title>PVP Projects - Engenharia de Qualidade</title>
<meta content="Especialistas em projetos de engenharia elétrica e hidrossanitária com atendimento nacional. Base em Porto Alegre/RS. Soluções precisas e inovadoras para todos os tipos de empreendimentos." name="description"/>
<meta content="engenharia elétrica, projetos hidrossanitários, Porto Alegre, engenheiro eletricista, Revit, AutoCAD" name="keywords"/>
<meta content="Eng. Pedro Vitor <PERSON>" name="author"/>
<meta content="index, follow" name="robots"/>
<meta content="#1e40af" name="theme-color"/>
<meta content="#1e40af" name="msapplication-TileColor"/>
<meta content="yes" name="apple-mobile-web-app-capable"/>
<meta content="default" name="apple-mobile-web-app-status-bar-style"/>
<meta content="PVP Projects - Engenharia de Qualidade" name="apple-mobile-web-app-title"/>
<meta content="website" property="og:type"/>
<meta content="PVP Projects - Engenharia de Qualidade" property="og:title"/>
<meta content="Especialistas em projetos de engenharia elétrica e hidrossanitária com atendimento nacional. Base em Porto Alegre/RS. Soluções precisas e inovadoras para todos os tipos de empreendimentos." property="og:description"/>
<meta content="https://pvp-projects.com/projetos/loja-avenida/" property="og:url"/>
<meta content="https://pvp-projects.com/assets/images/og-default.jpg" property="og:image"/>
<meta content="PVP Projects - Engenharia de Qualidade" property="og:site_name"/>
<meta content="pt_BR" property="og:locale"/>
<meta content="summary_large_image" name="twitter:card"/>
<meta content="PVP Projects - Engenharia de Qualidade" name="twitter:title"/>
<meta content="Especialistas em projetos de engenharia elétrica e hidrossanitária com atendimento nacional. Base em Porto Alegre/RS. Soluções precisas e inovadoras para todos os tipos de empreendimentos." name="twitter:description"/>
<meta content="https://pvp-projects.com/assets/images/og-default.jpg" name="twitter:image"/>
<link href="/assets/imagens/favicon-new.png" rel="icon" type="image/png"/>
<link href="/assets/imagens/favicon-new.png" rel="apple-touch-icon"/>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;family=Poppins:wght@400;500;600;700;800&amp;family=JetBrains+Mono:wght@400;500&amp;display=swap" rel="stylesheet"/>
<link as="style" href="/styles/style.css" rel="preload"/>
<link as="script" href="/scripts/main.js" rel="preload"/>
<link href="https://fonts.googleapis.com" rel="dns-prefetch"/>
<link href="https://cdnjs.cloudflare.com" rel="dns-prefetch"/>
<link href="/styles/style.css" rel="stylesheet"/>
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet"/>

</head>
<body class="antialiased bg-white text-gray-900 transition-colors duration-200" data-theme="light">
<a class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-lg z-50" href="#main-content">
Pular para o conteúdo principal
</a>
<div class="loader fixed inset-0 bg-white z-50 flex items-center justify-center transition-opacity duration-300">
<div class="animate-spin rounded-full h-12 w-12 border-4 border-primary-600 border-t-transparent"></div>
</div>
<header class="sticky top-0 z-40 bg-white/80 backdrop-blur-md border-b border-gray-200 transition-all duration-200">
<nav class="container mx-auto px-4 sm:px-6 lg:px-8">
<div class="flex items-center justify-between h-16">
<div class="flex-shrink-0">
<a class="flex items-center space-x-4" href="/">
<img alt="PVP Projects Logo" class="w-20 h-20 object-contain rounded-full" src="/assets/imagens/favicon-new.png"/>
<span class="font-bold text-xl text-gray-900">PVP Projects</span>
</a>
</div>
<div class="hidden md:block">
<div class="flex items-center space-x-8">
<div class="relative group">
<a class="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium" href="/">
Home
</a>
</div>
<div class="relative group">
<a class="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium" href="/sobre">
Sobre
</a>
</div>
<div class="relative group">
<a class="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium" href="/servicos">
Serviços
</a>
<div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
<div class="py-2">
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/servicos/eletricos">
<div class="font-medium">Projetos Elétricos</div>
<div class="text-sm text-gray-500">Instalações de baixa tensão</div>
</a>
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/servicos/hidrossanitarios">
<div class="font-medium">Projetos Hidrossanitários</div>
<div class="text-sm text-gray-500">Redes de água e esgoto</div>
</a>
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/servicos/comunicacao">
<div class="font-medium">Projetos de Comunicação</div>
<div class="text-sm text-gray-500">Infraestrutura de dados</div>
</a>
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/servicos/consultoria-bim">
<div class="font-medium">Consultoria BIM</div>
<div class="text-sm text-gray-500">Modelagem 3D em Revit</div>
</a>
</div>
</div>
</div>
<div class="relative group">
<a class="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium" href="/projetos-dedicada">
Projetos
</a>
<div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
<div class="py-2">
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/projetos-dedicada">
<div class="font-medium">Todos os Projetos</div>
<div class="text-sm text-gray-500">Portfólio completo</div>
</a>
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/projetos/categoria/predial">
<div class="font-medium">Projetos Prediais</div>
<div class="text-sm text-gray-500">Edifícios residenciais e comerciais</div>
</a>
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/projetos/categoria/residencial">
<div class="font-medium">Projetos Residenciais</div>
<div class="text-sm text-gray-500">Casas e apartamentos</div>
</a>
<a class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary-600 transition-colors duration-200" href="/projetos/categoria/comercial">
<div class="font-medium">Projetos Comerciais</div>
<div class="text-sm text-gray-500">Lojas e estabelecimentos</div>
</a>
</div>
</div>
</div>
<div class="relative group">
<a class="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium" href="/blog">
Blog
</a>
</div>
<div class="relative group">
<a class="text-gray-700 hover:text-primary-600 transition-colors duration-200 font-medium" href="/contato">
Contato
</a>
</div>
</div>
</div>
<div class="flex items-center space-x-4">
<button aria-label="Alternar tema" class="p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200" id="theme-toggle">
<svg class="w-5 h-5" fill="none" stroke="currentColor" viewbox="0 0 24 24">
<path d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</button>
<button aria-label="Buscar" class="p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200" id="search-toggle">
<svg class="w-5 h-5" fill="none" stroke="currentColor" viewbox="0 0 24 24">
<path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</button>
<button aria-label="Abrir menu" class="md:hidden p-2 text-gray-500 hover:text-gray-700 transition-colors duration-200" id="mobile-menu-toggle">
<svg class="w-6 h-6" fill="none" stroke="currentColor" viewbox="0 0 24 24">
<path d="M4 6h16M4 12h16M4 18h16" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</button>
<a class="hidden md:inline-flex bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200 font-medium" href="/contato">
Solicitar Orçamento
</a>
</div>
</div>
<div class="md:hidden hidden" id="mobile-menu">
<div class="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
<a class="block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/">
Home
</a>
<a class="block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/sobre">
Sobre
</a>
<a class="block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/servicos">
Serviços
</a>
<div class="pl-4 space-y-1">
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/servicos/eletricos">
Projetos Elétricos
</a>
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/servicos/hidrossanitarios">
Projetos Hidrossanitários
</a>
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/servicos/comunicacao">
Projetos de Comunicação
</a>
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/servicos/consultoria-bim">
Consultoria BIM
</a>
</div>
<a class="block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/projetos-dedicada">
Projetos
</a>
<div class="pl-4 space-y-1">
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/projetos-dedicada">
Todos os Projetos
</a>
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/projetos/categoria/predial">
Projetos Prediais
</a>
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/projetos/categoria/residencial">
Projetos Residenciais
</a>
<a class="block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/projetos/categoria/comercial">
Projetos Comerciais
</a>
</div>
<a class="block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/blog">
Blog
</a>
<a class="block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors duration-200" href="/contato">
Contato
</a>
</div>
</div>
</nav>
</header>
<div class="fixed inset-0 z-50 hidden" id="search-modal">
<div class="absolute inset-0 bg-black/50"></div>
<div class="absolute inset-0 flex items-center justify-center p-4">
<div class="bg-white rounded-lg shadow-xl w-full max-w-2xl">
<div class="p-6">
<div class="flex items-center justify-between mb-4">
<h3 class="text-lg font-semibold text-gray-900">Buscar</h3>
<button class="text-gray-400 hover:text-gray-600 transition-colors duration-200" id="search-close">
<svg class="w-6 h-6" fill="none" stroke="currentColor" viewbox="0 0 24 24">
<path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</button>
</div>
<input class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" id="search-input" placeholder="Digite para buscar projetos..." type="text"/>
<div class="mt-4 max-h-96 overflow-y-auto hidden" id="search-results"></div>
</div>
</div>
</div>
</div>
<main id="main-content">
<section class="relative py-12 sm:py-16 lg:py-20 overflow-hidden min-h-[60vh] sm:min-h-[70vh] lg:min-h-[80vh]">
<div class="absolute inset-0 bg-cover bg-center bg-no-repeat" style="background-image:url('/assets/imagens/render- av f.png');background-size:cover">
<div class="absolute inset-0 bg-black bg-opacity-50"></div>
</div>
<div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
<div class="text-center" data-aos="fade-up">
<h1 class="text-4xl lg:text-5xl font-bold text-white mb-6">
Loja AF
</h1>
<p class="text-xl text-white max-w-3xl mx-auto mb-8">
Projeto Elétrico Comercial
</p>
<p class="text-lg text-white max-w-4xl mx-auto mb-8">
Projeto elétrico para loja comercial
</p>
<div class="flex flex-wrap justify-center gap-2 mb-8">
<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white bg-opacity-20 text-white border border-white border-opacity-30">
Elétrico
</span>
<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white bg-opacity-20 text-white border border-white border-opacity-30">
Comercial
</span>
<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white bg-opacity-20 text-white border border-white border-opacity-30">
Predial
</span>
<span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white bg-opacity-20 text-white border border-white border-opacity-30">
Revit
</span>
</div>
<nav aria-label="Breadcrumb" class="flex justify-center mb-8">
<ol class="flex items-center space-x-2 text-sm text-white">
<li><a class="hover:text-primary-300 transition-colors duration-200" href="/">Home</a></li>
<li class="flex items-center">
<svg class="w-4 h-4 mx-2" fill="currentColor" viewbox="0 0 20 20">
<path clip-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" fill-rule="evenodd"></path>
</svg>
<a class="hover:text-primary-300 transition-colors duration-200" href="/projetos-dedicada">Projetos</a>
</li>
<li class="flex items-center">
<svg class="w-4 h-4 mx-2" fill="currentColor" viewbox="0 0 20 20">
<path clip-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" fill-rule="evenodd"></path>
</svg>
<a class="hover:text-primary-300 transition-colors duration-200" href="/projetos/categoria/comercial">Comercial</a>
</li>
<li class="flex items-center">
<svg class="w-4 h-4 mx-2" fill="currentColor" viewbox="0 0 20 20">
<path clip-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" fill-rule="evenodd"></path>
</svg>
<span class="text-primary-300 font-medium">Loja AF</span>
</li>
</ol>
</nav>
</div>
</div>
</section>
<section class="py-16 bg-white">
<div class="container mx-auto px-4 sm:px-6 lg:px-8">
<div class="max-w-6xl mx-auto">
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
<div class="lg:col-span-2">
<div class="bg-gray-50 rounded-lg p-6 mb-8">
<h2 class="text-2xl font-bold text-gray-900 mb-6">Informações do Projeto</h2>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
<div>
<h3 class="font-semibold text-gray-900 mb-2">Cliente</h3>
<p class="text-gray-700">Construtora</p>
</div>
<div>
<h3 class="font-semibold text-gray-900 mb-2">Localização</h3>
<p class="text-gray-700">Porto Alegre</p>
</div>
<div>
<h3 class="font-semibold text-gray-900 mb-2">Ano</h3>
<p class="text-gray-700">2023</p>
</div>
<div>
<h3 class="font-semibold text-gray-900 mb-2">Área</h3>
<p class="text-gray-700">200 m²</p>
</div>
<div>
<h3 class="font-semibold text-gray-900 mb-2">Pavimentos</h3>
<p class="text-gray-700">1</p>
</div>
<div>
<h3 class="font-semibold text-gray-900 mb-2">Ferramentas</h3>
<p class="text-gray-700">Revit</p>
</div>
</div>
</div>
<div class="bg-white rounded-lg shadow-lg p-6 mb-8">
<h2 class="text-2xl font-bold text-gray-900 mb-6">Desafios do Projeto</h2>
<div class="space-y-4">
<div class="flex items-start">
<div class="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mr-3 mt-1">
<svg class="w-4 h-4 text-primary-600" fill="currentColor" viewbox="0 0 20 20">
<path clip-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" fill-rule="evenodd"></path>
</svg>
</div>
<p class="text-gray-700">Layout comercial complexo</p>
</div>
<div class="flex items-start">
<div class="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mr-3 mt-1">
<svg class="w-4 h-4 text-primary-600" fill="currentColor" viewbox="0 0 20 20">
<path clip-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" fill-rule="evenodd"></path>
</svg>
</div>
<p class="text-gray-700">Sistema de climatização</p>
</div>
<div class="flex items-start">
<div class="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mr-3 mt-1">
<svg class="w-4 h-4 text-primary-600" fill="currentColor" viewbox="0 0 20 20">
<path clip-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" fill-rule="evenodd"></path>
</svg>
</div>
<p class="text-gray-700">Iluminação de destaque</p>
</div>
</div>
</div>
<div class="prose prose-lg max-w-none mb-8">
<h1>Projeto Elétrico Comercial 'AF'– Loja Modular em Revit</h1>
<p><strong>Loja comercial com quatro unidades independentes | Projeto modular com quatro medidores | Modelado em Revit 3D</strong></p>
<p>Projeto elétrico completo desenvolvido para empreendimento comercial de <strong>dois pavimentos</strong>, localizado em <strong>Porto Alegre – RS</strong>, com potencial de locação para até <strong>quatro lojas independentes</strong>. O projeto foi integralmente modelado em <strong>Revit</strong>, com estruturação elétrica modular, pronta para reformas e ampliações futuras, incluindo entrada de energia separada, quadros de cargas, espera para climatização e caixas de inspeção técnicas.</p>
<ul>
<li><strong>Local:</strong> Porto Alegre – RS</li>
<li><strong>Ano:</strong> 2024</li>
<li><strong>Tipo:</strong> Comercial – Loja modular</li>
<li><strong>Ferramenta:</strong> Revit</li>
</ul>
<h2>Descrição do Projeto</h2>
<p>Este projeto visou a implementação de uma infraestrutura elétrica robusta e escalável para <strong>um edifício comercial com quatro lojas independentes</strong>. A modelagem foi executada em <strong>BIM/Revit</strong>, incluindo todos os pavimentos e sistemas elétricos necessários para garantir a autonomia de cada unidade.</p>
<p>A <strong>modularidade do projeto</strong> é o grande diferencial: cada loja conta com <strong>medidor próprio, quadro individualizado</strong>, e infraestrutura preparada para climatização, comunicação e expansão. Também foram previstas <strong>caixas de inspeção técnicas</strong> em pontos estratégicos para facilitar reformas ou futuras readequações do espaço.</p>
<h3>Escopo Técnico</h3>
<h4>⚡ Elétrica</h4>
<ul>
<li><strong>Planta de Situação e Terreno</strong></li>
<li><strong>Dois Pavimentos (Térreo e 2º andar)</strong></li>
<li><strong>Projeto completo de iluminação, tomadas e esperas</strong></li>
<li><strong>4 Medidores e 4 Quadros Independentes</strong> – um para cada loja</li>
<li><strong>Entrada de Energia:</strong> dimensionada para atender todas as lojas com segurança</li>
<li><strong>Diagrama Unifilar e Quadro de Cargas</strong></li>
<li><strong>Infraestrutura para Ar-condicionado e Aquecedores</strong></li>
<li><strong>Caixas de Inspeção Técnica para manutenção e expansão</strong></li>
<li><strong>Modelagem 3D no Revit com visibilidade executiva</strong></li>
</ul>
<h2>Tecnologias e Normas</h2>
<ul>
<li><strong>Software:</strong> Autodesk Revit 2024</li>
<li><strong>Normas Técnicas:</strong>
<ul>
<li>NBR 5410 (instalações elétricas)</li>
<li>NBR 13570 (entrada de energia)</li>
<li>Normas da concessionária CEEE/Equatorial</li>
</ul>
</li>
<li><strong>Infraestrutura para futuras automações ou controle de consumo energético individualizado</strong></li>
</ul>
<h2>Documentação</h2>
<h3>🖼️ Painel de Medição</h3>
<div class="pdf-container reveal">
<object data="/assets/imagens/painel av f.png#toolbar=0" height="500" type="image/png" width="100%">
<p>Imagem indisponível — <a href="/assets/imagens/painel av f.png" target="_blank">baixar</a>.</p>
</object>
</div>
<h3>🖼️ Térreo</h3>
<div class="pdf-container reveal">
<object data="/assets/imagens/terreo av f.png#toolbar=0" height="500" type="image/png" width="100%">
<p>Imagem indisponível — <a href="/assets/imagens/terreo av f.png" target="_blank">baixar</a>.</p>
</object>
</div>
<h3>🖼️ Render</h3>
<div class="pdf-container reveal">
<object data="/assets/imagens/render- av f.png#toolbar=0" height="500" type="image/png" width="100%">
<p>Imagem indisponível — <a href="/assets/imagens/render- av f.png" target="_blank">baixar</a>.</p>
</object>
</div>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold text-gray-900 mb-6">Desafios</h2>
<ul class="space-y-3">
<li class="flex items-start">
<span class="text-primary-600 mr-3 mt-1">•</span>
<span class="text-gray-700">Layout comercial complexo</span>
</li>
<li class="flex items-start">
<span class="text-primary-600 mr-3 mt-1">•</span>
<span class="text-gray-700">Sistema de climatização</span>
</li>
<li class="flex items-start">
<span class="text-primary-600 mr-3 mt-1">•</span>
<span class="text-gray-700">Iluminação de destaque</span>
</li>
</ul>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold text-gray-900 mb-6">Soluções</h2>
<ul class="space-y-3">
<li class="flex items-start">
<span class="text-primary-600 mr-3 mt-1">•</span>
<span class="text-gray-700">Projeto elétrico detalhado</span>
</li>
<li class="flex items-start">
<span class="text-primary-600 mr-3 mt-1">•</span>
<span class="text-gray-700">Infraestrutura para ar condicionado</span>
</li>
<li class="flex items-start">
<span class="text-primary-600 mr-3 mt-1">•</span>
<span class="text-gray-700">Iluminação comercial eficiente</span>
</li>
</ul>
</div>
<div class="mb-8">
<h2 class="text-2xl font-bold text-gray-900 mb-6">Resultados</h2>
<ul class="space-y-3">
<li class="flex items-start">
<span class="text-primary-600 mr-3 mt-1">•</span>
<span class="text-gray-700">Ambiente comercial funcional</span>
</li>
<li class="flex items-start">
<span class="text-primary-600 mr-3 mt-1">•</span>
<span class="text-gray-700">Conforto térmico</span>
</li>
<li class="flex items-start">
<span class="text-primary-600 mr-3 mt-1">•</span>
<span class="text-gray-700">Eficiência energética</span>
</li>
</ul>
</div>
</div>
<div class="space-y-6">
<div class="bg-gray-50 rounded-lg p-6">
<h3 class="text-xl font-bold text-gray-900 mb-4">Documentos</h3>
<div class="space-y-6">
</div>
</div>
</div>
</div>
</div>
</div>
</section>
<section class="py-16 bg-primary-600">
<div class="container mx-auto px-4 sm:px-6 lg:px-8">
<div class="max-w-4xl mx-auto text-center">
<h2 class="text-3xl font-bold text-white mb-6">Interessado em um projeto similar?</h2>
<p class="text-xl text-primary-100 mb-8">Entre em contato conosco para discutir suas necessidades</p>
<div class="flex flex-col sm:flex-row gap-4 justify-center">
<a class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200" href="/contato">
Solicitar Orçamento
</a>
<a class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors duration-200" href="/projetos-dedicada">
Ver Mais Projetos
</a>
</div>
</div>
</div>
</section>
</main>
<footer class="bg-gray-900 text-white">
<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
<div class="grid grid-cols-1 md:grid-cols-4 gap-8">
<div class="md:col-span-2">
<div class="flex items-center space-x-2 mb-4">
<img alt="PVP Projects Logo" class="w-20 h-20 object-contain rounded-lg" src="/assets/imagens/favicon-new.png"/>
<span class="font-bold text-xl">PVP Projects</span>
</div>
<p class="text-gray-300 mb-4 max-w-md">Soluções técnicas lideradas por um Engenheiro Eletricista com experiência em projetos de alta qualidade – unindo segurança e design para encantar arquitetos e clientes.</p>
<div class="flex space-x-4">
<a aria-label="WhatsApp" class="text-gray-400 hover:text-white transition-colors duration-200" href="https://wa.me/5554991590379" target="_blank">
<svg class="w-5 h-5" fill="currentColor" viewbox="0 0 24 24">
<path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"></path>
</svg>
</a>
<a aria-label="Email" class="text-gray-400 hover:text-white transition-colors duration-200" href="mailto:<EMAIL>" target="_blank">
<svg class="w-5 h-5" fill="none" stroke="currentColor" viewbox="0 0 24 24">
<path d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</a>
<a aria-label="LinkedIn" class="text-gray-400 hover:text-white transition-colors duration-200" href="https://linkedin.com/in/pedro-vitor-pagliarin" target="_blank">
<svg class="w-5 h-5" fill="currentColor" viewbox="0 0 24 24">
<path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"></path>
</svg>
</a>
</div>
</div>
<div>
<h3 class="font-semibold text-white mb-4">Empresa</h3>
<ul class="space-y-2">
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/sobre">
Sobre Nós
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/servicos">
Serviços
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/projetos-dedicada">
Projetos
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/blog">
Blog
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/contato">
Contato
</a>
</li>
</ul>
</div>
<div>
<h3 class="font-semibold text-white mb-4">Serviços</h3>
<ul class="space-y-2">
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/servicos/eletricos">
Projetos Elétricos
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/servicos/hidrossanitarios">
Projetos Hidrossanitários
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/servicos/comunicacao">
Projetos de Comunicação
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/servicos/consultoria-bim">
Consultoria BIM
</a>
</li>
</ul>
</div>
<div>
<h3 class="font-semibold text-white mb-4">Legal</h3>
<ul class="space-y-2">
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/legal/privacidade">
Política de Privacidade
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/legal/termos">
Termos de Uso
</a>
</li>
<li>
<a class="text-gray-300 hover:text-white transition-colors duration-200" href="/legal/cookies">
Cookies
</a>
</li>
</ul>
</div>
</div>
<div class="border-t border-gray-800 mt-8 pt-8">
<div class="flex flex-col md:flex-row justify-between items-center">
<p class="text-gray-400 text-sm">
© 2024 PVP Projects. Todos os direitos reservados.
</p>
<div class="flex space-x-6 mt-4 md:mt-0">
<a class="text-gray-400 hover:text-white text-sm transition-colors duration-200" href="/legal/privacidade">
Política de Privacidade
</a>
<a class="text-gray-400 hover:text-white text-sm transition-colors duration-200" href="/legal/termos">
Termos de Uso
</a>
</div>
</div>
</div>
</div>
</footer>
<button aria-label="Voltar ao topo" class="scroll-to-top fixed bottom-8 right-8 bg-primary-600 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 transition-all duration-200 opacity-0 pointer-events-none z-50">
<svg class="w-6 h-6" fill="none" stroke="currentColor" viewbox="0 0 24 24">
<path d="M5 10l7-7m0 0l7 7m-7-7v18" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</button>
<div class="fixed top-4 right-4 z-50" id="notification-container"></div>
<a aria-label="Contatar via WhatsApp" class="fixed bottom-20 right-8 bg-green-500 text-white p-4 rounded-full shadow-lg hover:bg-green-600 transition-all duration-200 z-50 group" href="https://wa.me/5554991590379" target="_blank">
<svg class="w-6 h-6" fill="currentColor" viewbox="0 0 24 24">
<path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"></path>
</svg>
<span class="absolute left-full ml-3 bg-white text-gray-800 px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap text-sm font-medium">
Fale conosco!
</span>
</a>






</body>
</html> 