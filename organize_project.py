#!/usr/bin/env python3
"""
Organizador do projeto WebScraper.
"""

import os
import shutil
from pathlib import Path

def organize_project():
    """Organizar arquivos do projeto."""
    
    print("🗂️ ORGANIZANDO PROJETO WEBSCRAPER")
    print("="*50)
    
    # Criar estrutura de diretórios
    directories = {
        'tools': 'Ferramentas de scraping e análise',
        'examples': 'Exemplos e testes',
        'results': 'Resultados de scraping',
        'docs': 'Documentação',
        'scripts': 'Scripts utilitários'
    }
    
    for dir_name, description in directories.items():
        dir_path = Path(dir_name)
        dir_path.mkdir(exist_ok=True)
        print(f"📁 Criado: {dir_name}/ - {description}")
    
    # Mover arquivos para organização
    moves = {
        # Ferramentas principais
        'frontend_cloner.py': 'tools/',
        'spider_completo.py': 'tools/',
        'site_mapper_completo.py': 'tools/',
        'visualizar_clonagem.py': 'tools/',
        'visualizar_site_completo.py': 'tools/',
        'ver_dados.py': 'tools/',
        
        # Exemplos e testes
        'exemplo_funcionando.py': 'examples/',
        'exemplo_primeiro_teste.py': 'examples/',
        'exemplo_simples.py': 'examples/',
        'api_simples.py': 'examples/',
        'test_mvp.py': 'examples/',
        'testar_lerra.py': 'examples/',
        'testar_terra.py': 'examples/',
        
        # Resultados
        'cloned_35mm-one_vercel_app': 'results/',
        'cloned_pvpprojects_netlify_app': 'results/',
        'cloned_terra_com_br': 'results/',
        'pvp_projects_completo': 'results/',
        'pvp_spider_completo': 'results/',
        
        # Documentação
        'FASE2_COMPLETA.md': 'docs/',
        'FASE3_COMPLETA.md': 'docs/',
        'PLANO.MD': 'docs/',
        'relatorio_35mm.md': 'docs/',
        'relatorio_pvp_projects.md': 'docs/',
    }
    
    print(f"\n📦 MOVENDO ARQUIVOS:")
    for source, dest_dir in moves.items():
        source_path = Path(source)
        if source_path.exists():
            dest_path = Path(dest_dir) / source_path.name
            
            try:
                if source_path.is_dir():
                    if dest_path.exists():
                        shutil.rmtree(dest_path)
                    shutil.move(str(source_path), str(dest_path))
                else:
                    shutil.move(str(source_path), str(dest_path))
                print(f"   ✅ {source} → {dest_dir}")
            except Exception as e:
                print(f"   ❌ Erro movendo {source}: {e}")
    
    # Criar arquivo de estrutura
    create_structure_file()
    
    print(f"\n✅ PROJETO ORGANIZADO!")
    print(f"📁 Estrutura criada com sucesso")


def create_structure_file():
    """Criar arquivo com estrutura do projeto."""
    
    structure = """# 📁 ESTRUTURA DO PROJETO WEBSCRAPER

```
webscraper/
├── 📚 src/                     # Código fonte principal
│   ├── api/                    # API FastAPI
│   ├── core/                   # Funcionalidades core
│   ├── crawl/                  # Engines de crawling
│   ├── domains/                # Gerenciamento de domínios
│   ├── flows/                  # Fluxos Prefect
│   └── web/                    # Interface web
│
├── 🛠️ tools/                   # Ferramentas de análise
│   ├── frontend_cloner.py      # Clonador completo de frontend
│   ├── spider_completo.py      # Spider automático de descoberta
│   ├── site_mapper_completo.py # Mapeador profundo de sites
│   ├── visualizar_clonagem.py  # Visualizador de clonagens
│   ├── visualizar_site_completo.py # Análise completa de sites
│   └── ver_dados.py            # Visualizador de dados
│
├── 📝 examples/                # Exemplos e testes
│   ├── exemplo_funcionando.py  # Exemplo básico funcional
│   ├── exemplo_simples.py      # Exemplo simplificado
│   ├── api_simples.py          # API de exemplo
│   └── test_*.py               # Testes diversos
│
├── 📊 results/                 # Resultados de scraping
│   ├── cloned_terra_com_br/    # Terra.com.br clonado
│   ├── cloned_35mm-one_vercel_app/ # Site 35mm clonado
│   ├── cloned_pvpprojects_netlify_app/ # PVP Projects clonado
│   ├── pvp_projects_completo/  # Análise completa PVP
│   └── pvp_spider_completo/    # Spider completo PVP
│
├── 📖 docs/                    # Documentação
│   ├── FASE2_COMPLETA.md       # Documentação Fase 2
│   ├── FASE3_COMPLETA.md       # Documentação Fase 3
│   ├── PLANO.MD                # Plano do projeto
│   └── relatorio_*.md          # Relatórios de análise
│
├── 🗄️ data/                    # Dados persistentes
│   ├── webscraper.db           # Banco SQLite
│   ├── raw/                    # Dados brutos
│   ├── processed/              # Dados processados
│   └── screenshots/            # Screenshots de erro
│
├── 🐳 docker/                  # Configurações Docker
├── ☸️ k8s/                     # Configurações Kubernetes
├── ⚙️ configs/                 # Configurações
├── 🧪 tests/                   # Testes automatizados
│
├── 📋 README.md                # Este arquivo
├── 📦 pyproject.toml           # Configuração Python
├── 🐳 docker-compose.yml       # Orquestração Docker
└── 🔧 alembic.ini              # Migrações de banco
```

## 🎯 PRINCIPAIS FERRAMENTAS

### 🕷️ Spider Completo
- **Arquivo**: `tools/spider_completo.py`
- **Função**: Descoberta automática de TODOS os links de um site
- **Uso**: `python tools/spider_completo.py`

### 🎨 Frontend Cloner
- **Arquivo**: `tools/frontend_cloner.py`
- **Função**: Clonagem completa de frontend (HTML, CSS, JS, imagens)
- **Uso**: `python tools/frontend_cloner.py`

### 🗺️ Site Mapper
- **Arquivo**: `tools/site_mapper_completo.py`
- **Função**: Mapeamento profundo e análise de estrutura
- **Uso**: `python tools/site_mapper_completo.py`

### 📊 Visualizadores
- **Clonagem**: `tools/visualizar_clonagem.py`
- **Site Completo**: `tools/visualizar_site_completo.py`
- **Dados**: `tools/ver_dados.py`

## 🚀 EXEMPLOS DE USO

### Exemplo Básico
```bash
python examples/exemplo_funcionando.py
```

### API Simples
```bash
python examples/api_simples.py
```

### Análise Completa de Site
```bash
python tools/spider_completo.py
python tools/visualizar_site_completo.py
```
"""
    
    with open('ESTRUTURA.md', 'w', encoding='utf-8') as f:
        f.write(structure)
    
    print(f"   📄 Criado: ESTRUTURA.md")


if __name__ == "__main__":
    organize_project()
