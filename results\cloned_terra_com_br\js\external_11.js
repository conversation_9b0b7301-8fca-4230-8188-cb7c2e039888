/*! zaz-app-t360-championship-teams - v1.0.0 - 11/07/2025 -- 8:23pm */
zaz.use(function(pkg){var console=pkg.console,dictFactory;pkg.factoryManager.get("dict").create({name:"t360ChampionshipTeams",version:"1.0.0",state:"ok",extends:[],langs:{global:{termWithPlural:["plural","singular"]},pt:{term:"Termo em Português"},es:{term:"Termo in Español"},en:{term:"Term in English"},"es-AR":{term:"Termo en Argentina"}}})}),zaz.use(function appT360ChampionshipTeams(pkg){var console=pkg.console,appFactory=pkg.factoryManager.get("app"),STATIC_PUBLIC=null,STATIC_PRIVATE={};appFactory.create({name:"t360.championshipTeams",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/t360-championship-teams",source:"http://github.tpn.terra.com/Terra/t360-championship-teams",description:"Just another app",tests:"http://s1.trrsf.com/fe/t360-championship-teams/tests/index.htm?zaz[env]=tests",dependencies:[],dictionaries:[],templates:{},expects:{},setup:function(STATIC_PUBLIC,PUBLIC,__shared){},init:function(data,__shared){var PRIVATE={},PUBLIC=this;PRIVATE.carouselCommonWrapper=data.content.querySelector(".t360-carousel"),PRIVATE.teamsContainer=data.content.querySelector("#championship-teams-list"),PRIVATE.teams=PRIVATE.teamsContainer?PRIVATE.teamsContainer.children:void 0,PRIVATE.activeCarousel=null,PRIVATE.activateCarousel=function(){pkg.require("mod.t360.carouselRanges",function(T360CarouselRanges){PRIVATE.activeCarousel=new T360CarouselRanges({autoCountItems:!0,countItemsSelector:".t360-carousel-container",containerCarousel:".t360-carousel-container",itemsCarousel:".t360-carousel-item",dividerScrollCarousel:2,previousButtonCarousel:".t360-carousel-arrow-prev",nextButtonCarousel:".t360-carousel-arrow-next",automaticMarginResize:!0,minimumMarginInPixels:10,marginProperty:"marginRight"}),PRIVATE.activeCarousel.arrow(),window.addEventListener("resize",function(){PRIVATE.activeCarousel.updateStep()})},function(exception){console.error('Exception requiring "mod.t360.carouselRanges": ',exception)})};try{pkg.context.user.get("myTeam",function(userTeam){if(userTeam)for(var i=0;i<PRIVATE.teams.length;i++){var team=PRIVATE.teams[i];if(userTeam.musa_id===team.dataset.id){var clone=team.cloneNode(!0);team.remove(),PRIVATE.teamsContainer.insertAdjacentElement("afterbegin",clone);break}}"web"===pkg.context.platform.get("type")&&PRIVATE.activateCarousel()})}catch(err){console.error(err)}return window.addEventListener("resize",function(){PRIVATE.activeCarousel||PRIVATE.activateCarousel(),PRIVATE.activeCarousel.updateStep()}),STATIC_PRIVATE.totalInstances++,PRIVATE.id=STATIC_PRIVATE.totalInstances,this},teardown:function(why,__static,__proto,__shared){}})});