#!/usr/bin/env python3
"""
Teste da Fase 2 - Escala & Observabilidade.

Este script testa as funcionalidades empresariais implementadas na Fase 2:
- PostgreSQL/SQLite com SQLAlchemy
- Storage S3/MinIO
- Métricas Prometheus
- Sistema de alertas
- Coleta incremental com ETag
"""

import asyncio
import sys
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.alerts import alert_manager, AlertRule, AlertSeverity
from src.core.config import get_settings
from src.core.database import db_manager, create_tables
from src.core.incremental import perform_incremental_crawl
from src.core.logging import configure_logging, get_logger
from src.core.metrics import metrics_collector, get_metrics
from src.core.s3_storage import S3StorageBackend


async def test_database_integration():
    """Testar integração com banco de dados."""
    logger = get_logger(__name__)
    logger.info("Testing database integration")
    
    try:
        # Inicializar banco
        await db_manager.initialize()
        
        # Criar tabelas
        await create_tables()
        
        # Testar health check
        health = await db_manager.health_check()
        logger.info("Database health check", **health)
        
        # Obter estatísticas
        stats = await db_manager.get_stats()
        logger.info("Database stats", **stats)
        
        assert health["status"] == "healthy"
        assert "engine_info" in stats
        
        return True
        
    except Exception as e:
        logger.error("Database test failed", error=str(e), exc_info=True)
        return False


async def test_s3_storage():
    """Testar storage S3/MinIO."""
    logger = get_logger(__name__)
    logger.info("Testing S3 storage")
    
    try:
        # Configurar S3 backend (usando MinIO local se disponível)
        s3_backend = S3StorageBackend(
            endpoint_url="http://localhost:9000",  # MinIO local
            access_key="minioadmin",
            secret_key="minioadmin",
            bucket_name="webscraper-test",
        )
        
        # Tentar inicializar (pode falhar se MinIO não estiver rodando)
        try:
            await s3_backend.initialize()
            s3_available = True
        except Exception:
            logger.warning("S3/MinIO not available, skipping S3 tests")
            s3_available = False
        
        if s3_available:
            # Testar salvamento de conteúdo raw
            test_url = "https://example.com/test"
            test_content = "<html><body><h1>Test Content</h1></body></html>"
            test_metadata = {"test": "true", "timestamp": "2025-01-01"}
            
            raw_path = await s3_backend.save_raw_content(test_url, test_content, test_metadata)
            logger.info("Raw content saved", path=raw_path)
            
            # Verificar se existe
            exists = await s3_backend.exists(raw_path)
            assert exists, "Saved content should exist"
            
            # Obter estatísticas
            stats = await s3_backend.get_stats()
            logger.info("S3 storage stats", **stats)
            
            assert stats["total_objects"] >= 1
            
        return True
        
    except Exception as e:
        logger.error("S3 storage test failed", error=str(e), exc_info=True)
        return False


async def test_metrics_system():
    """Testar sistema de métricas Prometheus."""
    logger = get_logger(__name__)
    logger.info("Testing metrics system")
    
    try:
        # Registrar algumas métricas de teste
        metrics_collector.record_http_request(
            domain="example.com",
            status_code=200,
            method="GET",
            duration=1.5,
            response_size=1024,
            content_type="text/html"
        )
        
        metrics_collector.record_page_parsed(
            domain="example.com",
            status="success",
            parser_type="generic",
            duration=0.5,
            quality_score=85,
            quality_tier="good",
            word_count=500
        )
        
        metrics_collector.record_storage_operation(
            operation="save",
            backend="s3",
            status="success",
            duration=0.2
        )
        
        metrics_collector.record_content_version("example.com", "minor")
        metrics_collector.record_error("parser", "timeout", "example.com")
        
        # Definir métricas de gauge
        metrics_collector.set_active_sessions("example.com", 1)
        metrics_collector.set_queue_size("example.com", "pending", 100)
        metrics_collector.set_database_connections("active", 5)
        
        # Obter métricas no formato Prometheus
        metrics_output = get_metrics()
        
        logger.info("Metrics generated", lines=len(metrics_output.split('\n')))
        
        # Verificar se contém métricas esperadas
        assert "webscraper_http_requests_total" in metrics_output
        assert "webscraper_pages_parsed_total" in metrics_output
        assert "webscraper_storage_operations_total" in metrics_output
        assert "webscraper_content_versions_created_total" in metrics_output
        assert "webscraper_errors_total" in metrics_output
        
        return True
        
    except Exception as e:
        logger.error("Metrics test failed", error=str(e), exc_info=True)
        return False


async def test_alert_system():
    """Testar sistema de alertas."""
    logger = get_logger(__name__)
    logger.info("Testing alert system")
    
    try:
        # Adicionar regra de teste
        test_rule = AlertRule(
            name="test_high_error_rate",
            condition="error_rate > threshold",
            threshold=5.0,  # 5% (baixo para teste)
            severity=AlertSeverity.WARNING,
            component="test",
            description="Test alert rule for high error rate",
        )
        
        alert_manager.add_rule(test_rule)
        
        # Adicionar canal de notificação de teste
        alert_manager.add_notification_channel("webhook", {
            "url": "http://localhost:8080/test-webhook",
            "headers": {"Content-Type": "application/json"}
        })
        
        # Simular algumas métricas que podem disparar alertas
        for i in range(10):
            # Simular requisições com alguns erros
            status_code = 500 if i < 2 else 200  # 20% de erro
            metrics_collector.record_http_request(
                domain="test.com",
                status_code=status_code,
                method="GET",
                duration=1.0,
                response_size=1024
            )
        
        # Avaliar regras (normalmente seria feito em loop)
        new_alerts = await alert_manager.evaluate_all_rules()
        
        logger.info("Alert evaluation completed", new_alerts=len(new_alerts))
        
        # Obter alertas ativos
        active_alerts = await alert_manager.get_active_alerts()
        logger.info("Active alerts", count=len(active_alerts))
        
        return True
        
    except Exception as e:
        logger.error("Alert system test failed", error=str(e), exc_info=True)
        return False


async def test_incremental_collection():
    """Testar coleta incremental."""
    logger = get_logger(__name__)
    logger.info("Testing incremental collection")
    
    try:
        settings = get_settings()
        
        # Executar coleta incremental de teste
        stats = await perform_incremental_crawl(
            domain="httpbin.org",
            config=settings,
            max_urls=5  # Poucos URLs para teste
        )
        
        logger.info("Incremental crawl completed", **stats)
        
        # Verificar estatísticas
        assert "urls_processed" in stats
        assert "cache_stats" in stats
        assert "bandwidth_stats" in stats
        
        cache_stats = stats["cache_stats"]
        assert "hit_rate_percent" in cache_stats
        assert "total_requests" in cache_stats
        
        return True
        
    except Exception as e:
        logger.error("Incremental collection test failed", error=str(e), exc_info=True)
        return False


async def test_enterprise_flow():
    """Testar flow empresarial completo."""
    logger = get_logger(__name__)
    logger.info("Testing enterprise flow")
    
    try:
        from src.flows.enterprise_flow import enterprise_webscraper_flow
        
        # Executar flow empresarial
        result = await enterprise_webscraper_flow(
            domain="httpbin.org",
            max_pages=3,
            incremental=False,
            dry_run=True  # Modo de teste
        )
        
        logger.info("Enterprise flow completed", **result)
        
        # Verificar resultado
        assert "domain" in result
        assert "status" in result
        assert "system_info" in result
        
        system_info = result["system_info"]
        assert "database_initialized" in system_info
        assert system_info["database_initialized"] is True
        
        return True
        
    except Exception as e:
        logger.error("Enterprise flow test failed", error=str(e), exc_info=True)
        return False


async def main():
    """Função principal de teste da Fase 2."""
    print("🏢 Testando Fase 2 - Escala & Observabilidade...")
    
    # Configurar logging
    configure_logging(level="INFO", structured=True)
    
    tests = [
        ("Database Integration", test_database_integration),
        ("S3 Storage", test_s3_storage),
        ("Metrics System", test_metrics_system),
        ("Alert System", test_alert_system),
        ("Incremental Collection", test_incremental_collection),
        ("Enterprise Flow", test_enterprise_flow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Executando: {test_name}")
        try:
            success = await test_func()
            if success:
                print(f"✅ {test_name}: PASSOU")
                results.append(True)
            else:
                print(f"❌ {test_name}: FALHOU")
                results.append(False)
        except Exception as e:
            print(f"💥 {test_name}: ERRO - {e}")
            results.append(False)
    
    # Resumo final
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Resumo dos Testes da Fase 2:")
    print(f"✅ Passou: {passed}/{total}")
    print(f"❌ Falhou: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 Fase 2 implementada com sucesso!")
        return 0
    elif passed >= total * 0.8:  # 80% ou mais
        print("⚠️ Fase 2 parcialmente implementada. Alguns serviços externos podem não estar disponíveis.")
        return 0
    else:
        print("⚠️ Alguns testes falharam. Verifique os logs.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
