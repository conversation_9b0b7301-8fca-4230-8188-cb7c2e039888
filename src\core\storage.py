"""
Storage - Sistema de armazenamento abstrato.

Este módulo implementa uma abstração para diferentes tipos de storage,
incluindo filesystem local, S3/MinIO e banco de dados.
"""

import json
import os
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import structlog

from .validators import PageData

logger = structlog.get_logger(__name__)


class StorageBackend(ABC):
    """Interface abstrata para backends de storage."""
    
    @abstractmethod
    async def save_raw_content(self, url: str, content: str, metadata: Dict) -> str:
        """Salvar conteúdo bruto."""
        pass
    
    @abstractmethod
    async def save_processed_data(self, page_data: PageData) -> str:
        """Salvar dados processados."""
        pass
    
    @abstractmethod
    async def load_processed_data(self, url: str) -> Optional[PageData]:
        """Carregar dados processados."""
        pass
    
    @abstractmethod
    async def exists(self, url: str) -> bool:
        """Verificar se dados existem para uma URL."""
        pass
    
    @abstractmethod
    async def list_urls(self, domain: Optional[str] = None) -> List[str]:
        """Listar URLs armazenadas."""
        pass


class FilesystemStorage(StorageBackend):
    """Backend de storage usando filesystem local."""
    
    def __init__(self, base_path: str = "./data"):
        self.base_path = Path(base_path)
        self.raw_path = self.base_path / "raw"
        self.processed_path = self.base_path / "processed"
        
        # Criar diretórios se não existirem
        self.raw_path.mkdir(parents=True, exist_ok=True)
        self.processed_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(
            "Filesystem storage initialized",
            base_path=str(self.base_path),
            raw_path=str(self.raw_path),
            processed_path=str(self.processed_path),
        )
    
    def _get_file_path(self, url: str, data_type: str = "processed") -> Path:
        """Gerar caminho do arquivo baseado na URL."""
        from urllib.parse import urlparse
        import hashlib
        
        parsed = urlparse(url)
        domain = parsed.netloc
        
        # Usar hash da URL como nome do arquivo para evitar problemas com caracteres especiais
        url_hash = hashlib.sha256(url.encode('utf-8')).hexdigest()
        
        # Organizar por data e domínio
        today = datetime.utcnow().strftime("%Y-%m-%d")
        
        if data_type == "raw":
            return self.raw_path / today / domain / f"{url_hash}.html"
        else:
            return self.processed_path / domain / f"{url_hash}.json"
    
    async def save_raw_content(self, url: str, content: str, metadata: Dict) -> str:
        """Salvar conteúdo HTML bruto."""
        file_path = self._get_file_path(url, "raw")
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Salvar HTML
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Salvar metadados
        metadata_path = file_path.with_suffix('.meta.json')
        metadata_with_url = {**metadata, "url": url, "saved_at": datetime.utcnow().isoformat()}
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata_with_url, f, indent=2, ensure_ascii=False)
        
        logger.debug(
            "Raw content saved",
            url=url,
            file_path=str(file_path),
            content_size=len(content),
        )
        
        return str(file_path)
    
    async def save_processed_data(self, page_data: PageData) -> str:
        """Salvar dados processados."""
        file_path = self._get_file_path(str(page_data.url), "processed")
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Converter para dict e salvar
        data_dict = page_data.dict()
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data_dict, f, indent=2, ensure_ascii=False, default=str)
        
        logger.debug(
            "Processed data saved",
            url=page_data.url,
            file_path=str(file_path),
            quality_score=page_data.quality_score,
        )
        
        return str(file_path)
    
    async def load_processed_data(self, url: str) -> Optional[PageData]:
        """Carregar dados processados."""
        file_path = self._get_file_path(url, "processed")
        
        if not file_path.exists():
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data_dict = json.load(f)
            
            # Converter de volta para PageData
            page_data = PageData(**data_dict)
            
            logger.debug(
                "Processed data loaded",
                url=url,
                file_path=str(file_path),
            )
            
            return page_data
            
        except Exception as e:
            logger.error(
                "Failed to load processed data",
                url=url,
                file_path=str(file_path),
                error=str(e),
            )
            return None
    
    async def exists(self, url: str) -> bool:
        """Verificar se dados processados existem."""
        file_path = self._get_file_path(url, "processed")
        return file_path.exists()
    
    async def list_urls(self, domain: Optional[str] = None) -> List[str]:
        """Listar URLs armazenadas."""
        urls = []
        
        if domain:
            domain_path = self.processed_path / domain
            if domain_path.exists():
                for file_path in domain_path.glob("*.json"):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            if "url" in data:
                                urls.append(data["url"])
                    except Exception as e:
                        logger.warning(
                            "Failed to read file",
                            file_path=str(file_path),
                            error=str(e),
                        )
        else:
            # Listar todos os domínios
            for domain_path in self.processed_path.iterdir():
                if domain_path.is_dir():
                    domain_urls = await self.list_urls(domain_path.name)
                    urls.extend(domain_urls)
        
        return urls


class StorageManager:
    """Gerenciador de storage que coordena diferentes backends."""
    
    def __init__(self, backend: StorageBackend):
        self.backend = backend
        self._cache: Dict[str, PageData] = {}
        self.enable_cache = True
    
    async def save_page(
        self, 
        page_data: PageData, 
        raw_content: Optional[str] = None,
        raw_metadata: Optional[Dict] = None
    ) -> Dict[str, str]:
        """Salvar página completa (dados processados + conteúdo bruto)."""
        url = str(page_data.url)
        saved_paths = {}
        
        try:
            # Salvar dados processados
            processed_path = await self.backend.save_processed_data(page_data)
            saved_paths["processed"] = processed_path
            
            # Salvar conteúdo bruto se fornecido
            if raw_content:
                metadata = raw_metadata or {}
                raw_path = await self.backend.save_raw_content(url, raw_content, metadata)
                saved_paths["raw"] = raw_path
            
            # Atualizar cache
            if self.enable_cache:
                self._cache[url] = page_data
            
            logger.info(
                "Page saved successfully",
                url=url,
                paths=saved_paths,
                quality_score=page_data.quality_score,
            )
            
            return saved_paths
            
        except Exception as e:
            logger.error(
                "Failed to save page",
                url=url,
                error=str(e),
                exc_info=True,
            )
            raise
    
    async def load_page(self, url: str) -> Optional[PageData]:
        """Carregar dados de uma página."""
        # Verificar cache primeiro
        if self.enable_cache and url in self._cache:
            logger.debug("Cache hit", url=url)
            return self._cache[url]
        
        # Carregar do backend
        page_data = await self.backend.load_processed_data(url)
        
        if page_data and self.enable_cache:
            self._cache[url] = page_data
        
        return page_data
    
    async def page_exists(self, url: str) -> bool:
        """Verificar se página existe no storage."""
        # Verificar cache primeiro
        if self.enable_cache and url in self._cache:
            return True
        
        return await self.backend.exists(url)
    
    async def get_domain_stats(self, domain: str) -> Dict:
        """Obter estatísticas de um domínio."""
        urls = await self.backend.list_urls(domain)
        
        if not urls:
            return {
                "domain": domain,
                "total_pages": 0,
                "successful_pages": 0,
                "failed_pages": 0,
                "average_quality": 0,
            }
        
        # Carregar dados para calcular estatísticas
        successful_pages = 0
        failed_pages = 0
        total_quality = 0
        
        for url in urls:
            page_data = await self.load_page(url)
            if page_data:
                if page_data.status.value == "success":
                    successful_pages += 1
                    total_quality += page_data.quality_score
                else:
                    failed_pages += 1
        
        return {
            "domain": domain,
            "total_pages": len(urls),
            "successful_pages": successful_pages,
            "failed_pages": failed_pages,
            "average_quality": (
                total_quality / successful_pages if successful_pages > 0 else 0
            ),
            "success_rate": (
                successful_pages / len(urls) * 100 if urls else 0
            ),
        }
    
    async def cleanup_old_data(self, days_old: int = 30) -> int:
        """Limpar dados antigos (implementação básica)."""
        # TODO: Implementar limpeza real baseada em data
        logger.info("Cleanup requested", days_old=days_old)
        return 0
    
    def clear_cache(self) -> None:
        """Limpar cache em memória."""
        self._cache.clear()
        logger.debug("Storage cache cleared")
    
    def get_cache_stats(self) -> Dict:
        """Obter estatísticas do cache."""
        return {
            "cache_size": len(self._cache),
            "cache_enabled": self.enable_cache,
        }
