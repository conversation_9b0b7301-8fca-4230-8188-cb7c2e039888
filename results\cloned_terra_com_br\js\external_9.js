/*! zaz-app-t360-subject-table - v1.0.0 - 02/01/2025 -- 6:38pm */

zaz.use(function appT360SubjectTable(pkg){"use strict";var console=pkg.console,appFactory,STATIC_PUBLIC=null,STATIC_PRIVATE={};pkg.factoryManager.get("app").create({name:"t360.subjectTable",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/t360-subject-table",source:"http://github.tpn.terra.com/Terra/t360-subject-table",description:"Just another app",tests:"http://s1.trrsf.com/fe/t360-subject-table/tests/index.htm?zaz[env]=tests",dependencies:[],dictionaries:[],templates:{},expects:{},setup:function(STATIC_PUBLIC,PUBLIC,__shared){},init:function(params,__shared){var PRIVATE={},PUBLIC=this,elem=params.container.querySelector(".js-subject-table-more");return elem&&elem.addEventListener("click",function(){window.tga.send("send","event","subject-table","click","bot√£o ver mais")}),this},teardown:function(why,__static,__proto,__shared){}})});