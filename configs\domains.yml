# =============================================================================
# CONFIGURAÇÕES POR DOMÍNIO
# =============================================================================

# Configurações padrão aplicadas a todos os domínios
default:
  rate_limit:
    requests_per_second: 1.0
    burst_limit: 5
    backoff_factor: 2.0
    max_retries: 3
  
  timeouts:
    connect: 10
    read: 30
    total: 60
  
  headers:
    User-Agent: "WebScraper/1.0 (+mailto:<EMAIL>)"
    Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
    Accept-Language: "pt-BR,pt;q=0.9,en;q=0.8"
    Accept-Encoding: "gzip, deflate"
    DNT: "1"
  
  parsing:
    remove_selectors:
      - "nav"
      - "footer" 
      - ".advertisement"
      - ".sidebar"
      - "#comments"
    
    content_selectors:
      title: "h1, title"
      main_content: "main, article, .content, #content"
      headings: "h1, h2, h3, h4, h5, h6"
      links: "a[href]"
      code_blocks: "pre, code"
  
  quality:
    min_content_length: 100
    min_word_count: 20
    min_headings: 1
    required_elements: ["title"]

# =============================================================================
# CONFIGURAÇÕES ESPECÍFICAS POR DOMÍNIO
# =============================================================================

domains:
  # Microsoft Docs (exemplo para documentação do Revit)
  docs.microsoft.com:
    rate_limit:
      requests_per_second: 2.0  # Microsoft permite mais requisições
      burst_limit: 10
    
    scope:
      allowed_paths:
        - "/en-us/dotnet/api/"
        - "/en-us/windows/"
        - "/pt-br/"
      blocked_paths:
        - "/search"
        - "/feedback"
    
    parsing:
      content_selectors:
        title: "h1.title"
        main_content: ".content"
        breadcrumbs: ".breadcrumb"
        code_blocks: ".highlight, pre code"
        api_signature: ".signature"
      
      remove_selectors:
        - ".feedback-section"
        - ".page-actions"
        - ".contributors"
    
    dynamic_content:
      enabled: true
      wait_for_selector: ".content"
      wait_timeout: 10000
    
    quality:
      min_content_length: 200
      min_word_count: 50
      bonus_selectors:  # Elementos que aumentam o score de qualidade
        - ".code-example"
        - ".api-signature"
        - ".syntax"

  # Mozilla Developer Network
  developer.mozilla.org:
    rate_limit:
      requests_per_second: 1.5
    
    scope:
      allowed_paths:
        - "/en-US/docs/"
        - "/pt-BR/docs/"
      blocked_paths:
        - "/en-US/search"
    
    parsing:
      content_selectors:
        title: "h1"
        main_content: ".main-page-content"
        code_blocks: ".code-example pre, .highlight"
        syntax_box: ".syntaxbox"
      
      remove_selectors:
        - ".sidebar"
        - ".page-footer"
        - ".document-toc"
    
    quality:
      min_content_length: 150
      bonus_selectors:
        - ".code-example"
        - ".syntaxbox"
        - ".note"

  # Stack Overflow (exemplo de site com conteúdo dinâmico)
  stackoverflow.com:
    rate_limit:
      requests_per_second: 0.5  # Mais conservador
      burst_limit: 3
    
    scope:
      allowed_paths:
        - "/questions/"
      blocked_paths:
        - "/users/"
        - "/search"
    
    parsing:
      content_selectors:
        title: "h1[itemprop='name']"
        question: ".question .post-text"
        answers: ".answer .post-text"
        code_blocks: "pre code"
        tags: ".post-taglist .post-tag"
      
      remove_selectors:
        - ".sidebar"
        - ".comments"
        - ".vote-count-post"
    
    dynamic_content:
      enabled: true
      wait_for_selector: ".question"
      wait_timeout: 5000
    
    quality:
      min_content_length: 100
      min_word_count: 30
      bonus_selectors:
        - ".accepted-answer"
        - "pre code"

  # GitHub (para documentação em repositórios)
  github.com:
    rate_limit:
      requests_per_second: 1.0
    
    scope:
      allowed_paths:
        - "/*/blob/"
        - "/*/wiki/"
        - "/*/README"
      blocked_paths:
        - "/search"
        - "/notifications"
    
    parsing:
      content_selectors:
        title: ".repository-content h1, .markdown-body h1"
        main_content: ".markdown-body, .blob-wrapper"
        code_blocks: ".highlight pre, .markdown-body pre"
      
      remove_selectors:
        - ".file-navigation"
        - ".repository-sidebar"
    
    quality:
      min_content_length: 50  # READMEs podem ser curtos
      bonus_selectors:
        - ".markdown-body"
        - ".highlight"

# =============================================================================
# CONFIGURAÇÕES DE SITEMAP
# =============================================================================

sitemap_config:
  # Padrões comuns de sitemap
  sitemap_patterns:
    - "/sitemap.xml"
    - "/sitemap_index.xml"
    - "/robots.txt"  # Para encontrar referências ao sitemap
  
  # Filtros para URLs do sitemap
  url_filters:
    # Incluir apenas URLs que correspondem a estes padrões
    include_patterns:
      - ".*\\.html$"
      - ".*docs.*"
      - ".*api.*"
      - ".*reference.*"
    
    # Excluir URLs que correspondem a estes padrões
    exclude_patterns:
      - ".*\\.(pdf|jpg|png|gif|css|js)$"
      - ".*/search.*"
      - ".*/login.*"
      - ".*/admin.*"

# =============================================================================
# CONFIGURAÇÕES DE CRAWLER
# =============================================================================

crawler_config:
  # Configurações da fronteira (fila de URLs)
  frontier:
    max_depth: 5
    max_urls_per_domain: 10000
    priority_patterns:
      high:
        - ".*api.*"
        - ".*docs.*"
        - ".*reference.*"
        - ".*tutorial.*"
      medium:
        - ".*guide.*"
        - ".*example.*"
      low:
        - ".*blog.*"
        - ".*news.*"
  
  # Configurações de descoberta de URLs
  discovery:
    follow_external_links: false
    extract_from_javascript: false
    respect_nofollow: true
    
    # Seletores para encontrar links importantes
    important_link_selectors:
      - "nav a"
      - ".toc a"
      - ".menu a"
      - ".sidebar a"
