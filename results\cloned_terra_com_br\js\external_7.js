!function(){"use strict";if(window.AdManager)throw new Error("AdManager already loaded");var AdManager,doc,FiF,PUBLIC_STATIC={},PRIVATE={tgmKeyHasChanged:0,lockTgmKeyChange:!1,pageAds:{},ads:{},curViewport:1,viewportAds:{},tagmanAds:{},libs:{},attributes:{},setupPromises:{},settings:{VIEWPORT_MAX:4,VIEWPORT_ORDER:[2,3,4],TGMKEY:null,PPI_URL:"http://ppi.terra.com.br/showArea.asp",RUBICON_ACCOUNT_ID:"10828",RUBICON_DFP_NETWORK_ID:"1211",RUBICON_TIMEOUT:3e3,PLATFORM:"web",THIRDPARTY_TIMEOUT:3e3,tagmanDefaultArea:"setup",TAGMAN_TIMEOUT:5e3,PPI_TIMEOUT:2e3,DFP_TIMEOUT:800,DFP_IFRAME_INTERVAL_CHECKER:501,DEFAULT_GOOGLE_TAG:null,NO_ADS:!1,ADS_METRICS_PUSH:10,ADS_METRICS_PERCENTAGE:10,EXP_DISCARD_SETUP_FOR_TGMKEY:/\.(selo)/i},curEnv:"prd",env:{prd:{htmlPath:"/globalSTATIC/fe/zaz-mod-manager/",htmlFile:"zaz-frame.html",tagmanURL:"https://p1-cloud.trrsf.com.br/api/tagman/v2/ShowArea",tagmanV2ApiURL:"https://p1-cloud.trrsf.com.br/api/tagman/v2/ShowArea"},dsv:{htmlPath:"../dist/",htmlFile:"zaz-frame.html",tagmanURL:"https://p1-cloud.trrsf.com.br/api/tagman/ShowArea",tagmanV2ApiURL:"https://api-hlg.tpn.terra.com/tagman/ShowArea/v2"}},getTgmKey:function(){var match=window.location.search.match("[?&]tgmkey=([^&]+)"),tgmkey=null;return match?(PRIVATE.lockTgmKeyChange=!0,tgmkey=match[1]):window.tgmKey&&(tgmkey=window.tgmKey),tgmkey},isTGMKEYSetupable:function(tgmKey){var setupable=/(.+?[\.]){2,}/.test(tgmKey);return PRIVATE.settings.EXP_DISCARD_SETUP_FOR_TGMKEY instanceof RegExp&&PRIVATE.settings.EXP_DISCARD_SETUP_FOR_TGMKEY.test(tgmKey)&&(setupable=!1),setupable=/\.live/.test(tgmKey)&&tgmKey!==PRIVATE.settings.TGMKEY?!1:setupable},getTagmanUrl:function(area){var url=PRIVATE.env[PRIVATE.curEnv].tagmanV2ApiURL;return url},waitForSetupDefinition:function(tgmKey){var timer,done=!1,handlers={},isDocumentReady=!/in/.test(document.readyState),promise=new Promise(function(resolve,reject){console.log("[AdManager] waitForSetupDefinition for "+tgmKey),handlers.resolve=function(){done||(done=!0,timer=timer&&clearInterval(timer),PRIVATE.setupPromises[tgmKey]&&PRIVATE.setupPromises[tgmKey].resolve&&delete PRIVATE.setupPromises[tgmKey].resolve,PRIVATE.attributes["setup_done_for_"+tgmKey]||!PRIVATE.isTGMKEYSetupable(tgmKey)?(PRIVATE.isTGMKEYSetupable(tgmKey)?console.log("[AdManager] Setup resolved template declaration for tgmKey "+tgmKey):console.log("[AdManager] setup for "+tgmKey+" discarted by config"),resolve()):PRIVATE.requestSetupForTGMKEY(tgmKey).then(resolve,reject))},handlers.reject=function(){done=!0,timer=timer&&clearInterval(timer),PRIVATE.setupPromises[tgmKey].reject&&delete PRIVATE.setupPromises[tgmKey].reject,console.warn("[AdManager] setup for "+tgmKey+" rejected")},isDocumentReady||PRIVATE.attributes["setup_done_for_"+tgmKey]||!PRIVATE.isTGMKEYSetupable(tgmKey)?handlers.resolve():(document.addEventListener("DOMContentLoaded",handlers.resolve,!1),timer=setInterval(function(){/in/.test(document.readyState)||(timer=timer&&clearInterval(timer),done||handlers.resolve())},300))});return promise.resolve=handlers.resolve,promise},requestSetupForTGMKEY:function(tgmKey){return tgmKey=tgmKey||PRIVATE.settings.TGMKEY,new Promise(function(resolve,reject){var area=PRIVATE.settings.tagmanDefaultArea,url=PRIVATE.getTagmanUrl(area)+"?key="+tgmKey+"."+area+"&direct=1";console.log("[AdManager] Loading setup area for tgmKey "+tgmKey+"\n@ "+url),PRIVATE.Util.includeJS(url,function(){PRIVATE.log("[INFO] Successfully load setup tagman area."),PRIVATE.setupOnce=!0,PRIVATE.setupReady=!0,console.log("[AdManager] Setup resolved by external url for tgmKey "+tgmKey),resolve()},function(){var msg="[AdManager] Error loading setup area for tgmKey "+tgmKey+"\n@ "+url+"\nWe will try to load ups ads anymway...";PRIVATE.log("[ERROR] "+msg),resolve(msg),PUBLIC_STATIC.onError.call(this,msg)})})},setup:function(settings){if("[object Object]"!=Object.prototype.toString.call(settings))return Promise.reject(new Error("Invalid settings to run setup: "+settings));var tgmKey=settings.tgmkey||PRIVATE.settings.TGMKEY,isDocumentReady=!/in/.test(document.readyState);if(PRIVATE.setupPromises[tgmKey]){if((PRIVATE.attributes["setup_done_for_"+tgmKey]||!PRIVATE.isTGMKEYSetupable(tgmKey))&&"function"==typeof PRIVATE.setupPromises[tgmKey].resolve)try{PRIVATE.setupPromises[tgmKey].resolve()}catch(e){console.error("[AdManager] Error aborting dynamic setup of tgmkey "+tgmKey+": "+e.message,e)}}else if(console.log("[AdManager] First setup call for tgmKey "+tgmKey),PRIVATE.attributes["setup_done_for_"+tgmKey]||!PRIVATE.isTGMKEYSetupable(tgmKey))PRIVATE.isTGMKEYSetupable(tgmKey)?console.log("[AdManager] setup preloaded on template for "+tgmKey):console.log("[AdManager] setup for "+tgmKey+" discarted by config"),PRIVATE.setupPromises[tgmKey]=Promise.resolve(!0);else{"function"!=typeof settings.onBeforeStart&&(settings.onBeforeStart=isDocumentReady?PRIVATE.requestSetupForTGMKEY:PRIVATE.waitForSetupDefinition);try{PRIVATE.setupPromises[tgmKey]=settings.onBeforeStart(tgmKey)}catch(setupException){console.error("[AdManager] Error running setup of tgmkey "+tgmKey+": "+setupException.message,setupException),PRIVATE.setupPromises[tgmKey]=setupException}}return new Promise(function(resolve,reject){PRIVATE.setupPromises[tgmKey]instanceof Promise?PRIVATE.setupPromises[tgmKey].then(resolve,reject):resolve(PRIVATE.setupPromises[tgmKey])})},getGPT:function(){return PRIVATE.libs.gpt||(PRIVATE.log("[INFO] loading GPT API"),window.googletag&&"object"==typeof window.googletag||(window.googletag={}),window.googletag.cmd&&"object"==typeof window.googletag.cmd||(window.googletag.cmd=[]),PRIVATE.libs.gpt=new Promise(function(resolveGPT,rejectGPT){window.googletag.cmd.push(function(){try{window.googletag.pubads().setSafeFrameConfig({allowPushExpansion:!0,sandbox:!0}),window.googletag.pubads().disableInitialLoad(),window.googletag.pubads().enableSingleRequest(),window.googletag.pubads().enableAsyncRendering(),window.googletag.setConfig({threadYield:window.__gamThreadYield?"ENABLED_ALL_SLOTS":"DISABLED"});var gamPII=null;if(localStorage.getItem("mdivo-cpid"))gamPII=localStorage.getItem("mdivo-cpid");else if(localStorage.getItem("mdivo-id"))gamPII=localStorage.getItem("mdivo-id");else{var fpd=localStorage.getItem("FPD");if(fpd)try{(fpd=JSON.parse(fpd))&&fpd.emailhash&&(gamPII=fpd.emailhash)}catch(e){}}gamPII&&window.googletag.pubads().setPublisherProvidedId(gamPII),window.googletag.enableServices(),window.googletag.pubads().addEventListener("slotOnload",function(event){var width,height,containerId=event.slot.getSlotElementId(),conf=PRIVATE.pageAds[containerId];if(conf&&!(conf.reloadSize&&1<conf.reloadSize[0]&&1<conf.reloadSize[1])){if(event=document.getElementById(conf.placeholder))try{conf.iframeElem=event.getElementsByTagName("div")[0].getElementsByTagName("iframe")[0],PRIVATE.log("[INFO] Google Iframe for ["+conf.placeholder+"] found.")}catch(error){console.log("[AdManager] Cannot get sizes in Google Iframe for placeholder "+conf.placeholder)}conf.iframeElem&&(width=parseInt(conf.iframeElem.getAttribute("width")),height=parseInt(conf.iframeElem.getAttribute("height"))),1<width&&1<height&&(conf.reloadSize=[parseInt(width),parseInt(height)]),window.zaz.use(function(pkg){pkg.context.page.trigger("ad-slotrenderended",{sender:"zaz-admanager",id:containerId,slot:conf})})}}),window.googletag.pubads().addEventListener("slotRenderEnded",function(event){var width,height,placeholderElem,containerId=event.slot.getSlotElementId(),conf=PRIVATE.pageAds[containerId];if(conf){if(PRIVATE.checkReloadRules(conf,[conf]),window.zaz.use(function(pkg){pkg.context.page.trigger("ad-slotrenderended",{sender:"zaz-admanager",id:containerId,slot:conf})}),conf.exibitions=conf.exibitions||0,conf.exibitions++,!conf.reloadSize)try{if(event.size&&"object"==typeof event.size&&2==event.size.length&&1<event.size[0]&&1<event.size[1])width=event.size[0],height=event.size[1],"mob"==PRIVATE.settings.PLATFORM&&conf.parallax&&conf.parallax.visible&&0<conf.parallax.height&&(height=event.parallax.height);else{if(placeholderElem=document.getElementById(conf.placeholder))try{conf.iframeElem=placeholderElem.getElementsByTagName("div")[0].getElementsByTagName("iframe")[0],PRIVATE.log("[INFO] Google Iframe for ["+conf.placeholder+"] found.")}catch(error){console.log("[AdManager] Cannot get sizes in Google Iframe for placeholder "+conf.placeholder)}conf.iframeElem&&(width=conf.iframeElem.getAttribute("width"),height=conf.iframeElem.getAttribute("height"))}width=parseInt(width,10),height=parseInt(height,10),!width||!height||isNaN(width)||isNaN(height)?console.warn("[AdManager] Unknown dimensions for ["+conf.placeholder+"]. The corresponding iframe must declare a fixed width and height"):(console.log("[AdManager] AdSlot for placeholder "+conf.placeholder+" rendered as "+width+"x"+height),1<width&&1<height?conf.reloadSize=[width,height]:console.log("[AdManager] This adSlot is not reloadable: "+conf.placeholder))}catch(reloadSizeException){PRIVATE.log("[AdManager] Exception discovering reload size for "+conf.placeholder)}conf.resolve&&conf.resolve(conf)}}),PRIVATE.log("[INFO] GPT initialized")}catch(exception){PRIVATE.log("[WARN] Exception definning global setting for GPT: "+exception.message)}finally{resolveGPT(window.googletag)}}),window.zaz.use(function(pkg){PRIVATE.log("[INFO] requirering GPT API"),pkg.require(["mod.gpt"]).then(function(dependencies){var dependencies=dependencies.mod.gpt;if(!dependencies||"object"!=typeof dependencies||"object"!=typeof dependencies.cmd||"function"!=typeof dependencies.cmd.push)return dependencies=new Error("GPT API not available"),PRIVATE.log("[ERROR] "+dependencies.message),rejectGPT(dependencies),dependencies;PRIVATE.log("[INFO] mod.GPT successfully loaded by Includer")},function(e){e=new Error("Problem requesting GPT API: "+e.message);return PRIVATE.log("[ERROR] "+e.message),rejectGPT(e),e})})}),PRIVATE.libs.gpt)},getPrebid:function(){return PRIVATE.libs.prebid||(PRIVATE.libs.prebid=new Promise(function(resolvePrebid,rejectPrebid){PRIVATE.log("[INFO] Openning zaz frameworkt to require dependencies."),window.zaz.use(function(pkg){PRIVATE.log("[INFO] Requiring Prebid and GPT"),Promise.all([PRIVATE.getGPT(),pkg.require(["mod.prebid"]),pkg.require(["mod.aps"])]).then(function(deps){deps=deps[1];deps&&"object"==typeof deps.mod&&"object"==typeof deps.mod.prebid&&"object"==typeof deps.mod.prebid.que?(window.apstag.init({pubID:"bc92a28d-5a18-4ea4-9491-626e7826c69c",adServer:"googletag"}),resolvePrebid(window.pbjs)):rejectPrebid(new Error("Error loading Prebid API: invalid object window.rubicontag."))},function(e){e=new Error("Problem requesting PREBID dependencies: "+e.message);PRIVATE.log("[ERROR] "+e.message),rejectPrebid(e)})})}),PRIVATE.libs.prebid)},createWrapper:function(conf){var DOMElem=document.getElementById(conf.placeholder),wrapper=document.createElement("div"),computedStyle=window.getComputedStyle(DOMElem),minHeight=computedStyle.getPropertyValue("min-height"),computedStyle=computedStyle.getPropertyValue("height");if("0px"==minHeight&&(minHeight=!1),wrapper.style.display="none",wrapper.id=conf.placeholder+"--wrapper","auto"==computedStyle&&(DOMElem.style.minHeight=minHeight||DOMElem.style.minHeight||"1px"),DOMElem.parentNode.replaceChild(wrapper,DOMElem),wrapper.appendChild(DOMElem),wrapper.style.display="","mob"==PRIVATE.settings.PLATFORM&&conf.parallax&&conf.parallax.visible&&0<conf.parallax.height){DOMElem.style.height=conf.parallax.height+"px",DOMElem.className+=" ad-parallax-clip",DOMElem.parentNode.className+=" ad-parallax-container",DOMElem.parentNode.style.height=conf.parallax.height+"px";try{-1<DOMElem.parentNode.parentNode.className.indexOf("w2h2")&&(DOMElem.parentNode.parentNode.style.height=50+conf.parallax.height+"px")}catch(e){}}},setKeywords:function(adslot,keywords){var deviceMemory=null;if(navigator&&navigator.deviceMemory&&(deviceMemory=navigator.deviceMemory),null!==keywords)try{for(var i in keywords)adslot.setTargeting(i,keywords[i])}catch(e){}adslot.setTargeting("lite","0"),deviceMemory&&adslot.setTargeting("devicememory",deviceMemory),window.terra_info_author&&adslot.setTargeting("contentauthor",window.terra_info_author),window.terra_info_vendor&&adslot.setTargeting("vendor",window.terra_info_vendor),window.articleIsPrefetch?adslot.setTargeting("pf",window.articleIsPrefetch):adslot.setTargeting("pf","false"),document.prerendering?adslot.setTargeting("pr","true"):adslot.setTargeting("pr","false"),window.__gamThreadYield?adslot.setTargeting("gamThreadYield","true"):adslot.setTargeting("gamThreadYield","false"),window.zaz.use(function(pkg){pkg.context.page.get("abTest")&&""!=pkg.context.page.get("abTest")&&adslot.setTargeting("ab-test",pkg.context.page.get("abTest"))}),window.zaz.use(function(pkg){var benegripe,weather;sessionStorage.getItem("user-city-weather")&&(weather=JSON.parse(sessionStorage.getItem("user-city-weather")),pkg=pkg.context.page.get("query"),weather.chuva?benegripe="chuva":weather.benegripe&&(benegripe=weather.benegripe),(benegripe=pkg&&pkg.benegripe?pkg.benegripe:benegripe)&&adslot.setTargeting("benegripe",benegripe))})},render:function(conf){var renderAd;switch(conf.adserver){case"prebid":renderAd=PRIVATE.renderPrebid(conf);break;case"dfp":renderAd=PRIVATE.renderDFP(conf);break;case"uri":renderAd=PRIVATE.renderGeneric(conf);break;default:PRIVATE.log("[ERROR] Invalid adserver definition for ad "+conf.placeholder+".")}return renderAd},renderDFP:function(conf){var gtag,keywords,mapping,DOMElem=document.getElementById(conf.placeholder),adslot=null;return new Promise(function(resolve,reject){conf.resolve=resolve;var refreshParams={};if("boolean"==typeof conf.changeCorrelator&&(refreshParams.changeCorrelator=conf.changeCorrelator),conf.adslot||conf.reloaded){conf.reloadSize||(PRIVATE.log("[ERROR] Reload prevented because ad size could not be detected for "+conf.placeholder),reject());try{mapping=googletag.sizeMapping().addSize([0,0],conf.reloadSize).build(),conf.adslot=conf.adslot.defineSizeMapping(mapping),conf.adslot.setTargeting("refresh",++conf.adslot.countRefresh),googletag.pubads().refresh([conf.adslot],refreshParams)}catch(e){PRIVATE.log("[ERROR] Exception reloading "+conf.placeholder+" (using GPT API): "+e.message),reject()}}else PRIVATE.libs.gpt||PRIVATE.getGPT(),DOMElem&&"object"==typeof DOMElem&&1===DOMElem.nodeType?document.getElementById(conf.placeholder+"--wrapper")||PRIVATE.createWrapper(conf):(PRIVATE.log("[ERROR] Invalid placeholder "+conf.placeholder),reject()),PRIVATE.log("[INFO] renderDFP called ("+PRIVATE.settings.PLATFORM+")"),conf.viewport&&"viewport"==conf.renderType&&(conf.google_tag=conf.google_tag.substring(0,conf.google_tag.lastIndexOf("/"))+"/s"+conf.viewport),gtag=conf.google_tag&&"string"==typeof conf.google_tag?conf.google_tag:null,keywords=conf.keywords&&"object"==typeof conf.keywords?conf.keywords:null,null!==gtag&&PRIVATE.log("[INFO] Google Tag was found for ad "+conf.placeholder+" = "+gtag),PRIVATE.getGPT().then(function(googletag){PRIVATE.log("[INFO] GPT stuff... Defining slot, enabling single Requesting and services"),adslot=googletag.defineSlot(gtag||PRIVATE.settings.DEFAULT_GOOGLE_TAG,conf.sizes,conf.placeholder),(conf.adslot=adslot).addService(googletag.pubads()),mapping=googletag.sizeMapping().addSize([0,0],conf.sizes).build(),conf.adslot.defineSizeMapping(mapping),conf.viewport&&adslot.setTargeting("viewport",("viewport"===conf.renderType?"s":"")+conf.viewport),conf.adslot.countRefresh=0,conf.adslot.setTargeting("refresh","0"),conf.reloaded||(PRIVATE.pageAds[conf.placeholder]=conf),PRIVATE.setKeywords(adslot,keywords),conf.customKW&&PRIVATE.setKeywords(adslot,conf.customKW),googletag.display(conf.placeholder),googletag.pubads().refresh([conf.adslot],refreshParams)}).then(null,function(e){PRIVATE.log("[ERROR] Exception triggering "+conf.placeholder+": "+e.message),reject()})})}};PRIVATE.renderPrebid=function(conf){var mapping,prebidConf=Object.assign({},conf),PREBID_TIMEOUT=prebidConf.timeout||2e3,DOMElem=document.getElementById(prebidConf.placeholder);return new Promise(function(resolve,reject){prebidConf.resolve=resolve;var refreshParams={};function executeParallelAuctionAlongsidePrebid(){var apsSlotConfig,requestManager={adserverRequestSent:!1,aps:!1,prebid:!1};function biddersBack(){requestManager.aps&&requestManager.prebid&&sendAdserverRequest()}function sendAdserverRequest(){if(!0!==requestManager.adserverRequestSent){if(requestManager.adserverRequestSent=!0,/^header\-ad/.test(prebidConf.placeholder)&&window.performance&&"function"==typeof window.performance.mark)try{window.performance.mark("AD_S0_BIDDING_END")}catch(e){}window.googletag.cmd.push(function(){pbjs.setTargetingForGPTAsync([prebidConf.placeholder]),googletag.pubads().refresh([prebidConf.adslot],refreshParams)})}}apsSlotConfig={slotID:prebidConf.placeholder,slotName:prebidConf.google_tag},prebidConf.apsConfig?apsSlotConfig=Object.assign(apsSlotConfig,prebidConf.apsConfig):apsSlotConfig.sizes=prebidConf.sizes,window.apstag.fetchBids({slots:[apsSlotConfig]},function(bids){window.googletag.cmd.push(function(){window.apstag.setDisplayBids(),requestManager.aps=!0,biddersBack()})}),window.pbjs.que.push(function(){window.pbjs.requestBids({labels:["admanager"],adUnitCodes:[prebidConf.placeholder],timeout:PREBID_TIMEOUT,bidsBackHandler:function(){window.googletag.cmd.push(function(){pbjs.setTargetingForGPTAsync(),requestManager.prebid=!0,biddersBack()})}})}),window.setTimeout(function(){sendAdserverRequest()},2e3)}if("boolean"==typeof prebidConf.changeCorrelator&&(refreshParams.changeCorrelator=prebidConf.changeCorrelator),prebidConf.adslot||prebidConf.reloaded)if(prebidConf.reloadSize){try{mapping=googletag.sizeMapping().addSize([0,0],prebidConf.reloadSize).build(),prebidConf.adslot=prebidConf.adslot.defineSizeMapping(mapping)}catch(mappingException){return PRIVATE.log("[ERROR] Exception remapping slot sizes of  "+prebidConf.placeholder+" for reload (using GPT API): "+mappingException.message),void reject()}prebidConf.adslot.clearTargeting(),prebidConf.viewport&&prebidConf.adslot.setTargeting("viewport",("viewport"===prebidConf.renderType?"s":"")+prebidConf.viewport),prebidConf.adslot.setTargeting("refresh",++prebidConf.adslot.countRefresh),PRIVATE.setKeywords(prebidConf.adslot,prebidConf.keywords),prebidConf.customKW&&PRIVATE.setKeywords(prebidConf.adslot,prebidConf.customKW),executeParallelAuctionAlongsidePrebid()}else PRIVATE.log("[ERROR] Reload prevented because ad size could not be detected for "+prebidConf.placeholder);else{if(!DOMElem||"object"!=typeof DOMElem||1!==DOMElem.nodeType)return PRIVATE.log("[ERROR] Invalid placeholder "+prebidConf.placeholder),void reject();document.getElementById(prebidConf.placeholder+"--wrapper")||PRIVATE.createWrapper(prebidConf),PRIVATE.getPrebid().then(function(){PRIVATE.log("[INFO] renderPrebid called ("+PRIVATE.settings.PLATFORM+")"),prebidConf.viewport&&"viewport"==prebidConf.renderType&&(prebidConf.google_tag=prebidConf.google_tag.substring(0,prebidConf.google_tag.lastIndexOf("/"))+"/s"+prebidConf.viewport),adslot=googletag.defineSlot(prebidConf.google_tag,prebidConf.sizes,prebidConf.placeholder).addService(googletag.pubads()),prebidConf.viewport&&adslot.setTargeting("viewport",("viewport"===prebidConf.renderType?"s":"")+prebidConf.viewport);var adslot,adUnits=[{labelAny:["admanager"],mediaTypes:{banner:{sizes:prebidConf.sizes}},code:prebidConf.placeholder,bids:function(prebidConf){var validBids=[],tag=prebidConf.google_tag.split("/"),areaName=tag[tag.length-1];return prebidConf.bids.forEach(function(bid){"taboola"==bid.bidder?(bid.params.tagId=areaName,validBids.push(bid)):"medianet"!=bid.bidder&&validBids.push(bid)}),validBids}(prebidConf)}];if(adUnits[0].mediaTypes=Object.assign({},adUnits[0].mediaTypes,prebidConf.mediaTypes),prebidConf.adslot=adslot,prebidConf.adslot.countRefresh=0,prebidConf.adslot.setTargeting("refresh","0"),/^header\-ad/.test(prebidConf.placeholder)&&window.performance&&"function"==typeof window.performance.mark)try{window.performance.mark("AD_S0_BIDDING_START")}catch(e){}window.pbjs.que.push(function(){window.pbjs.enableSendAllBids&&window.pbjs.enableSendAllBids(),window.pbjs.addAdUnits(adUnits)}),prebidConf.reloaded||(PRIVATE.pageAds[prebidConf.placeholder]=prebidConf),PRIVATE.setKeywords(prebidConf.adslot,prebidConf.keywords),prebidConf.customKW&&PRIVATE.setKeywords(prebidConf.adslot,prebidConf.customKW),executeParallelAuctionAlongsidePrebid();try{console.log("[AdManager] triggering googletag.display() for "+prebidConf.placeholder),window.googletag.display(prebidConf.placeholder)}catch(googletagException){console.log("[AdManager] Exception triggering display() and Refresh on GPT for "+prebidConf.placeholder),reject()}}).then(null,function(e){PRIVATE.log("[ERROR] Exception triggering "+prebidConf.placeholder+": "+e.message),reject()})}})},PRIVATE.renderGeneric=function(conf){var wrapper,_width=conf.sizes&&conf.sizes[0][0]&&0<conf.sizes[0][0]?conf.sizes[0][0]:null,_height=conf.sizes&&conf.sizes[0][1]&&0<conf.sizes[0][1]?conf.sizes[0][1]:null,time=new Date,placeholder=document.getElementById(conf.placeholder);return placeholder&&((wrapper=document.createElement("div")).id=conf.placeholder+"--wrapper",placeholder.parentNode.replaceChild(wrapper,placeholder),wrapper.appendChild(placeholder)),PRIVATE.pageAds[conf.placeholder]=conf,new Promise(function(resolve,reject){conf.uri?PRIVATE.Util.FiF.create({placeholder:conf.placeholder,fifsrc:conf.uri,justrender:!0,width:_width,height:_height,callback:function(){var newTime=new Date,_message="Iframe was successfully created for ad "+conf.placeholder;PRIVATE.log("[INFO] "+_message),PRIVATE.log("[INFO] Generic AD for placeholder ["+conf.placeholder+"] was created in "+(newTime-time)+"ms"),resolve(conf)}}):(PRIVATE.log("[ERROR] Invalid settings rendering generic ad server ad"),reject())})},PRIVATE.loadShowArea=function(tagmanConf,forcePlatform){return new Promise(function(resolve,reject){function addViewportAds(setCache){setCache&&PRIVATE.viewportAds[PRIVATE.settings.TGMKEY+"_"+tagmanConf.originalArea]||(PRIVATE.viewportAds[PRIVATE.settings.TGMKEY+"_"+tagmanConf.originalArea]=[]);var y,platform=void 0!==forcePlatform?forcePlatform:PRIVATE.settings.PLATFORM;for(y in PRIVATE.ads)PRIVATE.ads[y].platform!==platform&&"responsive"!==PRIVATE.ads[y].platform||(PRIVATE.ads[y].viewport="stick"==tagmanConf.type?tagmanConf.area:tagmanConf.originalArea,PRIVATE.ads[y].renderType=tagmanConf.type,PRIVATE.ads[y].customKW=tagmanConf.customKW,PRIVATE.viewportAds[PRIVATE.settings.TGMKEY+"_"+tagmanConf.originalArea].push(PRIVATE.ads[y]));setCache&&(PRIVATE.tagmanAds[PRIVATE.settings.TGMKEY+"_"+tagmanConf.calculateArea]=JSON.parse(JSON.stringify(PRIVATE.viewportAds[PRIVATE.settings.TGMKEY+"_"+tagmanConf.originalArea]))),PRIVATE.ads={},resolve({instance:tagmanConf.context,ADS:PRIVATE.viewportAds[PRIVATE.settings.TGMKEY+"_"+tagmanConf.originalArea]})}var includeTimer,includeTimeout=!1,backendViewport=AdManager.get("backend_getviewport");if("viewport"==tagmanConf.type&&(PRIVATE.curViewport=tagmanConf.originalArea),PRIVATE.settings.NO_ADS)return PRIVATE.log("[WARN] HEY, JUST TO INFORM YOU: I'M IGNORING ADVERTISING. WHY? BECAUSE IT WAS DISABLED BY THE URL PARAMETER(noads)."),void resolve({ADS:[]});if("object"==typeof backendViewport&&backendViewport[tagmanConf.calculateArea]&&"function"==typeof backendViewport[tagmanConf.calculateArea]){PRIVATE.log("[INFO] Backend ADS, this request is already cached >>> "+tagmanConf.url);try{PRIVATE.viewportAds[PRIVATE.settings.TGMKEY+"_"+tagmanConf.originalArea]=[],backendViewport[tagmanConf.calculateArea].apply(this)}catch(e){PRIVATE.log("[ERROR] Something wrong with the cached request >>> "+tagmanConf.url),reject({error:tagmanConf.calculateArea+" ERROR | "+e})}addViewportAds(!0)}else PRIVATE.log("[INFO] GET URL >>> "+tagmanConf.url),PRIVATE.tagmanAds[PRIVATE.settings.TGMKEY+"_"+tagmanConf.calculateArea]?(PRIVATE.ads=JSON.parse(JSON.stringify(PRIVATE.tagmanAds[PRIVATE.settings.TGMKEY+"_"+tagmanConf.calculateArea])),addViewportAds(!1)):(PRIVATE.Util.includeJS(tagmanConf.url,function(){PRIVATE.log("[INFO] OK - GET URL >>> "+tagmanConf.url),includeTimeout=!0,clearTimeout(includeTimer),addViewportAds(!0)},function(e){PRIVATE.log("[ERROR] FAIL - GET URL >>> "+tagmanConf.url),PRIVATE.ads={};var err={error:"[TAGMAN REQUEST ERROR]"};reject(err),PUBLIC_STATIC.onError.call(this,err)}),PRIVATE.log("[INFO] Starting TAGMAN timeout checker...will wait "+PRIVATE.settings.TAGMAN_TIMEOUT+"ms for this request"),includeTimer=setTimeout(function(){var message;includeTimeout||(PRIVATE.ads={},message="TAGMAN TIMEOUT - The url "+tagmanConf.url+" took to long to load. None ads was delivered.",PRIVATE.log("[ERROR] "+message),reject({error:message}),PUBLIC_STATIC.onError.call(this,message))},PRIVATE.settings.TAGMAN_TIMEOUT))})},PRIVATE.calcViewportN=function(n){var VIEWPORT_MAX=PRIVATE.settings.VIEWPORT_MAX,VIEWPORT_ORDER=PRIVATE.settings.VIEWPORT_ORDER;return VIEWPORT_MAX<n?(n-VIEWPORT_MAX-1)%VIEWPORT_ORDER.length:n},PRIVATE.reloadHandlers={always:function(current){var reloads=0,conf=current,timer=setInterval(function(){PRIVATE.log("[INFO] Reload triggered for ad "+conf.placeholder+".");var elem=document.getElementById(conf.placeholder);try{if(!elem)throw new Error("[WARN] Container of "+conf.placeholder+" not found. Disabling automatic reload.");PUBLIC_STATIC.reload(conf.placeholder).then(function(data){reloads++,PRIVATE.log("[INFO] Reloded ad "+conf.placeholder),conf.reloadRule.times!==1/0&&reloads>=conf.reloadRule.times&&timer&&(timer=clearInterval(timer),PRIVATE.log("[INFO] Ad "+conf.placeholder+" reached its reload limit: "+conf.reloadRule.times+" times."))},function(e){timer=timer&&clearInterval(timer),e instanceof Error||(e=new Error("Unknown error >> "+e)),PRIVATE.log("[ERROR] Exception reloading ad "+conf.placeholder+": "+e.message)})}catch(e){timer=timer&&clearInterval(timer),PRIVATE.log("[ERROR] Exception reloading ad "+conf.placeholder+": "+e.message)}},conf.reloadRule.loop)},viewable:function(conf,wholeList){function checkViewability(conf){var container=document.getElementById(conf.placeholder);new IntersectionObserver(function(entries){entries.forEach(function(entry){var now;entry.isIntersecting?(now=new Date,PRIVATE.log("[INFO]"+conf.placeholder+" is visible"),entry.target.htmlDataset("isVisible",!0),interval=interval&&clearInterval(interval),conf.viewedOnce?!0===conf.reloadRule.reload&&times<=conf.reloadRule.times&&now-timestamp>=(reloadIntervalByURL||conf.reloadRule.loop)&&reload(conf,!0):timestamp=new Date,conf.viewedOnce=!0,function(conf){interval&&clearInterval(interval),timestamp=new Date,conf.reloadRule&&!0===conf.reloadRule.reload&&(PRIVATE.log("[INFO] Enabling viewable reload for ad "+conf.placeholder),interval=setInterval(function(){document.getElementById(conf.placeholder)&&times<=conf.reloadRule.times?reload(conf):(clearInterval(interval),PRIVATE.log("[INFO] Stopping to listen ad viewable "+conf.placeholder))},reloadIntervalByURL||conf.reloadRule.loop))}(conf)):(PRIVATE.log("[INFO]"+conf.placeholder+" is hidden"),entry.target.htmlDataset("isVisible",!1))})},{threshold:.5}).observe(container)}var placeholder,times=1,interval=null,timestamp=new Date,match=window.location.search.match("[?&]reload_interval=([^&]+)"),reloadIntervalByURL=!!match&&match[1],isPageVisible=function(){return document.visibilityState,document.visibilityState,"visible"===document.visibilityState},reload=function(conf,force){var elem=document.getElementById(conf.placeholder);timestamp=new Date,elem?force||isPageVisible()&&"true"===elem.htmlDataset("isVisible")?(PUBLIC_STATIC.reload(conf.placeholder).then(function(data){PRIVATE.log("[INFO] Reloading ad "+conf.placeholder)},function(e){}),times+=1):PRIVATE.log("[WARN] Ad "+conf.placeholder+" is not viewable or the page is hidden"):PRIVATE.log("[WARN] placeholder for "+conf.placeholder+" NOT FOUND. Bypassing reload call.")};PRIVATE.log("[INFO] Starting to listen viewable ad "+conf.placeholder+" for reloading feature");try{if(placeholder=conf.placeholder&&"string"==typeof conf.placeholder?document.getElementById(conf.placeholder):null,conf.reloadRule){if(!placeholder)return void PRIVATE.log("[WARN] There's no valid placeholder to apply viewable reloadRule: #"+conf.placeholder+" (element ofr this selector is "+Object.prototype.toString.call(placeholder)+")");if("string"==typeof conf.reloadRule&&(conf.reloadRule=JSON.parse(conf.reloadRule)),!conf.reloadRule||"object"!=typeof conf.reloadRule)throw new Error("Invalid value passed to reloadRule: "+conf.reloadRule);!0===conf.reloadRule.reload&&checkViewability(conf)}}catch(viewabilityException){PRIVATE.log("[ERROR] Applying viewabilty rules for "+conf.placeholder+": "+viewabilityException.message)}}},PRIVATE.checkReloadRules=function(conf,wholeList){var reloadRule;if(!conf.reloadRulesApplyed)if(conf.reloadRulesApplyed=!0,PRIVATE.log("[INFO] Evaluating reload rules for "+conf.placeholder+"."),"object"!=typeof conf||"object"!=typeof conf.reloadRule)PRIVATE.log("[WARN] There are no reload rules defined for ad "+conf.placeholder+". This one WILL NOT BE RELADED.");else if((reloadRule=conf.reloadRule).reload)if(reloadRule.type&&"string"==typeof reloadRule.type&&"object"==typeof PRIVATE.reloadHandlers&&"function"==typeof PRIVATE.reloadHandlers[reloadRule.type]){if(reloadRule.loop=parseInt(reloadRule.loop,10),isNaN(reloadRule.loop)||reloadRule.loop<35e3)return PRIVATE.log('[WARN] Invalid "loop" reload rule for ad '+conf.placeholder+": "+reloadRule.loop+". This one WILL NOT BE RELADED."),void(reloadRule.reload=!1);if(void 0===reloadRule.times&&(reloadRule.times=1/0),reloadRule.times!==1/0&&(reloadRule.times=parseInt(reloadRule.times,10)),reloadRule.times!==1/0&&("number"!=typeof reloadRule.times||isNaN(reloadRule.times)||reloadRule.times<=0))return PRIVATE.log('[WARN] Invalid "times" reload rule for ad '+conf.placeholder+": "+reloadRule.times+". This one WILL NOT BE RELADED."),void(reloadRule.reload=!1);try{PRIVATE.log("[INFO] Applying reload rules for "+conf.placeholder+"."),PRIVATE.reloadHandlers[reloadRule.type](conf,wholeList)}catch(e){PRIVATE.log('[Error] Exception triggering reload handler "'+reloadRule.type+'" for ad '+conf.placeholder+': "'+e.message+'". This one MAY NOT BE RELADED.')}}else PRIVATE.log('[WARN] Reload "type" invalid or not implemented for ad '+conf.placeholder+': "'+reloadRule.type+'". This one WILL NOT BE RELADED.')},PRIVATE.log=function(log){PRIVATE.isDebug&&console.log(log)},PRIVATE.init=function(){window.googletag=window.googletag||{},window.googletag.cmd=window.googletag.cmd||[];var regexLoadAds=/load_ads=(\w+)/.exec(document.cookie);regexLoadAds&&"false"==regexLoadAds[1]&&(PRIVATE.settings.NO_ADS=!0),(PRIVATE.settings.BYPASS||document.location.search.match(/remove-ads=true/g))&&(PRIVATE.settings.NO_ADS=!0),/tpn=1/.test(location.search)&&location.search.replace(/[\?&]mod\.adManager\.(.+?)=([^&]+)/g,function(match,key,value){if(PRIVATE.log('[INFO] special queryString param detected: "'+key+"="+value+'"'),/^\d+$/.test(value))value=parseInt(value,10);else if(/^true|false$/.test(value))value="true"===value;else if(/\[.*?\]/.test(value)&&"object"==typeof window.JSON)try{value=JSON.parse(value)}catch(e){PRIVATE.log('[WARN] Error evaluating special queryString param "'+key+"="+value+'": '+e.message)}else try{value=decodeURIComponent(value)}catch(e){PRIVATE.log('[WARN] Error decoding special queryString param "'+key+"="+value+'": '+e.message)}PRIVATE.settings[key.toUpperCase()]=value})},PRIVATE.Util={},PRIVATE.Util.includeJS=function(url,success,error){if("string"!=typeof url)throw new Error("Invalid url for Javascript include.");if(void 0!==error&&"function"!=typeof success)throw new Error('Invalid success handler (second parameter) for Javascript include "'+url+'". It must be a function.');if(void 0!==error&&"function"!=typeof error)throw new Error('Invalid error handler (second parameter) for Javascript include "'+url+'". It must be a function.');var head=document.getElementsByTagName("head")[0],tag=document.createElement("script");tag.type="text/javascript",tag.async=!0,tag.charset="utf-8",tag.onload=tag.onreadystatechange=function(){if(!this.readyState||"loaded"==this.readyState||"complete"==this.readyState){var details={src:this.src};this.onload=this.onreadystatechange=null;try{success.call(details,details)}catch(exception){exception.message="[Exceptions caught triggering error hadnler for request "+details.src+" Â» "+exception.message,error.call(details,details)}}},error&&tag.addEventListener&&tag.addEventListener("error",function(event){event={src:url,evt:event};error.call(event,event)},!0),tag.src=url,head.appendChild(tag)},PRIVATE.Util.FiF=(doc=document,(FiF={core:{settings:{placeholder:"",fifsrc:"iframe.html",adsrc:null,adcode:null,width:"",height:"",callback:function(){}},buildFiF:function(config){var fifBody,iframeModel,fifId,settings=FiF.core.settings=FiF.core.mergeObj(FiF.core.settings,config),config=doc,idocContent="",fif=null,fifHTML="";if(settings.adcode){fifBody='<!DOCTYPE html><html><body style="background-color:transparent;" id="FiF_container"><script>'+settings.adcode+"<\/script></body></html>",iframeModel='<iframe id="'+(fifId="fif_"+settings.placeholder)+'" style="width:100%;height:100%;border:none" vspace="0" hspace="0" allowtransparency="true" scrolling="no" frameborder="no" marginwidth="0" marginheight="0"></iframe>';try{config.getElementById(settings.placeholder).innerHTML=iframeModel,"function"==typeof settings.callback&&settings.callback()}catch(error){FiF.core.debug.errors.push({HumanReadable:"Error appendind Iframe in placeholder",error:error})}(idocContent=(fif=config.getElementById(fifId)).contentWindow.document).open(),idocContent.write(fifBody)}else settings.adsrc||settings.justrender?(iframeModel=settings.width?settings.width+"px":"100%",fifId=settings.height?settings.height+"px":"100%",fifHTML='<iframe id="fif_'+settings.placeholder+'" style="position:absolute" vspace="0" hspace="0" allowtransparency="true" scrolling="no" frameborder="no" marginwidth="0" marginheight="0"></iframe>',config.getElementById(settings.placeholder).innerHTML=fifHTML,fif=config.getElementById("fif_"+settings.placeholder),settings.meta&&(fif.meta=settings.meta),fif.src=settings.fifsrc,fif.setAttribute("seamless","seamless"),(fif.frameElement||fif).style.cssText="width: "+iframeModel+"; height: "+fifId+"; border: 0;margin:0;padding:0;borderWidth:0",settings.justrender||(fif.fif_src=settings.adsrc),fif.onload=function(){settings.callback()}):FiF.core.debug.errors.push("Neither src or code for ad were provided. Fix that! I do not do magic!")},mergeObj:function(o,ob){var z,obj={};for(z in ob)ob.hasOwnProperty(z)&&(obj[z]=ob[z]);return obj},debug:{errors:[]}},pub:{create:function(new_settings){FiF.core.buildFiF(new_settings)},showErrors:function(){window.console&&console.log(FiF.core.debug.errors)},reloadAll:function(){for(var iframes=document.getElementsByTagName("iframe"),if_length=iframes.length,fifid=null;0<if_length;)fifid=iframes[0].id,"/^fif_/".test(fifid)&&FiF.pub.reload(fifid.replace("fif_",""))},reload:function(fifid){document.getElementById("fif_"+fifid)?document.getElementById("fif_"+fifid).contentDocument.location.reload(!0):FiF.core.debug.errors.push("Error trying to reload an Iframe. The Friendly Iframe with id fif_"+fifid+" was not found.")}}}).pub),(PUBLIC_STATIC=AdManager=function(obj){this.getViewportAds=function(viewportN,forcePlatform){var context=this;return PRIVATE.settings.NO_ADS?Promise.resolve():PRIVATE.setup({tgmkey:PRIVATE.settings.tgmkey}).then(function(){var viewport=viewportN>PRIVATE.settings.VIEWPORT_MAX?PRIVATE.settings.VIEWPORT_ORDER[PRIVATE.calcViewportN(viewportN)]:viewportN,url=PRIVATE.getTagmanUrl("s"+viewportN)+"?key="+PRIVATE.settings.TGMKEY+".s"+viewport+"&area=s"+viewportN+"&direct=1";return PRIVATE.loadShowArea({type:"viewport",calculateArea:"s"+viewport,originalArea:viewportN,url:url,context:context},forcePlatform)})},this.createAds=function(conf){var returnedAds=[];if(!conf||"object"!=typeof conf||"[object Array]"!=Object.prototype.toString.call(conf.ADS)||!conf.ADS.length)throw new Error("Invalid ad configuration array");var current,adsLen=conf.ADS.length,i=0;if(0<adsLen)for(;i<adsLen;i+=1)(current=conf.ADS[i])&&"object"==typeof current?current.placeholder&&"string"==typeof current.placeholder&&document.getElementById(current.placeholder)?PRIVATE.pageAds[current.placeholder]?PRIVATE.log("[WARN] Discarting item passed to createdAds. Ad already created: "+current.placeholder):returnedAds.push(PRIVATE.render(current)):PRIVATE.log("[WARN] Discarting item passed to createdAds. Invalid placeholder: "+current.placeholder):PRIVATE.log("[WARN] Discarting item passed to createdAds. Invalid configuration object.");else PRIVATE.log("[ERROR] None DFP ads found");return Promise.all(returnedAds)},this.stickAd=function(conf){var context=this;if(PRIVATE.settings.NO_ADS)return Promise.resolve();function loadStickAd(){var stickTgmkey=(conf.tgmkey||PRIVATE.settings.TGMKEY)+"."+conf.area,url=PRIVATE.getTagmanUrl(conf.area)+"?key="+stickTgmkey+"&area="+conf.area+"&direct=1";PRIVATE.loadShowArea({type:"stick",calculateArea:stickTgmkey,originalArea:stickTgmkey,area:conf.area,url:url,context:context,customKW:conf.customKW}).then(function(obj){var ads=obj.ADS;return ads&&0<ads.length&&(ads[0].placeholder=conf.placeholder.id),obj.instance.createAds({tgmkey:"br",ADS:ads})})}if(!conf.onlyArea)return PRIVATE.setup({tgmkey:conf.tgmkey}).then(function(){loadStickAd()});loadStickAd()},this.multiTagsAd=function(conf){var context=this;return PRIVATE.settings.NO_ADS?Promise.resolve():PRIVATE.setup({tgmkey:conf.tgmkey}).then(function(){var stickTgmkey=(conf.tgmkey||PRIVATE.settings.TGMKEY)+"."+conf.area,url=PRIVATE.getTagmanUrl(conf.area)+"?key="+stickTgmkey+"&area="+conf.area+"&direct=1";return console.log("CALL AD: ",url),PRIVATE.loadShowArea({type:"stick",calculateArea:stickTgmkey,originalArea:stickTgmkey,area:conf.area,url:url,context:context})})}}).onError=function(p){},PUBLIC_STATIC.calcViewportN=function(n){var orderArray=PRIVATE.settings.VIEWPORT_ORDER,viewportMax=PRIVATE.settings.VIEWPORT_MAX;return!(!n||"number"!=typeof n)&&(n<=viewportMax?n:orderArray[PRIVATE.calcViewportN(n)])},PUBLIC_STATIC.reload=function(placeholderId){if(!placeholderId||"string"!=typeof placeholderId||"object"!=typeof PRIVATE.pageAds[placeholderId])throw new Error("Invalid placeholder id to reload");var conf=PRIVATE.pageAds[placeholderId];return new Promise(function(resolve,reject){var elem=document.getElementById(conf.placeholder),wrapper=document.getElementById(conf.placeholder+"--wrapper");elem?(elem.style&&elem.offsetHeight&&wrapper&&(wrapper.style.height=elem.offsetHeight+"px",setTimeout(function(){"mob"==PRIVATE.settings.PLATFORM&&conf.parallax&&conf.parallax.visible&&0<conf.parallax.height&&(wrapper.style.height=conf.parallax.height+"px")},1500)),conf.reloaded=!0,PRIVATE.render(conf)):reject("Invalid ad placeholder")})},PUBLIC_STATIC.debug=function(){PRIVATE.isDebug=!0},PUBLIC_STATIC.get=function(key){key=PRIVATE.attributes[key];return"function"==typeof key?key.call(this):key},PUBLIC_STATIC.set=function(key,value){key&&value&&(PRIVATE.attributes[key]=value,/setup_done_for_.+/.test(key)&&PRIVATE.setup({tgmkey:key.replace("setup_done_for_","")}))},PUBLIC_STATIC.getCurrentViewport=function(){return PRIVATE.curViewport},PUBLIC_STATIC.configure=function(obj){if(PRIVATE.log("[WARN] Public method configure was called..."),"object"!=typeof obj)throw"Invalid object configuration";obj.VIEWPORT_MAX&&0<obj.VIEWPORT_MAX&&(PRIVATE.settings.VIEWPORT_MAX=obj.VIEWPORT_MAX,PRIVATE.log("[WARN] Changing VIEWPORT_MAX to "+obj.VIEWPORT_MAX)),obj.DEFAULT_GOOGLE_TAG&&"string"==typeof obj.DEFAULT_GOOGLE_TAG&&(PRIVATE.settings.DEFAULT_GOOGLE_TAG=obj.DEFAULT_GOOGLE_TAG,PRIVATE.log("[WARN] Changing DEFAULT_GOOGLE_TAG to "+obj.DEFAULT_GOOGLE_TAG)),obj.DFP_TIMEOUT&&"number"==typeof obj.DFP_TIMEOUT&&(PRIVATE.settings.DFP_TIMEOUT=obj.DFP_TIMEOUT,PRIVATE.log("[WARN] Changing DFP_TIMEOUT to "+obj.DFP_TIMEOUT)),obj.TAGMAN_TIMEOUT&&"number"==typeof obj.TAGMAN_TIMEOUT&&(PRIVATE.settings.TAGMAN_TIMEOUT=obj.TAGMAN_TIMEOUT,PRIVATE.log("[WARN] Changing TAGMAN_TIMEOUT to "+obj.TAGMAN_TIMEOUT)),obj.PPI_TIMEOUT&&"number"==typeof obj.PPI_TIMEOUT&&(PRIVATE.settings.PPI_TIMEOUT=obj.PPI_TIMEOUT,PRIVATE.log("[WARN] Changing PPI_TIMEOUT to "+obj.PPI_TIMEOUT)),obj.THIRDPARTY_TIMEOUT&&"number"==typeof obj.THIRDPARTY_TIMEOUT&&(PRIVATE.settings.THIRDPARTY_TIMEOUT=obj.THIRDPARTY_TIMEOUT,PRIVATE.log("[WARN] Changing THIRDPARTY_TIMEOUT to "+obj.THIRDPARTY_TIMEOUT)),obj.DFP_IFRAME_INTERVAL_CHECKER&&"number"==typeof obj.DFP_IFRAME_INTERVAL_CHECKER&&(PRIVATE.settings.DFP_IFRAME_INTERVAL_CHECKER=obj.DFP_IFRAME_INTERVAL_CHECKER,PRIVATE.log("[WARN] Changing DFP_IFRAME_INTERVAL_CHECKER to "+obj.DFP_IFRAME_INTERVAL_CHECKER)),obj.htmlPath&&"string"==typeof obj.htmlPath&&(PRIVATE.env[PRIVATE.curEnv].htmlPath=obj.htmlPath,PRIVATE.log("[WARN] Changing htmlPath to "+obj.htmlPath)),obj.VIEWPORT_ORDER&&"object"==typeof obj.VIEWPORT_ORDER&&0<obj.VIEWPORT_ORDER.length&&(PRIVATE.settings.VIEWPORT_ORDER=obj.VIEWPORT_ORDER,PRIVATE.log("[WARN] Changing VIEWPORT_ORDER to "+obj.VIEWPORT_ORDER)),obj.TGMKEY&&(PRIVATE.tgmKeyHasChanged+=1,PRIVATE.lockTgmKeyChange?PRIVATE.log("[WARN] Attempt to change tgmkey was denied. Tgmkey parameter is present on the URL "+obj.TGMKEY):(PRIVATE.settings.TGMKEY=obj.TGMKEY,PRIVATE.log("[WARN] Changing TGMKEY to "+obj.TGMKEY))),obj.PLATFORM&&("mob"===obj.PLATFORM||"web"===obj.PLATFORM||"tab"===obj.PLATFORM||"responsive"===obj.PLATFORM?(PRIVATE.settings.PLATFORM=obj.PLATFORM,PRIVATE.log("[WARN] Changing PLATFORM to "+obj.PLATFORM)):PRIVATE.log("[ERROR] Invalid platform: "+obj.PLATFORM)),obj.TAGMAN_URL&&(PRIVATE.env[PRIVATE.curEnv].tagmanURL=obj.TAGMAN_URL,PRIVATE.settings.TAGMAN_URL=obj.TAGMAN_URL,PRIVATE.log("[WARN] Changing TAGMAN_URL to "+obj.TAGMAN_URL)),obj.ADS_METRICS_PUSH&&(obj.ADS_METRICS_PUSH&&"number"==typeof obj.ADS_METRICS_PUSH?(PRIVATE.settings.ADS_METRICS_PUSH=obj.ADS_METRICS_PUSH,PRIVATE.log("[WARN] Changing ADS_METRICS_PUSH to "+obj.ADS_METRICS_PUSH)):PRIVATE.log("[ERROR] Error changing ADS_METRICS_PUSH - Invalid parameter"))},PUBLIC_STATIC.defineAd=function(config,meta){var id="AdManager_"+Math.random().toString(32).slice(2);if(!config||"object"!=typeof config)throw"Invalid ad configuration";config.id=id,PRIVATE.log("[INFO] isolated rendering single call: "+JSON.stringify(config)),PRIVATE.ads[id]=config},PUBLIC_STATIC.outOfPage=function(conf){if(PRIVATE.settings.NO_ADS)return!1;PRIVATE.getGPT().then(function(){var elem,elemID="out-of-page-terra-ad";document.getElementById(elemID)||((elem=document.createElement("div")).setAttribute("id",elemID),document.getElementsByTagName("body")[0].appendChild(elem),window.googletag.cmd.push(function(){var slot=googletag.defineOutOfPageSlot(top.AdManager.get("google_tag"),elemID);slot.addService(googletag.pubads()),PRIVATE.setKeywords(slot,conf),window.googletag.pubads().refresh([slot]),window.googletag.display(elemID),window.googletag.enableServices()}))})},PUBLIC_STATIC.outOfPageInterstitial=function(conf){var regex=/ad_interstitial=(\w+)/.exec(document.cookie),removeAd=window.info_path&&1=={velvet:!0,"futuro-vivo":!0}[window.info_path.subchannel];if(regex&&"false"==regex[1]&&(removeAd=!0),PRIVATE.settings.NO_ADS)return!1;PRIVATE.getGPT().then(function(){window.googletag.cmd.push(function(){var slot;removeAd||(slot=window.googletag.defineOutOfPageSlot(top.AdManager.get("google_tag_interstitial"),window.googletag.enums.OutOfPageFormat.INTERSTITIAL))&&(slot.addService(window.googletag.pubads()),PRIVATE.setKeywords(slot,conf),window.googletag.pubads().setCentering(!0),window.googletag.pubads().enableSingleRequest(),window.googletag.pubads().refresh([slot]),window.googletag.display(slot),window.googletag.enableServices(),slot.setConfig({interstitial:{triggers:{unhideWindow:!removeAd}}}))})})},PUBLIC_STATIC.setup=function(obj){return new Promise(function(resolve,reject){!obj||obj.tgmKey?reject("Invalid tgmkey"):PRIVATE.setup(obj).then(function(){resolve()},function(){reject()})})},PUBLIC_STATIC.STATIC=PRIVATE,window.AdManager=PUBLIC_STATIC,PRIVATE.init(),"object"==typeof window.zaz&&"function"==typeof zaz.use&&zaz.use(function(pkg){try{pkg.define("mod.adManager",[],function(){return PUBLIC_STATIC})}catch(e){window.console&&window.console.warn&&window.console.warn('Exception registering "mod.adManager" on ZaZ Includer: '+e.message)}})}();