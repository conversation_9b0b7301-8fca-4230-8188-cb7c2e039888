#!/usr/bin/env python3
"""
🕷️ SPIDER COMPLETO - Descoberta Automática de TODOS os Links

Este spider rastreia automaticamente TODOS os links internos do site,
criando um mapa completo de todas as páginas existentes.
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import json
import time
from pathlib import Path
from datetime import datetime
import re


class SpiderCompleto:
    """Spider que descobre automaticamente todos os links do site."""
    
    def __init__(self, base_url, output_dir="spider_completo"):
        self.base_url = base_url.rstrip('/')
        self.domain = urlparse(base_url).netloc
        self.output_dir = Path(output_dir)
        
        # Controle de crawling
        self.discovered_urls = set()
        self.crawled_urls = set()
        self.failed_urls = set()
        self.to_crawl = set([base_url])
        
        # Dados coletados
        self.site_map = {
            'base_url': base_url,
            'domain': self.domain,
            'crawl_started': datetime.now().isoformat(),
            'pages': {},
            'link_structure': {},
            'url_patterns': {},
            'statistics': {}
        }
        
        # Configuração da sessão
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
        })
        
        # Criar diretórios
        self.output_dir.mkdir(parents=True, exist_ok=True)
        (self.output_dir / "pages").mkdir(exist_ok=True)
        (self.output_dir / "reports").mkdir(exist_ok=True)
    
    def crawl_site_completo(self, max_pages=100, delay=1):
        """Crawl completo do site descobrindo todos os links."""
        print(f"🕷️ SPIDER COMPLETO: {self.base_url}")
        print("="*60)
        
        crawl_count = 0
        
        while self.to_crawl and crawl_count < max_pages:
            url = self.to_crawl.pop()
            
            if url in self.crawled_urls:
                continue
            
            crawl_count += 1
            print(f"📄 [{crawl_count}] Crawling: {url}")
            
            try:
                # Crawl da página
                page_data = self.crawl_page(url)
                
                if page_data:
                    self.site_map['pages'][url] = page_data
                    self.crawled_urls.add(url)
                    
                    # Descobrir novos links
                    new_links = self.discover_links(page_data['links'])
                    print(f"   🔗 Descobertos {len(new_links)} novos links")
                    
                    # Adicionar à fila de crawling
                    for link in new_links:
                        if link not in self.crawled_urls and link not in self.failed_urls:
                            self.to_crawl.add(link)
                            self.discovered_urls.add(link)
                
                # Delay entre requests
                time.sleep(delay)
                
            except Exception as e:
                print(f"   ❌ Erro: {e}")
                self.failed_urls.add(url)
        
        # Finalizar análise
        self.finalize_analysis()
        
        print(f"\n✅ SPIDER COMPLETO CONCLUÍDO!")
        print(f"📄 Páginas crawled: {len(self.crawled_urls)}")
        print(f"🔗 URLs descobertas: {len(self.discovered_urls)}")
        print(f"❌ URLs com erro: {len(self.failed_urls)}")
        print(f"📁 Resultados em: {self.output_dir}")
    
    def crawl_page(self, url):
        """Crawl de uma página específica."""
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extrair dados da página
            page_data = {
                'url': url,
                'status_code': response.status_code,
                'title': self.extract_title(soup),
                'meta_description': self.extract_meta_description(soup),
                'meta_keywords': self.extract_meta_keywords(soup),
                'headings': self.extract_headings(soup),
                'links': self.extract_all_links(soup, url),
                'images': self.extract_images(soup, url),
                'forms': self.extract_forms(soup),
                'content_text': self.extract_text_content(soup),
                'word_count': self.count_words(soup),
                'html_size': len(response.content),
                'response_time': response.elapsed.total_seconds(),
                'crawled_at': datetime.now().isoformat(),
                'url_type': self.classify_url_type(url),
                'breadcrumbs': self.extract_breadcrumbs(soup),
                'navigation_menus': self.extract_navigation(soup)
            }
            
            # Salvar HTML da página
            filename = self.url_to_filename(url)
            with open(self.output_dir / "pages" / f"{filename}.html", 'w', encoding='utf-8') as f:
                f.write(str(soup))
            
            return page_data
            
        except Exception as e:
            print(f"   ❌ Erro crawling {url}: {e}")
            return None
    
    def extract_all_links(self, soup, base_url):
        """Extrair TODOS os links da página."""
        links = []
        
        # Links em tags <a>
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            if href:
                full_url = urljoin(base_url, href)
                links.append({
                    'url': full_url,
                    'text': link.get_text().strip(),
                    'title': link.get('title', ''),
                    'type': 'anchor',
                    'classes': link.get('class', []),
                    'target': link.get('target', '')
                })
        
        # Links em JavaScript (básico)
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                # Procurar por URLs em JavaScript
                js_urls = re.findall(r'["\']([^"\']*(?:\.html|/[^"\']*)["\'])', script.string)
                for url in js_urls:
                    if url and not url.startswith(('http', 'mailto:', 'tel:')):
                        full_url = urljoin(base_url, url.strip('"\''))
                        if self.is_internal_url(full_url):
                            links.append({
                                'url': full_url,
                                'text': '',
                                'title': '',
                                'type': 'javascript',
                                'classes': [],
                                'target': ''
                            })
        
        # Links em forms (action)
        for form in soup.find_all('form', action=True):
            action = form.get('action')
            if action:
                full_url = urljoin(base_url, action)
                if self.is_internal_url(full_url):
                    links.append({
                        'url': full_url,
                        'text': 'Form Action',
                        'title': '',
                        'type': 'form',
                        'classes': [],
                        'target': ''
                    })
        
        return links
    
    def discover_links(self, links):
        """Descobrir novos links internos."""
        new_links = set()
        
        for link in links:
            url = link['url']
            
            # Verificar se é link interno
            if self.is_internal_url(url):
                # Limpar URL (remover fragmentos, parâmetros desnecessários)
                clean_url = self.clean_url(url)
                
                if clean_url not in self.discovered_urls and clean_url not in self.crawled_urls:
                    new_links.add(clean_url)
        
        return new_links
    
    def is_internal_url(self, url):
        """Verificar se URL é interna."""
        parsed = urlparse(url)
        
        # URL relativa ou do mesmo domínio
        if not parsed.netloc or parsed.netloc == self.domain:
            return True
        
        return False
    
    def clean_url(self, url):
        """Limpar URL removendo fragmentos e parâmetros desnecessários."""
        parsed = urlparse(url)
        
        # Reconstruir URL sem fragmento
        clean_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        
        # Se não tem scheme, é relativa
        if not parsed.scheme:
            clean_url = urljoin(self.base_url, parsed.path)
        
        # Remover trailing slash duplo
        clean_url = clean_url.rstrip('/')
        if not clean_url.endswith('/') and not clean_url.split('/')[-1].count('.'):
            clean_url += '/'
        
        return clean_url
    
    def classify_url_type(self, url):
        """Classificar tipo da URL."""
        path = urlparse(url).path.lower()
        
        if not path or path == '/':
            return 'homepage'
        elif 'contato' in path:
            return 'contact'
        elif 'sobre' in path or 'about' in path:
            return 'about'
        elif 'servico' in path or 'service' in path:
            return 'service'
        elif 'projeto' in path or 'project' in path:
            return 'project'
        elif 'categoria' in path or 'category' in path:
            return 'category'
        elif 'blog' in path or 'artigo' in path:
            return 'blog'
        else:
            return 'page'
    
    def finalize_analysis(self):
        """Finalizar análise e gerar estatísticas."""
        # Estatísticas gerais
        stats = {
            'total_pages_crawled': len(self.crawled_urls),
            'total_urls_discovered': len(self.discovered_urls),
            'total_failed_urls': len(self.failed_urls),
            'crawl_completed_at': datetime.now().isoformat()
        }
        
        # Análise de tipos de URL
        url_types = {}
        for page_data in self.site_map['pages'].values():
            url_type = page_data['url_type']
            if url_type not in url_types:
                url_types[url_type] = 0
            url_types[url_type] += 1
        
        stats['url_types'] = url_types
        
        # Análise de estrutura de links
        link_structure = {}
        for url, page_data in self.site_map['pages'].items():
            internal_links = [link for link in page_data['links'] if self.is_internal_url(link['url'])]
            link_structure[url] = {
                'total_links': len(page_data['links']),
                'internal_links': len(internal_links),
                'external_links': len(page_data['links']) - len(internal_links)
            }
        
        # Padrões de URL
        url_patterns = self.analyze_url_patterns()
        
        # Adicionar aos dados do site
        self.site_map['statistics'] = stats
        self.site_map['link_structure'] = link_structure
        self.site_map['url_patterns'] = url_patterns
        
        # Salvar dados completos
        self.save_results()
    
    def analyze_url_patterns(self):
        """Analisar padrões de URL."""
        patterns = {
            'depth_levels': {},
            'common_paths': {},
            'file_extensions': {},
            'url_lengths': []
        }
        
        for url in self.crawled_urls:
            parsed = urlparse(url)
            path = parsed.path
            
            # Análise de profundidade
            depth = len([p for p in path.split('/') if p])
            if depth not in patterns['depth_levels']:
                patterns['depth_levels'][depth] = 0
            patterns['depth_levels'][depth] += 1
            
            # Caminhos comuns
            if path:
                first_segment = path.split('/')[1] if len(path.split('/')) > 1 else 'root'
                if first_segment not in patterns['common_paths']:
                    patterns['common_paths'][first_segment] = 0
                patterns['common_paths'][first_segment] += 1
            
            # Extensões de arquivo
            if '.' in path.split('/')[-1]:
                ext = path.split('.')[-1].lower()
                if ext not in patterns['file_extensions']:
                    patterns['file_extensions'][ext] = 0
                patterns['file_extensions'][ext] += 1
            
            # Comprimento das URLs
            patterns['url_lengths'].append(len(url))
        
        return patterns
    
    def save_results(self):
        """Salvar todos os resultados."""
        # Dados completos em JSON
        with open(self.output_dir / "reports" / "spider_completo.json", 'w', encoding='utf-8') as f:
            json.dump(self.site_map, f, indent=2, ensure_ascii=False)
        
        # Lista de todas as URLs descobertas
        all_urls = {
            'crawled_urls': list(self.crawled_urls),
            'discovered_urls': list(self.discovered_urls),
            'failed_urls': list(self.failed_urls),
            'pending_urls': list(self.to_crawl)
        }
        
        with open(self.output_dir / "reports" / "todas_urls.json", 'w', encoding='utf-8') as f:
            json.dump(all_urls, f, indent=2, ensure_ascii=False)
        
        # Relatório em Markdown
        self.generate_markdown_report()
        
        # Sitemap simples
        sitemap = []
        for url, page_data in self.site_map['pages'].items():
            sitemap.append({
                'url': url,
                'title': page_data['title'],
                'type': page_data['url_type'],
                'word_count': page_data['word_count'],
                'links_count': len(page_data['links'])
            })
        
        with open(self.output_dir / "reports" / "sitemap_completo.json", 'w', encoding='utf-8') as f:
            json.dump(sitemap, f, indent=2, ensure_ascii=False)
    
    def generate_markdown_report(self):
        """Gerar relatório em Markdown."""
        stats = self.site_map['statistics']
        
        report = f"""# 🕷️ SPIDER COMPLETO - {self.domain.upper()}

## 📊 RESUMO EXECUTIVO

- **🌐 Site Base**: {self.base_url}
- **📄 Páginas Crawled**: {stats['total_pages_crawled']}
- **🔗 URLs Descobertas**: {stats['total_urls_discovered']}
- **❌ URLs com Erro**: {stats['total_failed_urls']}
- **📅 Crawl Iniciado**: {self.site_map['crawl_started']}
- **📅 Crawl Finalizado**: {stats['crawl_completed_at']}

## 📋 TODAS AS PÁGINAS ENCONTRADAS

"""
        
        # Listar todas as páginas por tipo
        pages_by_type = {}
        for url, page_data in self.site_map['pages'].items():
            url_type = page_data['url_type']
            if url_type not in pages_by_type:
                pages_by_type[url_type] = []
            pages_by_type[url_type].append((url, page_data))
        
        for url_type, pages in pages_by_type.items():
            report += f"\n### 📂 {url_type.upper()} ({len(pages)} páginas)\n\n"
            for url, page_data in pages:
                report += f"- **{page_data['title'] or 'Sem título'}**\n"
                report += f"  - URL: {url}\n"
                report += f"  - Palavras: {page_data['word_count']}\n"
                report += f"  - Links: {len(page_data['links'])}\n\n"
        
        report += f"""
## 🔗 ANÁLISE DE LINKS

### Estrutura de Links Internos
"""
        
        for url, link_data in self.site_map['link_structure'].items():
            page_title = self.site_map['pages'][url]['title'] or 'Sem título'
            report += f"- **{page_title}**: {link_data['internal_links']} links internos, {link_data['external_links']} externos\n"
        
        report += f"""
## 📊 PADRÕES DE URL

### Profundidade das URLs
"""
        
        for depth, count in self.site_map['url_patterns']['depth_levels'].items():
            report += f"- Nível {depth}: {count} páginas\n"
        
        report += f"""
### Caminhos Mais Comuns
"""
        
        for path, count in self.site_map['url_patterns']['common_paths'].items():
            report += f"- /{path}: {count} páginas\n"
        
        # Salvar relatório
        with open(self.output_dir / "reports" / "relatorio_spider.md", 'w', encoding='utf-8') as f:
            f.write(report)
    
    # Métodos auxiliares de extração
    def extract_title(self, soup):
        title = soup.find('title')
        return title.get_text().strip() if title else ''
    
    def extract_meta_description(self, soup):
        meta = soup.find('meta', attrs={'name': 'description'})
        return meta.get('content', '').strip() if meta else ''
    
    def extract_meta_keywords(self, soup):
        meta = soup.find('meta', attrs={'name': 'keywords'})
        return meta.get('content', '').strip() if meta else ''
    
    def extract_headings(self, soup):
        headings = []
        for level in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            for heading in soup.find_all(level):
                headings.append({
                    'level': level,
                    'text': heading.get_text().strip()
                })
        return headings
    
    def extract_images(self, soup, base_url):
        images = []
        for img in soup.find_all('img'):
            src = img.get('src')
            if src:
                full_url = urljoin(base_url, src)
                images.append({
                    'url': full_url,
                    'alt': img.get('alt', ''),
                    'title': img.get('title', '')
                })
        return images
    
    def extract_forms(self, soup):
        forms = []
        for form in soup.find_all('form'):
            inputs = []
            for inp in form.find_all(['input', 'textarea', 'select']):
                inputs.append({
                    'type': inp.get('type', inp.name),
                    'name': inp.get('name', ''),
                    'placeholder': inp.get('placeholder', '')
                })
            forms.append({
                'action': form.get('action', ''),
                'method': form.get('method', 'get'),
                'inputs': inputs
            })
        return forms
    
    def extract_text_content(self, soup):
        # Remover scripts e styles
        for script in soup(["script", "style"]):
            script.decompose()
        return soup.get_text().strip()
    
    def count_words(self, soup):
        text = self.extract_text_content(soup)
        return len(text.split()) if text else 0
    
    def extract_breadcrumbs(self, soup):
        breadcrumbs = []
        # Procurar por breadcrumbs comuns
        breadcrumb_selectors = [
            '.breadcrumb', '.breadcrumbs', '[aria-label="breadcrumb"]',
            '.nav-breadcrumb', '#breadcrumb'
        ]
        
        for selector in breadcrumb_selectors:
            breadcrumb_nav = soup.select_one(selector)
            if breadcrumb_nav:
                links = breadcrumb_nav.find_all('a')
                for link in links:
                    breadcrumbs.append({
                        'text': link.get_text().strip(),
                        'url': link.get('href', '')
                    })
                break
        
        return breadcrumbs
    
    def extract_navigation(self, soup):
        navigation = []
        nav_elements = soup.find_all('nav')
        
        for nav in nav_elements:
            nav_links = []
            for link in nav.find_all('a'):
                nav_links.append({
                    'text': link.get_text().strip(),
                    'url': link.get('href', ''),
                    'classes': link.get('class', [])
                })
            
            if nav_links:
                navigation.append({
                    'type': nav.get('class', ['unknown'])[0] if nav.get('class') else 'unknown',
                    'links': nav_links
                })
        
        return navigation
    
    def url_to_filename(self, url):
        """Converter URL para nome de arquivo."""
        parsed = urlparse(url)
        path = parsed.path.strip('/')
        
        if not path:
            return 'index'
        
        # Substituir caracteres problemáticos
        filename = path.replace('/', '_').replace('?', '_').replace('&', '_')
        return filename[:100]  # Limitar tamanho


def main():
    """Função principal."""
    print("🕷️ SPIDER COMPLETO - Descoberta Automática de Links")
    print("="*60)
    
    # Configuração
    base_url = "https://chatbot2-flame-beta.vercel.app"
    max_pages = 20  # Limite de segurança
    delay = 2  # Delay entre requests (segundos)
    
    print(f"🎯 Site: {base_url}")
    print(f"📄 Máximo de páginas: {max_pages}")
    print(f"⏱️ Delay entre requests: {delay}s")
    
    # Criar spider e executar
    spider = SpiderCompleto(base_url, "chatbot_multisocios_spider")
    spider.crawl_site_completo(max_pages=max_pages, delay=delay)
    
    print(f"\n🎉 SPIDER COMPLETO FINALIZADO!")
    print(f"📁 Resultados em: chatbot_multisocios_spider/")
    print(f"📊 Relatório: chatbot_multisocios_spider/reports/relatorio_spider.md")
    print(f"🔗 Todas URLs: chatbot_multisocios_spider/reports/todas_urls.json")


if __name__ == "__main__":
    main()
