#!/usr/bin/env python3
"""
🎬 LIBRAS DOWNLOAD MONITOR
Monitor em tempo real do progresso de download de vídeos de Libras
Roda em background e permite usar o prompt para outras tarefas
"""

import time
import json
import os
import sys
from datetime import datetime
from pathlib import Path
import subprocess
import threading
import signal

class LibrasMonitor:
    def __init__(self):
        self.running = True
        self.last_update = None
        self.start_time = datetime.now()
        self.log_file = "libras_downloader_complete.log"
        self.progress_file = "libras_videos_complete/progress/monitor_status.json"
        self.terminal_id = None
        
    def find_running_process(self):
        """Encontra o processo de download em execução"""
        try:
            # Verifica se há processo Python executando o downloader
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True, shell=True)
            if 'python.exe' in result.stdout:
                return True
        except:
            pass
        return False
    
    def parse_log_progress(self):
        """Extrai progresso do arquivo de log"""
        if not os.path.exists(self.log_file):
            return None
            
        try:
            with open(self.log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            # Procura pela última linha de progresso
            for line in reversed(lines):
                if "Página" in line and "vídeos encontrados" in line:
                    # Extrai página atual
                    if "Página " in line:
                        parts = line.split("Página ")[1].split(":")
                        if len(parts) > 0:
                            page_info = parts[0].strip()
                            if "/" in page_info:
                                current, total = page_info.split("/")
                                return {
                                    "current_page": int(current),
                                    "total_pages": int(total),
                                    "phase": "FASE 1 - Extraindo Links",
                                    "timestamp": datetime.now().isoformat()
                                }
                elif "FASE 2" in line and "DOWNLOAD" in line:
                    return {
                        "phase": "FASE 2 - Baixando Vídeos",
                        "timestamp": datetime.now().isoformat()
                    }
                elif "CONCLUÍDO" in line or "FINALIZADO" in line:
                    return {
                        "phase": "CONCLUÍDO",
                        "timestamp": datetime.now().isoformat()
                    }
        except Exception as e:
            return {"error": str(e)}
        
        return None
    
    def get_folder_stats(self):
        """Verifica estatísticas das pastas de download"""
        stats = {}
        
        # Pasta de vídeos (verificar ambas as pastas)
        videos_dirs = [
            Path("libras_videos_complete/videos"),
            Path("libras_videos_fixed/videos")
        ]

        total_videos = 0
        total_size = 0

        for videos_dir in videos_dirs:
            if videos_dir.exists():
                video_files = list(videos_dir.glob("*.mp4"))
                total_videos += len(video_files)
                total_size += sum(f.stat().st_size for f in video_files if f.exists())

        if total_videos > 0:
            stats["videos"] = {
                "count": total_videos,
                "size_mb": round(total_size / (1024*1024), 1)
            }
        
        # Pasta de progresso
        progress_dir = Path("libras_videos_complete/progress")
        if progress_dir.exists():
            progress_files = list(progress_dir.glob("*.json"))
            stats["progress_files"] = len(progress_files)
            
        return stats
    
    def display_status(self, progress, stats):
        """Exibe status formatado"""
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print("🎬 " + "="*60)
        print("   LIBRAS DOWNLOAD MONITOR - TEMPO REAL")
        print("="*64)
        
        if progress:
            if "current_page" in progress:
                current = progress["current_page"]
                total = progress["total_pages"]
                percent = (current / total) * 100
                
                # Barra de progresso
                bar_length = 30
                filled = int(bar_length * current / total)
                bar = "█" * filled + "░" * (bar_length - filled)
                
                print(f"📊 FASE 1: EXTRAINDO LINKS")
                print(f"   Página {current}/{total} ({percent:.1f}%)")
                print(f"   [{bar}]")
                
                # Estimativa de tempo
                elapsed = (datetime.now() - self.start_time).total_seconds()
                if current > 0:
                    time_per_page = elapsed / current
                    remaining_pages = total - current
                    eta_seconds = remaining_pages * time_per_page
                    eta_minutes = int(eta_seconds / 60)
                    print(f"   ⏱️  ETA: ~{eta_minutes} minutos")
                
            elif progress.get("phase") == "FASE 2 - Baixando Vídeos":
                print(f"📥 FASE 2: BAIXANDO VÍDEOS")
                if stats.get("videos"):
                    count = stats["videos"]["count"]
                    size = stats["videos"]["size_mb"]
                    print(f"   Vídeos baixados: {count}")
                    print(f"   Tamanho total: {size} MB")
                    
            elif progress.get("phase") == "CONCLUÍDO":
                print(f"✅ DOWNLOAD CONCLUÍDO!")
                if stats.get("videos"):
                    count = stats["videos"]["count"]
                    size = stats["videos"]["size_mb"]
                    print(f"   Total de vídeos: {count}")
                    print(f"   Tamanho final: {size} MB")
        
        # Estatísticas gerais
        if stats:
            print(f"\n📁 ESTATÍSTICAS:")
            if "videos" in stats:
                print(f"   Vídeos: {stats['videos']['count']} ({stats['videos']['size_mb']} MB)")
            if "progress_files" in stats:
                print(f"   Arquivos de progresso: {stats['progress_files']}")
        
        # Status do processo
        process_running = self.find_running_process()
        status_icon = "🟢" if process_running else "🔴"
        status_text = "EXECUTANDO" if process_running else "PARADO"
        print(f"\n{status_icon} Status: {status_text}")
        
        # Timestamp
        print(f"🕐 Última atualização: {datetime.now().strftime('%H:%M:%S')}")
        
        print("\n" + "="*64)
        print("💡 Este monitor roda em background.")
        print("   Você pode usar o prompt normalmente!")
        print("   Pressione Ctrl+C para parar o monitor.")
        print("="*64)
    
    def monitor_loop(self):
        """Loop principal de monitoramento"""
        while self.running:
            try:
                progress = self.parse_log_progress()
                stats = self.get_folder_stats()
                self.display_status(progress, stats)
                
                # Salva status para outros processos
                status_data = {
                    "progress": progress,
                    "stats": stats,
                    "timestamp": datetime.now().isoformat(),
                    "running": self.find_running_process()
                }
                
                # Cria diretório se não existir
                os.makedirs(os.path.dirname(self.progress_file), exist_ok=True)
                
                with open(self.progress_file, 'w', encoding='utf-8') as f:
                    json.dump(status_data, f, indent=2, ensure_ascii=False)
                
                # Verifica se processo terminou
                if not self.find_running_process() and progress and progress.get("phase") == "CONCLUÍDO":
                    print("\n🎉 DOWNLOAD FINALIZADO! Monitor será encerrado em 30 segundos...")
                    time.sleep(30)
                    break
                
                time.sleep(5)  # Atualiza a cada 5 segundos
                
            except KeyboardInterrupt:
                print("\n\n⏹️  Monitor interrompido pelo usuário.")
                break
            except Exception as e:
                print(f"\n❌ Erro no monitor: {e}")
                time.sleep(10)
        
        self.running = False

def main():
    """Função principal"""
    print("🎬 Iniciando LIBRAS Download Monitor...")
    print("   Monitor em tempo real do progresso de download")
    print("   Pressione Ctrl+C para parar\n")
    
    monitor = LibrasMonitor()
    
    try:
        monitor.monitor_loop()
    except KeyboardInterrupt:
        print("\n⏹️  Monitor encerrado.")

if __name__ == "__main__":
    main()
