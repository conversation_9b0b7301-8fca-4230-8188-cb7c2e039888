#!/usr/bin/env python3
"""
LIBRAS VIDEO DOWNLOADER - VERSAO CORRIGIDA PARA WINDOWS
Download direto dos videos de Libras sem problemas de Unicode
"""

import requests
import json
import os
import time
from pathlib import Path
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor
from urllib.parse import urljoin
import logging

# Configurar logging sem emojis
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('libras_downloader_fixed.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LibrasDownloaderFixed:
    def __init__(self):
        self.base_url = "https://libras.cin.ufpe.br"
        self.output_dir = Path("libras_videos_fixed")
        self.videos_dir = self.output_dir / "videos"
        self.progress_dir = self.output_dir / "progress"
        
        # Criar diretórios
        self.videos_dir.mkdir(parents=True, exist_ok=True)
        self.progress_dir.mkdir(parents=True, exist_ok=True)
        
        # Session para requests
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        self.stats = {
            'videos_downloaded': 0,
            'videos_skipped': 0,
            'errors': 0,
            'total_size_mb': 0
        }
    
    def load_video_links(self):
        """Carrega links dos videos dos arquivos JSON existentes"""
        logger.info("Carregando links dos videos dos arquivos de progresso...")

        all_links = []
        # Usar a pasta correta onde os arquivos foram salvos
        original_progress_dir = Path("libras_videos_complete/progress")
        progress_files = list(original_progress_dir.glob("page_*.json"))
        
        if not progress_files:
            logger.error("Nenhum arquivo de progresso encontrado!")
            logger.info("Execute primeiro: python tools/libras_downloader_complete.py")
            return []
        
        for progress_file in sorted(progress_files):
            try:
                with open(progress_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    all_links.extend(data.get('video_links', []))
            except Exception as e:
                logger.error(f"Erro ao ler {progress_file}: {e}")
        
        logger.info(f"Total de links carregados: {len(all_links)}")
        return all_links
    
    def extract_video_url_from_page(self, video_link):
        """Extrai URL real do video de uma pagina de sinal"""
        try:
            from bs4 import BeautifulSoup
            import re
            
            response = self.session.get(video_link['full_url'], timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            source_tags = soup.find_all('source')
            
            if source_tags:
                # Determinar qual source usar baseado no interprete
                interpreter_match = re.search(r'#interpreter_(\d+)', video_link['full_url'])
                interpreter_num = int(interpreter_match.group(1)) if interpreter_match else 1
                
                # Os videos estao em ordem: interpreter_3, interpreter_2, interpreter_1
                source_index = 3 - interpreter_num
                
                if 0 <= source_index < len(source_tags):
                    video_url = source_tags[source_index].get('src')
                else:
                    video_url = source_tags[0].get('src')  # Fallback
                
                if video_url:
                    return urljoin(self.base_url, video_url)
            
            return None
            
        except Exception as e:
            logger.error(f"Erro ao extrair video de {video_link['full_url']}: {e}")
            return None
    
    def download_single_video(self, video_link):
        """Baixa um video especifico"""
        try:
            # Extrair URL do video
            video_url = self.extract_video_url_from_page(video_link)
            
            if not video_url:
                logger.warning(f"URL nao encontrada para {video_link['signal_name']} - Articulador {video_link['articulador']}")
                self.stats['errors'] += 1
                return False
            
            # Nome do arquivo
            safe_name = video_link['signal_name'].replace(' ', '_').replace('/', '_')
            filename = f"{safe_name}_articulador_{video_link['articulador']}.mp4"
            filepath = self.videos_dir / filename
            
            # Verificar se ja existe
            if filepath.exists():
                logger.info(f"SKIP: {filename} (ja existe)")
                self.stats['videos_skipped'] += 1
                return True
            
            # Download
            logger.info(f"BAIXANDO: {filename}")
            response = self.session.get(video_url, stream=True, timeout=30)
            response.raise_for_status()
            
            total_size = 0
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        total_size += len(chunk)
            
            size_mb = total_size / (1024 * 1024)
            self.stats['videos_downloaded'] += 1
            self.stats['total_size_mb'] += size_mb
            
            logger.info(f"OK: {filename} ({size_mb:.1f} MB)")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao baixar {video_link['signal_name']}: {e}")
            self.stats['errors'] += 1
            return False
    
    def download_all_videos(self, max_workers=2):
        """Baixa todos os videos usando threading"""
        video_links = self.load_video_links()
        
        if not video_links:
            logger.error("Nenhum link de video encontrado!")
            return
        
        logger.info(f"INICIANDO DOWNLOAD DE {len(video_links)} VIDEOS")
        logger.info(f"Pasta de destino: {self.videos_dir}")
        logger.info(f"Workers: {max_workers}")
        
        start_time = time.time()
        
        # Download com threading
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(self.download_single_video, link) for link in video_links]
            
            # Processar resultados
            for i, future in enumerate(futures):
                try:
                    future.result()
                    
                    # Progress report a cada 50 videos
                    if (i + 1) % 50 == 0:
                        elapsed = time.time() - start_time
                        rate = (i + 1) / elapsed
                        eta = (len(video_links) - i - 1) / rate if rate > 0 else 0
                        
                        logger.info(f"PROGRESSO: {i+1}/{len(video_links)} videos processados")
                        logger.info(f"Taxa: {rate:.1f} videos/seg, ETA: {eta/60:.1f} min")
                        logger.info(f"Baixados: {self.stats['videos_downloaded']}, Erros: {self.stats['errors']}")
                        
                except Exception as e:
                    logger.error(f"Erro no future {i}: {e}")
        
        # Relatorio final
        elapsed = time.time() - start_time
        logger.info("="*60)
        logger.info("DOWNLOAD CONCLUIDO!")
        logger.info(f"Videos baixados: {self.stats['videos_downloaded']}")
        logger.info(f"Videos pulados: {self.stats['videos_skipped']}")
        logger.info(f"Erros: {self.stats['errors']}")
        logger.info(f"Tamanho total: {self.stats['total_size_mb']:.1f} MB")
        logger.info(f"Tempo total: {elapsed/60:.1f} minutos")
        logger.info(f"Pasta: {self.videos_dir}")
        logger.info("="*60)

def main():
    """Funcao principal"""
    print("LIBRAS VIDEO DOWNLOADER - VERSAO CORRIGIDA")
    print("==========================================")
    print("Este script baixa os videos usando os links ja coletados")
    print()
    
    # Verificar se existem arquivos de progresso
    progress_dir = Path("libras_videos_complete/progress")
    if not progress_dir.exists() or not list(progress_dir.glob("page_*.json")):
        print("ERRO: Arquivos de progresso nao encontrados!")
        print("Execute primeiro: python tools/libras_downloader_complete.py")
        print("para coletar os links dos videos.")
        return
    
    confirm = input("Deseja iniciar o download dos videos? (s/N): ").lower().strip()
    if confirm != 's':
        print("Download cancelado.")
        return
    
    downloader = LibrasDownloaderFixed()
    downloader.download_all_videos(max_workers=3)

if __name__ == "__main__":
    main()
