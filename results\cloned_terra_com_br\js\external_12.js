/*! zaz-app-t360-editorial-table - v1.0.0 - 19/02/2025 -- 8:47pm */

zaz.use(function(pkg){"use strict";var console=pkg.console,dictFactory;pkg.factoryManager.get("dict").create({name:"t360EditorialTable",version:"1.0.0",state:"ok",extends:[],langs:{global:{termWithPlural:["plural","singular"]},pt:{term:"Termo em Português"},es:{term:"Termo in Español"},en:{term:"Term in English"},"es-AR":{term:"Termo en Argentina"}}})}),zaz.use(function appT360EditorialTable(pkg){"use strict";var console=pkg.console,appFactory,STATIC_PUBLIC=null,STATIC_PRIVATE={};pkg.factoryManager.get("app").create({name:"t360.editorialTable",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/t360-editorial-table",source:"http://github.tpn.terra.com/Terra/t360-editorial-table",description:"Just another app",tests:"http://s1.trrsf.com/fe/t360-editorial-table/tests/index.htm?zaz[env]=tests",dependencies:["mod.adManager"],dictionaries:["t360EditorialTable"],templates:{},expects:{},setup:function(STATIC_PUBLIC,PUBLIC,__shared){},init:function(data,__shared){var PRIVATE={},PUBLIC=this;return PRIVATE.bindMetrics=function(){var elemModuleItems=data.container.querySelectorAll(".card-subject-modules__title");window.tga.event("subject-modules","click",elemModuleItems)},data&&data.container&&PRIVATE.bindMetrics(),PRIVATE.configureAdManager=function(){PRIVATE.tgmKey=pkg.context.page.get("tgmkey")||window.tgmKey||"br.homepage.home",PRIVATE.promiseAdManager=pkg.require(["mod.adManager"],function(AdManager){AdManager.configure({TGMKEY:PRIVATE.tgmKey,PLATFORM:"mob"!==pkg.context.platform.get("type")?"web":"mob"})})},PRIVATE.loadFloater=function(){var areaName="floater",elemSubjectModule=data.container.querySelector(".table-editorial__line.subject-modules"),elemFloater=document.createElement("div");elemFloater.id="ad-floater-container",elemFloater.className="ad-floater hide";var elemFloaterContainer=document.createElement("div");elemFloaterContainer.id="ad-floater",elemFloaterContainer.className="ad-floater__content",elemFloater.appendChild(elemFloaterContainer);var elemCloseButton=document.createElement("span");elemCloseButton.className="ad-floater__close-btn",elemFloater.appendChild(elemCloseButton),elemCloseButton.addEventListener("click",function(){elemFloater.classList.add("hide"),elemFloater.innerHTML=""}),window.addEventListener("message",function(b){-1!=b.origin.indexOf("googlesyndication.com")&&"videofloater"==b.data&&elemFloater.classList.add("hide")},!1),elemSubjectModule&&(elemSubjectModule.appendChild(elemFloater),"mob"===pkg.context.platform.get("type")&&(areaName="floatermob"),window.googletag.cmd.push(function(){window.googletag.pubads().addEventListener("slotRenderEnded",function(event){var containerId;event.slot&&("ad-floater"!=event.slot.getSlotElementId()||event.isEmpty||(elemFloater.className="ad-floater"))})}),PRIVATE.objAdmanager.stickAd({placeholder:elemFloaterContainer,area:areaName,tgmkey:PRIVATE.tgmKey}).catch(function(e){elemFloater.style.display="none"}))},PRIVATE.loadSFlex=function(){var adSFlex=data.container.querySelector("#ad-sflex"),observerSFlex;adSFlex&&new IntersectionObserver(function callbackSFlex(entries,observer){entries.forEach(function(entry){entry.isIntersecting&&(console.log("##### observer - callbackSFlex target",entry.target),PRIVATE.objAdmanager.stickAd({placeholder:entry.target,area:"sflex",tgmkey:PRIVATE.tgmKey}).catch(function(e){entry.target.style.display="none"}),observer.disconnect())})},{rootMargin:"0px 0px 100px 0px",threshold:1}).observe(adSFlex)},!0===pkg.context.page.get("isHome")&&(PRIVATE.configureAdManager(),PRIVATE.promiseAdManager.then(function(dependencies){PRIVATE.objAdmanager=new dependencies.mod.adManager,PRIVATE.loadFloater(),"mob"===pkg.context.platform.get("type")&&PRIVATE.loadSFlex()})),this},teardown:function(why,__static,__proto,__shared){}})});