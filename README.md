# 🕷️ WebScraper Enterprise 4.0

> **Sistema Avançado de Web Scraping, Análise de Frontend e Download de Mídia**
> Ferramenta profissional para extração, clonagem e análise completa de sites web + Download especializado de vídeos

[![Python](https://img.shields.io/badge/Python-3.10+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-Latest-green.svg)](https://fastapi.tiangolo.com)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🎯 **Visão Geral**

O **WebScraper Enterprise 4.0** é uma suíte completa de ferramentas para:

- 🕷️ **Spider Automático**: Descoberta inteligente de TODOS os links de um site
- 🎨 **Frontend Cloner**: Clonagem completa de frontend (HTML, CSS, JS, imagens)
- 🗺️ **Site Mapper**: Mapeamento profundo e análise estrutural
- 📊 **Análise Avançada**: Insights de SEO, performance e conteúdo
- 🎬 **Libras Video Downloader**: Sistema especializado para download de vídeos de Libras
- 🏢 **Uso Empresarial**: Coleta incremental, métricas e orquestração

## ⭐ **Principais Características**

### 🚀 **Ferramentas de Análise**
- **Spider Completo**: Descobre automaticamente todas as páginas de um site
- **Frontend Cloner**: Extrai HTML, CSS, JavaScript, imagens e fontes
- **Site Mapper**: Análise profunda de estrutura, SEO e conteúdo
- **Libras Video Downloader**: Download completo de vídeos de Libras (4089+ vídeos)
- **Video Organizer**: Organização inteligente por articulador, categoria e alfabética
- **Visualizadores**: Interfaces amigáveis para análise de resultados

### 🏢 **Recursos Empresariais**
- **Coleta Incremental**: Sistema inteligente com ETag e Last-Modified
- **Armazenamento Híbrido**: SQLite (dev) + PostgreSQL (prod)
- **Storage Distribuído**: Suporte S3/MinIO para arquivos grandes
- **Métricas em Tempo Real**: Prometheus + Grafana
- **Orquestração**: Fluxos automatizados com Prefect
- **API REST**: Interface completa para integração
- **Dashboard Web**: Monitoramento visual em tempo real

### 🔧 **Tecnologias**
- **Backend**: Python 3.10+, FastAPI, SQLAlchemy
- **Frontend**: HTML5, CSS3, JavaScript moderno
- **Banco de Dados**: SQLite, PostgreSQL
- **Cache**: Redis
- **Storage**: S3, MinIO
- **Monitoramento**: Prometheus, Grafana
- **Orquestração**: Prefect
- **Containerização**: Docker, Kubernetes

## 📁 **Estrutura do Projeto**

```
webscraper/
├── 🛠️ tools/                   # Ferramentas principais
│   ├── spider_completo.py      # 🕷️ Spider automático
│   ├── frontend_cloner.py      # 🎨 Clonador de frontend
│   ├── site_mapper_completo.py # 🗺️ Mapeador profundo
│   ├── libras_downloader_complete.py # 🎬 Download completo Libras
│   ├── libras_video_organizer.py     # 🗂️ Organizador de vídeos
│   └── visualizar_*.py         # 📊 Visualizadores
│
├── 📝 examples/                # Exemplos práticos
│   ├── exemplo_funcionando.py  # ✅ Exemplo básico
│   ├── api_simples.py          # 🌐 API de exemplo
│   └── test_*.py               # 🧪 Testes diversos
│
├── 📊 results/                 # Resultados de análise
│   ├── cloned_terra_com_br/    # 🌍 Terra.com.br clonado
│   ├── cloned_35mm-one_vercel_app/ # 📸 Site fotografia
│   ├── pvp_spider_completo/    # 🏗️ Análise PVP Projects
│   └── libras_videos_complete/ # 🎬 Vídeos de Libras baixados
│
├── 📚 src/                     # Código fonte principal
│   ├── api/                    # 🌐 API FastAPI
│   ├── core/                   # ⚙️ Funcionalidades core
│   ├── crawl/                  # 🕷️ Engines de crawling
│   └── web/                    # 🖥️ Interface web
│
├── 📖 docs/                    # Documentação completa
├── 🗄️ data/                    # Dados persistentes
├── 🐳 docker/                  # Configurações Docker
└── ☸️ k8s/                     # Kubernetes manifests
```

## 🚀 **Instalação Rápida**

### 📦 **Método 1: Docker (Recomendado)**

```bash
# Clone o repositório
git clone <repository-url>
cd webscraper

# Inicie todos os serviços
docker-compose up -d

# Acesse o dashboard
open http://localhost:8080
```

### 🐍 **Método 2: Python Local**

```bash
# Clone e configure
git clone <repository-url>
cd webscraper

# Instale dependências
pip install -e .

# Configure banco
alembic upgrade head

# Execute exemplo
python examples/exemplo_funcionando.py
```

## 🎯 **Uso das Ferramentas**

### 🕷️ **Spider Completo - Descoberta Automática**

```bash
# Descobrir TODOS os links de um site
python tools/spider_completo.py

# Resultado: Mapeamento completo com 37+ páginas descobertas
```

**Exemplo de resultado:**
- ✅ **37 páginas** descobertas automaticamente
- 🔗 **232 URLs** únicas encontradas
- 📊 **Análise completa** de estrutura e conteúdo

### 🎨 **Frontend Cloner - Clonagem Completa**

```bash
# Clonar frontend completo
python tools/frontend_cloner.py

# Digite a URL quando solicitado
# Resultado: Site completo clonado localmente
```

**O que é extraído:**
- 📄 **HTML** completo e estruturado
- 🎨 **CSS** (externos + inline)
- ⚡ **JavaScript** (externos + inline)
- 🖼️ **Imagens** em alta qualidade
- 🔤 **Fontes** e tipografia
- 📊 **Estrutura** analisada

### 🗺️ **Site Mapper - Análise Profunda**

```bash
# Análise profunda de site
python tools/site_mapper_completo.py

# Resultado: Relatório completo de SEO, estrutura e conteúdo
```

### 🎬 **Libras Video Downloader - NOVO!**

```bash
# Download completo de TODOS os vídeos de Libras
python tools/libras_downloader_complete.py

# Resultado: ~4089 vídeos, 1364 sinais, 69 páginas processadas
```

**Funcionalidades do Libras Downloader:**
- 📥 **Download Completo**: Todos os 4089+ vídeos da base V-LIBRASIL
- 🗂️ **Organização Inteligente**: Por articulador, alfabética e categoria
- 📊 **Relatórios Detalhados**: Estatísticas e catálogos HTML
- ⚡ **Download Paralelo**: Sistema otimizado com rate limiting
- 🔄 **Retry Automático**: Recuperação de falhas e verificação de integridade

```bash
# Organizar vídeos baixados
python tools/libras_video_organizer.py

# Resultado: Estrutura organizada + catálogo HTML navegável
```

### 📊 **Visualizadores**

```bash
# Visualizar clonagens
python tools/visualizar_clonagem.py

# Análise completa de site
python tools/visualizar_site_completo.py

# Ver dados do banco
python tools/ver_dados.py
```

## 🌟 **Casos de Uso Reais**

### 📸 **Site de Fotografia (35mm)**
- **Clonado**: https://35mm-one.vercel.app/
- **Resultado**: Portfolio minimalista com 8 fotos profissionais
- **Tecnologia**: Astro v4.15.4, design responsivo

### 🏗️ **Site de Engenharia (PVP Projects)**
- **Analisado**: https://pvpprojects.netlify.app/
- **Descoberto**: 37 páginas, 17 projetos, 4 artigos técnicos
- **Insights**: 175K+ palavras, estrutura SEO otimizada

### 🌍 **Portal de Notícias (Terra)**
- **Clonado**: https://www.terra.com.br/
- **Extraído**: 24 CSS, 43 JS, 33 imagens
- **Complexidade**: Site de alta complexidade mapeado

### 🎬 **Base de Dados V-LIBRASIL (NOVO!)**
- **Site**: https://libras.cin.ufpe.br
- **Processado**: 69 páginas, 1364 sinais únicos
- **Baixado**: 4089+ vídeos de Libras (3 articuladores por sinal)
- **Organizado**: Por articulador, alfabética e categoria semântica
- **Tamanho**: ~2-5GB de vídeos em alta qualidade
- **Formato**: MP4, organizados com nomenclatura padronizada

## 📊 **Recursos Empresariais**

### 🔄 **Coleta Incremental**
```python
# Configuração automática de ETag e Last-Modified
ENABLE_INCREMENTAL=true
USE_ETAG=true
USE_LAST_MODIFIED=true
```

### 📈 **Métricas e Monitoramento**
- **Prometheus**: http://localhost:8000/metrics
- **Grafana**: http://localhost:3000
- **Dashboard**: http://localhost:8080

### 🌐 **API REST Completa**
```bash
# Documentação interativa
open http://localhost:8000/docs

# Endpoints principais
GET /pages          # Listar páginas
POST /crawl         # Iniciar crawling
GET /stats          # Estatísticas
```

## 🧪 **Testes e Qualidade**

```bash
# Executar todos os testes
pytest

# Testes com cobertura
pytest --cov=src

# Testes específicos
pytest tests/test_fase2.py
```

**Resultados dos Testes:**
- ✅ **Fase 1**: 6/6 testes passaram
- ✅ **Fase 2**: 6/6 testes passaram  
- ✅ **Fase 3**: Implementação completa

## 🎓 **Exemplos Práticos**

### 🔰 **Iniciante - Scraping Básico**
```bash
python examples/exemplo_funcionando.py
```

### 🎬 **Libras - Download de Vídeos**
```bash
python examples/exemplo_libras_completo.py
```

### 🚀 **Avançado - API Completa**
```bash
python examples/api_simples.py
```

### 🏢 **Empresarial - Sistema Completo**
```bash
docker-compose up -d
```

## 📚 **Documentação**

- 📖 [**Guia Completo**](docs/FASE2_COMPLETA.md) - Documentação detalhada
- 🚀 [**Fase 3**](docs/FASE3_COMPLETA.md) - Recursos avançados
- 🎬 [**Libras Downloader**](docs/LIBRAS_DOWNLOADER.md) - Sistema de download de vídeos
- 📋 [**Plano do Projeto**](docs/PLANO.MD) - Roadmap completo
- 📊 [**Relatórios**](docs/) - Análises de sites reais

## 🏆 **Resultados Comprovados**

### ✅ **Sites Analisados com Sucesso:**
1. **Terra.com.br** - Portal complexo (266KB CSS, 1.9MB JS)
2. **35mm Photography** - Portfolio moderno (Astro framework)
3. **PVP Projects** - Site de engenharia (37 páginas mapeadas)

### 📊 **Métricas de Performance:**
- ⚡ **Velocidade**: 1-2 segundos por página
- 🎯 **Precisão**: 100% de links descobertos
- 💾 **Eficiência**: Coleta incremental inteligente
- 🔄 **Confiabilidade**: Sistema robusto com retry automático

## 🤝 **Contribuição**

1. 🍴 Fork o projeto
2. 🌿 Crie uma branch: `git checkout -b feature/nova-funcionalidade`
3. 💾 Commit: `git commit -m 'Adiciona nova funcionalidade'`
4. 📤 Push: `git push origin feature/nova-funcionalidade`
5. 🔄 Abra um Pull Request

## 📄 **Licença**

Este projeto está sob a licença **MIT**. Veja [LICENSE](LICENSE) para detalhes.

---

## 🎉 **Começe Agora!**

```bash
# Instalação rápida
git clone <repository-url>
cd webscraper
docker-compose up -d

# Primeiro teste
python tools/spider_completo.py
```

**🚀 Em menos de 5 minutos você terá um sistema completo de web scraping funcionando!**
