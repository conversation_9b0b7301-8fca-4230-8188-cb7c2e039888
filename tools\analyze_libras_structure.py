#!/usr/bin/env python3
"""
Análise da estrutura da página de Libras para entender como extrair os vídeos.
"""

import requests
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin, urlparse

def analyze_libras_page():
    """Analisar a estrutura da página de Libras."""
    
    # Fazer request para a primeira página
    url = 'https://libras.cin.ufpe.br/?page=1'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    print("🔍 ANÁLISE DA ESTRUTURA DA PÁGINA DE LIBRAS")
    print("=" * 60)
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.content, 'html.parser')
        
        print(f"✅ Status: {response.status_code}")
        print(f"🌐 URL: {url}")
        print()
        
        # 1. Ana<PERSON><PERSON> botões "Exibir"
        print("📺 ANÁLISE DOS BOTÕES DE VÍDEO")
        print("-" * 40)

        # Procurar por diferentes formas de botões "Exibir"
        exibir_buttons = soup.find_all('a', string='Exibir')
        exibir_buttons_alt = soup.find_all('a', string=re.compile(r'Exibir', re.IGNORECASE))
        exibir_buttons_class = soup.find_all('a', class_=re.compile(r'exibir', re.IGNORECASE))

        print(f"Botões 'Exibir' (texto exato): {len(exibir_buttons)}")
        print(f"Botões 'Exibir' (regex): {len(exibir_buttons_alt)}")
        print(f"Botões 'Exibir' (por classe): {len(exibir_buttons_class)}")

        # Procurar por células que contêm "Exibir"
        exibir_cells = soup.find_all('td', string='Exibir')
        print(f"Células com 'Exibir': {len(exibir_cells)}")

        # Procurar por links dentro de células que contêm "Exibir"
        exibir_links_in_cells = []
        for cell in exibir_cells:
            links = cell.find_all('a')
            exibir_links_in_cells.extend(links)

        print(f"Links dentro de células 'Exibir': {len(exibir_links_in_cells)}")

        # Analisar os 60 botões encontrados com regex
        if exibir_buttons_alt:
            print(f"\n🔗 Primeiros 5 botões 'Exibir' (regex):")
            for i, button in enumerate(exibir_buttons_alt[:5]):
                href = button.get('href', '')
                full_url = urljoin(url, href) if href else ''
                onclick = button.get('onclick', '')
                text = button.get_text().strip()
                parent = button.parent.name if button.parent else 'None'
                print(f"  {i+1}. href: {href}")
                print(f"     Full URL: {full_url}")
                print(f"     onclick: {onclick}")
                print(f"     text: '{text}'")
                print(f"     parent: {parent}")
                print()

        if exibir_links_in_cells:
            print("\n🔗 Links dentro de células 'Exibir':")
            for i, button in enumerate(exibir_links_in_cells[:5]):
                href = button.get('href', '')
                full_url = urljoin(url, href) if href else ''
                onclick = button.get('onclick', '')
                print(f"  {i+1}. href: {href}")
                print(f"     Full URL: {full_url}")
                print(f"     onclick: {onclick}")
                print(f"     text: '{button.get_text().strip()}'")

        # Procurar por qualquer link que possa levar a vídeos
        all_links = soup.find_all('a', href=True)
        potential_video_links = []
        for link in all_links:
            href = link.get('href', '')
            if 'video' in href.lower() or 'exibir' in href.lower() or 'show' in href.lower():
                potential_video_links.append(link)

        print(f"\nLinks potenciais para vídeos: {len(potential_video_links)}")
        for i, link in enumerate(potential_video_links[:3]):
            href = link.get('href', '')
            print(f"  {i+1}. {href}")
        
        # 2. Analisar estrutura da tabela
        print("\n📊 ANÁLISE DA ESTRUTURA DA TABELA")
        print("-" * 40)
        
        # Procurar pela tabela principal
        table_rows = soup.find_all('tr')
        print(f"Linhas de tabela encontradas: {len(table_rows)}")
        
        if table_rows:
            print("\n📋 Estrutura das primeiras 3 linhas:")
            for i, row in enumerate(table_rows[:3]):
                cells = row.find_all(['td', 'th'])
                print(f"  Linha {i+1}: {len(cells)} células")
                for j, cell in enumerate(cells):
                    text = cell.get_text().strip()[:50]
                    print(f"    Célula {j+1}: {text}")
        
        # 3. Analisar paginação
        print("\n📄 ANÁLISE DA PAGINAÇÃO")
        print("-" * 40)
        
        # Procurar informações de paginação
        pagination_text = soup.find(string=re.compile(r'Página.*de.*Mostrando'))
        if pagination_text:
            print(f"Info de paginação: {pagination_text.strip()}")
        
        # Procurar links de páginas
        page_links = soup.find_all('a', href=re.compile(r'\?page=\d+'))
        print(f"Links de páginas encontrados: {len(page_links)}")
        
        if page_links:
            print("\n🔢 Algumas páginas disponíveis:")
            for link in page_links[:10]:
                href = link.get('href', '')
                text = link.get_text().strip()
                print(f"  - {href} (texto: '{text}')")
        
        # 4. Procurar por padrões de URL de vídeo
        print("\n🎬 ANÁLISE DE PADRÕES DE VÍDEO")
        print("-" * 40)
        
        # Procurar por links que podem conter vídeos
        all_links = soup.find_all('a', href=True)
        video_patterns = []
        
        for link in all_links:
            href = link.get('href', '')
            if any(ext in href.lower() for ext in ['.mp4', '.avi', '.mov', '.webm']):
                video_patterns.append(href)
        
        print(f"Links diretos para vídeos encontrados: {len(video_patterns)}")
        for pattern in video_patterns[:5]:
            print(f"  - {pattern}")
        
        # 5. Analisar estrutura dos sinais
        print("\n🤟 ANÁLISE DOS SINAIS")
        print("-" * 40)
        
        # Procurar por nomes de sinais
        signal_names = []
        for row in table_rows:
            cells = row.find_all('td')
            if len(cells) >= 2:  # Primeira célula geralmente tem o nome do sinal
                signal_name = cells[0].get_text().strip()
                if signal_name and len(signal_name) > 1:
                    signal_names.append(signal_name)
        
        print(f"Sinais identificados: {len(signal_names)}")
        if signal_names:
            print("\n📝 Primeiros 10 sinais:")
            for i, signal in enumerate(signal_names[:10]):
                print(f"  {i+1}. {signal}")
        
        return {
            'exibir_buttons': len(exibir_buttons),
            'table_rows': len(table_rows),
            'page_links': len(page_links),
            'video_patterns': len(video_patterns),
            'signal_names': len(signal_names),
            'first_exibir_links': [btn.get('href', '') for btn in exibir_buttons[:5]]
        }
        
    except Exception as e:
        print(f"❌ Erro ao analisar página: {e}")
        return None

def test_video_page():
    """Testar acesso a uma página específica de vídeo."""
    
    print("\n🎯 TESTE DE PÁGINA DE VÍDEO ESPECÍFICA")
    print("=" * 60)
    
    # Tentar acessar uma página de vídeo específica
    # Baseado no padrão que vimos na imagem: storage/videos/...
    test_url = "https://libras.cin.ufpe.br/storage/videos/20210126072453_601096b5ed907.mp4"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.head(test_url, headers=headers, timeout=10)
        print(f"✅ Status do vídeo teste: {response.status_code}")
        print(f"📁 Content-Type: {response.headers.get('content-type', 'N/A')}")
        print(f"📏 Content-Length: {response.headers.get('content-length', 'N/A')} bytes")
        
        if response.status_code == 200:
            print("🎉 Vídeo acessível diretamente!")
        
    except Exception as e:
        print(f"❌ Erro ao testar vídeo: {e}")

if __name__ == "__main__":
    # Executar análise
    result = analyze_libras_page()
    
    # Testar acesso a vídeo
    test_video_page()
    
    if result:
        print("\n📊 RESUMO DA ANÁLISE")
        print("=" * 60)
        print(f"✅ Botões 'Exibir': {result['exibir_buttons']}")
        print(f"✅ Linhas de tabela: {result['table_rows']}")
        print(f"✅ Links de páginas: {result['page_links']}")
        print(f"✅ Padrões de vídeo: {result['video_patterns']}")
        print(f"✅ Sinais identificados: {result['signal_names']}")
        
        print(f"\n🎯 PRÓXIMOS PASSOS:")
        print("1. Navegar por todas as páginas (1-69)")
        print("2. Extrair todos os links 'Exibir'")
        print("3. Acessar cada link para encontrar o vídeo real")
        print("4. Fazer download dos vídeos organizadamente")
