#!/bin/bash

# =============================================================================
# Script de entrada para o container WebScraper
# =============================================================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# =============================================================================
# Verificações iniciais
# =============================================================================

log "Iniciando WebScraper..."

# Verificar se o diretório de dados existe
if [ ! -d "/app/data" ]; then
    log "Criando diretório de dados..."
    mkdir -p /app/data/raw /app/data/processed /app/data/screenshots
fi

# Verificar se o diretório de logs existe
if [ ! -d "/app/logs" ]; then
    log "Criando diretório de logs..."
    mkdir -p /app/logs
fi

# =============================================================================
# Configuração do banco de dados
# =============================================================================

# Aguardar banco de dados (se PostgreSQL)
if [[ "${DATABASE_URL}" == postgresql* ]]; then
    log "Aguardando PostgreSQL..."
    
    # Extrair host e porta da URL do banco
    DB_HOST=$(echo $DATABASE_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
    DB_PORT=$(echo $DATABASE_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
    
    if [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ]; then
        warn "Não foi possível extrair host/porta do DATABASE_URL"
    else
        # Aguardar até o banco estar disponível
        timeout=60
        while ! nc -z "$DB_HOST" "$DB_PORT" 2>/dev/null; do
            if [ $timeout -le 0 ]; then
                error "Timeout aguardando PostgreSQL em $DB_HOST:$DB_PORT"
                exit 1
            fi
            log "Aguardando PostgreSQL em $DB_HOST:$DB_PORT... ($timeout segundos restantes)"
            sleep 2
            timeout=$((timeout - 2))
        done
        success "PostgreSQL está disponível!"
    fi
fi

# Executar migrações do banco
if [ "${RUN_MIGRATIONS:-true}" = "true" ]; then
    log "Executando migrações do banco de dados..."
    if alembic upgrade head; then
        success "Migrações executadas com sucesso!"
    else
        error "Falha ao executar migrações"
        exit 1
    fi
fi

# =============================================================================
# Configuração do Redis (se habilitado)
# =============================================================================

if [ "${ENABLE_CACHE:-false}" = "true" ] && [ -n "${REDIS_URL}" ]; then
    log "Verificando conexão com Redis..."
    
    # Extrair host e porta da URL do Redis
    REDIS_HOST=$(echo $REDIS_URL | sed -n 's/redis:\/\/\([^:]*\):.*/\1/p')
    REDIS_PORT=$(echo $REDIS_URL | sed -n 's/redis:\/\/[^:]*:\([0-9]*\).*/\1/p')
    
    if [ -n "$REDIS_HOST" ] && [ -n "$REDIS_PORT" ]; then
        timeout=30
        while ! nc -z "$REDIS_HOST" "$REDIS_PORT" 2>/dev/null; do
            if [ $timeout -le 0 ]; then
                warn "Timeout aguardando Redis em $REDIS_HOST:$REDIS_PORT - continuando sem cache"
                export ENABLE_CACHE=false
                break
            fi
            log "Aguardando Redis em $REDIS_HOST:$REDIS_PORT... ($timeout segundos restantes)"
            sleep 2
            timeout=$((timeout - 2))
        done
        
        if [ "${ENABLE_CACHE}" = "true" ]; then
            success "Redis está disponível!"
        fi
    fi
fi

# =============================================================================
# Configuração do Prefect (se habilitado)
# =============================================================================

if [ "${PREFECT_API_URL}" ]; then
    log "Configurando Prefect..."
    
    # Configurar API URL
    prefect config set PREFECT_API_URL="${PREFECT_API_URL}"
    
    # Verificar conexão com Prefect
    if prefect server health-check 2>/dev/null; then
        success "Prefect server está disponível!"
    else
        warn "Prefect server não está disponível - flows locais serão usados"
    fi
fi

# =============================================================================
# Configurações de desenvolvimento
# =============================================================================

if [ "${ENVIRONMENT:-production}" = "development" ]; then
    log "Modo de desenvolvimento ativado"
    
    # Configurações específicas para desenvolvimento
    export PYTHONPATH="/app/src:${PYTHONPATH}"
    
    # Instalar dependências de desenvolvimento se necessário
    if [ "${INSTALL_DEV_DEPS:-false}" = "true" ]; then
        log "Instalando dependências de desenvolvimento..."
        pip install -e ".[dev]"
    fi
fi

# =============================================================================
# Verificações finais
# =============================================================================

# Verificar se o Playwright está instalado
if ! playwright --version >/dev/null 2>&1; then
    warn "Playwright não encontrado - instalando browsers..."
    playwright install chromium
fi

# Verificar permissões dos diretórios
if [ ! -w "/app/data" ]; then
    error "Sem permissão de escrita no diretório /app/data"
    exit 1
fi

if [ ! -w "/app/logs" ]; then
    error "Sem permissão de escrita no diretório /app/logs"
    exit 1
fi

# =============================================================================
# Executar comando
# =============================================================================

log "Configuração concluída. Executando comando: $*"

# Se nenhum comando foi fornecido, usar o padrão
if [ $# -eq 0 ]; then
    set -- "webscraper" "run"
fi

# Executar o comando
exec "$@"
