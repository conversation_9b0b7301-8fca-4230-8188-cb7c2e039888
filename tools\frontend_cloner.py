#!/usr/bin/env python3
"""
🎨 FRONTEND CLONER - Extrator Completo de Frontend

Este script extrai TODAS as informações necessárias para replicar um site:
- HTML completo
- CSS (inline, interno, externo)
- JavaScript (inline, interno, externo)
- Imagens, fontes, ícones
- Estrutura de componentes
- APIs e endpoints
"""

import asyncio
import os
import sys
import json
import re
from pathlib import Path
from urllib.parse import urljoin, urlparse
import requests
from bs4 import BeautifulSoup

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / "src"))


class FrontendCloner:
    """Clonador completo de frontend."""
    
    def __init__(self, base_url, output_dir="cloned_site"):
        self.base_url = base_url
        self.domain = urlparse(base_url).netloc
        self.output_dir = Path(output_dir)
        self.session = requests.Session()
        
        # Headers realistas
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # Estrutura de dados coletados
        self.extracted_data = {
            'html': '',
            'css_files': [],
            'js_files': [],
            'images': [],
            'fonts': [],
            'apis': [],
            'components': [],
            'inline_styles': [],
            'inline_scripts': [],
            'meta_tags': [],
            'links': [],
            'forms': [],
            'structure': {}
        }
        
        # Criar diretórios
        self.setup_directories()
    
    def setup_directories(self):
        """Criar estrutura de diretórios."""
        dirs = [
            self.output_dir,
            self.output_dir / "html",
            self.output_dir / "css",
            self.output_dir / "js",
            self.output_dir / "images",
            self.output_dir / "fonts",
            self.output_dir / "data",
            self.output_dir / "components",
        ]
        
        for dir_path in dirs:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def clone_site(self):
        """Clonar site completo."""
        print(f"🎨 CLONANDO FRONTEND: {self.base_url}")
        print("="*60)
        
        try:
            # 1. Obter HTML principal
            print("📄 1. Extraindo HTML principal...")
            self.extract_main_html()
            
            # 2. Extrair CSS
            print("🎨 2. Extraindo CSS...")
            self.extract_css()
            
            # 3. Extrair JavaScript
            print("⚡ 3. Extraindo JavaScript...")
            self.extract_javascript()
            
            # 4. Extrair imagens
            print("🖼️ 4. Extraindo imagens...")
            self.extract_images()
            
            # 5. Extrair fontes
            print("🔤 5. Extraindo fontes...")
            self.extract_fonts()
            
            # 6. Analisar estrutura
            print("🏗️ 6. Analisando estrutura...")
            self.analyze_structure()
            
            # 7. Detectar APIs
            print("🔌 7. Detectando APIs...")
            self.detect_apis()
            
            # 8. Extrair componentes
            print("🧩 8. Identificando componentes...")
            self.extract_components()
            
            # 9. Gerar relatório
            print("📊 9. Gerando relatório...")
            self.generate_report()
            
            # 10. Criar versão replicável
            print("🚀 10. Criando versão replicável...")
            self.create_replica()
            
            print(f"\n✅ CLONAGEM CONCLUÍDA!")
            print(f"📁 Arquivos salvos em: {self.output_dir}")
            
        except Exception as e:
            print(f"❌ Erro na clonagem: {e}")
            import traceback
            traceback.print_exc()
    
    def extract_main_html(self):
        """Extrair HTML principal."""
        response = self.session.get(self.base_url, timeout=30)
        response.raise_for_status()
        
        self.soup = BeautifulSoup(response.content, 'html.parser')
        self.extracted_data['html'] = str(self.soup)
        
        # Salvar HTML original
        with open(self.output_dir / "html" / "original.html", 'w', encoding='utf-8') as f:
            f.write(self.extracted_data['html'])
        
        print(f"   ✅ HTML extraído: {len(self.extracted_data['html'])} caracteres")
    
    def extract_css(self):
        """Extrair todos os CSS."""
        css_count = 0
        
        # CSS externos (link tags)
        for link in self.soup.find_all('link', rel='stylesheet'):
            href = link.get('href')
            if href:
                css_url = urljoin(self.base_url, href)
                css_content = self.download_resource(css_url)
                if css_content:
                    filename = f"external_{css_count}.css"
                    self.save_file(self.output_dir / "css" / filename, css_content)
                    self.extracted_data['css_files'].append({
                        'url': css_url,
                        'filename': filename,
                        'size': len(css_content)
                    })
                    css_count += 1
        
        # CSS inline (style tags)
        for style in self.soup.find_all('style'):
            if style.string:
                filename = f"inline_{len(self.extracted_data['inline_styles'])}.css"
                self.save_file(self.output_dir / "css" / filename, style.string)
                self.extracted_data['inline_styles'].append({
                    'content': style.string,
                    'filename': filename
                })
        
        # CSS inline (style attributes)
        inline_styles = []
        for element in self.soup.find_all(style=True):
            inline_styles.append({
                'tag': element.name,
                'style': element.get('style'),
                'classes': element.get('class', [])
            })
        
        if inline_styles:
            with open(self.output_dir / "css" / "inline_attributes.json", 'w') as f:
                json.dump(inline_styles, f, indent=2)
        
        print(f"   ✅ CSS extraído: {css_count} arquivos externos, {len(self.extracted_data['inline_styles'])} inline")
    
    def extract_javascript(self):
        """Extrair todo o JavaScript."""
        js_count = 0
        
        # JavaScript externo (script src)
        for script in self.soup.find_all('script', src=True):
            src = script.get('src')
            if src:
                js_url = urljoin(self.base_url, src)
                js_content = self.download_resource(js_url)
                if js_content:
                    filename = f"external_{js_count}.js"
                    self.save_file(self.output_dir / "js" / filename, js_content)
                    self.extracted_data['js_files'].append({
                        'url': js_url,
                        'filename': filename,
                        'size': len(js_content)
                    })
                    js_count += 1
        
        # JavaScript inline (script tags sem src)
        for script in self.soup.find_all('script'):
            if not script.get('src') and script.string:
                filename = f"inline_{len(self.extracted_data['inline_scripts'])}.js"
                self.save_file(self.output_dir / "js" / filename, script.string)
                self.extracted_data['inline_scripts'].append({
                    'content': script.string,
                    'filename': filename
                })
        
        print(f"   ✅ JavaScript extraído: {js_count} arquivos externos, {len(self.extracted_data['inline_scripts'])} inline")
    
    def extract_images(self):
        """Extrair todas as imagens."""
        img_count = 0
        
        # Imagens em tags img
        for img in self.soup.find_all('img'):
            src = img.get('src') or img.get('data-src')
            if src:
                img_url = urljoin(self.base_url, src)
                img_data = self.download_resource(img_url, binary=True)
                if img_data:
                    ext = self.get_file_extension(img_url) or '.jpg'
                    filename = f"image_{img_count}{ext}"
                    self.save_file(self.output_dir / "images" / filename, img_data, binary=True)
                    self.extracted_data['images'].append({
                        'url': img_url,
                        'filename': filename,
                        'alt': img.get('alt', ''),
                        'size': len(img_data)
                    })
                    img_count += 1
        
        # Imagens em CSS (background-image)
        css_images = self.extract_css_images()
        self.extracted_data['images'].extend(css_images)
        
        print(f"   ✅ Imagens extraídas: {len(self.extracted_data['images'])} arquivos")
    
    def extract_fonts(self):
        """Extrair fontes."""
        font_count = 0
        
        # Fontes em CSS (@font-face, Google Fonts, etc.)
        for link in self.soup.find_all('link'):
            href = link.get('href', '')
            if 'font' in href.lower() or 'googleapis.com/css' in href:
                font_css = self.download_resource(urljoin(self.base_url, href))
                if font_css:
                    filename = f"font_{font_count}.css"
                    self.save_file(self.output_dir / "fonts" / filename, font_css)
                    self.extracted_data['fonts'].append({
                        'url': href,
                        'filename': filename,
                        'type': 'css'
                    })
                    font_count += 1
        
        print(f"   ✅ Fontes extraídas: {len(self.extracted_data['fonts'])} arquivos")
    
    def analyze_structure(self):
        """Analisar estrutura do site."""
        structure = {
            'title': self.soup.find('title').get_text() if self.soup.find('title') else '',
            'meta_tags': [],
            'headings': {},
            'navigation': [],
            'forms': [],
            'sections': []
        }
        
        # Meta tags
        for meta in self.soup.find_all('meta'):
            structure['meta_tags'].append({
                'name': meta.get('name'),
                'property': meta.get('property'),
                'content': meta.get('content')
            })
        
        # Headings
        for level in range(1, 7):
            headings = self.soup.find_all(f'h{level}')
            structure['headings'][f'h{level}'] = [h.get_text().strip() for h in headings]
        
        # Navegação
        for nav in self.soup.find_all('nav'):
            links = [{'text': a.get_text().strip(), 'href': a.get('href')} 
                    for a in nav.find_all('a')]
            structure['navigation'].append(links)
        
        # Formulários
        for form in self.soup.find_all('form'):
            inputs = [{'type': inp.get('type'), 'name': inp.get('name'), 'placeholder': inp.get('placeholder')} 
                     for inp in form.find_all(['input', 'textarea', 'select'])]
            structure['forms'].append({
                'action': form.get('action'),
                'method': form.get('method'),
                'inputs': inputs
            })
        
        self.extracted_data['structure'] = structure
        
        # Salvar estrutura
        with open(self.output_dir / "data" / "structure.json", 'w', encoding='utf-8') as f:
            json.dump(structure, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ Estrutura analisada: {len(structure['meta_tags'])} meta tags, {sum(len(h) for h in structure['headings'].values())} headings")
    
    def detect_apis(self):
        """Detectar chamadas de API."""
        apis = []
        
        # Procurar por URLs de API no JavaScript
        for script_data in self.extracted_data['inline_scripts']:
            content = script_data['content']
            
            # Padrões comuns de API
            api_patterns = [
                r'fetch\([\'"]([^\'"]+)[\'"]',
                r'axios\.get\([\'"]([^\'"]+)[\'"]',
                r'\.ajax\(\s*[\'"]([^\'"]+)[\'"]',
                r'XMLHttpRequest.*open\([\'"][^\'"]++[\'"],\s*[\'"]([^\'"]+)[\'"]'
            ]
            
            for pattern in api_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if match.startswith(('http', '/api', '/v1')):
                        apis.append({
                            'url': match,
                            'method': 'GET',  # Assumir GET por padrão
                            'source': 'javascript'
                        })
        
        self.extracted_data['apis'] = apis
        
        # Salvar APIs
        with open(self.output_dir / "data" / "apis.json", 'w') as f:
            json.dump(apis, f, indent=2)
        
        print(f"   ✅ APIs detectadas: {len(apis)} endpoints")
    
    def extract_components(self):
        """Identificar componentes reutilizáveis."""
        components = []
        
        # Identificar padrões comuns
        component_selectors = [
            ('.card', 'Card'),
            ('.button', 'Button'),
            ('.modal', 'Modal'),
            ('.navbar', 'Navbar'),
            ('.sidebar', 'Sidebar'),
            ('.footer', 'Footer'),
            ('.header', 'Header'),
            ('[class*="component"]', 'Component'),
            ('[data-component]', 'DataComponent')
        ]
        
        for selector, name in component_selectors:
            elements = self.soup.select(selector)
            if elements:
                for i, element in enumerate(elements[:3]):  # Máximo 3 exemplos
                    component_html = str(element)
                    filename = f"{name.lower()}_{i}.html"
                    self.save_file(self.output_dir / "components" / filename, component_html)
                    components.append({
                        'name': name,
                        'selector': selector,
                        'count': len(elements),
                        'filename': filename
                    })
        
        self.extracted_data['components'] = components
        print(f"   ✅ Componentes identificados: {len(components)} tipos")
    
    def generate_report(self):
        """Gerar relatório completo."""
        report = {
            'site_info': {
                'url': self.base_url,
                'domain': self.domain,
                'title': self.extracted_data['structure'].get('title', ''),
                'cloned_at': str(Path().cwd())
            },
            'summary': {
                'html_size': len(self.extracted_data['html']),
                'css_files': len(self.extracted_data['css_files']),
                'js_files': len(self.extracted_data['js_files']),
                'images': len(self.extracted_data['images']),
                'fonts': len(self.extracted_data['fonts']),
                'apis': len(self.extracted_data['apis']),
                'components': len(self.extracted_data['components'])
            },
            'extracted_data': self.extracted_data
        }
        
        # Salvar relatório
        with open(self.output_dir / "REPORT.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # Relatório em texto
        with open(self.output_dir / "README.md", 'w', encoding='utf-8') as f:
            f.write(f"""# 🎨 Frontend Clone - {self.domain}

## 📊 Resumo da Clonagem

- **Site Original**: {self.base_url}
- **HTML**: {len(self.extracted_data['html']):,} caracteres
- **CSS**: {len(self.extracted_data['css_files'])} arquivos externos
- **JavaScript**: {len(self.extracted_data['js_files'])} arquivos externos
- **Imagens**: {len(self.extracted_data['images'])} arquivos
- **Fontes**: {len(self.extracted_data['fonts'])} arquivos
- **APIs**: {len(self.extracted_data['apis'])} endpoints detectados
- **Componentes**: {len(self.extracted_data['components'])} tipos identificados

## 📁 Estrutura de Arquivos

```
{self.output_dir}/
├── html/           # HTML original e processado
├── css/            # Todos os arquivos CSS
├── js/             # Todos os arquivos JavaScript
├── images/         # Todas as imagens
├── fonts/          # Fontes e tipografia
├── components/     # Componentes identificados
├── data/           # Dados estruturados (APIs, estrutura)
└── replica/        # Versão replicável do site
```

## 🚀 Como Usar

1. Abra `replica/index.html` para ver a versão clonada
2. Consulte `data/structure.json` para entender a estrutura
3. Veja `data/apis.json` para endpoints de API
4. Use os componentes em `components/` para replicação

## 🎯 Próximos Passos

- Adaptar caminhos de recursos
- Implementar funcionalidades JavaScript
- Conectar com APIs identificadas
- Personalizar estilos conforme necessário
""")
    
    def create_replica(self):
        """Criar versão replicável do site."""
        replica_dir = self.output_dir / "replica"
        replica_dir.mkdir(exist_ok=True)
        
        # HTML modificado com caminhos locais
        modified_html = self.extracted_data['html']
        
        # Substituir caminhos de CSS
        for i, css_file in enumerate(self.extracted_data['css_files']):
            original_url = css_file['url']
            new_path = f"../css/{css_file['filename']}"
            modified_html = modified_html.replace(original_url, new_path)
        
        # Substituir caminhos de JS
        for i, js_file in enumerate(self.extracted_data['js_files']):
            original_url = js_file['url']
            new_path = f"../js/{js_file['filename']}"
            modified_html = modified_html.replace(original_url, new_path)
        
        # Substituir caminhos de imagens
        for img in self.extracted_data['images']:
            original_url = img['url']
            new_path = f"../images/{img['filename']}"
            modified_html = modified_html.replace(original_url, new_path)
        
        # Salvar HTML modificado
        with open(replica_dir / "index.html", 'w', encoding='utf-8') as f:
            f.write(modified_html)
        
        print(f"   ✅ Versão replicável criada em: {replica_dir}")
    
    # Métodos auxiliares
    def download_resource(self, url, binary=False):
        """Download de recurso."""
        try:
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            return response.content if binary else response.text
        except:
            return None
    
    def save_file(self, path, content, binary=False):
        """Salvar arquivo."""
        mode = 'wb' if binary else 'w'
        encoding = None if binary else 'utf-8'
        with open(path, mode, encoding=encoding) as f:
            f.write(content)
    
    def get_file_extension(self, url):
        """Obter extensão do arquivo."""
        return Path(urlparse(url).path).suffix
    
    def extract_css_images(self):
        """Extrair imagens referenciadas no CSS."""
        # Implementação simplificada
        return []


def main():
    """Função principal."""
    print("🎨 FRONTEND CLONER - Extrator Completo")
    print("="*50)
    
    # URL para clonar - ChatBot MultiSócios
    url = "https://chatbot2-flame-beta.vercel.app"
    
    # Nome do diretório
    domain = urlparse(url).netloc.replace('www.', '')
    output_dir = f"cloned_chatbot_multisocios"
    
    print(f"🎯 Site: {url}")
    print(f"📁 Salvando em: {output_dir}")
    
    # Criar clonador e executar
    cloner = FrontendCloner(url, output_dir)
    cloner.clone_site()
    
    print(f"\n🎉 CLONAGEM CONCLUÍDA!")
    print(f"📁 Arquivos em: {output_dir}")
    print(f"🚀 Abra: {output_dir}/replica/index.html")


if __name__ == "__main__":
    main()
