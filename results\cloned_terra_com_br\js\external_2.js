/*! zaz-ui-t360 - v1.0.25 - 06/08/2025 -- 1:38pm */

zaz.use(function generateNotifications(pkg){"use strict";pkg.require(["modFactory"],function(modFactory){modFactory.create({name:"generateNotifications",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/zaz-ui-portal",source:"http://github.tpn.terra.com/Terra/zaz-ui-portal",description:"gerador de notificacoes do portal",tests:"",dependencies:[],setup:function(data){var PUBLIC={},PRIVATE={renotifyAfterSeconds:2592e3,signos:{aquarius:"aquário",pisces:"peixes",aries:"áries",taurus:"touro",gemini:"gêmeos",cancer:"câncer",leo:"leão",virgo:"virgem",libra:"libra",scorpio:"escorpi<PERSON>",sagittarius:"sagitário",capricorn:"capricórnio"}};return PUBLIC.generate=function(userArea,supportsPush){pkg.require(["mod.notifications"],function(modNotification){modNotification.getAllNotifications(function getAllNotifications(notifications){function callNotifications(){Promise.all([notifyCookies(),notifyPWA(),notifyDarkTheme(),notifyGoogleSignIn()]).then(function(){supportsPush&&"denied"!=window.Notification.permission?Promise.all([addBreakingNewsNotification(),addChannelNotification(),addHoroscopeNotification(),addTeamNotification()]).then(function(){PRIVATE.readNotifications()}):PRIVATE.readNotifications()})}var addHoroscopeNotification=function(){return new Promise(function(resolve,reject){pkg.require("mod.t360.icons.zodiac.solid",function(PopupNotifications){pkg.context.user.get("horoscope",function(dataHoroscope){var notifyKey="horoscope",horoscope=dataHoroscope||pkg.context.page.get("horoscope");if(userArea&&userArea.notifications&&userArea.notifications.horoscope)return resolve();if(horoscope){for(var objNotifications,groupName="web_notification_horoscope_v1",dataHoroscope={message:{pt:"Você gostaria de receber o horóscopo de <b>"+PRIVATE.signos[horoscope].toUpperCase()+"</b> todas as manhãs?",es:""},complement:"",group:groupName,icon:"icon-citrino icon-"+horoscope,button:{url:"#horoscope"}},i=0;i<notifications.length;i++)notifications[i].group===groupName&&(objNotifications=notifications[i]);objNotifications?(objNotifications.icon!=horoscope&&modNotification.update(objNotifications.id,dataHoroscope,function(){resolve()}),modNotification.setUnread(objNotifications.id,PRIVATE.renotifyAfterSeconds,function(){resolve()})):modNotification.add(dataHoroscope,function(){resolve()})}else resolve()})})})},addTeamNotification=function(){return new Promise(function(resolve,reject){pkg.require("mod.t360.icons.shields",function(PopupNotifications){return userArea&&userArea.notifications&&userArea.notifications.myteammatches?resolve():void pkg.context.user.get("myTeam",function(team){if(team&&team.userTeam&&team.userTeam.teamName&&team.userTeam.teamId){for(var groupName="web_notification_team_matches_v1",notified=!1,i=0,segments;i<notifications.length;i++)if(notifications[i].group===groupName){notified=!0;break}notified?(notifications[i].message.includes(team.userTeam.teamName)||modNotification.update(notifications[i].id,{message:{pt:"Clique aqui e seja notificado quando o <b>"+team.userTeam.teamName+"</b> estiver jogando.",es:""},icon:"team--"+team.channel_id},function(){console.log("Mensagem de notificação de time atualizada!")}),modNotification.setUnread(notifications[i].id,PRIVATE.renotifyAfterSeconds,function(){resolve()})):(segments="br_matches_"+team.userTeam.teamId,modNotification.add({message:{pt:"Clique aqui e seja notificado quando o <b>"+team.userTeam.teamName+"</b> estiver jogando.",es:""},complement:"",group:groupName,icon:"team--"+team.channel_id,button:{url:"#myteammatches"}},function(){resolve()}))}else resolve()})})})},addBreakingNewsNotification=function(){return new Promise(function(resolve,reject){var notifyKey="breakingnews";if(userArea&&userArea.notifications&&userArea.notifications[notifyKey])return resolve();for(var groupName="web_notification_breakingnews_v1",notified=!1,i=0;i<notifications.length;i++)if(notifications[i].group===groupName){notified=!0;break}notified?modNotification.setUnread(notifications[i].id,PRIVATE.renotifyAfterSeconds,function(){resolve()}):modNotification.add({message:{pt:"O <b>Terra</b> gostaria de enviar notificações das principais notícias para você.",es:""},complement:"",group:groupName,icon:"icon-bell icon-citrino",button:{url:"#"+notifyKey}},function(){resolve()})})},addChannelNotification=function(){return new Promise(function(resolve,reject){var notifyKey=pkg.context.page.get("notifyKey"),notifyIcon="",notifyText=pkg.context.page.get("notifyText");if(notifyKey&&userArea&&userArea.notifications&&userArea.notifications[notifyKey])return resolve();if(notifyKey&&notifyText){for(var groupName="web_notification_"+notifyKey+"_v1",notified=!1,i=0;i<notifications.length;i++)if(notifications[i].group===groupName){notified=!0;break}notified?modNotification.setUnread(notifications[i].id,PRIVATE.renotifyAfterSeconds,function(){resolve()}):modNotification.add({message:{pt:notifyText,es:""},complement:"",group:groupName,icon:"",button:{url:"#"+notifyKey}},function(){resolve()})}else resolve()})},notifyCookies=function(){return new Promise(function(resolve,reject){for(var notified=!1,i=0;i<notifications.length;i++)if("user_config_cookies"===notifications[i].group){notified=!0;break}"true"==pkg.utils.getCookie("user-lgpd-accept")&&(notified=!0,localStorage.setItem("user-lgpd-accept","true")),notified?resolve():modNotification.add({message:{pt:'O <b>Terra</b> utiliza cookies e outras tecnologias semelhantes para melhorar a sua experiência, de acordo com a nossa <a href="https://www.terra.com.br/avisolegal/privacidade.html">Política de Privacidade</a> e, ao continuar navegando, você concorda com estas condições.',es:""},icon:"",group:"user_config_cookies",button:{url:"#cookies"}},function(){resolve()})})},notifyPWA=function(){return new Promise(function(resolve,reject){window.addEventListener("beforeinstallprompt",function(e){e.preventDefault(),pkg.context.page.set("beforeinstallprompt",e),!PRIVATE.promptNewsUnread&&PRIVATE.asyncPromptNewsUnread&&pkg.require("app.t360.navbarNotifications",function(PopupNotifications){var popup=new PopupNotifications(PRIVATE.asyncPromptNewsUnread)})});for(var notified=!1,i=0;i<notifications.length;i++)if("user_config_pwa"===notifications[i].group){notified=!0;break}notified?modNotification.setUnread(notifications[i].id,PRIVATE.renotifyAfterSeconds,function(){resolve()}):modNotification.add({message:{pt:"Instale o <b>APP do Terra</b> e fique por dentro de todo nosso conteúdo",es:""},icon:"",group:"user_config_pwa",button:{url:"#user_config_pwa"}},function(){resolve()})})},notifyDarkTheme=function(){return new Promise(function(resolve,reject){for(var notified=!1,notification={group:"user_config_dark_theme",title:"",message:"",acceptText:"",declineText:""},userThemeCookie=pkg.utils.getCookie("user_theme"),i=0;i<notifications.length;i++)if(notifications[i].group===notification.group){notified=!0;break}"dark"!=window.osThemeColor||""!=userThemeCookie&&"default"!=userThemeCookie?notified=!0:(notification.message='Detectamos que o seu dispositivo está usando <b>tema escuro</b>. Troque o tema a qualquer momento no menu de <b>Configurações</b> (&nbsp;<span style="vertical-align:middle" class="icon icon-12 icon-cog icon-solid icon-color-black"></span>&nbsp;) no Seu Terra.',notification.acceptText="Manter",notification.declineText="Tema Claro"),notified?resolve():modNotification.add({message:notification.message,icon:"icon-night icon-acqua-dark",group:notification.group,button:{url:"#user_config_dark_theme",acceptText:notification.acceptText,declineText:notification.declineText}},function(){resolve()})})},notifyWhatsapp=function(){return new Promise(function(resolve,reject){for(var notified=!1,i=0;i<notifications.length;i++)if("user_whatsapp"===notifications[i].group){notified=!0;break}notified?resolve():modNotification.add({message:{pt:"As notícias mais importantes do dia direto no seu <b>ZAP</b>",es:""},icon:"",group:"user_whatsapp",button:{url:"#user_whatsapp"},whatsappConfirmation:!1},function(){resolve()})})},notifyGoogleSignIn=function(){return new Promise(function(resolve,reject){for(var notified=!1,notification_group="user_google_signin",i=0;i<notifications.length;i++)if(notifications[i].group===notification_group){notified=!0;break}pkg.require(["mod.googleOneTap"],function(GoogleOneTap){GoogleOneTap.updateLoginStatus.then(function(loggedUser){loggedUser||notified||modNotification.add({message:"",icon:"",group:notification_group,button:{url:"#user_google_signin",acceptText:"",declineText:""}},function(){resolve()})}).finally(function(){resolve()})})})};PRIVATE.readNotifications=function(){modNotification.getAllNotifications(function(notificationsUpdated){PRIVATE.promptNewsUnread=!1;var notifyKey=pkg.context.page.get("notifyKey"),notificationsPrompt=["#cookies","#user_ad_notification","#user_google_signin","#user_whatsapp","#breakingnews","#user_config_dark_theme","#user_config_pwa"];"signo"==pkg.context.page.get("templateLayout")?notificationsPrompt=["#cookies"]:"special-article"===pkg.context.page.get("templateLayout")?notificationsPrompt=["#cookies","#user_ad_notification","#user_whatsapp","#user_config_dark_theme"]:notifyKey&&(notificationsPrompt=["#cookies","#user_ad_notification","#"+notifyKey,"#user_whatsapp","#user_config_dark_theme","#user_config_pwa"]);for(var j=0;j<notificationsPrompt.length;j++){for(var i=0;i<notificationsUpdated.length;i++)if(notificationsPrompt[j]==notificationsUpdated[i].button.url&&!notificationsUpdated[i].read){if("user_config_pwa"!=notificationsUpdated[i].group||pkg.context.page.get("beforeinstallprompt")){PRIVATE.promptNewsUnread=notificationsUpdated[i];break}PRIVATE.asyncPromptNewsUnread=notificationsUpdated[i]}if(PRIVATE.promptNewsUnread)break}PRIVATE.promptNewsUnread&&(pkg.require("app.t360.navbarNotifications",function(PopupNotifications){var popup=new PopupNotifications(PRIVATE.promptNewsUnread)}),modNotification.at("update",function(changedNotification){var notificationBar=document.querySelector(".t360-notification-bar");notificationBar&&notificationBar.dataset&&notificationBar.dataset.buttonUrl==changedNotification.button.url&&pkg.watch("app.t360.navbar",function(AppNavbar){var navbarInstance;AppNavbar.getInstances()[0].removeNotification()})}))})},document.prerendering?document.addEventListener("prerenderingchange",function(){callNotifications()}):callNotifications()})})},PUBLIC},teardown:function(data){}})})}),zaz.use(function T360modNotificationsHelper(pkg){"use strict";var modFactory=pkg.factoryManager.get("mod"),thisAddon=null,env="prod",configs,urlsCengine,urlCengine={prod:"https://api.terra.com.br/notifications/send",dsv:"https://api-hlg.tpn.terra.com/notifications/send"}[env=0<=location.hostname.indexOf("dsv-")||0<=location.hostname.indexOf("hlg-")?"dsv":env],fcmConfig={dsv:{apiKey:"AIzaSyCsjQOiiGih5sNfdzHHACm83rjHGggRoIU",authDomain:"terra-portal-dsv.firebaseapp.com",databaseURL:"https://terra-portal-dsv.firebaseio.com",projectId:"terra-portal-dsv",storageBucket:"terra-portal-dsv.appspot.com",messagingSenderId:"738343154803",appId:"1:738343154803:web:e6559ff889d83777d4a606"},prod:{apiKey:"AIzaSyA8bKJkd73ANkU5_u5cIwny-HJuCHd5Lhs",authDomain:"terra.com.br",databaseURL:"https://sacred-lane-175517.firebaseio.com",projectId:"sacred-lane-175517",storageBucket:"sacred-lane-175517.appspot.com",messagingSenderId:"1094148948752",appId:"1:1094148948752:web:ae02d9a4c83f8da1171b95",measurementId:"G-1DWVJXCPX0"}}[env];modFactory.create({name:"t360.notificationsHelper",version:"1.0.0",state:"ok",dependencies:[],description:"Utilitarios para notificações.",setup:function(){console.log("modulo t360.notificationsHelper criado"),thisAddon=this},loadFirebaseMessaging:function(){return new Promise(function(resolve,reject){pkg.require(["FireBaseCore"],function(){var firebase;window.firebase.apps.length||(firebase=window.firebase.initializeApp(fcmConfig));var messaging=window.firebase.messaging();resolve(messaging)})})},addNotificationListener:function(messaging){messaging.onMessage(function(payload){var notificationTitle=payload.data.title,notificationOptions={body:payload.data.body,icon:payload.data.icon,image:payload.data.image,tag:payload.data.title,actions:JSON.parse(payload.data.actions),data:payload.data};"true"===payload.data.silent&&(notificationOptions.silent=!0);var date=payload.data.date.substr(0,10);window.tga.send("send","event","push_notification_show",payload.data.segment,payload.data.title+" - "+payload.data.body+" - "+date),navigator.serviceWorker.getRegistration("/push/fcm/").then(function(registration){registration.showNotification(notificationTitle,notificationOptions)})})},postSegments:function(action,segments,token,stalkerKeyName,provider){return new Promise(function(resolve,reject){var xhr=new XMLHttpRequest;xhr.timeout=1e4,xhr.onreadystatechange=function(){xhr.readyState==XMLHttpRequest.DONE&&(200==xhr.status?(console.log("Topic registration success.",xhr.response),resolve()):(console.log("Topic registration failed.",xhr.response,xhr.status),reject()))},xhr.open("POST",urlCengine,!0),xhr.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),xhr.send(encodeURI("segments="+segments+"&segments_action="+action+"&country=br&provider="+provider+"&user_token="+encodeURI(token)))})},isFirebaseSWRegistered:function(){return new Promise(function(resolve,reject){navigator.serviceWorker.getRegistration("/push/fcm/").then(function(registration){registration&&registration.active&&0<registration.active.scriptURL.indexOf("fcm.js")?(console.log("Firebase serviceworker is registered.",registration),resolve(registration)):reject()})})},subscribeSegment:function(action,segment,stalkerKeyName){return new Promise(function(resolve,reject){pkg.watch("app.navbarUserArea",function(UA){UA.subscribeSegments(action,segment,stalkerKeyName),pkg.context.user.on("userArea",function(userArea){(userArea.notifications[stalkerKeyName]?resolve:reject)()})})})},browserSupportsPush:function(){return"serviceWorker"in navigator&&"PushManager"in window&&"Notification"in window&&"fetch"in window&&ServiceWorkerRegistration.prototype.hasOwnProperty("showNotification")&&PushSubscription.prototype.hasOwnProperty("getKey")},openAuthorizationPopup:function(action,segments,stalkerKeyName){var params,urlPopup,w=window.open("https://www.terra.com.br/push/fcm/notifications_popup_fcm.html"+"?action="+action+"&segments="+segments+"&stalkerKeyName="+stalkerKeyName,"Notification","width=600,height=400")},teardown:function(reason){}})}),zaz.use(function modGoogleOneTap(pkg){"use strict";pkg.require(["modFactory"],function(modFactory){modFactory.create({name:"googleOneTap",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/zaz-ui-portal",source:"http://github.tpn.terra.com/Terra/zaz-ui-portal",description:"gerador de notificacoes do portal",tests:"",dependencies:["mod.googleAccounts"],setup:function(data){var PUBLIC={},PRIVATE={googleAccountID:"***********-64nrh3rrhfj072r7fg23q68ekm6h9ke8.apps.googleusercontent.com",isAMP:!1,userData:{}};return PRIVATE.queryStrings=pkg.context.page.get("query"),PRIVATE.handleCredentialResponse=function(token){var formData="login";PRIVATE.isAMP&&(formData="amp-login"),"btn_confirm"==token.select_by?window.tga.send("send","event","google-button","login","token received"):window.tga.send("send","event","one-tap-sign-in",formData,"token received");var formData=new FormData;formData.append("credential",token.credential),fetch("https://api.terra.com.br/google-sign-in/credential",{method:"POST",credentials:"same-origin",body:formData}).then(function(response){if(response.ok)return response.json()}).then(function(data){data&&(PRIVATE.userData=data)&&(data.email&&(pkg.context.user.set("googleSignInEmail",data.email),delete data.email),data.sub&&(PRIVATE.loginSuccess=!0,pkg.context.user.set("googleSignInSub",data.sub)),pkg.context.user.set("googleSignInData",data),data.emailhash&&pkg.context.user.get("stalker_br",PRIVATE.saveFPD),window.tga.send("send","event","one-tap-sign-in","login","success"),PRIVATE.loadUserInfo(),PRIVATE.sendLiveRampHashes())})},PRIVATE.saveFPD=function(terraUserInfoIPERMH){var fpdDict={},signoDict;terraUserInfoIPERMH&&(terraUserInfoIPERMH.zodiac&&(fpdDict.zodiac={aquarius:"aquario",pisces:"peixes",aries:"aries",taurus:"touro",gemini:"gemeos",cancer:"cancer",leo:"leao",virgo:"virgem",libra:"libra",scorpio:"escorpiao",sagittarius:"sagitario",capricorn:"capricornio"}[terraUserInfoIPERMH.zodiac]),terraUserInfoIPERMH.sportsteams&&terraUserInfoIPERMH.sportsteams.soccer&&terraUserInfoIPERMH.sportsteams.soccer.name&&(fpdDict.team=terraUserInfoIPERMH.sportsteams.soccer.name)),PRIVATE.userData.emailhash&&(fpdDict.emailhash=PRIVATE.userData.emailhash),PRIVATE.userData.sub&&(fpdDict.sub=PRIVATE.userData.sub);var terraUserInfoEMH=localStorage.getItem("mdivo-id"),terraUserInfoIPERMH=localStorage.getItem("mdivo-cpid");terraUserInfoEMH&&(fpdDict.mdivoID=terraUserInfoEMH),terraUserInfoIPERMH&&(fpdDict.mdivoCPID=terraUserInfoIPERMH);var terraUserInfoEMH=localStorage.getItem("terrauserinfo-emh"),terraUserInfoIPERMH=localStorage.getItem("terrauserinfo-idpermh");terraUserInfoEMH&&(fpdDict.terraUserInfoEMH=terraUserInfoEMH),terraUserInfoIPERMH&&(fpdDict.terraUserInfoIPERMH=terraUserInfoIPERMH),localStorage.setItem("FPD",JSON.stringify(fpdDict))},PRIVATE.loadUserInfo=function(){var elem=document.querySelector(".navbar__right--user-area");elem&&(document.querySelector(".tailtarget-pixel-loaded")||PUBLIC.createImgTagWithEmailHash(),elem.innerHTML='<img src="'+PRIVATE.userData.picture+'" width="30" height="30" style="border-radius: 30px;'+(PRIVATE.userData.customer?" border: 2px solid #609":"")+'">',elem.className="navbar__right--user-area")},PUBLIC.loadLoginPrompt=function(){window.google.accounts.id.initialize({client_id:PRIVATE.googleAccountID,callback:PRIVATE.handleCredentialResponse,auto_select:!0,cancel_on_tap_outside:!1,select_by:"auto",use_fedcm_for_prompt:!0}),window.google.accounts.id.prompt(function(notification){var isDisplayed=!0;notification.isSkippedMoment()&&(isDisplayed=!1,window.tga.send("send","event","one-tap-sign-in","SkippedReason","no reason")),notification.isDismissedMoment()&&(isDisplayed=!1,window.tga.send("send","event","one-tap-sign-in","DismissedReason",notification.getDismissedReason())),pkg.context.page.trigger("google-one-tap",{promptShow:isDisplayed})})},PRIVATE.registerASN=function(){fetch("https://api.terra.com.br/geoloc/ASN",{method:"POST"}).then(function(response){if(response.ok)return response.json()}).then(function(data){if(window.atob&&data.ASN)try{var val=window.atob(data.ASN);val&&window.tga.send("send","event","RUM","isp",val,{dimension15:val})}catch(e){}}).catch(function(err){})},PRIVATE.sendLiveRamp=function(){window.ats?PRIVATE.sendLiveRampHashes():window.addEventListener("envelopeModuleReady",function(){PRIVATE.sendLiveRampHashes()})},PRIVATE.sendLiveRampHashes=function(){pkg.context.user.get("emailHashes",function(data){data&&data.md5&&window.ats.setAdditionalData({type:"emailHashes",id:[data.sha1,data.sha256,data.md5]})})},PRIVATE.checkTerraLogin=function(){pkg.utils.getCookie("U")&&window.tga.send("send","event","terra-login","login","active")},PRIVATE.checkActiveToken=function(){PRIVATE.loginSuccess=!1;try{pkg.context.user.get("googleSignInData",function(data){var isSync;data?(PRIVATE.userData=data,PRIVATE.userData&&PRIVATE.userData.sub&&(PRIVATE.loadUserInfo(),PRIVATE.loginSuccess=!0,PRIVATE.registerASN(),PRIVATE.sendLiveRamp()),data.email&&(delete data.email,pkg.context.user.set("googleSignInData",data)),PRIVATE.userData.emailhash?(localStorage.getItem("googleSignInSync")||pkg.require(["mod.stalker"],function(Stalker){var stalker;(new Stalker).syncGoogleSignInUsers().then(function(syncCBData){localStorage.setItem("googleSignInSync",!0)})}),pkg.context.user.get("stalker_br",PRIVATE.saveFPD)):pkg.context.user.get("googleSignInEmailHash",function(emailhash){emailhash&&(PRIVATE.userData.emailhash=emailhash,pkg.context.user.set("googleSignInData",PRIVATE.userData),pkg.context.user.get("stalker_br",PRIVATE.saveFPD))})):(localStorage.getItem("mdivo-id")||localStorage.getItem("terrauserinfo-emh"))&&pkg.context.user.get("stalker_br",PRIVATE.saveFPD),PRIVATE.loginStatusResolve(PRIVATE.loginSuccess)})}catch(e){PRIVATE.loginStatusReject(e)}},PUBLIC.createImgTagWithEmailHash=function(){var gamPII;PRIVATE.divNavBar=document.querySelector(".navbar__right"),PRIVATE.divNavBar&&(gamPII=null,localStorage.getItem("mdivo-cpid")?gamPII=localStorage.getItem("mdivo-cpid"):localStorage.getItem("mdivo-id")?gamPII=localStorage.getItem("mdivo-id"):PRIVATE.userData&&PRIVATE.userData.emailhash&&(gamPII=PRIVATE.userData.emailhash),gamPII&&(PRIVATE.tagImg=document.createElement("img"),PRIVATE.tagImg.src="//m.t.tailtarget.com/sync/TT-10969-0/"+gamPII,PRIVATE.tagImg.classList.add("tailtarget-pixel-loaded"),PRIVATE.tagImg.style.width="1px",PRIVATE.tagImg.style.height="1px",PRIVATE.tagImg.style.position="absolute",PRIVATE.tagImg.style.bottom="0",PRIVATE.tagImg.style.left="0",PRIVATE.divNavBar.append(PRIVATE.tagImg)))},PUBLIC.getLoginStatus=function(){return PRIVATE.loginSuccess},PUBLIC.getLoginData=function(){return PRIVATE.userData},PUBLIC.setAMP=function(val){PRIVATE.isAMP=val},PUBLIC.renderButtonLogin=function(container){container&&(window.google.accounts.id.initialize({client_id:PRIVATE.googleAccountID,callback:PRIVATE.handleCredentialResponse,auto_select:!0,cancel_on_tap_outside:!1,select_by:"auto",use_fedcm_for_prompt:!0}),window.google.accounts.id.renderButton(container,{theme:"outline",text:"signin_with",size:"large",logo_alignment:"left",width:222,locale:"pt-BR"}))},PUBLIC.logoff=function(){PRIVATE.loginSuccess=!1,window.google.accounts.id.initialize({client_id:PRIVATE.googleAccountID,auto_select:!0,select_by:"auto",use_fedcm_for_prompt:!0}),pkg.context.user.get("googleSignInSub",function(sub){sub&&(window.google.accounts.id.revoke(sub),pkg.context.user.set("googleSignInEmail",""),pkg.context.user.set("googleSignInSub",""),pkg.context.user.set("googleSignInData",""),PRIVATE.removeGoogleDataFromFPD(),window.tga.send("send","event","one-tap-sign-in","login","revoke"))})},PRIVATE.updateUserInfoCookie=function(){pkg.context.user.get("stalker_br",function(stalkerData){var cookie={},signObj,cookie,cookie;stalkerData&&(stalkerData.zodiac&&(cookie.zodiac={aquarius:"aquario",pisces:"peixes",aries:"aries",taurus:"touro",gemini:"gemeos",cancer:"cancer",leo:"leao",virgo:"virgem",libra:"libra",scorpio:"escorpiao",sagittarius:"sagitario",capricorn:"capricornio"}[stalkerData.zodiac]),stalkerData.sportsteams&&stalkerData.sportsteams.soccer&&stalkerData.sportsteams.soccer.name&&(cookie.team=stalkerData.sportsteams.soccer.name),cookie=JSON.stringify(cookie),cookie="zaz-user-info-stalker="+encodeURIComponent(cookie)+";max-age=604800;secure",document.cookie=cookie)})},PRIVATE.removeGoogleDataFromFPD=function(val){var fpdDict=localStorage.getItem("FPD"),fpdDict;fpdDict&&(delete(fpdDict=JSON.parse(fpdDict)).emailhash,delete fpdDict.sub,localStorage.setItem("FPD",JSON.stringify(fpdDict)))},PUBLIC.updateLoginStatus=new Promise(function(resolve,reject){PRIVATE.loginStatusResolve=resolve,PRIVATE.loginStatusReject=reject,PRIVATE.checkActiveToken()}),PUBLIC.createImgTagWithEmailHash(),PRIVATE.checkTerraLogin(),PRIVATE.updateUserInfoCookie(),PUBLIC},teardown:function(data){}})})}),zaz.use(function prefetchURL(pkg){"use strict";pkg.require(["modFactory"],function(modFactory){modFactory.create({name:"prefetchURL",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/zaz-ui-portal",source:"http://github.tpn.terra.com/Terra/zaz-ui-portal",description:"prefetch de URL's",tests:"",dependencies:[],setup:function(data){var PUBLIC={cardLinksQuery:'div[data-type="N"].card-news .main-url'},PRIVATE={terraPrefetch:function(nodeLists,minConnectionType,isPrerender){function bindElementObserve(elements){var linkList={};elements.forEach(function(link){(link.href.startsWith("https://www.terra.com.br")||link.href.startsWith("http://hlg-cms.hlg-montador.tpn.terra.com"))&&(linkList[link.href.replace("#pf","")]||(linkList[link.href]=!0,observer.observe(link),document.querySelectorAll("[href='"+link.href+"']").forEach(function(pfLink){pfLink.href=pfLink.href+"#pf"})))})}minConnectionType=minConnectionType||"3g";var connDictionary={"slow-2g":0,"2g":1,"3g":2,"4g":3},fetchLinkList,observer;if(window.IntersectionObserver){if(navigator){if(navigator.connection&&navigator.connection.effectiveType){if(void 0===connDictionary[minConnectionType])return void console.error("prefetchURL: select a valid type of minConnectionType");if(connDictionary[minConnectionType]>connDictionary[navigator.connection.effectiveType])return void console.error("prefetchURL: network conditions are too poor for the use of prefetch")}if(navigator.deviceMemory&&navigator.deviceMemory<=2)return void console.error("prefetchURL: device memory is insufficient")}0!==nodeLists.length?(fetchLinkList={},observer=new IntersectionObserver(function(entries){entries.forEach(function(entry){var link;entry.isIntersecting&&(fetchLinkList[entry.target.href]||(fetchLinkList[entry.target.href]=!0,(link=document.createElement("link")).rel="prefetch",link.href=entry.target.href,document.body.appendChild(link)),observer.unobserve(entry=entry.target))})}),nodeLists.forEach(function(nodeList){bindElementObserve(nodeList)})):console.error("prefetchURL: nodeList must have at least one node to prefetch")}else console.error("prefetchURL: This device has no intersection observer support")}};return PUBLIC.getDeepNestedElements=function(parentNode,childrenNodes){var parentNode=document.querySelector(parentNode);if(parentNode){var childrenNodes=parentNode.querySelectorAll(childrenNodes);return 0===childrenNodes.length?void console.error("prefetchURL: children nodes not found, please make sure the childrenQuery is correct"):childrenNodes}console.error("prefetchURL: parent element not found, please make sure the parentQuery is correct")},PUBLIC.setElementsToObserve=function(queryStrings,minConnectionType,isPrerender){var idleCallback=window.requestIdleCallback||function(cb){var start=Date.now();return setTimeout(function(){cb({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-start))}})},1)},filteredNodeLists=queryStrings.filter(function(nodeList){if(NodeList.prototype.isPrototypeOf(nodeList))return nodeList}),prerenderSupport=HTMLScriptElement.supports&&HTMLScriptElement.supports("speculationrules"),queryStrings=pkg.context.page.get("query"),urlsToPrerender;isPrerender=!!(queryStrings&&queryStrings.prerender&&isPrerender)&&queryStrings.prerender,prerenderSupport&&isPrerender?(urlsToPrerender=[],filteredNodeLists.forEach(function(nodeList){nodeList.forEach(function(node){node.href=node.href,urlsToPrerender.push(node.href)})}),PRIVATE.prerender(urlsToPrerender)):idleCallback(function(){PRIVATE.terraPrefetch(filteredNodeLists,minConnectionType)})},PRIVATE.prerender=function(urlsToPrerender){var specScript=document.createElement("script"),specRules;specScript.type="speculationrules",specScript.textContent=JSON.stringify({prerender:[{source:"list",urls:urlsToPrerender}]}),document.body.append(specScript)},PUBLIC},teardown:function(data){}})})}),zaz.use(function modUiUtils(pkg){"use strict";pkg.require(["modFactory"],function(modFactory){modFactory.create({name:"uiUtils",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/zaz-ui-t360",source:"http://github.tpn.terra.com/Terra/zaz-ui-t360",description:"Módulo de funções utilitárias de FE",tests:"",dependencies:[],setup:function(data){var PUBLIC={},PRIVATE={};return PUBLIC.getChannelColorName=function(cartolaURL){var channelClass=[{channel:"terraia",class:"terraia"},{channel:"vidav",class:"vidav"},{channel:"horoscopo",class:"horoscopo"},{channel:"paradasp",class:"paradasp"},{channel:"educacao",class:"educacao"},{channel:"carnaval",class:"carnaval"},{channel:"papo-de-arena",class:"papodearena"},{channel:"verao",class:"verao"},{channel:"black-friday",class:"blackfriday"},{channel:"entre-telas",class:"entretelas"},{channel:"planeta",class:"planeta"},{channel:"eleicoes",class:"eleicoes"},{channel:"saude-bucal",class:"saudebucal"},{channel:"byte",class:"byte"},{channel:"degusta",class:"degusta"},{channel:"mobilidade",class:"mobilidade"},{channel:"comunidade",class:"comunidade"},{channel:"nos",class:"nos"},{channel:"gameon",class:"gameon"},{channel:"vida-e-estilo",class:"vidaeestilo"},{channel:"diversao",class:"diversao"},{channel:"esportes",class:"esportes"},{channel:"visao-do-corre",class:"visaodocorre"},{channel:"economia",class:"economia"},{channel:"noticias",class:"noticias"}];if(cartolaURL){var url=cartolaURL.replace(/[^/]*\.html/,""),channelClass=channelClass.find(function(channelClass){return-1<url.search(channelClass.channel)});if(channelClass)return channelClass.class}return"default"},PUBLIC},teardown:function(data){}})})}),zaz.use(function adobeTarget(pkg){"use strict";pkg.require(["modFactory"],function(modFactory){modFactory.create({name:"t360.adobeTarget",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/zaz-ui-portal",source:"http://github.tpn.terra.com/Terra/zaz-ui-portal",description:"Módulo para interagir com a Adobe Target CDP",tests:"",dependencies:[],setup:function(data){var PUBLIC={},PRIVATE={catalog:[{item_name:"XLL FIBRA",item_id:"500MB",icon:"https://cdn1.iconfinder.com/data/icons/essentials-pack/96/wifi_wireless_internet_network_signal-256.png"},{item_name:"XLL FIBRA",item_id:"FIBRASILREG",icon:"https://cdn1.iconfinder.com/data/icons/essentials-pack/96/wifi_wireless_internet_network_signal-256.png"},{item_name:"MIG MOVEL",item_id:"SELFTITESSE",icon:"https://cdn0.iconfinder.com/data/icons/user-interface-line-32/32/interface_web_smartphone_export_import_data-256.png"},{item_name:"XLL SVA",item_id:"NETFLIX",icon:"https://cdn3.iconfinder.com/data/icons/social-networks-29/250/netflix-256.png"},{item_name:"XLL FIBRA",item_id:"VIVOTOTAL4",icon:"https://cdn1.iconfinder.com/data/icons/essentials-pack/96/wifi_wireless_internet_network_signal-256.png"},{item_name:"XLL ACESSORIOS",item_id:"ACESSANDR",icon:""},{item_name:"XLL SMARTPHONE",item_id:"FAMILIAS",icon:"https://cdn4.iconfinder.com/data/icons/mobile-56/50/smartphones-256.png"},{item_name:"XLL SMARTPHONE",item_id:"IPHONE",icon:"https://cdn1.iconfinder.com/data/icons/computer-techologies-outline-free/128/ic_apple_iphone-256.png"},{item_name:"XLL SVA",item_id:"SPOTIFYPREMIUMFA",icon:"https://cdn2.iconfinder.com/data/icons/social-networks-18/100/Spotify_Line-256.png"},{item_name:"UPS MOVEL",item_id:"CTRL8GB",icon:"https://cdn3.iconfinder.com/data/icons/essential-pack-2/48/32-Smartphone-256.png"},{item_name:"UPS MOVEL",item_id:"CTRL6GB",icon:"https://cdn3.iconfinder.com/data/icons/essential-pack-2/48/32-Smartphone-256.png"},{item_name:"XLL ACESSORIOS",item_id:"ACESSAPPLE",icon:"https://cdn1.iconfinder.com/data/icons/social-media-vol-3-1/24/_apple-256.png"},{item_name:"XLL FIBRA",item_id:"VIVOTOTAL54",icon:"https://cdn1.iconfinder.com/data/icons/essentials-pack/96/wifi_wireless_internet_network_signal-256.png"},{item_name:"XLL ACESSORIOS",item_id:"NOTESTABLETS",icon:"https://cdn3.iconfinder.com/data/icons/streamline-icon-set-free-pack/48/Streamline-06-256.png"},{item_name:"XLL SMARTPHONE",item_id:"FAMILIAA",icon:"https://cdn4.iconfinder.com/data/icons/mobile-56/50/smartphones-256.png"},{item_name:"XLL FIBRA",item_id:"FIBRASILESP2",icon:"https://cdn1.iconfinder.com/data/icons/essentials-pack/96/wifi_wireless_internet_network_signal-256.png"},{item_name:"XLL SVA",item_id:"SVA",icon:""},{item_name:"UPS MOVEL",item_id:"SELF1",icon:"https://cdn3.iconfinder.com/data/icons/essential-pack-2/48/32-Smartphone-256.png"},{item_name:"XLL SMARTPHONE",item_id:"FAMILIAMOTO",icon:"https://cdn4.iconfinder.com/data/icons/mobile-56/50/smartphones-256.png"},{item_name:"XLL FIBRA",item_id:"FIBRASILESP1",icon:"https://cdn1.iconfinder.com/data/icons/essentials-pack/96/wifi_wireless_internet_network_signal-256.png"}],customMDIVOError:""};return PRIVATE.queryStrings=pkg.context.page.get("query"),PRIVATE.recommendationList=[],PRIVATE.LAST_EXEC_STORAGE_KEY="AT_REC_LAST_EXECUTION",PRIVATE.callback=function(product,data){if("rec"==product){if("custom"in data){for(var adobeTargetData=data.custom,recCount=1;recCount<=3;recCount++)adobeTargetData["offerCode"+recCount]&&adobeTargetData["offer"+recCount]&&PRIVATE.recommendationList.push({code:adobeTargetData["offerCode"+recCount],offer:adobeTargetData["offer"+recCount]});PRIVATE.sendToGA4("view_item_list")}PRIVATE.queryStrings&&PRIVATE.queryStrings.adobeTarget&&PRIVATE.sleep(100,function(){var atcFeatContainer,atcFeatProducts,atcS1Ad,atcS1Label;PRIVATE.s1Container=document.querySelector(".card-ad"),PRIVATE.s1Container?((atcFeatContainer=document.createElement("div")).className="atc-featured-section",(atcFeatProducts=document.createElement("div")).className="atc-featured-products",atcFeatProducts.id="atc-featured",atcFeatContainer.appendChild(atcFeatProducts),atcS1Ad=PRIVATE.s1Container.childNodes[1],atcS1Label=PRIVATE.s1Container.childNodes[3],PRIVATE.s1Container.insertBefore(atcFeatContainer,atcS1Ad),atcS1Ad.remove(),PRIVATE.renderApiItems(atcFeatProducts),atcS1Label.innerText="Recomendações"):console.log("['AdobeTarget'] Não foi possível encontrar o slot S1")})}},PRIVATE.sendToDataLayer=function(mid,cpid){window.dataLayer=window.dataLayer||[],window.dataLayer.push({event:"identification",interaction:{component:"identification",action:"identification:success",description:"success",value:0},user:{msisdn:mid,cpid:cpid}})},PRIVATE.shouldRunToday=function(){if(PRIVATE.queryStrings&&PRIVATE.queryStrings.adobeTarget)return!0;var lastExecution=localStorage.getItem(PRIVATE.LAST_EXEC_STORAGE_KEY),now=new Date;if(!lastExecution)return!0;var lastExecution=new Date(lastExecution);return!(lastExecution.getDate()===now.getDate()&&lastExecution.getMonth()===now.getMonth()&&lastExecution.getFullYear()===now.getFullYear())},PRIVATE.sendToGA4=function(event){!event||!PRIVATE.recommendationList||PRIVATE.recommendationList.length<=0||window.tga.sendGA4(event,{item_list_id:"adobe-target-recommendation",item_list_name:"adobe-target-recommendation",items:PRIVATE.recommendationList.map(function(item,idx){return{item_id:item.code,item_name:item.offer,index:idx,item_list_id:"adobe-target-recommendation",item_list_name:"adobe-target-recommendation"}})})},PRIVATE.mid=!1,PRIVATE.cpid=!1,PRIVATE.sleep=function(time,callback){setTimeout(function(){callback()},time)},PRIVATE.findCatalogItem=function(offerName,offerCode){return PRIVATE.catalog.find(function(item){return item.item_name===offerName&&item.item_id===offerCode})},PRIVATE.renderApiItems=function(errorMessage,card){var apiContainer=errorMessage,productList=PRIVATE.recommendationList;card&&(productList=card);var errorMessage=!1,card;PRIVATE.mid||PRIVATE.cpid?productList.length<=0&&(errorMessage="Nenhuma recomendação encontrada para o hash: "+PRIVATE.cpid):errorMessage="Usuário não identificado: "+PRIVATE.customMDIVOError,errorMessage?((card=document.createElement("div")).className="atc-featured-card error",card.innerText=errorMessage,apiContainer.appendChild(card)):productList.forEach(function(apiItem){var catalogMatch=PRIVATE.findCatalogItem(apiItem.offer,apiItem.code),card=document.createElement("div"),infoContainer,code;card.className="atc-featured-card",catalogMatch&&catalogMatch.icon?((infoContainer=document.createElement("img")).src=catalogMatch.icon,infoContainer.alt=apiItem.offer,card.appendChild(infoContainer)):((code=document.createElement("div")).className="atc-default-icon",code.textContent="?",card.appendChild(code));var infoContainer=document.createElement("div");infoContainer.className="atc-featured-info";var code=document.createElement("h4");code.textContent=apiItem.offer,infoContainer.appendChild(code);var code=document.createElement("p");code.className="atc-product-id",code.textContent=apiItem.code,infoContainer.appendChild(code),card.appendChild(infoContainer),apiContainer.appendChild(card)})},PRIVATE.startApp=function(){PRIVATE.mid&&PRIVATE.cpid?PRIVATE.shouldRunToday()?pkg.require(["mod.adobeTarget"],function(modAdobeTarget){console.log("mod.adobeTarget loaded with success"),window.callbackAdobeTarget=PRIVATE.callback,PRIVATE.sendToDataLayer(PRIVATE.mid,PRIVATE.cpid);var now=new Date;localStorage.setItem(PRIVATE.LAST_EXEC_STORAGE_KEY,now.toISOString())}):console.log("Adobe Target recommendations already obtained today. Skipping..."):(PRIVATE.queryStrings&&PRIVATE.queryStrings.mockedData&&(console.log("[AdobeTarget] Showing mocked data"),PRIVATE.mid="MOCKED",PRIVATE.cpid="MOCKED"),PRIVATE.queryStrings&&PRIVATE.queryStrings.adobeTarget&&PRIVATE.sleep(100,function(){var apiMockList,atcFeatProducts,qtd,atcS1Label,apiMockList,qtd,tempList,apiMockList;PRIVATE.s1Container=document.querySelector(".card-ad"),PRIVATE.s1Container?((apiMockList=document.createElement("div")).className="atc-featured-section",(atcFeatProducts=document.createElement("div")).className="atc-featured-products",atcFeatProducts.id="atc-featured",apiMockList.appendChild(atcFeatProducts),(qtd=PRIVATE.s1Container.childNodes[1]).style.display="none",PRIVATE.s1Container.childNodes[3].innerText="Recomendações",PRIVATE.s1Container.insertBefore(apiMockList,qtd),qtd.remove(),apiMockList=[{offer:"XLL FIBRA",code:"FIBRASILREG"},{offer:"XLL CREDITO",code:"VIVOMONEY"},{offer:"XLL SVA",code:"NETFLIX"}],PRIVATE.queryStrings&&PRIVATE.queryStrings.mockedDataItems&&(qtd=Number(PRIVATE.queryStrings.mockedDataItems),apiMockList=apiMockList.slice(0,qtd)),PRIVATE.renderApiItems(atcFeatProducts,apiMockList)):console.log("['AdobeTarget'] Não foi possível encontrar o slot S1")}))},pkg.context.page.at("mdivo-loaded",function(data){data.error?PRIVATE.customMDIVOError=data.error:Object.hasOwn(data,"data")&&(Object.keys(data.data).length<=0?PRIVATE.customMDIVOError="MDIVO não retornou dados":data.data["mdivo-id"]&&data.data["mdivo-cpid"]?(PRIVATE.mid=data.data["mdivo-id"],PRIVATE.cpid=data.data["mdivo-cpid"]):PRIVATE.customMDIVOError="MDIVO Erro genérico"),PRIVATE.startApp()}),PUBLIC},teardown:function(data){}})})}),zaz.use(function modUiUtils(pkg){"use strict";pkg.require(["modFactory"],function(modFactory){modFactory.create({name:"userLocation",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/zaz-ui-t360",source:"http://github.tpn.terra.com/Terra/zaz-ui-t360",description:"Módulo de funções utilitárias de FE",tests:"",dependencies:[],setup:function(data){var PUBLIC={},PRIVATE={alredyTrrgeo:!1,allowState:{"SAO PAULO":!0,"MINAS GERAIS":!0,PARANA:!0,"RIO GRANDE DO SUL":!0},removeAccents:function(s){if(!s)return"";var map={"â":"a","Â":"A","à":"a","À":"A","á":"a","Á":"A","ã":"a","Ã":"A","ê":"e","Ê":"E","è":"e","È":"E","é":"e","É":"E","î":"i","Î":"I","ì":"i","Ì":"I","í":"i","Í":"I","õ":"o","Õ":"O","ô":"o","Ô":"O","ò":"o","Ò":"O","ó":"o","Ó":"O","ü":"u","Ü":"U","û":"u","Û":"U","ú":"u","Ú":"U","ù":"u","Ù":"U","ç":"c","Ç":"C"};return s.replace(/[\W\[\] ]/g,function(a){return map[a]||a})}};return PUBLIC.getTrrGeo=function getTrrGeo(){return new Promise(function(resolve,reject){var decoded,cookieValue=pkg.utils.getCookie("trrgeo");(decoded=cookieValue?(decoded=decodeURI(cookieValue)).split("|"):decoded)&&1<decoded.length?resolve({latitude:decoded[0],longitude:decoded[1],city:decoded[2].replaceAll("+"," "),stateName:decoded[3].replaceAll("+"," "),state:decoded[4],country:decoded[5],source:decoded[5],idWeatherAdvisor:decoded[6]}):reject(!1)})},PRIVATE.getUserCoords=function(){return new Promise(function(resolve,reject){var onSuccess,options;navigator.geolocation.getCurrentPosition(function(position){if(!position||!position.coords)throw new Error("Navigator could not retrieve coordinates");resolve({latitude:position.coords.latitude,longitude:position.coords.longitude})},reject,{enableHighAccuracy:!0,timeout:1e4,maximumAge:0})})},PRIVATE.validateGeolocPermission=function(){return new Promise(function(resolve,reject){navigator&&navigator.permissions?navigator.permissions.query({name:"geolocation"}).then(function(result){return resolve("granted"===result.state)}).catch(function(e){resolve(!1)}):resolve(!1)})},PRIVATE.getLocation=function(resolve,reject){return new Promise(function(resolve,reject){PRIVATE.validateGeolocPermission().then(function(result){resolve(result?PRIVATE.getUserCoords():PUBLIC.getTrrGeo())})})},PRIVATE.getCityWeather=function(){PRIVATE.getLocation().then(function(coords){fetch("https://p1-cloud.trrsf.com.br/api/weather-api/getlocal?climatempo=true&lat="+coords.latitude+"&lon="+coords.longitude,{signal:window.AbortSignal.timeout(1e4)}).then(function(response){if(response.ok)return response.json()}).then(function(data){return!!PRIVATE.allowState[PRIVATE.removeAccents(data.stateName).toUpperCase()]&&void(data&&data.climatempoAdvisorId&&fetch("https://p1-cloud.trrsf.com.br/api/weather-api/buscatempo?id_climatempo_advisor="+data.climatempoAdvisorId,{signal:window.AbortSignal.timeout(1e4)}).then(function(response){if(response.ok)return response.json()}).then(function(data){data&&data.climatempo&&data.climatempo[1]&&data.climatempo[1].momento&&sessionStorage.setItem("user-city-weather",JSON.stringify(data.climatempo[1].momento[0]))}))})})},PUBLIC.init=function(){PUBLIC.getTrrGeo().then(function(trrgeo){PRIVATE.alredyTrrgeo=!0,PRIVATE.allowState[PRIVATE.removeAccents(trrgeo.stateName).toUpperCase()]&&PRIVATE.getCityWeather()})},cookieStore&&cookieStore.addEventListener("change",function cookieChange(event){""==pkg.utils.getCookie("trrgeo")||PRIVATE.alredyTrrgeo||(console.log("----------\x3e cookieStore"),PRIVATE.alredyTrrgeo=!0,PUBLIC.init(),cookieStore.removeEventListener("change",cookieChange))}),PUBLIC},teardown:function(data){}})})}),zaz.use(function t360(pkg){"use strict";var queryStrings=pkg.context.page.get("query");pkg.require(["mod.t360.customer"],function(modCustomer){!queryStrings||"true"!=queryStrings.adobeTarget&&"1"!=queryStrings.adobeTarget||pkg.require(["mod.t360.adobeTarget"],function(t360AdobeTarget){console.log("mod.t360.adobeTarget carregado")})});var dbName="terra-sw-data",createObjectStore=function(event){var db;return event.target.result.createObjectStore("context",{keyPath:"name"})},setSiteInfo=function(data,callback){var open=window.indexedDB.open(dbName,1),addData=function(datat,store){var promisseList=[],i=0,putData=function(x,data){return new Promise(function(resolve,reject){var insert;store.put({name:x,value:data[x]}).onsuccess=function(){resolve()}})},x;for(x in data)data.hasOwnProperty(x)&&(promisseList[i]=putData(x,data),i++);return store.transaction.db.close(),Promise.all(promisseList)};open.onupgradeneeded=createObjectStore,open.onsuccess=function(db){if(data){var db=db.target.result;try{var tx,store=db.transaction("context","readwrite").objectStore("context");addData(data,store).then(callback)}catch(e){0===db.objectStoreNames.length&&(db.close(),window.indexedDB.deleteDatabase(dbName))}}}},testPath,pathSW,existBreakingNewsBar,observerComments;try{var testPath=document.cookie.match(/_ga=(.+?);/)[1].split(".").slice(-2).join(".");"indexedDB"in window&&testPath&&0<testPath.length&&setSiteInfo({cid:testPath})}catch(e){}"serviceWorker"in navigator&&(testPath="",queryStrings&&"true"==queryStrings.swTest&&(testPath="test/"),navigator.serviceWorker.register("/globalSTATIC/fe/zaz-ui-t360/_js/"+testPath+"sw.js",{scope:"/"}).then(function(registration){navigator.permissions&&navigator.permissions.query({name:"periodic-background-sync"}).then(function(result){"granted"==result.state&&"periodicSync"in registration&&registration.periodicSync.register("update-feed-news",{minInterval:864e5})})})),window.trr.contextData.contentPage&&window.trr.contextData.contentPage.has_comments&&((existBreakingNewsBar=document.querySelector(".social-comments"))&&new IntersectionObserver(function callbackComments(entries,observer){entries.forEach(function(entry){entry.isIntersecting&&(console.log("##### observer - callbackComments target",entry.target),console.info("Loading Facebook SDK..."),pkg.require(["mod.auth"],function(auth){console.info("Facebook SDK loaded"),observer.disconnect()}))})},{rootMargin:"0px 0px 1500px 0px",threshold:1}).observe(existBreakingNewsBar)),pkg.require(["mod.liveRamp"],function(modLiveRamp){console.log("mod.liveRamp loaded with success")}),pkg.require(["mod.userLocation"],function(ModUserLocation){ModUserLocation.init(),console.log("mod.userLocation loaded with success")}),pkg.context.user.get("userArea",function(userArea){pkg.require(["mod.generateNotifications","mod.t360.notificationsHelper"],function(GenerateNotifications,notificationsHelper){var supportsPush=notificationsHelper.browserSupportsPush();GenerateNotifications.generate(userArea,supportsPush),"www.terra.com.br"==window.location.hostname&&supportsPush&&"granted"==window.Notification.permission?notificationsHelper.isFirebaseSWRegistered().then(function(registration){notificationsHelper.loadFirebaseMessaging().then(function(messaging){console.log("Firebase Messaging loaded on ui-portal.",messaging),notificationsHelper.addNotificationListener(messaging),messaging.getToken({serviceWorkerRegistration:registration}).then(function(token){var areaToken;console.log("Firebase Token",token),token&&(areaToken=null,token!=(areaToken=userArea&&userArea.FirebaseToken?userArea.FirebaseToken:areaToken)&&(console.log("Firebase token "+token+" is different from the userArea token "+areaToken),window.tga.send("send","event","info","stalker","Different Firebase Token")),(userArea=userArea||{theme:"default",notifications:{}}).FirebaseToken=token,pkg.context.user.set("userArea",userArea))})})}).catch(function(){console.log("Firebase SW is not registered")}):"www.terra.com.br"==window.location.hostname&&"denied"==window.Notification.permission&&userArea&&userArea.FirebaseToken&&(delete userArea.FirebaseToken,pkg.context.user.set("userArea",userArea),window.tga.send("send","event","info","stalker","Removed Firebase Token"))})});var existBreakingNewsBar=document.querySelector(".breaking-news-bar"),breakingNewsBarText,alertType,fixFullAdPosition;existBreakingNewsBar&&(breakingNewsBarText=document.querySelector(".breaking-news-bar__text").innerText,alertType="urgent",existBreakingNewsBar.classList.contains("live")&&(alertType="live"),existBreakingNewsBar.addEventListener("click",function(){window.tga.send("send","event","breaking-news-bar",alertType,breakingNewsBarText)})),fixFullAdPosition=function(elem){var domSTR=elem.innerHTML;/width="1272"/gi.exec(domSTR)&&elem.classList.add("big-ad-1272"),/width="1260"/gi.exec(domSTR)&&elem.classList.add("big-ad-1260"),/width="970"/gi.exec(domSTR)&&elem.classList.add("big-ad-970"),/width="728"/gi.exec(domSTR)&&elem.classList.add("big-ad-728")},pkg.context.page.on("ad-slotrenderended",function(elem){var elem=document.getElementById(elem.id);elem&&elem.parentNode&&elem.parentNode.parentNode&&("mob"!==pkg.context.platform.get("type")&&elem.parentNode.parentNode.classList.contains("ad-get-size")&&fixFullAdPosition(elem.parentNode.parentNode),"mob"==pkg.context.platform.get("type")&&elem.firstElementChild&&600<=elem.firstElementChild.offsetHeight&&(elem.parentElement.classList.add("ad-parallax-container"),elem.classList.add("ad-parallax-clip")))})}),function(){"use strict";var loadSource=!0;if(loadSource=/tpn=1/.test(location.search)&&/remove-ads=true/.test(location.search)?!1:loadSource){window._ttconversion=window._ttconversion||[],window._ttconversion.push(["_event","_channel","terra-br"]),window._ttconversion.push(["_customEvent",{stage:"view",s1:window.terra_info_channel||"",s2:"contenido_digital",s3:window.terra_info_channeldetail||"",s4:"",s5:"",i1:window.terra_stats_idCrtfc||""}]),window._ttconversion.push(["_track"]);try{var e="script",s=document.createElement(e),t=document.getElementsByTagName(e)[0];s.type="text/java"+e,s.async=!0,s.src="https://tags.t.tailtarget.com/t3m.js?i=TT-10969-0/CT-411",t.parentNode.insertBefore(s,t)}catch(error){}}}(),function registerComscore(){"use strict";function callComscore(){var s,el;window._comscore=window._comscore||[],window._comscore.push({c1:"2",c2:"3000033",options:{bypassUserConsentRequirementFor1PCookie:!0,enableFirstPartyCookie:!0}}),s=document.createElement("script"),el=document.getElementsByTagName("script")[0],s.async=!0,s.src="https://sb.scorecardresearch.com/cs/3000033/beacon.js",el.parentNode.insertBefore(s,el)}document.prerendering?document.addEventListener("prerenderingchange",callComscore):callComscore()}();