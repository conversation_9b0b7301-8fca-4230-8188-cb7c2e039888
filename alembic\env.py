"""
Alembic environment configuration for WebScraper.

Este arquivo configura o ambiente do Alembic para executar migrações
do banco de dados do WebScraper.
"""

import asyncio
import os
from logging.config import fileConfig
from typing import Any

from alembic import context
from sqlalchemy import pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import async_engine_from_config

# Importar modelos para que o Alembic possa detectar mudanças
# TODO: Importar quando os modelos forem criados
# from src.core.models import Base

# Configuração do Alembic
config = context.config

# Interpretar arquivo de configuração para logging
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Adicionar MetaData dos modelos aqui para suporte a 'autogenerate'
# target_metadata = Base.metadata
target_metadata = None

# Outras configurações
def get_database_url() -> str:
    """
    Obter URL do banco de dados das variáveis de ambiente.
    
    Prioridade:
    1. DATABASE_URL (variável de ambiente)
    2. Construir a partir de variáveis individuais
    3. Usar valor padrão do alembic.ini
    """
    # Tentar variável de ambiente primeiro
    database_url = os.getenv("DATABASE_URL")
    if database_url:
        return database_url
    
    # Construir a partir de variáveis individuais
    db_type = os.getenv("DB_TYPE", "sqlite")
    
    if db_type == "postgresql":
        user = os.getenv("POSTGRES_USER", "webscraper")
        password = os.getenv("POSTGRES_PASSWORD", "webscraper123")
        host = os.getenv("POSTGRES_HOST", "localhost")
        port = os.getenv("POSTGRES_PORT", "5432")
        database = os.getenv("POSTGRES_DB", "webscraper")
        return f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{database}"
    
    # Padrão SQLite
    return "sqlite+aiosqlite:///./data/webscraper.db"


def run_migrations_offline() -> None:
    """
    Executar migrações em modo 'offline'.
    
    Configura o contexto apenas com uma URL e não com um Engine,
    embora um Engine também seja aceitável aqui. Ao pular a criação
    do Engine, não precisamos nem de um DBAPI disponível.
    
    Chama context.execute() para emitir a string SQL para um arquivo.
    """
    url = get_database_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    """Executar migrações com uma conexão existente."""
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        compare_type=True,
        compare_server_default=True,
    )

    with context.begin_transaction():
        context.run_migrations()


async def run_async_migrations() -> None:
    """
    Executar migrações em modo assíncrono.
    
    Cria um Engine assíncrono e associa uma conexão com o contexto.
    """
    # Configurar URL do banco
    database_url = get_database_url()
    
    # Atualizar configuração com a URL correta
    config.set_main_option("sqlalchemy.url", database_url)
    
    # Criar engine assíncrono
    connectable = async_engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """
    Executar migrações em modo 'online'.
    
    Neste cenário, precisamos criar um Engine e associar uma conexão
    com o contexto.
    """
    # Verificar se estamos usando async
    database_url = get_database_url()
    
    if "asyncpg" in database_url or "aiosqlite" in database_url:
        # Usar modo assíncrono
        asyncio.run(run_async_migrations())
    else:
        # Usar modo síncrono (fallback)
        connectable = context.config.attributes.get("connection", None)
        
        if connectable is None:
            from sqlalchemy import create_engine
            connectable = create_engine(database_url)

        with connectable.connect() as connection:
            do_run_migrations(connection)


# Determinar se executar em modo online ou offline
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
