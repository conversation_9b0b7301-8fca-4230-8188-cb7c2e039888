"""
Crawl - Sistema de descoberta e crawling de URLs.

Este módulo implementa o sistema de descoberta de URLs e gerenciamento
da fronteira de crawling, incluindo:

- sitemap: Descoberta de URLs via sitemap.xml
- frontier: Fila de URLs com priorização e deduplicação
- discovery: Descoberta de URLs a partir de páginas
"""

from .sitemap import SitemapDiscovery
from .frontier import URLFrontier

__all__ = [
    "SitemapDiscovery",
    "URLFrontier",
]
