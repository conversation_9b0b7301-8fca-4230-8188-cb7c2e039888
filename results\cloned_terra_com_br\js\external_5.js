/*! zaz-app-t360-cards - v1.0.0 - 09/07/2025 -- 2:10pm */

zaz.use(function(pkg){"use strict";var console=pkg.console,dictFactory;pkg.factoryManager.get("dict").create({name:"t360Cards",version:"1.0.0",state:"ok",extends:[],langs:{global:{termWithPlural:["plural","singular"]},pt:{term:"Termo em Português"},es:{term:"Termo in Español"},en:{term:"Term in English"},"es-AR":{term:"Termo en Argentina"}}})}),zaz.use(function appT360Cards(pkg){"use strict";var console=pkg.console,appFactory,STATIC_PUBLIC=null,STATIC_PRIVATE={};pkg.factoryManager.get("app").create({name:"t360.cards",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/t360-cards",source:"http://github.tpn.terra.com/Terra/t360-cards",description:"Just another app ",tests:"http://s1.trrsf.com/fe/t360-cards/tests/index.htm?zaz[env]=tests",dependencies:[],dictionaries:["t360Cards"],templates:{},expects:{},setup:function(STATIC_PUBLIC,PUBLIC,__shared){},init:function(data,__shared){var PUBLIC=this,PRIVATE={};PRIVATE.istouchdevice=1==("ontouchstart"in window?1:0),PRIVATE.objTgmKeyStamp=null;var stampTgmkeys=pkg.context.page.get("tagmanStampFeed");return stampTgmkeys&&0<stampTgmkeys.length&&(PRIVATE.objTgmKeyStamp=stampTgmkeys),PUBLIC.bindEvents=function(cardList,elemTable){if(cardList&&0<cardList.length)for(var i=0;i<cardList.length;i++)"P"===cardList[i].htmlDataset("type")&&PRIVATE.bindClickPremium(cardList[i],elemTable),"N"!==cardList[i].htmlDataset("type")&&"V"!==cardList[i].htmlDataset("type")&&"G"!==cardList[i].htmlDataset("type")&&"S"!==cardList[i].htmlDataset("type")&&"STR"!==cardList[i].htmlDataset("type")||(PRIVATE.regMetricsHat(cardList[i]),PRIVATE.regMetricsSource(cardList[i]),PUBLIC.bindClickCard(cardList[i],elemTable)),"B"!==cardList[i].htmlDataset("type")&&"EPL"!==cardList[i].htmlDataset("type")||PUBLIC.bindClickCard(cardList[i],elemTable),"S"===cardList[i].htmlDataset("type")&&"iPhone"==pkg.context.platform.get("device")&&PRIVATE.changeTargetLink(cardList[i]),"A"===cardList[i].htmlDataset("type")&&PUBLIC.bindClickApp(cardList[i],elemTable),cardList[i].htmlDataset("reason")&&-1!==cardList[i].htmlDataset("reason").search("g")&&pkg.require("mod.t360.icons.flags"),"STR"===cardList[i].htmlDataset("type")&&PRIVATE.bindClickCardStory(cardList[i])},PRIVATE.createStoryPlayerConfig=function(container,storyUrl){var embedCard=document.createElement("div"),ampStoryPlayer=document.createElement("amp-story-player"),scriptConfig=document.createElement("script"),anchorStory=document.createElement("a"),documentFragment=document.createDocumentFragment(),configJson={behavior:{pageScroll:!1,autoplay:!1},controls:[{name:"close",position:"start"}]};embedCard.classList.add("t360-stories-embed-card","closed","loading-app"),ampStoryPlayer.classList.add("t360-stories-embed-card__player","i-amphtml-story-player-loaded"),container.matches(".card-news__list-item")&&ampStoryPlayer.classList.add("card-premium-related-item"),scriptConfig.setAttribute("type","application/json"),scriptConfig.textContent=JSON.stringify(configJson),anchorStory.setAttribute("loading","lazy"),anchorStory.setAttribute("href",storyUrl),documentFragment.appendChild(embedCard).appendChild(ampStoryPlayer).append(scriptConfig,anchorStory),container.appendChild(documentFragment)},PRIVATE.startStoryPlayer=function(containerStoriesEmbed,player){containerStoriesEmbed.classList.remove("closed"),player.getStories().length<1||(player.addEventListener("storyNavigation",function(){containerStoriesEmbed.classList.remove("loading-app")}),player.show(null,null,{animate:!1}).catch(function(error){console.log("Erro ao executar o metodo show do story player: "+error)}),PRIVATE.tagHTML=document.getElementsByTagName("html"),PRIVATE.tagHTML[0].style.overflowY="hidden",containerStoriesEmbed.style.transform="translate3d(0, 0, 0) scale3d(1, 1, 1)",player.play(),window.addEventListener("popstate",function(){history.replaceState({},"go back to firstView",PRIVATE.firstView),PRIVATE.closeStoryPlayer(containerStoriesEmbed,player)}))},PRIVATE.closeStoryPlayer=function(containerStoriesEmbed,player){PRIVATE.tagHTML[0].style.overflow="",player.pause(),containerStoriesEmbed.classList.add("closed")},PRIVATE.bindClickStory=function(clickableElements,objCard){var player=null,containerStoriesEmbed=null,container=objCard;clickableElements.forEach(function(element){element.addEventListener("click",function(event){event.preventDefault(),PRIVATE.firstView=window.location.href,history.pushState({data:"first-story",url:this.href},"",this.href.split(".br/")[1]),pkg.require("mod.ampStoryPlayer",function(){console.log("Carregando amp-story-player")}).then(function(){var playerElement=objCard.querySelector("amp-story-player:not(.card-premium-related-item)");if(element.parentElement.matches(".card-news__list-item")&&(playerElement=element.parentElement.querySelector("amp-story-player.card-premium-related-item"),container=element.parentElement),!playerElement){if(!element.hasAttribute("href"))return;PRIVATE.createStoryPlayerConfig(container,element.getAttribute("href")),containerStoriesEmbed=container.querySelector(".t360-stories-embed-card");var playerElement=container.querySelector(".t360-stories-embed-card__player");(player=new AmpStoryPlayer(window,playerElement)).load()}PRIVATE.startStoryPlayer(containerStoriesEmbed,player),player.addEventListener("amp-story-player-close",function(){history.replaceState({},"go back to firstView",PRIVATE.firstView),PRIVATE.closeStoryPlayer(containerStoriesEmbed,player)})})})})},PRIVATE.bindClickCardStory=function(objCard){var linkElements=objCard.querySelectorAll(".card-news__url");PRIVATE.bindClickStory(linkElements,objCard)},PRIVATE.bindClickPremium=function(objCard,elemTable){var cards=objCard.querySelectorAll(".metric-item"),storyCards=[];if(cards){for(var i=0;i<cards.length;i++)PUBLIC.bindClickCard(cards[i],elemTable),"STR"==cards[i].htmlDataset("type")&&storyCards.push(cards[i]);PRIVATE.bindClickStory(storyCards,objCard)}},PRIVATE.regMetricsHat=function(elemHat){var elemHat=elemHat.querySelector(".hat"),label,action;elemHat&&(label=elemHat.innerHTML,action=elemHat.href,elemHat.addEventListener("click",function(){window.tga.send("send","event","chapeu",action,label)}))},PRIVATE.regMetricsSource=function(elemTitle){var elemSponsor=elemTitle.querySelector(".card-news__sponsor"),action,label,elemTitle;elemSponsor&&(action="",label=elemSponsor.href,(elemTitle=elemSponsor.querySelector(".card-news__sponsor--title"))&&(action=elemTitle.innerText),elemSponsor.addEventListener("click",function(){window.tga.send("send","event","parceiro",action,label)}))},PUBLIC.bindClickApp=function(objCard,elemTable){var clickEvent=function(){PUBLIC.registerEventCard(objCard,elemTable),objCard.removeEventListener("click",clickEvent)};objCard.addEventListener("click",clickEvent)},PUBLIC.bindClickCard=function(objCard,elemTable){objCard.addEventListener("click",function(){PUBLIC.registerEventCard(objCard,elemTable)})},PUBLIC.registerEventCard=function(objCard,elemTable){var dataCD={},cardReson="",ga4Params,tableArea=elemTable.htmlDataset("area");"NEWS"!==elemTable.htmlDataset("type")&&"PARTIAL"!==elemTable.htmlDataset("type")||(tableArea=elemTable.htmlDataset("area")),"NEWS"!==elemTable.htmlDataset("type")&&"TABLE-APP"!==elemTable.htmlDataset("type")&&"TABLE-NEWS-APP"!==elemTable.htmlDataset("type")&&"PARTIAL"!==elemTable.htmlDataset("type")||(ga4Params=elemTable.htmlDataset("position"))&&1==ga4Params.toString().length&&(ga4Params="0"+ga4Params),objCard.htmlDataset("reason")&&(cardReson=objCard.htmlDataset("reason")),objCard.htmlDataset("reasonStatic")&&(cardReson=objCard.htmlDataset("reasonStatic")),elemTable.htmlDataset("contentType")&&(dataCD.dimension7=elemTable.htmlDataset("contentType"));var ga4Params=PUBLIC.registerMetricCard(objCard,"addProduct",tableArea,ga4Params);window.tga.sendGA4("select_item",{item_list_id:tableArea,item_list_name:tableArea,items:[ga4Params]})},PUBLIC.registerMetricCard=function(geoloc,getSourceTypes,ga4Params,tablePosition){var channel=[],cardReson="",source="",sourceType="",cardTitle="",cardTypes={A:"app",N:"not",S:"not",V:"vid",EPL:"vid",STR:"story"},channelInfo=geoloc.htmlDataset("channelInfo");getSourceTypes=getSourceTypes||"addProduct";var getSourceTypes=function(elemA){var elemA,url,type="",sourceTypes=elemA.htmlDataset("sourceTypes"),elemA=elemA.querySelector(".main-url")||elemA;return elemA&&elemA.href&&(url=elemA.href),sourceTypes=sourceTypes.split(","),!1!==PRIVATE.getIndexArray(sourceTypes,"PRT")&&url?type=PRIVATE.isInternalDomain(url)?"interno":"externo":!1!==PRIVATE.getIndexArray(sourceTypes,"SRC")&&(type="agencia"),type};geoloc.htmlDataset("sourceTypes")&&(sourceType=getSourceTypes(geoloc)),cardTitle="A"===geoloc.htmlDataset("type")&&""===geoloc.title?geoloc.htmlDataset("appName"):geoloc.htmlDataset("gaLabel")||geoloc.title||geoloc.innerText,channelInfo&&(channel=channelInfo.split(".")),geoloc.htmlDataset("sourceName")&&(source=geoloc.htmlDataset("sourceName")),geoloc.htmlDataset("reason")&&(cardReson=geoloc.htmlDataset("reason")),geoloc.htmlDataset("reasonStatic")&&(cardReson=geoloc.htmlDataset("reasonStatic"));var ga4Params={item_id:cardReson,item_name:cardTitle,item_list_id:ga4Params,item_list_name:ga4Params},reasonDetail,state,geoloc;return window.terra_info_type&&(ga4Params.item_content_type=window.terra_info_type.toLowerCase()),source&&(ga4Params.item_source=source),sourceType&&(ga4Params.item_partner_type=sourceType),geoloc.htmlDataset("type")&&(ga4Params.item_card_type=cardTypes[geoloc.htmlDataset("type")]||""),geoloc.htmlDataset("size")&&(ga4Params.item_card_size=geoloc.htmlDataset("size")||""),tablePosition&&(ga4Params.item_table_position=tablePosition),channel[0]&&(ga4Params.item_channel=channel[0]),channel[1]&&(ga4Params.item_subchannel=channel[1]),channel[2]&&(ga4Params.item_channeldetail=channel[2]),cardReson&&-1<cardReson.search("g")&&geoloc.htmlDataset("reasonDetail")&&((geoloc=geoloc.htmlDataset("reasonDetail").split("/"))&&1<geoloc.length&&(state=geoloc[1]),state&&(ga4Params.item_variant=state)),ga4Params},PRIVATE.isInternalDomain=function(url){return url&&(-1<url.search("www.terra.com.br")||-1<url.search("tpn.terra.com"))},PRIVATE.changeTargetLink=function(objCard){var listLinks=objCard.querySelectorAll('a[target="_blank"]');if(listLinks)for(var i,length=listLinks.length,i=0;i<length;i++)listLinks[i].target="_top"},PRIVATE.getIndexArray=function(array,val){for(var i=0,j=array.length;i<j;i++)if(array[i]===val)return i;return!1},PUBLIC},teardown:function(why,__static,__proto,__shared){}})});