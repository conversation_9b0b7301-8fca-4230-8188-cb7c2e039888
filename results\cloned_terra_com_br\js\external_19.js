/*! zaz-ui-t360-home - v1.0.0 - 10/07/2025 -- 9:25pm */
zaz.use(function AdsCommon(pkg){"use strict";pkg.factoryManager.get("mod").create({name:"adsCommon",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/Terra/zaz-ui-t360-content",source:"http://github.tpn.terra.com/Terra/zaz-ui-t360-content",description:"Funções comum de publicidade",dependencies:["mod.manager"],tests:"",setup:function(data){var MODULE={},PRIVATE={},tgmkeyGroup=pkg.context.page.get("tgmkey");return PRIVATE.objAdmanager=new data.dependencies.mod.adManager,MODULE.admanagerSetup=function(){return PRIVATE.promiseSetup||(PRIVATE.promiseSetup=new Promise(function(resolve,reject){PRIVATE.tgmkey=pkg.context.page.get("tgmKey")||window.tgmKey||"br.homepage.home",window.AdManager.configure({TGMKEY:PRIVATE.tgmkey,PLATFORM:"mob"===pkg.context.platform.get("type")?"mob":"web"}),window.AdManager.get("backend_getviewport")?resolve(PRIVATE.tgmkey):window.AdManager.setup({tgmkey:PRIVATE.tgmkey}).then(function(){resolve(PRIVATE.tgmkey)})})),PRIVATE.promiseSetup},MODULE.setTgmkey=function(tgmkey){PRIVATE.tgmkey=tgmkey},MODULE.loadAdHeader=function(){MODULE.admanagerSetup().then(function(){var elemAd=document.getElementById("header-full-ad");elemAd&&PRIVATE.objAdmanager.stickAd({placeholder:elemAd,area:"cabeceira",tgmkey:PRIVATE.tgmkey})})},MODULE.loadAdAnchor=function(elemAd){return MODULE.admanagerSetup().then(function(){return elemAd?(window.googletag.pubads().addEventListener("slotRenderEnded",function(event){var containerId;event.slot&&("bottom-ad"!=event.slot.getSlotElementId()||event.isEmpty||(elemAd.parentElement.parentElement.style.height=elemAd.offsetHeight-1+"px"))}),PRIVATE.objAdmanager.stickAd({placeholder:elemAd,area:"ancora",tgmkey:PRIVATE.tgmkey})):Promise.reject()})},MODULE.admanagerSetup(),MODULE},teardown:function(data){}})}),zaz.use(function appInfinite(pkg){"use strict";var STATIC={};pkg.factoryManager.get("mod").create({name:"infinite",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/infinite",source:"http://github.tpn.terra.com/Terra/infinite",description:"Just another app",tests:"http://s1.trrsf.com/fe/infinite/tests/index.htm?zaz[env]=tests",dependencies:["app.t360.cards","mod.regMetrics"],dictionaries:[],setup:function(data){var PUBLIC=this,PRIVATE={},CONTEXT={},data=(PRIVATE.modRegMetrics=data.dependencies.mod.regMetrics,PRIVATE.appCards=new data.dependencies.app.t360.cards({}),PRIVATE.hasFooter=!1,PRIVATE.tableAreaAd=PRIVATE.tableAdSequence=0,PRIVATE.data=data,PRIVATE.cacheName="infinite-v1",PRIVATE.config={pagination:4},PRIVATE.loadingFat=400,"mob"===pkg.context.platform.get("type")&&(PRIVATE.loadingFat=800),PRIVATE.isBackForward=!1,performance.getEntriesByType("navigation")),data;return 0<data.length&&"back_forward"===data[0].type&&(PRIVATE.isBackForward=!0),history&&history.state&&history.state.backToHome&&((data=history.state.backToHome).tableId&&(PRIVATE.backToHome={tableId:data.tableId,cardId:data.cardId||null})),PRIVATE.getContext=function(){var queryStrings=pkg.context.page.get("query");queryStrings&&queryStrings.croupier&&(CONTEXT.croupier=queryStrings.croupier),queryStrings&&queryStrings.renderer&&(CONTEXT.renderer=queryStrings.renderer),queryStrings&&queryStrings.taboola&&(CONTEXT.taboola=queryStrings.taboola),CONTEXT.channelId=pkg.context.page.get("channelID"),CONTEXT.pageSize=pkg.context.page.get("pageSize"),CONTEXT.timelineSize=pkg.context.page.get("timelineSize"),CONTEXT.lomas=pkg.context.page.get("lomas"),CONTEXT.dflt=pkg.context.page.get("dflt"),CONTEXT.cardTypes=pkg.context.page.get("cardTypes"),CONTEXT.feedUuid=pkg.context.page.get("uuid"),CONTEXT.country=pkg.context.page.get("country"),CONTEXT.lang=pkg.context.page.get("lang"),CONTEXT.locale=pkg.context.page.get("locale"),CONTEXT.channel=pkg.context.page.get("channel"),CONTEXT.countryLive=pkg.context.page.get("countryLive"),CONTEXT.channelPath=pkg.context.page.get("channelPath"),CONTEXT.timelineApps=pkg.context.page.get("timelineApps"),CONTEXT.prioritized=pkg.context.page.get("prioritized"),CONTEXT.isLatestPage=pkg.context.page.get("isLatestPage")||!1,CONTEXT.editorialTable=pkg.context.page.get("editorialTable")||"",CONTEXT.subjectTables=pkg.context.page.get("subjectTables")||"",CONTEXT.specialCoverageCards=pkg.context.page.get("specialCoverageCards")||"",CONTEXT.nativeAd=pkg.context.page.get("nativeAd")||0,CONTEXT.hasPagination=pkg.context.page.get("hasPagination")||!1,CONTEXT.isHome=pkg.context.page.get("isHome")||!1,CONTEXT.colorPage=pkg.context.page.get("colorPage")||"",CONTEXT.musaIdTeam=pkg.context.page.get("musaIdTeam")||"",CONTEXT.truvid=pkg.context.page.get("truvid")||"",CONTEXT.dateResponse=pkg.context.page.get("dateResponse")||"",CONTEXT.playerHighlight=pkg.context.page.get("playerHighlight")||"",CONTEXT.dateResponse=pkg.context.page.get("dateResponse")||"",CONTEXT.timelineApps=PRIVATE.feedAppsNormalize(CONTEXT.timelineApps)},PRIVATE.feedAppsNormalize=function(feed,rFeed){var feedApps=rFeed||{},x;for(x in feed)"A"==feed[x].type&&(feedApps[feed[x].id]=feed[x],0<feed[x].items.length&&PRIVATE.feedAppsNormalize(feed[x].items,feedApps));return feedApps},PRIVATE.configureAdManager=function(){PRIVATE.tgmKey=pkg.context.page.get("tgmkey")||window.tgmKey||"br.homepage.home";var adsWallObj=null;try{adsWallObj=JSON.parse(localStorage.getItem("adsWallStatus"))}catch(e){}"NOT"===window.terra_info_type&&adsWallObj&&adsWallObj.reduceAds?PRIVATE.tgmKey="br.none.none":"object"==typeof PRIVATE.tgmKey&&PRIVATE.tgmKey.home&&(PRIVATE.tgmKey=PRIVATE.tgmKey.home),PRIVATE.promiseAdManager=pkg.require(["mod.adManager"],function(AdManager){AdManager.configure({TGMKEY:PRIVATE.tgmKey,PLATFORM:"mob"!==pkg.context.platform.get("type")?"web":"mob"})})},PRIVATE.tableHide=function(table){},PRIVATE.tableShow=function(table){if(table.htmlDataset("showAd"))return!1;table.htmlDataset("showAd",!0),"NEWS"!==table.htmlDataset("type")&&"STICK"!==table.htmlDataset("typeAd")&&"PARTIAL"!==table.htmlDataset("type")||PRIVATE.tableAdShow(table),"NEWS"!==table.htmlDataset("type")&&"PARTIAL"!==table.htmlDataset("type")&&!table.htmlDataset("cardsBackend")||PRIVATE.appCards.bindEvents(PRIVATE.getCardList(table),table),"TABLE-APP"!==table.htmlDataset("type")&&!table.htmlDataset("appName")||(PRIVATE.loadApp(table),"TABOOLA"===table.htmlDataset("area")&&(!1!==pkg.context.page.get("loadAds")?PRIVATE.loadNativeAd():table.remove())),PRIVATE.startApps(table)},PRIVATE.tableRegisterMetrics=function(table){if(!table||table.htmlDataset("regMetrics"))return!1;var listCards=PRIVATE.getCardList(table);listCards&&0<listCards.length&&("NEWS"===table.htmlDataset("type")||"PARTIAL"===table.htmlDataset("type")?pkg.context.page.trigger("register-metrics",{sender:"app.infinite",type:"card-view",tableType:"NEWS",cards:listCards,area:table.htmlDataset("area"),position:table.htmlDataset("position")}):"TABLE-APP"!==table.htmlDataset("type")&&"TABLE-NEWS-APP"!==table.htmlDataset("type")||pkg.context.page.trigger("register-metrics",{sender:"app.infinite",type:"card-view",tableType:table.htmlDataset("type"),cards:listCards,area:table.htmlDataset("area"),position:table.htmlDataset("position"),isVisiable:!0})),table.htmlDataset("regMetrics",!0)},PRIVATE.getCardList=function(table){var table,table="EDITORIAL-TABLE"==table.htmlDataset("area")?"PARTIAL"==table.htmlDataset("type")&&"APPS"==table.htmlDataset("partialType")?table.querySelectorAll(".zaz-app-t360-editorial-apps .card.card-app"):"PARTIAL"==table.htmlDataset("type")&&"SUBJECT"==table.htmlDataset("partialType")?table.querySelectorAll(".subject-modules .card-subject-modules .card"):table.querySelectorAll(".table-editorial-partial-1 .card"):"no-card"==table.htmlDataset("table")?table.querySelectorAll(".metric-item"):table.querySelectorAll(".card");return table},PRIVATE.getAppList=function(table){var listCards=table.querySelectorAll(".card-app");if("EDITORIAL-TABLE"==table.htmlDataset("area")&&"NEWS"==table.htmlDataset("type")){var listCardsFilter=[],editorialTableApps=table.querySelectorAll(".zaz-app-t360-editorial-apps .card-app"),i;for(i in listCards)if(listCards.hasOwnProperty(i)){var addElem=!0,x;for(x in editorialTableApps)if(editorialTableApps.hasOwnProperty(x)&&listCards[i].id==editorialTableApps[x].id){addElem=!1;break}addElem&&listCardsFilter.push(listCards[i])}listCards=listCardsFilter}return listCards},PRIVATE.loadCalhau=function(elem){var html,html;elem&&(html='<iframe src="https://www.terra.com.br/globalSTATIC/fe/zaz-calhau-news/_templates/300x250.html" width="300" height="250" style="margin: 5px"></iframe>',elem.closest(".card-ad-vert")&&(html+='<iframe src="https://www.terra.com.br/globalSTATIC/fe/zaz-calhau-news/_templates/300x250.html" width="300" height="250" style="margin: 5px"></iframe>'),elem.innerHTML=html+='<div class="card-ad__title">Mais lidas</div>',(html=elem.parentNode.querySelector(":scope > .card-ad__title"))&&html.remove())},PRIVATE.tableAdShow=function(table){var areaAd=table.htmlDataset("areaAd"),typeAd=table.htmlDataset("typeAd"),contentPath=table.htmlDataset("path"),elemAdSense=table.querySelector(".card-ad.adsense");if(!areaAd&&!typeAd)return!1;if(elemAdSense&&(!1!==pkg.context.page.get("loadAds")?PRIVATE.loadAdSense(elemAdSense):PRIVATE.loadCalhau(elemAdSense)),!1===pkg.context.page.get("loadAds")&&"mob"!==pkg.context.platform.get("type"))return PRIVATE.loadCalhau(document.getElementById("ad-"+table.id+"-1")),!1;if("STICK"!=typeAd)1==areaAd&&window.performance&&window.performance.mark&&window.performance.mark("AD_S1_BEFORE_DFP"),PRIVATE.promiseAdManager.then(function(dependencies){var objAdmanager;(new dependencies.mod.adManager).getViewportAds(areaAd).then(function(obj){for(var countAd=1,ads=obj.ADS,adsLen=ads.length,objLabel=null,fullAdId,x=0;x<adsLen;x++)"card"===ads[x].type||"mob"===pkg.context.platform.get("type")&&1===adsLen?(ads[x].placeholder="ad-"+table.id+"-"+countAd,countAd++):"full"===ads[x].type&&(fullAdId=table.htmlDataset("fullAdId")?table.htmlDataset("fullAdId"):"ad-full-"+table.id,ads[x].placeholder=fullAdId),ads[x].customKW=PRIVATE.getContentPath(contentPath);obj.instance.createAds({tgmkey:"br",ADS:ads})})});else for(var adArea,adPlaceholder,adElemts=table.querySelectorAll('div[data-type="AD"]'),i=0;i<adElemts.length;i++)adArea=adElemts[i].htmlDataset("adArea"),adPlaceholder=adElemts[i].htmlDataset("adPlaceholder"),adArea&&adPlaceholder&&PRIVATE.promiseAdManager.then(function(dependencies){var objAdmanager;(new dependencies.mod.adManager).stickAd({placeholder:document.getElementById(adPlaceholder),area:adArea,tgmkey:PRIVATE.tgmKey,customKW:PRIVATE.getContentPath(contentPath)})})},PRIVATE.getContentPath=function(path){var objPath={};return objPath=path?{channel:[(path=path.split("."))[0]],subchannel:[path[1]||""],channeldetail:[path[2]||""]}:objPath},PRIVATE.loadAdSense=function(elemAd){try{var elemAdsense=elemAd.querySelector("ins");elemAdsense.htmlDataset("adClient","ca-pub-7059064278943417"),elemAdsense.htmlDataset("adSlot",elemAd.htmlDataset("adAdslot")),elemAdsense.htmlDataset("adLayout","in-article"),elemAdsense.htmlDataset("adFormat","fluid"),elemAdsense.classList.add("adsbygoogle"),(window.adsbygoogle=window.adsbygoogle||[]).push({}),pkg.require(["mod.adsbygoogle"])}catch(e){}},PRIVATE.loadNativeAd=function(){var elemTable=document.getElementById("table-fixo-taboola");elemTable&&-1<elemTable.className.search("loading-app")&&(window._taboola=window._taboola||[],window._taboola.push({homepage:"auto"}),window._taboola.push({mode:"thumbnails-a-1",container:"widget-taboola-home",placement:"Below Homepage",target_type:"mix"}),window._taboola.push({flush:!0}),elemTable.classList.remove("loading-app"))},PRIVATE.loadApp=function(elemApp){PRIVATE.addTimelineData(elemApp);var appId=elemApp.htmlDataset("id"),appName;CONTEXT.timelineApps[appId]&&CONTEXT.timelineApps[appId].name&&(appName=CONTEXT.timelineApps[appId].name,pkg.require([appName],function(App){var objApp;elemApp.className&&(elemApp.className=elemApp.className.replace(" loading-app","")),App&&"Application"==App.name&&(objApp=new App({data:CONTEXT.timelineApps[appId],container:elemApp}))}))},PRIVATE.startApps=function(table){var apps=PRIVATE.getAppList(table),appsLength=apps.length;if(!(0<appsLength))return!1;for(var appId=null,x=0;x<appsLength;x++)if(appId=apps[x].htmlDataset("id"),PRIVATE.addTimelineData(apps[x]),CONTEXT.timelineApps[appId]&&"false"!==apps[x].htmlDataset("renderApp"))try{"AUTOPLAY"===CONTEXT.timelineApps[appId].name&&(CONTEXT.timelineApps[appId].name="app.t360.live"),PRIVATE.loadApp(apps[x])}catch(e){console.log("ERRO para iniciar o APP ------\x3e",apps[x])}},PRIVATE.addTimelineData=function(card){if(card.htmlDataset("appData"))try{CONTEXT.timelineApps[card.id]=JSON.parse(card.htmlDataset("appData"))}catch(e){CONTEXT.timelineApps[card.id]={}}},PRIVATE.setTableEvents=function(tables){for(var i=0,tableArea=0,tableSetViewable=!1,tablePosition,tableAreaAd,typeAd,tableType=0,observerTableMetricsOptions={rootMargin:"0px 0px "+("mob"===pkg.context.platform.get("type")?"-50%":"0px")+" 0px",threshold:"mob"===pkg.context.platform.get("type")?.01:.1},observerTableMetrics=new IntersectionObserver(function callbackTableMetrics(entries,observer){entries.forEach(function(entry){entry.isIntersecting&&(console.log("##### observer - callbackTableMetrics target",entry.target),PRIVATE.tableRegisterMetrics(entry.target),observer.unobserve(entry.target))})},observerTableMetricsOptions),observerTableMetricsOptions={rootMargin:"0px 0px "+("mob"===pkg.context.platform.get("type")?"100%":"50%")+" 0px",threshold:0},observerTableShow=new IntersectionObserver(function callbackTableShow(entries,observer){entries.forEach(function(entry){entry.isIntersecting&&(console.log("##### observer - callbackTableShow target",entry.target),PRIVATE.tableShow(entry.target),entry.target.__viewable["load-footer"]&&!PRIVATE.hasFooter&&PRIVATE.loadFooter(),observer.unobserve(entry.target))})},observerTableMetricsOptions),i=0;i<tables.length;i++)tables[i]&&(tableArea=tables[i].htmlDataset("area"),(tableSetViewable=tables[i].htmlDataset("setViewable"))||(tableType=tables[i].htmlDataset("type"),tablePosition=parseInt(tables[i].htmlDataset("position")),tableAreaAd=parseInt(tables[i].htmlDataset("areaAd")),typeAd=tables[i].htmlDataset("typeAd"),tables[i].htmlDataset("setViewable",!0),tables[i].__viewable={},tablePosition&&(PRIVATE.tablePosition=tablePosition,PRIVATE.tableAdSequence=tablePosition),"STICK"!=typeAd&&-1<tableAreaAd&&(PRIVATE.tableAreaAd=tableAreaAd),1===tablePosition&&"NEWS"==tableType?(PRIVATE.tableRegisterMetrics(tables[i]),PRIVATE.tableShow(tables[i]),(tables[i].htmlDataset("smallTable")||!CONTEXT.hasPagination&&1==tables.length)&&PRIVATE.loadFooter()):("AD"!==tableType&&tableArea&&observerTableMetrics.observe(tables[i]),observerTableShow.observe(tables[i]),i!=tables.length-1||CONTEXT.hasPagination||(tables[i].__viewable["load-footer"]=!0))))},PRIVATE.loadHeaderAd=function(){var headerAd=document.getElementById("header-full-ad"),observerHeaderAd;headerAd&&new IntersectionObserver(function callbackHeaderAd(entries,observer){entries.forEach(function(entry){entry.isIntersecting&&(console.log("##### observer - callbackHeaderAd target",entry.target),PRIVATE.promiseAdManager.then(function(dependencies){var objAdmanager;(new dependencies.mod.adManager).stickAd({placeholder:entry.target,area:"cabeceira",tgmkey:PRIVATE.tgmKey})}),observer.disconnect())})},{rootMargin:"300px 0px 0px 0px",threshold:.1}).observe(headerAd)},PRIVATE.saveClickStateBackToHome=function(event){var event=event.target,isClickableElement,tableNews,event;(event.closest(".card")||event.closest(".table-news"))&&(tableNews=event.closest(".table-news"),event=event.closest(".card"),tableNews&&tableNews.hasAttribute("id")&&history.replaceState({backToHome:{tableId:tableNews.getAttribute("id"),cardId:event&&event.hasAttribute("id")?event.getAttribute("id"):null}},""))},PRIVATE.setCroupierURL=function(){var hasLive=0,croupierURL=location.protocol+"//"+location.hostname;PRIVATE.elemHomeTables&&PRIVATE.elemHomeTables.querySelector("#app-app-live-id")&&(hasLive=1),location.port&&(croupierURL+=":"+location.port),croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL+="?html=1&ch="+CONTEXT.channelId)+"&p=1&psz="+CONTEXT.pageSize)+"&cb=timeline_cb&sz="+CONTEXT.timelineSize)+"&channel_path="+CONTEXT.channelPath)+"&lomas="+CONTEXT.lomas)+"&dflt="+CONTEXT.dflt)+"&f=1&card_types="+CONTEXT.cardTypes)+"&country="+CONTEXT.country)+"&position_web=gt3&subject_tables="+CONTEXT.subjectTables)+"&special_coverage_cards="+CONTEXT.specialCoverageCards+"&terra360=1","false"===CONTEXT.prioritized&&(croupierURL+="&prioritized="+CONTEXT.prioritized),croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL=(croupierURL+="&context=country="+CONTEXT.country)+",lang="+CONTEXT.lang)+",locale="+CONTEXT.locale)+",channel="+CONTEXT.channel)+",idItemMenu="+CONTEXT.channel)+",countryLive="+CONTEXT.countryLive)+",device="+pkg.context.platform.get("type"))+",channelID="+CONTEXT.channelId)+",deliverFormat=json,channelPath="+CONTEXT.channelPath)+",tgmKey="+PRIVATE.tgmKey)+",isLatestPage="+CONTEXT.isLatestPage)+",editorialTable="+CONTEXT.editorialTable)+",playerHighlight="+CONTEXT.playerHighlight)+",tableSequence="+PRIVATE.tableAdSequence)+",adRepetitionTableCount="+PRIVATE.tableAreaAd)+",nativeAd="+CONTEXT.nativeAd)+",isHome="+CONTEXT.isHome)+",hasLive="+hasLive+",musaIdTeam="+CONTEXT.musaIdTeam)+",color_page="+CONTEXT.colorPage)+",truvid="+CONTEXT.truvid)+",read_autoplay="+(hasLive?"True":"")+",taboola="+CONTEXT.taboola,CONTEXT.croupier&&(croupierURL+="&croupier="+CONTEXT.croupier),CONTEXT.renderer&&(croupierURL+="&renderer="+CONTEXT.renderer),PRIVATE.croupierURL=croupierURL},PRIVATE.fetchFeed=function(){return fetch(PRIVATE.croupierURL,{method:"GET",headers:{"Content-Type":"application/json","X-XACT-Function":"timeline","X-XAct-Original-UUID":CONTEXT.feedUuid}})},PRIVATE.getCroupierFeed=function(){PRIVATE.deleteOldCaches(PRIVATE.cacheName).then(function(){caches.open(PRIVATE.cacheName).then(function(cacheStorage){return PRIVATE.fetchFeed().then(function(response){return cacheStorage.put(PRIVATE.croupierURL,response.clone()),response}).catch(function(e){return window.tga.send("send","event","info","error","croupier request: name:"+e.name+" - msg:"+e.message),cacheStorage.match(PRIVATE.croupierURL).then(function(response){return response})})}).then(function(response){return!(!response||!response.ok)&&response.json()}).then(function(json){return!!json&&PRIVATE.processFeedData(json)})})},PRIVATE.getCachedFeed=function(){caches.open(PRIVATE.cacheName).then(function(cacheStorage){return cacheStorage.match(PRIVATE.croupierURL).then(function(response){return void 0!==response?response:PRIVATE.fetchFeed().then(function(response){return cacheStorage.put(PRIVATE.croupierURL,response.clone()),response}).catch(function(e){window.tga.send("send","event","info","error","croupier request: name:"+e.name+" - msg:"+e.message)})})}).then(function(response){return!(!response||!response.ok)&&response.json()}).then(function(json){return!!json&&void PRIVATE.processFeedData(json)})},PRIVATE.deleteOldCaches=function(currentCache){return caches.keys().then(function(keys){return Promise.all(keys.map(function(key){var isOurCache;if("infinite-v"===key.slice(0,10))return caches.delete(key)}))})},PRIVATE.processFeedData=function(data){PRIVATE.parsePageData(data),PRIVATE.backToHomeScroll()},PRIVATE.parsePageData=function(data){var x,elemTable,elemTemp,nameApp,dataApp;if(PRIVATE.tablesBuffer=[],PRIVATE.includerComponents={},data&&data.tables&&data.timelineApps){for(x in data.tables)data.tables.hasOwnProperty(x)&&data.tables[x]&&data.tables[x].html&&((elemTemp=(elemTable=PRIVATE.createTableFragment(data.tables[x].html)).querySelector(".table-news"))&&(nameApp=elemTemp.htmlDataset("appName"))&&(PRIVATE.includerComponents[nameApp]=!0),PRIVATE.tablesBuffer.push(elemTable));for(x in"mob"!==pkg.context.platform.get("type")&&Object.keys(PRIVATE.includerComponents)&&pkg.includer.load(Object.keys(PRIVATE.includerComponents)),data.timelineApps)if(data.timelineApps.hasOwnProperty(x)){dataApp=data.timelineApps[x],CONTEXT.timelineApps[x]=dataApp;for(var i=0;i<dataApp.items.length;i++)"A"==dataApp.items[i].type&&(CONTEXT.timelineApps[dataApp.items[i].id]=dataApp.items[i])}CONTEXT.isHome&&pkg.require(["mod.taboola"])}0<PRIVATE.tablesBuffer.length&&PRIVATE.pagination()},PRIVATE.createTableFragment=function(tableHTML){var elemTemp=document.createElement("div"),fragment=document.createDocumentFragment();elemTemp.innerHTML=tableHTML.trim();for(var i=0;i<elemTemp.childNodes.length;i++)fragment.appendChild(elemTemp.childNodes[i]);return fragment},PRIVATE.pagination=function(){var observerFooter;PRIVATE.elemFooter=document.getElementById("zaz-app-t360-footer"),PRIVATE.elemFooter.classList.add("loading-app"),new IntersectionObserver(function callbackPagination(entries,observer){entries.forEach(function(entry){console.log("##### observer - callbackPagination target",entry.target),entry.isIntersecting&&0===PRIVATE.insertTables()&&observer.disconnect()})},{rootMargin:"0px 0px 100px 0px",threshold:.01}).observe(PRIVATE.elemFooter)},PRIVATE.insertTables=function(){for(var elemTable,elemTableNews,i=0;i<PRIVATE.config.pagination;i++)0<PRIVATE.tablesBuffer.length&&(PRIVATE.tableLoadingComplete=!1,(elemTableNews=(elemTable=PRIVATE.tablesBuffer.shift()).querySelectorAll(".table-news"))&&(PRIVATE.elemHomeTables.appendChild(elemTable),PRIVATE.setTableEvents(elemTableNews),elemTableNews.forEach(function(elem){elem.addEventListener("click",PRIVATE.saveClickStateBackToHome)})));return 0===PRIVATE.tablesBuffer.length&&(PRIVATE.elemFooter.classList.remove("loading-app"),PRIVATE.hasFooter||PRIVATE.loadFooter()),PRIVATE.tablesBuffer.length},PRIVATE.loadFooter=function(){PRIVATE.hasFooter=!0,pkg.require("app.t360.footer",function(Footer){var elem=document.getElementById("zaz-app-t360-footer"),f;elem&&(f=new Footer({container:elem}))},function(exception){console.error('Exception requirering "app.t360.footer": ',exception)})},PRIVATE.backToHomeScroll=function(){if(PRIVATE.isBackForward&&PRIVATE.backToHome){var isMob=!1;for("mob"===pkg.context.platform.get("type")&&(isMob=!0);PRIVATE.insertTables(),!PRIVATE.checkTableOrCardExists(isMob,PRIVATE.backToHome.tableId,PRIVATE.backToHome.cardId)&&0<PRIVATE.tablesBuffer.length;);var elementScroll=PRIVATE.checkTableOrCardExists(isMob,PRIVATE.backToHome.tableId,PRIVATE.backToHome.cardId);elementScroll&&(elementScroll.scrollIntoView({block:"center",inline:"center"}),history.replaceState(null,""))}},PRIVATE.checkTableOrCardExists=function(isMob,tableId,cardId){var tableId=document.getElementById(tableId),cardId=document.getElementById(cardId);return isMob?cardId||tableId:tableId||cardId},PRIVATE.setBackendTablesEvent=function(backendTables){backendTables.forEach(function(table){table.addEventListener("click",function(){history.replaceState(null,"")})})},this.init=function(){PRIVATE.elemHomeTables=document.getElementById("home-tables");var elemTables=PRIVATE.elemHomeTables.querySelectorAll(".table-news");PRIVATE.getContext(),PRIVATE.configureAdManager(),PRIVATE.setBackendTablesEvent(elemTables),PRIVATE.setTableEvents(elemTables),PRIVATE.setCroupierURL(),CONTEXT.hasPagination&&(PRIVATE.isBackForward?PRIVATE.getCachedFeed():PRIVATE.getCroupierFeed()),PRIVATE.loadHeaderAd()},this},teardown:function(why,__static,__proto,__shared){}})}),zaz.use(function regMetrics(pkg){"use strict";pkg.factoryManager.get("mod").create({name:"regMetrics",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/zaz-ui-portal-home",source:"http://github.tpn.terra.com/Terra/zaz-ui-portal-home",description:"Registra as metricas das mesas",dependencies:["app.t360.cards"],tests:"http://s1.trrsf.com/fe/zaz-ui-portal-home/tests/index.htm?zaz[env]=tests",setup:function(data){var MODULE={},PRIVATE={},data=(PRIVATE.templateType=pkg.context.publisher.get("template"),PRIVATE.objTerraMetricsBuffer={},PRIVATE.objCards=new data.dependencies.app.t360.cards({}),pkg.context.page.at("register-metrics",function(data){var tableData;data&&data.area&&data.sender&&"card-view"===data.type&&("TABLE-APP"===data.tableType?PRIVATE.regTerraMetrics(data):"TABLE-NEWS-APP"===data.tableType?PRIVATE.objTerraMetricsBuffer[data.area]&&(PRIVATE.objTerraMetricsBuffer[data.area].isVisiable||data.isVisiable)?(tableData=PRIVATE.objTerraMetricsBuffer[data.area].data,"app.infinite"!==data.sender&&data.cards&&(tableData.cards=data.cards),tableData.cards&&PRIVATE.regTerraMetrics(tableData),PRIVATE.objTerraMetricsBuffer[data.area]=null):PRIVATE.objTerraMetricsBuffer[data.area]={data:data,isVisiable:data.isVisiable||!1}:"NEWS"===data.tableType&&data&&data.cards&&PRIVATE.regTerraMetrics(data))}),PRIVATE.regTerraMetrics=function(data){var objCard,duplicateItems={},viewItemList=[],itemList,item;data.position&&1==data.position.toString().length&&(data.position="0"+data.position);for(var i=0;i<data.cards.length;i++)itemList=item=null,duplicateItems[(objCard=data.cards[i]).id]||(duplicateItems[objCard.id]=!0,"AD"!==objCard.htmlDataset("type")&&("P"===objCard.htmlDataset("type")?itemList=PRIVATE.getMetricPremium(objCard,data):item=PRIVATE.objCards.registerMetricCard(objCard,"addImpression",data.area,data.position),item&&""!=item.item_id?viewItemList.push(item):itemList&&0<itemList.length&&(viewItemList=viewItemList.concat(itemList))));window.tga.sendGA4("view_item_list",{item_list_id:data.area,item_list_name:data.area,items:viewItemList})},PRIVATE.getMetricPremium=function(objPremium,data){for(var item,reason=objPremium.htmlDataset("reason"),reasonDetail=objPremium.htmlDataset("reasonDetail"),items=objPremium.querySelectorAll(".metric-item"),duplicateItems={},viewItemList=[],i=0;i<items.length;i++)duplicateItems[items[i].id]||(duplicateItems[items[i].id]=!0,reason&&items[i].htmlDataset("reason",reason),reasonDetail&&items[i].htmlDataset("reasonDetail",reasonDetail),(item=PRIVATE.objCards.registerMetricCard(items[i],"addImpression",data.area,data.position))&&""!=item.item_id&&viewItemList.push(item));return viewItemList},document.querySelector(".metrics-menu-aberto"));return data&&window.tga.event("menu-aberto","click",data.querySelectorAll("li a")),MODULE},teardown:function(data){}})}),zaz.use(function adAnchor(pkg){"use strict";pkg.require(["modFactory"],function(modFactory){modFactory.create({name:"adAnchor",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/zaz-ui-portal-content",source:"http://github.tpn.terra.com/Terra/zaz-ui-portal-content",description:"Controls the Footer Home",tests:"http://s1.trrsf.com/fe/zaz-ui-portal-content/tests/index.htm?zaz[env]=tests",dependencies:["mod.adsCommon"],setup:function(data){var PRIVATE={},MODULE={},elemCardAd=(PRIVATE.startedAd=!1,PRIVATE.objAdsCommon=data.dependencies.mod.adsCommon,document.querySelector("#home-tables .table-news .card-ad__content"));return PRIVATE.controlPosition=function(){var canLoaded=!1,rect;(canLoaded=!elemCardAd||"mob"===pkg.context.platform.get("type")||elemCardAd.getBoundingClientRect().y<-300?!0:canLoaded)&&!PRIVATE.startedAd&&PRIVATE.startAd()},MODULE.init=function(data){window.addEventListener("scroll",PRIVATE.controlPosition,!1,{controller:"none",time:0})},PRIVATE.startAd=function(){PRIVATE.startedAd=!0,PRIVATE.elemAdWrapper=document.createElement("div"),PRIVATE.elemAdWrapper.className="bottom-ad-container",PRIVATE.elemAdWrapper.innerHTML='<div class="bottom-ad-container__full" id="bottom-ad"></div>',PRIVATE.elemAdContainer=PRIVATE.elemAdWrapper.querySelector("#bottom-ad"),document.body.appendChild(PRIVATE.elemAdWrapper),PRIVATE.objAdsCommon.loadAdAnchor(PRIVATE.elemAdContainer).then(function(){!0!==window.AdManager.get("removeAncoraButton")&&PRIVATE.addCloseButton()})},PRIVATE.addCloseButton=function(){var elemCloseButton=document.createElement("div");elemCloseButton.className="bottom-ad-container__close-button",elemCloseButton.innerHTML='<span class="icon-solid icon-12 icon-times icon-color-black"></span>',elemCloseButton.addEventListener("click",function(){window.removeEventListener("scroll",PRIVATE.controlPosition,!1),PRIVATE.elemAdWrapper.style.display="none",window.tga.send("send","event","ad","close","ancora")}),PRIVATE.elemAdWrapper.appendChild(elemCloseButton)},MODULE},teardown:function(data){}})})}),zaz.use(function HeaderTeam(pkg){"use strict";pkg.factoryManager.get("mod").create({name:"headerTeam",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/Terra/zaz-ui-t360-home",source:"http://github.tpn.terra.com/Terra/zaz-ui-t360-home",description:"Montar o header do channel do time",dependencies:["mod.t360.notificationsHelper"],tests:"",setup:function(data){var MODULE={},PRIVATE={data:data};return MODULE.init=function(){PRIVATE.myTeam=document.querySelector("#my-team"),PRIVATE.myTeamIcon=document.querySelector("#my-team-icon"),PRIVATE.myTeamLabel=document.querySelector(".header-team__my-team--label"),PRIVATE.musaId=pkg.context.page.get("musaIdTeam"),PRIVATE.myTeamLabelTextSelected="Meu time",PRIVATE.myTeamLabelTextUnselected="Favoritar Time",PRIVATE.saveMyTeam=function(){var objSave={name:PRIVATE.pageTeamInfo.name,channel_id:PRIVATE.pageTeamInfo.channelId,musa_id:PRIVATE.pageTeamInfo.id,userTeam:{teamChannelId:PRIVATE.pageTeamInfo.channelId,teamId:PRIVATE.pageTeamInfo.id||"",teamName:PRIVATE.pageTeamInfo.name||"",teamMnemonic:PRIVATE.pageTeamInfo.mnemonic||"",teamCountry:pkg.context.page.getCountry()||"",teamUrl:PRIVATE.pageTeamInfo.url,iconURL:PRIVATE.pageTeamInfo.shield}};PRIVATE.myTeamId=PRIVATE.musaId,window.tga.send("send","event","futebol","button",PRIVATE.pageTeamInfo.name),pkg.context.user.set("myTeam",objSave)},PRIVATE.removeMyTeam=function(){PRIVATE.myTeamId=null,pkg.context.user.set("myTeam",""),console.log("removeMyTeam")},PRIVATE.changeMyTeam=function(){PRIVATE.myTeamId?PRIVATE.musaId==PRIVATE.myTeamId?(PRIVATE.myTeamIcon.classList.remove("icon-star"),PRIVATE.myTeamIcon.classList.add("icon-star-regular"),PRIVATE.myTeamLabel&&(PRIVATE.myTeamLabel.innerHTML=PRIVATE.myTeamLabelTextUnselected),PRIVATE.removeMyTeam()):(pkg.context.user.get("header",function(header){var country=pkg.context.page.getCountry(),segmentsAdd=country+"_matches_"+PRIVATE.musaId,country;header.FirebaseToken&&(PRIVATE.myTeam&&(country=country+"_matches_"+PRIVATE.myTeam,PRIVATE.data.dependencies.mod.t360.notificationsHelper.postSegments("segments_remove",country,header.FirebaseToken,"sw-myteammatches","fcm").then(function(){console.log("postSegments segmento time antigo removido com sucesso")})),header.notifications&&header.notifications.myteammatches&&PRIVATE.data.dependencies.mod.t360.notificationsHelper.postSegments("segments_add",segmentsAdd,header.FirebaseToken,"sw-myteammatches","fcm").then(function(){console.log("postSegments segmento time novo adicionado com sucesso")}))}),PRIVATE.myTeamIcon.classList.remove("icon-star-regular"),PRIVATE.myTeamIcon.classList.add("icon-star"),PRIVATE.myTeamLabel&&(PRIVATE.myTeamLabel.innerHTML=PRIVATE.myTeamLabelTextSelected),PRIVATE.saveMyTeam()):(PRIVATE.myTeamIcon.classList.remove("icon-star-regular"),PRIVATE.myTeamIcon.classList.add("icon-star"),PRIVATE.myTeamLabel&&(PRIVATE.myTeamLabel.innerHTML=PRIVATE.myTeamLabelTextSelected),PRIVATE.saveMyTeam())},PRIVATE.retrieveTeamInfo=function(){PRIVATE.pageTeamInfo||fetch("https://p1-cloud.trrsf.com/api/musa-api/profile-contents?lang=pt-BR&id="+PRIVATE.musaId).then(function(response){return response.json()}).then(function(json){var json=json.root.result.profiles[0],potentialChannelId=json.contents.filter(function(channel){return 12==channel.id_content}),channelId="",potentialChannelId=(potentialChannelId.length&&(channelId=potentialChannelId[0].url_content),"");json.contents.length&&json.contents[0].url_content&&(potentialChannelId=json.contents[0].url_content),PRIVATE.pageTeamInfo={id:json.id,name:json.name_competitor,channelId:channelId,shield:PRIVATE.myTeam.getAttribute("data-team-shield"),url:potentialChannelId,mnemonic:json.mnemonic}}),console.log(PRIVATE.pageTeamInfo)},PRIVATE.myTeam&&(PRIVATE.retrieveTeamInfo(),pkg.context.user.get("myTeam",function(objUserTeam){objUserTeam&&objUserTeam.musa_id?(PRIVATE.myTeamId=objUserTeam.musa_id,PRIVATE.myTeamId==PRIVATE.musaId?(PRIVATE.myTeamIcon.classList.add("icon-star"),PRIVATE.myTeamLabel&&(PRIVATE.myTeamLabel.innerHTML=PRIVATE.myTeamLabelTextSelected)):(PRIVATE.myTeamIcon.classList.add("icon-star-regular"),PRIVATE.myTeamLabel&&(PRIVATE.myTeamLabel.innerHTML=PRIVATE.myTeamLabelTextUnselected))):(PRIVATE.myTeamIcon.classList.add("icon-star-regular"),PRIVATE.myTeamLabel&&(PRIVATE.myTeamLabel.innerHTML=PRIVATE.myTeamLabelTextUnselected)),PRIVATE.myTeamIcon.classList.add("icon-color-black"),PRIVATE.myTeam.addEventListener("click",PRIVATE.changeMyTeam,!1)}))},MODULE},teardown:function(data){}})}),zaz.use(function Countdown(pkg){"use strict";var console=pkg.console,appFactory,STATIC_PUBLIC=null,STATIC_PRIVATE={};pkg.factoryManager.get("app").create({name:"t360.countdown",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/Terra/zaz-ui-t360-content",source:"http://github.tpn.terra.com/Terra/zaz-ui-t360-content",description:"",dependencies:[],tests:"",templates:{},setup:function(STATIC_PUBLIC,PUBLIC,__shared){},init:function(data,__shared){var PUBLIC=this,PRIVATE={};return PRIVATE.elems={hour:data.container.querySelector(".app-t360-countdown__content--hour"),minute:data.container.querySelector(".app-t360-countdown__content--minute"),second:data.container.querySelector(".app-t360-countdown__content--second")},PRIVATE.updateDate=function(distancia){var distancia=distancia/1e3,h=Math.floor(distancia/3600),m=Math.floor(distancia%3600/60),distancia=Math.floor(distancia%3600%60);PRIVATE.elems.hour.innerHTML=(1==h.toString().length?"0":"")+h+"h",PRIVATE.elems.minute.innerHTML=(1==m.toString().length?"0":"")+m+"m",PRIVATE.elems.second.innerHTML=(1==distancia.toString().length?"0":"")+distancia+"s"},PRIVATE.startCountdown=function(dataInicio,dataFim){var contagem=setInterval(function(){var agora=(new Date).getTime(),distanciaInicio=new Date(dataInicio).getTime(),distanciaFim=new Date(dataFim).getTime(),distanciaInicio;agora<distanciaInicio?PRIVATE.updateDate(distanciaFim-distanciaInicio):(distanciaInicio=distanciaFim-agora)<0?clearInterval(contagem):PRIVATE.updateDate(distanciaInicio)},1e3)},PRIVATE.startCountdown("2024-11-25T16:00:00","2024-11-28T18:00:00"),this},teardown:function(data){}})}),zaz.use(function t360Home(pkg){"use strict";window.tga.sendGA4("page_view");var console=pkg.console,queryStrings=pkg.context.page.get("query"),icons=(pkg.require("mod.infinite",function(AppInfinite){AppInfinite.init()},function(exception){console.error('Exception requirering "mod.infinite": ',exception)}),!1!==pkg.context.page.get("ticker")&&pkg.require("app.t360.ticker",function(AppTicker){var app=new AppTicker({placeholder:document.getElementById("zaz-app-t360-ticker"),defaultTabName:"esportes"})},function(exception){console.error('Exception requirering "app.t360.ticker": ',exception)}),0<pkg.context.page.get("musaIdTeam")&&pkg.require("mod.headerTeam",function(HeaderTeam){HeaderTeam.init()},function(exception){console.error('Exception requirering "mod.headerTeam": ',exception)}),!1!==pkg.context.page.get("header")&&pkg.require("app.t360.navbar",function(AppNavbar){var app=new AppNavbar},function(exception){console.error('Exception requirering "app.t360.navbar": ',exception)}),pkg.context.page.at("hash",function(data){data&&"#xact"==data.current&&pkg.require("app.t360.dashboard")}),pkg.utils.cheats.create("xact".split(""),function(){pkg.require("app.t360.dashboard")}),document.querySelectorAll(".t360-partner-icons > a")),loadPageTs,icons=(0<icons.length&&window.tga.event("social networks","click",icons),!1!==pkg.context.page.get("loadAds")&&pkg.require(["mod.adAnchor"],function(adHeader){adHeader.init()}),!pkg.context.page.get("isHome")||queryStrings&&1==queryStrings.tpn||(loadPageTs=Date.now(),window.addEventListener("focus",function(){Date.now()>=loadPageTs+6e5&&zaz.use(function(pkg){pkg.require(["app.player"],function(P){if(P.instances&&0<P.instances.length)for(var players=P.instances,index=0,player;index<players.length;index++){if(players[index].videojsPlayer._PRIVATE.state.get("userInteraction"))return}window.location.reload(),window.scrollTo(0,0)})})})),pkg.context.page.get("mnemonic")),championships,elemCountdown;icons&&["libertadores","brasileiro-serie-a","brasileiro-serie-b","copa-do-brasil","sudamericana"].includes(icons)&&pkg.require("app.t360.championshipTeams",function(ChampionshipTeams){var championshipTeams=new ChampionshipTeams({content:document.querySelector(".app-t360-championship-teams")})},function(exception){console.error('Exception requirering "app.t360.championshipTeams": ',exception)}),!pkg.context.page.get("isHome")||(elemCountdown=document.querySelector(".app-t360-countdown"))&&pkg.require("app.t360.countdown",function(Countdown){var app=new Countdown({container:elemCountdown})},function(exception){console.error('Exception requirering "app.t360.countdown": ',exception)})});