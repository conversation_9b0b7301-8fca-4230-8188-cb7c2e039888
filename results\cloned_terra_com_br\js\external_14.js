/*! zaz-app-t360-teams - v1.0.0 - 04/11/2024 -- 9:40pm */

zaz.use(function appTeams(pkg){"use strict";var appFactory;pkg.factoryManager.get("app").create({name:"t360.teams",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/zaz-app-t360-teams",source:"http://github.tpn.terra.com/Terra/zaz-app-t360-teams",description:"Just another app",tests:"http://s1.trrsf.com/fe/zaz-app-t360-teams/tests/index.htm?zaz[env]=tests",dependencies:[],dictionaries:[],templates:{index:'{% set title = card_data.title %}{% set objReason = card_data|get_reasons %}<div class="card card-app app-{{ card_data.id }} {{ card_info.class }} loading-app" id="app-{{ card_data.id }}" data-type="A" data-app-name="{{ card_data.name }}" data-render-type="backend" data-id="{{ card_data.id }}" data-reason="{{ objReason[\'reason\'] }}" data-ga-label="{{ card_data.name }}{{ \' - \' + title|e if title else \'\' }}" title="Futebol" data-source-name="APP"><div class="app-teams"><header class="app-teams__header"><div class="app-teams__header__title"><a class="app-teams__header__title__link" target="_top" href="https://www.terra.com.br/esportes/futebol/">Campeonato Brasileiro</a><span class="app-teams__header__title--bell icon-solid icon-24 icon-color icon-bell" data-click="false"></span></div><nav class="app-teams__header__menu"><li class="app-teams__header__item"><span class="app-teams__button" data-id="256"> Série A </span19/11/2019></li><li class="app-teams__header__item"><span class="is-disabled app-teams__button" data-id="257"> Série B </span></li></nav></header><div class="app-teams__content"></div></div></div>',teams:'{% for champ in champs %}<ul class="app-teams__content__list {{ \'is-active\' if loop.index == 1 else \'\' }}" data-id="{{ champ.id }}">{% for team in champ.teams %}<li class="app-teams__team" data-id="{{ team.teamId }}"><a {% if renderMode==\'normal\' %} href="{{ team.url }}" {% endif %} alt="{{ team.name }}" title="{{ team.name }}"><span class="icon icon-32 team--{{ team.channelId }}"></span></a></li>{% endfor %}</ul>{% endfor %}'},expects:{type:"object",required:!1,properties:{exhibitionMode:{type:"string",default:"normal",required:!1},data:{type:"object",required:!1}}},setup:function(STATIC_PUBLIC,PUBLIC,__shared){},init:function(data,__shared){var PRIVATE={},PUBLIC=this;return!!data&&(PRIVATE.data=data,PRIVATE.feedMusa="https://p1-cloud.trrsf.com/api/musa-api/locale-team-guide?acronym=BRA&lang=pt-BR",PRIVATE.contentTypes={channelId:12,url:1},PRIVATE.loadFeed=function(){fetch(PRIVATE.feedMusa).then(function(response){return response.json()}).then(function(data){PRIVATE.champsFeed=PRIVATE.canonizeFeed(data),PRIVATE.storeTeamsInfo(),PRIVATE.render(),PRIVATE.bindEvents()})},PRIVATE.canonizeFeed=function(feedData){var feed=[];if(feedData&&feedData.root&&feedData.root.result&&feedData.root.result.championships)for(var champs=feedData.root.result.championships,i=0;i<champs.length;i++)feed.push({id:champs[i].id,name:champs[i].name,url:PRIVATE.getFeedInfo(champs[i].contents,PRIVATE.contentTypes.url),shortname:champs[i].shortname,teams:PRIVATE.getChampionshipTeams(champs[i].teams),renderMode:PRIVATE.data.exhibitionMode});return feed},PRIVATE.getChampionshipTeams=function(feedTeams){for(var teams=[],i=0;i<feedTeams.length;i++)teams.push({name:feedTeams[i].name,channelId:PRIVATE.getFeedInfo(feedTeams[i].contents,PRIVATE.contentTypes.channelId),url:PRIVATE.getFeedInfo(feedTeams[i].contents,PRIVATE.contentTypes.url),teamId:feedTeams[i].id,mnemonic:feedTeams[i].mnemonic,urlPicture:feedTeams[i].url_picture});return teams},PRIVATE.storeTeamsInfo=function(){PRIVATE.teamsInfos={};for(var i=0;i<PRIVATE.champsFeed.length;i++)for(var j=0;j<PRIVATE.champsFeed[i].teams.length;j++)PRIVATE.teamsInfos[PRIVATE.champsFeed[i].teams[j].teamId]={musaId:PRIVATE.champsFeed[i].teams[j].teamId,channelId:PRIVATE.champsFeed[i].teams[j].channelId,url:PRIVATE.champsFeed[i].teams[j].url,name:PRIVATE.champsFeed[i].teams[j].name,mnemonic:PRIVATE.champsFeed[i].teams[j].mnemonic,urlPicture:PRIVATE.champsFeed[i].teams[j].urlPicture}},PRIVATE.getFeedInfo=function(obj,infoTypeId){var obj=obj.filter(function(obj){if(obj.id==infoTypeId)return!0});return obj&&0<obj.length?obj[0].url_content:null},PRIVATE.getReasons=function(json){var objReason={reason:"",reasonDetail:""},objLocation;if(json.croupier&&json.croupier.reasons){for(var k in json.croupier.reasons)json.croupier.reasons.hasOwnProperty(k)&&(objReason.reason+=k);json.croupier.reasons.p&&json.croupier.reasons.p.segments?objReason.reasonDetail=json.croupier.reasons.p.segments:json.croupier.reasons.x&&json.croupier.reasons.x.segments?objReason.reasonDetail=json.croupier.reasons.x.segments:json.croupier.reasons.g&&json.croupier.reasons.g.location?(objLocation=json.croupier.reasons.g.location,objReason.reasonDetail=objLocation.country,objLocation.state&&(objReason.reasonDetail+="/"+objLocation.state),objLocation.city&&(objReason.reasonDetail+="/"+objLocation.city)):json.croupier.reasons.f&&json.croupier.reasons.f.position&&(objReason.reasonDetail=json.croupier.reasons.f.position)}return objReason},PRIVATE.getTitle=function(card){var title="",title;return title=card.fullTitle&&""!=card.fullTitle?card.fullTitle:card.title},PRIVATE.render=function(){var elemContent;PUBLIC.templates.engine.addFilter("get_reasons",function(){}),"backend"!=PRIVATE.data.container.htmlDataset("renderType")&&(PUBLIC.templates.engine.addFilter("get_reasons",PRIVATE.getReasons),PUBLIC.templates.engine.addFilter("get_title",PRIVATE.getTitle),elemContent=PUBLIC.templates.render("index",{card_data:{id:"fakeid-navbar",name:"app.t360.teams",items:[{fullTitle:"teste"}]},card_info:{}}),PRIVATE.data.container.appendChild(elemContent.children[0].firstChild));var elemChamps=PUBLIC.templates.render("teams",{champs:PRIVATE.champsFeed,renderMode:PRIVATE.data.exhibitionMode}),elemContent=PRIVATE.data.container.querySelector(".app-teams__content");elemContent&&(elemContent.appendChild(elemChamps),PRIVATE.elemChamps=elemContent.querySelectorAll(".app-teams__content__list")),PRIVATE.listTeams=PRIVATE.data.container.querySelector(".app-teams__content__list")},PRIVATE.bindEvents=function(){PRIVATE.elemButtons=PRIVATE.data.container.querySelectorAll(".app-teams__button"),PRIVATE.elemButtons&&PRIVATE.elemButtons.forEach(function(elem){elem.addEventListener("click",PRIVATE.changeChamp)}),"normal"!=PRIVATE.data.exhibitionMode&&(PRIVATE.teamsIcons=PRIVATE.data.container.querySelectorAll(".app-teams__team"),PRIVATE.teamsIcons&&PRIVATE.teamsIcons.forEach(function(elem){elem.addEventListener("click",function(){pkg.context.page.trigger("app-t360-teams",PRIVATE.teamsInfos[elem.getAttribute("data-id")])})}))},PRIVATE.changeChamp=function(evt){var id=evt.currentTarget.htmlDataset("id");PRIVATE.elemButtons.forEach(function(elem){elem.getClassList().add("is-disabled")}),evt.currentTarget.getClassList().remove("is-disabled"),PRIVATE.elemChamps.forEach(function(elem){id!=elem.htmlDataset("id")?elem.getClassList().remove("is-active"):elem.getClassList().add("is-active")})},PRIVATE.configNotificatins=function(){PRIVATE.btBell=PRIVATE.data.container.querySelector(".app-teams__header__title--bell"),PRIVATE.nav=PRIVATE.data.container.querySelector(".app-teams__header__menu"),PRIVATE.teamsContent=PRIVATE.data.container.querySelector(".app-teams__content"),pkg.require(["app.t360.teamsNotifications"],function(T360TeamsNotifications){PRIVATE.templateTeamsNotification=new T360TeamsNotifications({components:{nav:PRIVATE.nav,btBell:PRIVATE.btBell},container:PRIVATE.teamsContent})})},PRIVATE.configNotificatins(),PRIVATE.loadFeed(),PUBLIC)},teardown:function(why,__static,__proto,__shared){}})});