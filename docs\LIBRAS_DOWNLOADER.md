# 🎬 Libras Video Downloader - Documentação Completa

> **Sistema especializado para download completo da base de dados V-LIBRASIL**  
> Download automatizado de 4089+ vídeos de Libras com organização inteligente

## 🎯 **Visão Geral**

O **Libras Video Downloader** é um sistema especializado desenvolvido para fazer download completo da base de dados V-LIBRASIL (https://libras.cin.ufpe.br), que contém vídeos de sinais de Libras executados por diferentes articuladores.

### 📊 **Estatísticas da Base**
- **Total de páginas**: 69
- **Sinais únicos**: 1364
- **Total de vídeos**: 4089+ (3 articuladores por sinal)
- **Tamanho estimado**: 2-5GB
- **Formato**: MP4 em alta qualidade

## 🛠️ **Ferramentas Disponíveis**

### 1. 🔍 **Analisador de Estrutura**
**Arquivo**: `tools/analyze_libras_structure.py`

Analisa a estrutura da página para entender como os vídeos estão organizados.

```bash
python tools/analyze_libras_structure.py
```

**Funcionalidades:**
- Análise da estrutura HTML
- Identificação de padrões de URL
- Teste de acesso a vídeos
- Mapeamento da paginação

### 2. 🧪 **Testador de Download**
**Arquivo**: `tools/test_download_sample.py`

Testa o download de uma pequena amostra para verificar funcionamento.

```bash
python tools/test_download_sample.py
```

**Funcionalidades:**
- Download de 3 vídeos de teste
- Verificação de integridade
- Teste de nomenclatura
- Validação do sistema

### 3. 🎬 **Downloader Completo**
**Arquivo**: `tools/libras_downloader_complete.py`

Sistema principal para download de todos os vídeos.

```bash
python tools/libras_downloader_complete.py
```

**Funcionalidades:**
- Download de todas as 69 páginas
- Processamento de 4089+ vídeos
- Sistema de retry automático
- Rate limiting respeitoso
- Relatórios detalhados
- Controle de progresso

### 4. 🗂️ **Organizador de Vídeos**
**Arquivo**: `tools/libras_video_organizer.py`

Organiza os vídeos baixados em diferentes estruturas.

```bash
python tools/libras_video_organizer.py
```

**Funcionalidades:**
- Organização por articulador
- Organização alfabética
- Organização por categoria semântica
- Geração de catálogo HTML
- Índices JSON estruturados

## 📁 **Estrutura de Saída**

### **Downloader Completo**
```
libras_videos_complete/
├── videos/                     # Vídeos baixados
│   ├── À_noite_toda_articulador_1.mp4
│   ├── À_noite_toda_articulador_2.mp4
│   └── ...
├── metadata/                   # Metadados
│   └── complete_metadata.json
├── progress/                   # Progresso por página
│   ├── page_01.json
│   └── ...
└── reports/                    # Relatórios
    └── final_report_YYYYMMDD_HHMMSS.txt
```

### **Organizador**
```
libras_organized/
├── por_articulador/           # Por articulador
│   ├── articulador_1/
│   ├── articulador_2/
│   └── articulador_3/
├── alfabetico/                # Por letra
│   ├── A/
│   ├── B/
│   └── ...
├── por_categoria/             # Por categoria semântica
│   ├── Tempo/
│   ├── Animais/
│   ├── Alimentos/
│   └── ...
├── indices/                   # Índices e catálogos
│   ├── indice_geral.json
│   ├── indice_alfabetico.json
│   └── catalogo.html
└── relatorios/               # Relatórios
    └── organizacao_YYYYMMDD_HHMMSS.txt
```

## 🚀 **Guia de Uso Rápido**

### **Passo 1: Teste Inicial**
```bash
# Testar com amostra pequena
python tools/test_download_sample.py
```

### **Passo 2: Download Completo**
```bash
# Download de todos os vídeos (pode demorar várias horas)
python tools/libras_downloader_complete.py
```

### **Passo 3: Organização**
```bash
# Organizar vídeos baixados
python tools/libras_video_organizer.py
```

### **Passo 4: Visualização**
```bash
# Abrir catálogo HTML
start libras_organized/indices/catalogo.html
```

## ⚙️ **Configurações Avançadas**

### **Rate Limiting**
O sistema usa rate limiting respeitoso:
- 1 segundo entre páginas
- 2 segundos entre downloads
- Máximo 2 threads paralelas

### **Retry e Recuperação**
- Retry automático em falhas
- Verificação de integridade
- Continuação de downloads interrompidos

### **Nomenclatura de Arquivos**
```
[NOME_DO_SINAL]_articulador_[1-3].mp4
```

Exemplos:
- `À_noite_toda_articulador_1.mp4`
- `Abacaxi_articulador_2.mp4`
- `Acenar_articulador_3.mp4`

## 📊 **Categorias Semânticas**

O organizador classifica sinais em categorias:

- **Tempo**: noite, tarde, manhã, dia, hora, tempo
- **Animais**: abelha, gato, cachorro, pássaro, peixe
- **Alimentos**: abacaxi, abóbora, comida, fruta, verdura
- **Ações**: abanar, abandonar, abençoar, abraço, acenar
- **Lugares**: academia, casa, escola, hospital
- **Sentimentos**: amor, raiva, feliz, triste
- **Família**: pai, mãe, filho, irmão
- **Cores**: azul, vermelho, verde, amarelo
- **Números**: um, dois, três, quatro, cinco
- **Outros**: categoria padrão para sinais não classificados

## 🎯 **Casos de Uso**

### **Pesquisa Acadêmica**
- Análise de variações entre articuladores
- Estudos de consistência de sinais
- Desenvolvimento de algoritmos de reconhecimento

### **Educação**
- Material didático para ensino de Libras
- Comparação de execução de sinais
- Treinamento de intérpretes

### **Desenvolvimento de Software**
- Dataset para machine learning
- Aplicações de reconhecimento de sinais
- Sistemas de tradução automática

### **Preservação Cultural**
- Backup da base de dados V-LIBRASIL
- Arquivo histórico de sinais de Libras
- Documentação da língua

## 🔧 **Requisitos Técnicos**

### **Dependências Python**
```bash
pip install requests beautifulsoup4 pathlib
```

### **Espaço em Disco**
- Mínimo: 5GB livres
- Recomendado: 10GB livres

### **Conexão de Internet**
- Estável para downloads longos
- Velocidade mínima: 1 Mbps

### **Tempo Estimado**
- Download completo: 2-6 horas
- Organização: 10-30 minutos
- Total: 3-7 horas

## 📋 **Logs e Monitoramento**

### **Arquivos de Log**
- `libras_downloader_complete.log`: Log detalhado do download
- Console: Progresso em tempo real

### **Métricas Disponíveis**
- Páginas processadas
- Vídeos encontrados vs baixados
- Taxa de sucesso
- Velocidade de download
- Tamanho total baixado

## 🎉 **Resultados Esperados**

Após execução completa, você terá:

✅ **4089+ vídeos** de Libras organizados  
✅ **Catálogo HTML** navegável  
✅ **Índices JSON** estruturados  
✅ **Organização múltipla** (articulador, alfabética, categoria)  
✅ **Relatórios detalhados** de estatísticas  
✅ **Base completa** da V-LIBRASIL preservada  

## 🤝 **Contribuição**

Para melhorar o sistema:

1. **Novas categorias semânticas**
2. **Otimizações de performance**
3. **Funcionalidades de análise**
4. **Melhorias na organização**

## 📄 **Licença e Uso Ético**

- Respeite os termos de uso do site V-LIBRASIL
- Use rate limiting apropriado
- Cite a fonte original em pesquisas
- Contribua de volta para a comunidade

---

**🎬 Sistema desenvolvido como parte do WebScraper Enterprise 4.0**  
**📧 Para suporte: consulte a documentação principal do projeto**
