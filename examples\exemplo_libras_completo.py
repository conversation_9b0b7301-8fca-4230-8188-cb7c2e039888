#!/usr/bin/env python3
"""
🎬 EXEMPLO COMPLETO - LIBRAS VIDEO DOWNLOADER
Demonstração do uso completo do sistema de download de vídeos de Libras.

Este exemplo mostra como:
1. Fazer download de uma amostra
2. Executar download completo
3. Organizar os vídeos
4. Gerar relatórios
"""

import os
import sys
from pathlib import Path

# Adicionar o diretório tools ao path
sys.path.append(str(Path(__file__).parent.parent / "tools"))

def exemplo_teste_rapido():
    """Exemplo de teste rápido com amostra."""
    print("🧪 EXEMPLO 1: TESTE RÁPIDO")
    print("="*50)
    
    try:
        from test_download_sample import test_download_sample
        
        print("📥 Executando download de amostra...")
        success = test_download_sample()
        
        if success:
            print("✅ Teste bem-sucedido!")
            print("📁 Vídeos salvos em: test_libras_sample/")
            print("🎯 Pronto para executar download completo")
        else:
            print("❌ Teste falhou - verifique a conexão")
            
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        print("💡 Execute a partir do diretório raiz do projeto")

def exemplo_download_completo():
    """Exemplo de download completo."""
    print("\n🎬 EXEMPLO 2: DOWNLOAD COMPLETO")
    print("="*50)
    
    print("⚠️  ATENÇÃO: Este processo pode demorar várias horas!")
    print("📊 Estimativa: ~4089 vídeos, 2-5GB de dados")
    
    resposta = input("🤔 Deseja continuar? (s/N): ").strip().lower()
    
    if resposta in ['s', 'sim', 'y', 'yes']:
        try:
            from libras_downloader_complete import LibrasCompleteDownloader
            
            print("🚀 Iniciando download completo...")
            downloader = LibrasCompleteDownloader(max_workers=2)
            downloader.run_complete_download()
            
            print("🎉 Download completo finalizado!")
            
        except ImportError as e:
            print(f"❌ Erro de importação: {e}")
        except Exception as e:
            print(f"❌ Erro durante download: {e}")
    else:
        print("❌ Download cancelado pelo usuário")

def exemplo_organizacao():
    """Exemplo de organização dos vídeos."""
    print("\n🗂️ EXEMPLO 3: ORGANIZAÇÃO DOS VÍDEOS")
    print("="*50)
    
    # Verificar se existem vídeos para organizar
    possible_dirs = [
        "libras_videos_complete",
        "test_libras_sample",
        "libras_videos"
    ]
    
    source_dir = None
    for dir_name in possible_dirs:
        if Path(dir_name).exists():
            source_dir = dir_name
            break
    
    if not source_dir:
        print("❌ Nenhum diretório de vídeos encontrado")
        print("📁 Execute primeiro o download de vídeos")
        return
    
    print(f"📁 Usando diretório: {source_dir}")
    
    try:
        from libras_video_organizer import LibrasVideoOrganizer
        
        print("🗂️ Iniciando organização...")
        organizer = LibrasVideoOrganizer(source_dir)
        organizer.organize_all()
        
        print("✅ Organização concluída!")
        print("📋 Catálogo HTML: libras_organized/indices/catalogo.html")
        
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
    except Exception as e:
        print(f"❌ Erro durante organização: {e}")

def exemplo_analise_estrutura():
    """Exemplo de análise da estrutura do site."""
    print("\n🔍 EXEMPLO 4: ANÁLISE DA ESTRUTURA")
    print("="*50)
    
    try:
        from analyze_libras_structure import analyze_libras_page, test_video_page
        
        print("🔍 Analisando estrutura da página...")
        result = analyze_libras_page()
        
        if result:
            print("✅ Análise concluída!")
            print(f"📊 Botões encontrados: {result['exibir_buttons']}")
            print(f"📄 Linhas de tabela: {result['table_rows']}")
            print(f"🔗 Links de páginas: {result['page_links']}")
        
        print("\n🎯 Testando acesso a vídeo...")
        test_video_page()
        
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
    except Exception as e:
        print(f"❌ Erro durante análise: {e}")

def mostrar_estatisticas():
    """Mostrar estatísticas dos vídeos baixados."""
    print("\n📊 ESTATÍSTICAS DOS VÍDEOS BAIXADOS")
    print("="*50)
    
    # Procurar por diretórios de vídeos
    video_dirs = []
    for dir_name in ["libras_videos_complete", "test_libras_sample", "libras_organized"]:
        if Path(dir_name).exists():
            video_dirs.append(dir_name)
    
    if not video_dirs:
        print("❌ Nenhum diretório de vídeos encontrado")
        return
    
    for video_dir in video_dirs:
        print(f"\n📁 Diretório: {video_dir}")
        
        # Contar arquivos .mp4
        video_files = list(Path(video_dir).rglob("*.mp4"))
        total_size = sum(f.stat().st_size for f in video_files)
        
        print(f"   📹 Vídeos: {len(video_files)}")
        print(f"   💾 Tamanho: {total_size:,} bytes ({total_size/1024/1024:.1f} MB)")
        
        if video_files:
            print(f"   📝 Exemplos:")
            for video_file in video_files[:3]:
                size = video_file.stat().st_size
                print(f"      - {video_file.name} ({size:,} bytes)")

def menu_principal():
    """Menu principal interativo."""
    print("🎬 LIBRAS VIDEO DOWNLOADER - EXEMPLOS")
    print("="*60)
    print("Sistema completo para download de vídeos de Libras")
    print("Base: V-LIBRASIL (https://libras.cin.ufpe.br)")
    print()
    
    while True:
        print("\n📋 OPÇÕES DISPONÍVEIS:")
        print("1. 🧪 Teste rápido (3 vídeos)")
        print("2. 🔍 Análise da estrutura")
        print("3. 🎬 Download completo (~4089 vídeos)")
        print("4. 🗂️ Organizar vídeos")
        print("5. 📊 Ver estatísticas")
        print("6. ❌ Sair")
        
        escolha = input("\n🤔 Escolha uma opção (1-6): ").strip()
        
        if escolha == "1":
            exemplo_teste_rapido()
        elif escolha == "2":
            exemplo_analise_estrutura()
        elif escolha == "3":
            exemplo_download_completo()
        elif escolha == "4":
            exemplo_organizacao()
        elif escolha == "5":
            mostrar_estatisticas()
        elif escolha == "6":
            print("👋 Saindo...")
            break
        else:
            print("❌ Opção inválida. Tente novamente.")

def main():
    """Função principal."""
    # Verificar se estamos no diretório correto
    if not Path("tools").exists():
        print("❌ Execute este script a partir do diretório raiz do projeto")
        print("💡 Exemplo: python examples/exemplo_libras_completo.py")
        return
    
    # Verificar dependências
    try:
        import requests
        import bs4
    except ImportError as e:
        print(f"❌ Dependência faltando: {e}")
        print("💡 Execute: pip install requests beautifulsoup4")
        return
    
    # Executar menu principal
    menu_principal()

if __name__ == "__main__":
    main()
