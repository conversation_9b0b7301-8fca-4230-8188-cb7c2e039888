#!/usr/bin/env python3
"""
🗺️ SITE MAPPER COMPLETO - Análise Profunda de Todo o Site

Este script mapeia e analisa TODAS as páginas de um site,
extraindo informações detalhadas de cada seção.
"""

import asyncio
import os
import sys
import json
import re
from pathlib import Path
from urllib.parse import urljoin, urlparse
import requests
from bs4 import BeautifulSoup
from datetime import datetime

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / "src"))


class SiteMapperCompleto:
    """Mapeador completo de site com análise profunda."""
    
    def __init__(self, base_url, output_dir="site_completo"):
        self.base_url = base_url.rstrip('/')
        self.domain = urlparse(base_url).netloc
        self.output_dir = Path(output_dir)
        self.session = requests.Session()
        
        # Headers realistas
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        
        # Dados coletados
        self.site_data = {
            'base_url': base_url,
            'domain': self.domain,
            'pages': {},
            'sitemap': [],
            'navigation_structure': {},
            'content_analysis': {},
            'seo_analysis': {},
            'technical_analysis': {},
            'business_analysis': {}
        }
        
        # URLs para mapear
        self.urls_to_map = set()
        self.mapped_urls = set()
        
        # Criar diretórios
        self.setup_directories()
    
    def setup_directories(self):
        """Criar estrutura de diretórios."""
        dirs = [
            self.output_dir,
            self.output_dir / "pages",
            self.output_dir / "analysis",
            self.output_dir / "assets",
            self.output_dir / "reports",
        ]
        
        for dir_path in dirs:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def mapear_site_completo(self, urls_especificas=None):
        """Mapear site completo."""
        print(f"🗺️ MAPEAMENTO COMPLETO: {self.base_url}")
        print("="*70)
        
        try:
            # 1. Mapear homepage
            print("🏠 1. Analisando homepage...")
            self.mapear_pagina(self.base_url, "homepage")
            
            # 2. Descobrir URLs automaticamente
            print("🔍 2. Descobrindo URLs automaticamente...")
            self.descobrir_urls_automaticamente()
            
            # 3. Adicionar URLs específicas fornecidas
            if urls_especificas:
                print("📋 3. Adicionando URLs específicas...")
                for url in urls_especificas:
                    self.urls_to_map.add(url)
            
            # 4. Mapear todas as URLs descobertas
            print("🗂️ 4. Mapeando todas as páginas...")
            self.mapear_todas_paginas()
            
            # 5. Análise de navegação
            print("🧭 5. Analisando estrutura de navegação...")
            self.analisar_navegacao()
            
            # 6. Análise de conteúdo
            print("📝 6. Analisando conteúdo...")
            self.analisar_conteudo()
            
            # 7. Análise SEO
            print("🔍 7. Analisando SEO...")
            self.analisar_seo()
            
            # 8. Análise técnica
            print("⚙️ 8. Analisando aspectos técnicos...")
            self.analisar_tecnico()
            
            # 9. Análise de negócio
            print("💼 9. Analisando aspectos de negócio...")
            self.analisar_negocio()
            
            # 10. Gerar relatórios
            print("📊 10. Gerando relatórios...")
            self.gerar_relatorios()
            
            print(f"\n✅ MAPEAMENTO COMPLETO CONCLUÍDO!")
            print(f"📁 Arquivos salvos em: {self.output_dir}")
            print(f"📄 Páginas mapeadas: {len(self.site_data['pages'])}")
            
        except Exception as e:
            print(f"❌ Erro no mapeamento: {e}")
            import traceback
            traceback.print_exc()
    
    def mapear_pagina(self, url, page_type="page"):
        """Mapear uma página específica."""
        if url in self.mapped_urls:
            return
        
        try:
            print(f"   📄 Mapeando: {url}")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extrair dados da página
            page_data = {
                'url': url,
                'page_type': page_type,
                'status_code': response.status_code,
                'title': self.extrair_titulo(soup),
                'meta_description': self.extrair_meta_description(soup),
                'headings': self.extrair_headings(soup),
                'content': self.extrair_conteudo(soup),
                'links': self.extrair_links(soup),
                'images': self.extrair_imagens(soup),
                'forms': self.extrair_formularios(soup),
                'meta_tags': self.extrair_meta_tags(soup),
                'word_count': self.contar_palavras(soup),
                'html_size': len(response.content),
                'mapped_at': datetime.now().isoformat()
            }
            
            # Salvar HTML da página
            page_filename = self.url_to_filename(url)
            with open(self.output_dir / "pages" / f"{page_filename}.html", 'w', encoding='utf-8') as f:
                f.write(str(soup))
            
            # Adicionar aos dados do site
            self.site_data['pages'][url] = page_data
            self.mapped_urls.add(url)
            
            # Descobrir novos links
            for link in page_data['links']:
                if self.is_internal_link(link['url']):
                    self.urls_to_map.add(link['url'])
            
        except Exception as e:
            print(f"   ❌ Erro mapeando {url}: {e}")
    
    def descobrir_urls_automaticamente(self):
        """Descobrir URLs automaticamente da homepage."""
        homepage_data = self.site_data['pages'].get(self.base_url, {})
        links = homepage_data.get('links', [])
        
        for link in links:
            if self.is_internal_link(link['url']):
                self.urls_to_map.add(link['url'])
        
        print(f"   🔍 Descobertas {len(self.urls_to_map)} URLs automaticamente")
    
    def mapear_todas_paginas(self):
        """Mapear todas as páginas descobertas."""
        urls_para_mapear = list(self.urls_to_map - self.mapped_urls)
        
        for i, url in enumerate(urls_para_mapear, 1):
            print(f"   📄 [{i}/{len(urls_para_mapear)}] {url}")
            page_type = self.determinar_tipo_pagina(url)
            self.mapear_pagina(url, page_type)
    
    def analisar_navegacao(self):
        """Analisar estrutura de navegação."""
        navigation = {
            'main_menu': [],
            'breadcrumbs': [],
            'footer_links': [],
            'internal_linking': {},
            'url_structure': {}
        }
        
        # Analisar estrutura de URLs
        for url in self.site_data['pages'].keys():
            path = urlparse(url).path
            parts = [p for p in path.split('/') if p]
            
            if len(parts) > 0:
                category = parts[0]
                if category not in navigation['url_structure']:
                    navigation['url_structure'][category] = []
                navigation['url_structure'][category].append(url)
        
        # Analisar linking interno
        for url, page_data in self.site_data['pages'].items():
            internal_links = [link for link in page_data['links'] if self.is_internal_link(link['url'])]
            navigation['internal_linking'][url] = len(internal_links)
        
        self.site_data['navigation_structure'] = navigation
    
    def analisar_conteudo(self):
        """Analisar conteúdo do site."""
        content_analysis = {
            'total_pages': len(self.site_data['pages']),
            'total_words': 0,
            'content_types': {},
            'topics': [],
            'services': [],
            'projects': [],
            'contact_info': {}
        }
        
        for url, page_data in self.site_data['pages'].items():
            content_analysis['total_words'] += page_data['word_count']
            
            # Identificar tipo de conteúdo
            page_type = self.identificar_tipo_conteudo(page_data)
            if page_type not in content_analysis['content_types']:
                content_analysis['content_types'][page_type] = 0
            content_analysis['content_types'][page_type] += 1
            
            # Extrair serviços
            if 'servico' in url.lower() or 'service' in url.lower():
                content_analysis['services'].append({
                    'url': url,
                    'title': page_data['title'],
                    'description': page_data['meta_description']
                })
            
            # Extrair projetos
            if 'projeto' in url.lower() or 'project' in url.lower():
                content_analysis['projects'].append({
                    'url': url,
                    'title': page_data['title'],
                    'description': page_data['meta_description']
                })
        
        self.site_data['content_analysis'] = content_analysis
    
    def analisar_seo(self):
        """Analisar aspectos de SEO."""
        seo_analysis = {
            'pages_with_title': 0,
            'pages_with_description': 0,
            'pages_with_h1': 0,
            'duplicate_titles': {},
            'duplicate_descriptions': {},
            'title_lengths': [],
            'description_lengths': [],
            'missing_meta': []
        }
        
        titles = {}
        descriptions = {}
        
        for url, page_data in self.site_data['pages'].items():
            title = page_data['title']
            description = page_data['meta_description']
            
            # Contar páginas com elementos SEO
            if title:
                seo_analysis['pages_with_title'] += 1
                seo_analysis['title_lengths'].append(len(title))
                
                if title in titles:
                    titles[title].append(url)
                else:
                    titles[title] = [url]
            
            if description:
                seo_analysis['pages_with_description'] += 1
                seo_analysis['description_lengths'].append(len(description))
                
                if description in descriptions:
                    descriptions[description].append(url)
                else:
                    descriptions[description] = [url]
            
            # Verificar H1
            h1_headings = [h for h in page_data['headings'] if h['level'] == 'h1']
            if h1_headings:
                seo_analysis['pages_with_h1'] += 1
            
            # Identificar problemas
            issues = []
            if not title:
                issues.append('missing_title')
            if not description:
                issues.append('missing_description')
            if not h1_headings:
                issues.append('missing_h1')
            
            if issues:
                seo_analysis['missing_meta'].append({
                    'url': url,
                    'issues': issues
                })
        
        # Identificar duplicatas
        seo_analysis['duplicate_titles'] = {title: urls for title, urls in titles.items() if len(urls) > 1}
        seo_analysis['duplicate_descriptions'] = {desc: urls for desc, urls in descriptions.items() if len(urls) > 1}
        
        self.site_data['seo_analysis'] = seo_analysis
    
    def analisar_tecnico(self):
        """Analisar aspectos técnicos."""
        technical_analysis = {
            'total_html_size': 0,
            'avg_page_size': 0,
            'largest_pages': [],
            'response_codes': {},
            'forms_found': 0,
            'images_found': 0,
            'external_links': 0
        }
        
        page_sizes = []
        
        for url, page_data in self.site_data['pages'].items():
            size = page_data['html_size']
            technical_analysis['total_html_size'] += size
            page_sizes.append((url, size))
            
            # Contar elementos
            technical_analysis['forms_found'] += len(page_data['forms'])
            technical_analysis['images_found'] += len(page_data['images'])
            
            # Contar links externos
            external_links = [link for link in page_data['links'] if not self.is_internal_link(link['url'])]
            technical_analysis['external_links'] += len(external_links)
            
            # Status codes
            status = page_data['status_code']
            if status not in technical_analysis['response_codes']:
                technical_analysis['response_codes'][status] = 0
            technical_analysis['response_codes'][status] += 1
        
        if page_sizes:
            technical_analysis['avg_page_size'] = technical_analysis['total_html_size'] / len(page_sizes)
            technical_analysis['largest_pages'] = sorted(page_sizes, key=lambda x: x[1], reverse=True)[:5]
        
        self.site_data['technical_analysis'] = technical_analysis
    
    def analisar_negocio(self):
        """Analisar aspectos de negócio."""
        business_analysis = {
            'business_type': 'engineering',
            'services_offered': [],
            'contact_methods': [],
            'location_info': {},
            'professional_info': {},
            'certifications': [],
            'project_types': []
        }
        
        # Analisar conteúdo para extrair informações de negócio
        all_content = ""
        for page_data in self.site_data['pages'].values():
            all_content += " " + page_data['content']
        
        all_content = all_content.lower()
        
        # Identificar serviços
        services_keywords = [
            'projetos elétricos', 'projetos hidrossanitários', 'projetos de comunicação',
            'consultoria', 'bim', 'revit', 'autocad', 'engenharia'
        ]
        
        for keyword in services_keywords:
            if keyword in all_content:
                business_analysis['services_offered'].append(keyword)
        
        # Identificar informações de contato
        contact_keywords = ['telefone', 'email', 'whatsapp', 'contato', 'porto alegre']
        for keyword in contact_keywords:
            if keyword in all_content:
                business_analysis['contact_methods'].append(keyword)
        
        # Identificar certificações
        cert_keywords = ['crea', 'engenheiro', 'registro profissional']
        for keyword in cert_keywords:
            if keyword in all_content:
                business_analysis['certifications'].append(keyword)
        
        self.site_data['business_analysis'] = business_analysis
    
    def gerar_relatorios(self):
        """Gerar relatórios completos."""
        # Relatório principal
        with open(self.output_dir / "reports" / "site_completo.json", 'w', encoding='utf-8') as f:
            json.dump(self.site_data, f, indent=2, ensure_ascii=False)
        
        # Relatório em Markdown
        self.gerar_relatorio_markdown()
        
        # Sitemap
        self.gerar_sitemap()
    
    def gerar_relatorio_markdown(self):
        """Gerar relatório em Markdown."""
        content_analysis = self.site_data['content_analysis']
        seo_analysis = self.site_data['seo_analysis']
        technical_analysis = self.site_data['technical_analysis']
        business_analysis = self.site_data['business_analysis']
        
        markdown = f"""# 🗺️ ANÁLISE COMPLETA DO SITE - {self.domain.upper()}

## 📊 RESUMO EXECUTIVO

- **🌐 Site**: {self.base_url}
- **📄 Páginas Mapeadas**: {content_analysis['total_pages']}
- **📝 Total de Palavras**: {content_analysis['total_words']:,}
- **📏 Tamanho Total HTML**: {technical_analysis['total_html_size']:,} bytes
- **📅 Análise Realizada**: {datetime.now().strftime('%d/%m/%Y %H:%M')}

## 📋 PÁGINAS MAPEADAS

"""
        
        for i, (url, page_data) in enumerate(self.site_data['pages'].items(), 1):
            markdown += f"{i}. **{page_data['title']}**\n"
            markdown += f"   - URL: {url}\n"
            markdown += f"   - Palavras: {page_data['word_count']}\n"
            markdown += f"   - Tamanho: {page_data['html_size']:,} bytes\n\n"
        
        markdown += f"""
## 🎯 ANÁLISE DE CONTEÚDO

- **Serviços Identificados**: {len(business_analysis['services_offered'])}
- **Projetos Encontrados**: {len(content_analysis['projects'])}
- **Tipos de Conteúdo**: {', '.join(content_analysis['content_types'].keys())}

## 🔍 ANÁLISE SEO

- **Páginas com Título**: {seo_analysis['pages_with_title']}/{content_analysis['total_pages']}
- **Páginas com Descrição**: {seo_analysis['pages_with_description']}/{content_analysis['total_pages']}
- **Páginas com H1**: {seo_analysis['pages_with_h1']}/{content_analysis['total_pages']}

## ⚙️ ANÁLISE TÉCNICA

- **Tamanho Médio da Página**: {technical_analysis['avg_page_size']:,.0f} bytes
- **Formulários Encontrados**: {technical_analysis['forms_found']}
- **Imagens Encontradas**: {technical_analysis['images_found']}
- **Links Externos**: {technical_analysis['external_links']}

## 💼 ANÁLISE DE NEGÓCIO

- **Tipo de Negócio**: Engenharia
- **Serviços Oferecidos**: {', '.join(business_analysis['services_offered'])}
- **Certificações**: {', '.join(business_analysis['certifications'])}
"""
        
        with open(self.output_dir / "reports" / "relatorio_completo.md", 'w', encoding='utf-8') as f:
            f.write(markdown)
    
    def gerar_sitemap(self):
        """Gerar sitemap do site."""
        sitemap = []
        for url, page_data in self.site_data['pages'].items():
            sitemap.append({
                'url': url,
                'title': page_data['title'],
                'word_count': page_data['word_count'],
                'last_mapped': page_data['mapped_at']
            })
        
        with open(self.output_dir / "reports" / "sitemap.json", 'w', encoding='utf-8') as f:
            json.dump(sitemap, f, indent=2, ensure_ascii=False)
    
    # Métodos auxiliares
    def extrair_titulo(self, soup):
        title = soup.find('title')
        return title.get_text().strip() if title else ''
    
    def extrair_meta_description(self, soup):
        meta = soup.find('meta', attrs={'name': 'description'})
        return meta.get('content', '').strip() if meta else ''
    
    def extrair_headings(self, soup):
        headings = []
        for level in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            for heading in soup.find_all(level):
                headings.append({
                    'level': level,
                    'text': heading.get_text().strip()
                })
        return headings
    
    def extrair_conteudo(self, soup):
        # Remover scripts e styles
        for script in soup(["script", "style"]):
            script.decompose()
        return soup.get_text().strip()
    
    def extrair_links(self, soup):
        links = []
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            if href:
                full_url = urljoin(self.base_url, href)
                links.append({
                    'url': full_url,
                    'text': link.get_text().strip(),
                    'title': link.get('title', '')
                })
        return links
    
    def extrair_imagens(self, soup):
        images = []
        for img in soup.find_all('img'):
            src = img.get('src')
            if src:
                full_url = urljoin(self.base_url, src)
                images.append({
                    'url': full_url,
                    'alt': img.get('alt', ''),
                    'title': img.get('title', '')
                })
        return images
    
    def extrair_formularios(self, soup):
        forms = []
        for form in soup.find_all('form'):
            inputs = []
            for inp in form.find_all(['input', 'textarea', 'select']):
                inputs.append({
                    'type': inp.get('type', inp.name),
                    'name': inp.get('name', ''),
                    'placeholder': inp.get('placeholder', '')
                })
            forms.append({
                'action': form.get('action', ''),
                'method': form.get('method', 'get'),
                'inputs': inputs
            })
        return forms
    
    def extrair_meta_tags(self, soup):
        meta_tags = []
        for meta in soup.find_all('meta'):
            meta_tags.append({
                'name': meta.get('name'),
                'property': meta.get('property'),
                'content': meta.get('content')
            })
        return meta_tags
    
    def contar_palavras(self, soup):
        text = self.extrair_conteudo(soup)
        return len(text.split()) if text else 0
    
    def is_internal_link(self, url):
        return self.domain in url or url.startswith('/')
    
    def url_to_filename(self, url):
        path = urlparse(url).path
        filename = path.replace('/', '_').strip('_')
        return filename if filename else 'index'
    
    def determinar_tipo_pagina(self, url):
        path = url.lower()
        if 'contato' in path:
            return 'contact'
        elif 'servico' in path:
            return 'service'
        elif 'projeto' in path:
            return 'project'
        elif 'sobre' in path:
            return 'about'
        else:
            return 'page'
    
    def identificar_tipo_conteudo(self, page_data):
        title = page_data['title'].lower()
        url = page_data['url'].lower()
        
        if 'contato' in title or 'contato' in url:
            return 'contact'
        elif 'servico' in title or 'servico' in url:
            return 'service'
        elif 'projeto' in title or 'projeto' in url:
            return 'project'
        elif 'sobre' in title or 'sobre' in url:
            return 'about'
        else:
            return 'content'


def main():
    """Função principal."""
    print("🗺️ SITE MAPPER COMPLETO - Análise Profunda")
    print("="*50)
    
    # URL base
    base_url = "https://pvpprojects.netlify.app"
    
    # URLs específicas para mapear
    urls_especificas = [
        "https://pvpprojects.netlify.app/projetos-dedicada/",
        "https://pvpprojects.netlify.app/projetos/categoria/predial/",
        "https://pvpprojects.netlify.app/servicos/hidrossanitarios/",
        "https://pvpprojects.netlify.app/projetos/casa-gp/",
        "https://pvpprojects.netlify.app/contato/"
    ]
    
    print(f"🎯 Site: {base_url}")
    print(f"📋 URLs específicas: {len(urls_especificas)}")
    
    # Criar mapper e executar
    mapper = SiteMapperCompleto(base_url, "pvp_projects_completo")
    mapper.mapear_site_completo(urls_especificas)
    
    print(f"\n🎉 ANÁLISE COMPLETA CONCLUÍDA!")
    print(f"📁 Resultados em: pvp_projects_completo/")
    print(f"📊 Relatório: pvp_projects_completo/reports/relatorio_completo.md")


if __name__ == "__main__":
    main()
