"""
Configuração global para testes do WebScraper.

Este arquivo contém fixtures e configurações compartilhadas
por todos os testes do sistema.
"""

import asyncio
import os
import tempfile
from pathlib import Path
from typing import AsyncGenerator, Generator

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

# Configurar variáveis de ambiente para testes
os.environ["ENVIRONMENT"] = "test"
os.environ["LOG_LEVEL"] = "DEBUG"
os.environ["ENABLE_CACHE"] = "false"


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """
    Criar event loop para testes assíncronos.
    """
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def temp_dir() -> Generator[Path, None, None]:
    """
    Criar diretório temporário para testes.
    """
    with tempfile.TemporaryDirectory() as tmp_dir:
        yield Path(tmp_dir)


@pytest.fixture(scope="session")
def test_database_url(temp_dir: Path) -> str:
    """
    URL do banco de dados de teste (SQLite em memória).
    """
    db_path = temp_dir / "test_webscraper.db"
    return f"sqlite+aiosqlite:///{db_path}"


@pytest_asyncio.fixture(scope="session")
async def test_engine(test_database_url: str):
    """
    Engine do banco de dados para testes.
    """
    engine = create_async_engine(
        test_database_url,
        echo=False,  # Mudar para True para debug SQL
        future=True,
    )
    
    # TODO: Criar tabelas quando os modelos estiverem prontos
    # async with engine.begin() as conn:
    #     await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    await engine.dispose()


@pytest_asyncio.fixture
async def db_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """
    Sessão do banco de dados para testes.
    """
    async_session = sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
        await session.rollback()


@pytest.fixture
def sample_html() -> str:
    """
    HTML de exemplo para testes de parsing.
    """
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Página de Teste</title>
        <meta charset="utf-8">
    </head>
    <body>
        <nav>
            <a href="/home">Home</a>
            <a href="/about">About</a>
        </nav>
        
        <main>
            <h1>Título Principal</h1>
            <h2>Subtítulo</h2>
            
            <p>Este é um parágrafo de exemplo com <strong>texto em negrito</strong>.</p>
            
            <pre><code class="python">
def hello_world():
    print("Hello, World!")
            </code></pre>
            
            <h3>Lista de Itens</h3>
            <ul>
                <li>Item 1</li>
                <li>Item 2</li>
                <li>Item 3</li>
            </ul>
            
            <table>
                <thead>
                    <tr>
                        <th>Coluna 1</th>
                        <th>Coluna 2</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Valor 1</td>
                        <td>Valor 2</td>
                    </tr>
                </tbody>
            </table>
        </main>
        
        <footer>
            <p>© 2025 Teste</p>
        </footer>
    </body>
    </html>
    """


@pytest.fixture
def sample_config() -> dict:
    """
    Configuração de exemplo para testes.
    """
    return {
        "domains": {
            "example.com": {
                "rate_limit": {
                    "requests_per_second": 2.0,
                    "burst_limit": 5,
                },
                "parsing": {
                    "content_selectors": {
                        "title": "h1",
                        "main_content": "main",
                        "headings": "h1, h2, h3, h4, h5, h6",
                    },
                    "remove_selectors": ["nav", "footer"],
                },
                "quality": {
                    "min_content_length": 50,
                    "min_word_count": 10,
                },
            }
        }
    }


@pytest.fixture
def mock_response_data() -> dict:
    """
    Dados de resposta HTTP simulados.
    """
    return {
        "status_code": 200,
        "headers": {
            "content-type": "text/html; charset=utf-8",
            "etag": '"abc123"',
            "last-modified": "Wed, 07 Sep 2025 12:00:00 GMT",
        },
        "content": """
        <html>
        <head><title>Test Page</title></head>
        <body>
            <h1>Test Title</h1>
            <p>Test content with enough words to pass quality checks.</p>
        </body>
        </html>
        """,
    }


# Markers personalizados para organizar testes
pytest_plugins = []

def pytest_configure(config):
    """Configurar markers personalizados."""
    config.addinivalue_line(
        "markers", "unit: marca testes unitários"
    )
    config.addinivalue_line(
        "markers", "integration: marca testes de integração"
    )
    config.addinivalue_line(
        "markers", "slow: marca testes lentos"
    )
    config.addinivalue_line(
        "markers", "parser: marca testes de parsers"
    )
    config.addinivalue_line(
        "markers", "http: marca testes de requisições HTTP"
    )
