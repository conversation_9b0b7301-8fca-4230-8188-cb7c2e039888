"""
MVP Flow - Flow principal do Prefect para scraping.

Este módulo implementa o flow principal de scraping usando Prefect,
orquestrando descoberta de URLs, fetching e parsing de conteúdo.
"""

import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional

import structlog
from prefect import flow, task
from prefect.task_runners import ConcurrentTask<PERSON>unner

from ..core.http_client import AsyncHTTPClient
from ..core.normalize import ContentNormalizer
from ..core.quality import AdvancedQualityScorer
from ..core.storage import FilesystemStorage, StorageManager
from ..core.validators import CrawlStats, ScrapingConfig, ScrapingStatus
from ..core.versioning import ContentVersionManager
from ..crawl.frontier import URLFrontier
from ..crawl.sitemap import SitemapDiscovery
from ..domains.generic import GenericParser

logger = structlog.get_logger(__name__)


@task(name="load_config")
async def load_config_task(config_file: Optional[str] = None) -> ScrapingConfig:
    """Carregar configuração de scraping."""
    # TODO: Implementar carregamento real de configuração
    # Por enquanto, usar configuração padrão
    
    logger.info("Loading scraping configuration", config_file=config_file)
    
    # Configuração básica para MVP
    config = ScrapingConfig(
        user_agent="WebScraper/1.0 MVP (+mailto:<EMAIL>)",
        max_concurrent_requests=5,
        enable_cache=True,
        cache_ttl=3600,
    )
    
    logger.info("Configuration loaded successfully")
    return config


@task(name="discover_urls")
async def discover_urls_task(
    domain: str, 
    config: ScrapingConfig, 
    max_urls: int = 1000
) -> List[str]:
    """Descobrir URLs de um domínio."""
    logger.info("Starting URL discovery", domain=domain, max_urls=max_urls)
    
    async with AsyncHTTPClient(config) as http_client:
        sitemap_discovery = SitemapDiscovery(http_client, config)
        
        try:
            url_infos = await sitemap_discovery.discover_urls(domain, max_urls)
            urls = [str(url_info.url) for url_info in url_infos]
            
            logger.info(
                "URL discovery completed",
                domain=domain,
                discovered_count=len(urls),
            )
            
            return urls
            
        except Exception as e:
            logger.error(
                "URL discovery failed",
                domain=domain,
                error=str(e),
                exc_info=True,
            )
            return []


@task(name="fetch_page")
async def fetch_page_task(url: str, config: ScrapingConfig) -> Optional[Dict]:
    """Fazer fetch de uma página."""
    logger.debug("Fetching page", url=url)
    
    async with AsyncHTTPClient(config) as http_client:
        try:
            response = await http_client.get(url)
            
            if response.status_code == 200:
                logger.debug(
                    "Page fetched successfully",
                    url=url,
                    status_code=response.status_code,
                    content_length=len(response.content),
                )
                
                return {
                    "url": url,
                    "status_code": response.status_code,
                    "content": response.content,
                    "headers": response.headers.dict(),
                    "fetched_at": response.fetched_at.isoformat(),
                    "duration_ms": response.duration_ms,
                }
            else:
                logger.warning(
                    "Page fetch returned non-200 status",
                    url=url,
                    status_code=response.status_code,
                )
                return None
                
        except Exception as e:
            logger.error(
                "Page fetch failed",
                url=url,
                error=str(e),
            )
            return None


@task(name="parse_page")
async def parse_page_task(page_data: Dict, config: ScrapingConfig) -> Optional[Dict]:
    """Fazer parse avançado de uma página com normalização e versionamento."""
    if not page_data:
        return None

    url = page_data["url"]
    logger.debug("Parsing page with advanced features", url=url)

    try:
        # Criar response object
        from ..core.validators import HTTPResponse, HTTPHeaders

        response = HTTPResponse(
            url=url,
            status_code=page_data["status_code"],
            content=page_data["content"],
            headers=HTTPHeaders(**page_data["headers"]),
            fetched_at=datetime.fromisoformat(page_data["fetched_at"]),
            duration_ms=page_data["duration_ms"],
        )

        # Configurar componentes avançados
        domain = response.url.host
        domain_config = config.get_domain_config(domain)

        # Parser com suporte a Playwright
        parser = GenericParser(domain_config, config)

        # Normalização de conteúdo
        normalizer = ContentNormalizer()

        # Sistema de qualidade avançado
        quality_scorer = AdvancedQualityScorer()

        # Parse inicial da página
        parsed_data = await parser.parse(response)

        # Normalização avançada
        normalized_data = normalizer.normalize_page_data(parsed_data, str(response.url))

        # Score de qualidade avançado
        advanced_score, quality_metrics = quality_scorer.calculate_quality_score(
            normalized_data, domain_config.quality
        )

        # Atualizar score no page_data
        normalized_data.quality_score = advanced_score

        # Indicadores de qualidade do conteúdo
        content_indicators = normalizer.get_content_quality_indicators(normalized_data)

        logger.info(
            "Advanced page parsing completed",
            url=url,
            quality_score=advanced_score,
            quality_tier=quality_metrics.quality_tier,
            confidence=quality_metrics.confidence,
            word_count=normalized_data.word_count,
        )

        return {
            "url": url,
            "title": normalized_data.title,
            "text_content": normalized_data.text_content,
            "content_hash": normalized_data.content_hash,
            "headings_count": len(normalized_data.headings_tree),
            "code_blocks_count": len(normalized_data.code_blocks),
            "tables_count": len(normalized_data.tables),
            "internal_links_count": len(normalized_data.internal_links),
            "external_links_count": len(normalized_data.external_links),
            "quality_score": advanced_score,
            "quality_tier": quality_metrics.quality_tier,
            "quality_confidence": quality_metrics.confidence,
            "content_indicators": content_indicators,
            "word_count": normalized_data.word_count,
            "status": normalized_data.status.value,
            "errors": normalized_data.errors,
            "processed_at": normalized_data.processed_at.isoformat(),
            "page_data": normalized_data.dict(),  # Dados completos para storage
        }

    except Exception as e:
        logger.error(
            "Advanced page parsing failed",
            url=url,
            error=str(e),
            exc_info=True,
        )
        return None


@task(name="save_results")
async def save_results_task(results: List[Dict], dry_run: bool = False) -> Dict:
    """Salvar resultados com versionamento e storage avançado."""
    if dry_run:
        logger.info("Dry run mode - not saving results")
        return {"saved": 0, "dry_run": True, "versions_created": 0}

    # Configurar storage e versionamento
    storage_backend = FilesystemStorage("./data")
    storage_manager = StorageManager(storage_backend)
    version_manager = ContentVersionManager()

    successful_results = [r for r in results if r and r.get("status") == "success"]
    failed_results = [r for r in results if r and r.get("status") == "failed"]

    logger.info(
        "Saving results with advanced storage",
        total_results=len(results),
        successful=len(successful_results),
        failed=len(failed_results),
    )

    saved_count = 0
    versions_created = 0
    version_stats = {"new_content": 0, "updated_content": 0, "unchanged_content": 0}

    for result in successful_results:
        try:
            # Reconstruir PageData do resultado
            from ..core.validators import PageData
            page_data = PageData(**result["page_data"])

            url = result["url"]

            # Verificar mudanças de conteúdo
            content_changed, semantic_changed, changes = version_manager.detect_changes(url, page_data)

            if content_changed:
                # Criar nova versão
                version = version_manager.create_version(url, page_data, changes)
                versions_created += 1

                if semantic_changed:
                    version_stats["updated_content"] += 1
                    logger.info(
                        "Content updated",
                        url=url,
                        version=version.version_number,
                        changes=changes,
                    )
                else:
                    version_stats["new_content"] += 1
                    logger.info(
                        "New content detected",
                        url=url,
                        version=version.version_number,
                    )

                # Salvar no storage
                await storage_manager.save_page(
                    page_data,
                    raw_content=result.get("raw_content"),
                    raw_metadata={
                        "status_code": result.get("status_code", 200),
                        "headers": result.get("headers", {}),
                        "version_info": version.to_dict(),
                    }
                )
                saved_count += 1

            else:
                version_stats["unchanged_content"] += 1
                logger.debug("Content unchanged, skipping save", url=url)

        except Exception as e:
            logger.error(
                "Failed to save result",
                url=result.get("url", "unknown"),
                error=str(e),
                exc_info=True,
            )

    # Estatísticas do versionamento
    versioning_stats = version_manager.get_stats()

    logger.info(
        "Advanced saving completed",
        saved=saved_count,
        versions_created=versions_created,
        version_stats=version_stats,
        versioning_stats=versioning_stats,
    )

    return {
        "saved": saved_count,
        "failed": len(failed_results),
        "total": len(results),
        "versions_created": versions_created,
        "version_stats": version_stats,
        "versioning_stats": versioning_stats,
    }


@flow(
    name="webscraper-daily",
    description="Flow diário de scraping de websites",
    task_runner=ConcurrentTaskRunner(),
)
async def webscraper_daily_flow(
    domain: Optional[str] = None,
    max_pages: int = 100,
    dry_run: bool = False,
    config_file: Optional[str] = None,
) -> Dict:
    """
    Flow principal de scraping diário.
    
    Args:
        domain: Domínio específico para scraping (opcional)
        max_pages: Número máximo de páginas para processar
        dry_run: Se True, não salva dados (apenas teste)
        config_file: Arquivo de configuração personalizado
    
    Returns:
        Estatísticas da execução
    """
    start_time = datetime.utcnow()
    
    logger.info(
        "Starting webscraper daily flow",
        domain=domain,
        max_pages=max_pages,
        dry_run=dry_run,
        config_file=config_file,
    )
    
    try:
        # 1. Carregar configuração
        config = await load_config_task(config_file)
        
        # 2. Determinar domínios para processar
        domains_to_process = []
        if domain:
            domains_to_process = [domain]
        else:
            # TODO: Carregar domínios da configuração
            domains_to_process = ["example.com"]  # Placeholder
        
        all_results = []
        
        # 3. Processar cada domínio
        for target_domain in domains_to_process:
            logger.info("Processing domain", domain=target_domain)
            
            # 3.1. Descobrir URLs
            urls = await discover_urls_task(target_domain, config, max_pages)
            
            if not urls:
                logger.warning("No URLs discovered", domain=target_domain)
                continue
            
            # 3.2. Fetch páginas em paralelo (limitado)
            fetch_tasks = []
            for url in urls[:max_pages]:
                task = fetch_page_task.submit(url, config)
                fetch_tasks.append(task)
            
            # Aguardar todos os fetches
            fetch_results = []
            for task in fetch_tasks:
                result = await task
                if result:
                    fetch_results.append(result)
            
            logger.info(
                "Fetch phase completed",
                domain=target_domain,
                attempted=len(urls),
                successful=len(fetch_results),
            )
            
            # 3.3. Parse páginas em paralelo
            parse_tasks = []
            for page_data in fetch_results:
                task = parse_page_task.submit(page_data, config)
                parse_tasks.append(task)
            
            # Aguardar todos os parses
            parse_results = []
            for task in parse_tasks:
                result = await task
                if result:
                    parse_results.append(result)
            
            logger.info(
                "Parse phase completed",
                domain=target_domain,
                attempted=len(fetch_results),
                successful=len(parse_results),
            )
            
            all_results.extend(parse_results)
        
        # 4. Salvar resultados
        save_stats = await save_results_task(all_results, dry_run)
        
        # 5. Calcular estatísticas finais
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()
        
        stats = {
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "duration_seconds": duration,
            "domains_processed": len(domains_to_process),
            "total_pages_attempted": sum(len(urls) for urls in [urls] if urls),
            "total_pages_processed": len(all_results),
            "successful_pages": len([r for r in all_results if r.get("status") == "success"]),
            "failed_pages": len([r for r in all_results if r.get("status") == "failed"]),
            "average_quality_score": (
                sum(r.get("quality_score", 0) for r in all_results) / len(all_results)
                if all_results else 0
            ),
            "save_stats": save_stats,
            "dry_run": dry_run,
        }
        
        logger.info(
            "Webscraper daily flow completed",
            **stats
        )
        
        return stats
        
    except Exception as e:
        logger.error(
            "Webscraper daily flow failed",
            error=str(e),
            exc_info=True,
        )
        raise


if __name__ == "__main__":
    # Permitir execução direta para testes
    import asyncio
    
    async def main():
        result = await webscraper_daily_flow(
            domain="example.com",
            max_pages=5,
            dry_run=True,
        )
        print("Flow result:", result)
    
    asyncio.run(main())
