#!/usr/bin/env python3
"""
Teste de download de uma amostra pequena de vídeos de Libras.
"""

import requests
from bs4 import BeautifulSoup
import re
import os
from pathlib import Path
from urllib.parse import urljoin
import time

def test_download_sample():
    """Testar download de alguns vídeos de amostra."""
    
    print("🧪 TESTE DE DOWNLOAD DE AMOSTRA")
    print("="*50)
    
    # Configuração
    base_url = "https://libras.cin.ufpe.br"
    output_dir = Path("test_libras_sample")
    output_dir.mkdir(exist_ok=True)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    # 1. Extrair links da primeira página
    print("📄 Extraindo links da primeira página...")
    
    page_url = f"{base_url}/?page=1"
    response = session.get(page_url, timeout=10)
    response.raise_for_status()
    
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # Extrair links de vídeo
    video_links = []
    table_rows = soup.find_all('tr')
    current_signal = None
    
    for row in table_rows[1:]:  # Pular cabeçalho
        cells = row.find_all(['td', 'th'])
        
        if len(cells) >= 5:
            # Primeira célula tem o nome do sinal
            signal_name = cells[0].get_text().strip()
            if signal_name and len(signal_name) > 1:
                current_signal = signal_name
            
            # Células 3, 4, 5 têm os links dos articuladores
            for i, articulador_num in enumerate([1, 2, 3], start=2):
                if i < len(cells):
                    cell = cells[i]
                    link = cell.find('a')
                    if link and link.get('href'):
                        video_links.append({
                            'signal_name': current_signal,
                            'articulador': articulador_num,
                            'page_url': link.get('href'),
                            'full_url': urljoin(page_url, link.get('href'))
                        })
    
    print(f"✅ Encontrados {len(video_links)} links de vídeo")
    
    # 2. Testar download dos primeiros 3 vídeos
    print(f"\n⬇️ Testando download dos primeiros 3 vídeos...")
    
    for i, video_link in enumerate(video_links[:3]):
        print(f"\n🎬 Processando vídeo {i+1}/3:")
        print(f"   Sinal: {video_link['signal_name']}")
        print(f"   Articulador: {video_link['articulador']}")
        print(f"   URL da página: {video_link['full_url']}")
        
        # Acessar página do sinal
        try:
            sign_response = session.get(video_link['full_url'], timeout=10)
            sign_response.raise_for_status()
            
            sign_soup = BeautifulSoup(sign_response.content, 'html.parser')
            
            # Extrair URL do vídeo
            source_tags = sign_soup.find_all('source')
            
            if source_tags:
                # Determinar qual source usar baseado no intérprete
                interpreter_match = re.search(r'#interpreter_(\d+)', video_link['full_url'])
                interpreter_num = int(interpreter_match.group(1)) if interpreter_match else 1
                
                # Os vídeos estão em ordem: interpreter_3, interpreter_2, interpreter_1
                source_index = 3 - interpreter_num
                
                if 0 <= source_index < len(source_tags):
                    video_url = source_tags[source_index].get('src')
                else:
                    video_url = source_tags[0].get('src')  # Fallback
                
                if video_url:
                    video_url = urljoin(base_url, video_url)
                    print(f"   ✅ URL do vídeo: {video_url}")
                    
                    # Fazer download
                    try:
                        # Criar nome de arquivo seguro
                        safe_signal_name = re.sub(r'[^\w\s-]', '', video_link['signal_name']).strip()
                        safe_signal_name = re.sub(r'[-\s]+', '_', safe_signal_name)
                        
                        filename = f"{safe_signal_name}_articulador_{video_link['articulador']}.mp4"
                        filepath = output_dir / filename
                        
                        print(f"   ⬇️ Baixando: {filename}")
                        
                        video_response = session.get(video_url, timeout=30, stream=True)
                        video_response.raise_for_status()
                        
                        with open(filepath, 'wb') as f:
                            for chunk in video_response.iter_content(chunk_size=8192):
                                if chunk:
                                    f.write(chunk)
                        
                        file_size = filepath.stat().st_size
                        print(f"   ✅ Download concluído: {file_size:,} bytes")
                        
                    except Exception as e:
                        print(f"   ❌ Erro no download: {e}")
                
                else:
                    print(f"   ⚠️ URL do vídeo não encontrada")
            
            else:
                print(f"   ⚠️ Tags source não encontradas")
        
        except Exception as e:
            print(f"   ❌ Erro ao acessar página: {e}")
        
        # Rate limiting
        time.sleep(2)
    
    # 3. Verificar resultados
    print(f"\n📊 RESULTADOS:")
    downloaded_files = list(output_dir.glob("*.mp4"))
    print(f"✅ Arquivos baixados: {len(downloaded_files)}")
    
    total_size = 0
    for file in downloaded_files:
        size = file.stat().st_size
        total_size += size
        print(f"   - {file.name}: {size:,} bytes")
    
    print(f"📁 Diretório: {output_dir}")
    print(f"💾 Tamanho total: {total_size:,} bytes ({total_size/1024/1024:.1f} MB)")
    
    return len(downloaded_files) > 0

def main():
    """Função principal."""
    print("🧪 TESTE DE DOWNLOAD DE VÍDEOS DE LIBRAS")
    print("="*60)
    
    success = test_download_sample()
    
    if success:
        print(f"\n🎉 TESTE BEM-SUCEDIDO!")
        print(f"✅ O sistema está funcionando corretamente")
        print(f"🚀 Pronto para executar o download completo")
    else:
        print(f"\n❌ TESTE FALHOU")
        print(f"⚠️ Verifique os erros acima")

if __name__ == "__main__":
    main()
