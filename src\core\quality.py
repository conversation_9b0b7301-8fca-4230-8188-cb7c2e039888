"""
Quality - Sistema avançado de avaliação de qualidade de conteúdo.

Este módulo implementa algoritmos sofisticados para avaliar a qualidade
do conteúdo extraído, com pesos configuráveis e validação rigorosa.
"""

import re
from typing import Dict, List, Optional, Tuple

import structlog
from pydantic import BaseModel, Field

from .validators import PageData, QualityConfig

logger = structlog.get_logger(__name__)


class QualityWeights(BaseModel):
    """Pesos configuráveis para cálculo de qualidade."""
    
    # Conteúdo básico
    content_length: float = Field(default=0.15, ge=0, le=1)
    word_count: float = Field(default=0.15, ge=0, le=1)
    
    # Estrutura
    title_quality: float = Field(default=0.10, ge=0, le=1)
    heading_structure: float = Field(default=0.15, ge=0, le=1)
    
    # Elementos ricos
    code_blocks: float = Field(default=0.10, ge=0, le=1)
    tables: float = Field(default=0.05, ge=0, le=1)
    links: float = Field(default=0.05, ge=0, le=1)
    
    # Qualidade do texto
    readability: float = Field(default=0.10, ge=0, le=1)
    information_density: float = Field(default=0.10, ge=0, le=1)
    
    # Penalizações
    duplicate_content: float = Field(default=-0.05, ge=-1, le=0)
    boilerplate_ratio: float = Field(default=-0.10, ge=-1, le=0)
    
    def validate_weights(self) -> bool:
        """Validar se os pesos somam aproximadamente 1.0."""
        positive_weights = [
            self.content_length, self.word_count, self.title_quality,
            self.heading_structure, self.code_blocks, self.tables,
            self.links, self.readability, self.information_density
        ]
        
        total = sum(positive_weights)
        return 0.9 <= total <= 1.1  # Permitir pequena variação


class QualityMetrics(BaseModel):
    """Métricas individuais de qualidade."""
    
    # Scores individuais (0-100)
    content_length_score: int = Field(ge=0, le=100)
    word_count_score: int = Field(ge=0, le=100)
    title_quality_score: int = Field(ge=0, le=100)
    heading_structure_score: int = Field(ge=0, le=100)
    code_blocks_score: int = Field(ge=0, le=100)
    tables_score: int = Field(ge=0, le=100)
    links_score: int = Field(ge=0, le=100)
    readability_score: int = Field(ge=0, le=100)
    information_density_score: int = Field(ge=0, le=100)
    
    # Penalizações
    duplicate_content_penalty: int = Field(ge=0, le=100)
    boilerplate_ratio_penalty: int = Field(ge=0, le=100)
    
    # Score final
    overall_score: int = Field(ge=0, le=100)
    
    # Metadados
    quality_tier: str = Field(default="")  # excellent, good, fair, poor
    confidence: float = Field(ge=0, le=1)  # Confiança no score


class AdvancedQualityScorer:
    """Avaliador avançado de qualidade de conteúdo."""
    
    def __init__(self, weights: Optional[QualityWeights] = None):
        self.weights = weights or QualityWeights()
        
        if not self.weights.validate_weights():
            logger.warning("Quality weights don't sum to ~1.0, results may be skewed")
    
    def calculate_quality_score(
        self, 
        page_data: PageData, 
        config: QualityConfig
    ) -> Tuple[int, QualityMetrics]:
        """
        Calcular score de qualidade avançado.
        
        Returns:
            (overall_score, detailed_metrics)
        """
        logger.debug("Calculating advanced quality score", url=page_data.url)
        
        # Calcular métricas individuais
        metrics = self._calculate_individual_metrics(page_data, config)
        
        # Calcular score ponderado
        weighted_score = self._calculate_weighted_score(metrics)
        
        # Aplicar penalizações
        final_score = self._apply_penalties(weighted_score, metrics)
        
        # Determinar tier de qualidade
        quality_tier = self._determine_quality_tier(final_score)
        
        # Calcular confiança
        confidence = self._calculate_confidence(page_data, metrics)
        
        # Criar métricas finais
        final_metrics = QualityMetrics(
            **metrics,
            overall_score=final_score,
            quality_tier=quality_tier,
            confidence=confidence,
        )
        
        logger.info(
            "Quality score calculated",
            url=page_data.url,
            overall_score=final_score,
            quality_tier=quality_tier,
            confidence=confidence,
        )
        
        return final_score, final_metrics
    
    def _calculate_individual_metrics(
        self, 
        page_data: PageData, 
        config: QualityConfig
    ) -> Dict[str, int]:
        """Calcular métricas individuais."""
        
        # 1. Content Length Score
        content_length_score = min(
            100, 
            (len(page_data.text_content) / max(config.min_content_length * 5, 500)) * 100
        )
        
        # 2. Word Count Score
        word_count_score = min(
            100,
            (page_data.word_count / max(config.min_word_count * 10, 200)) * 100
        )
        
        # 3. Title Quality Score
        title_quality_score = self._evaluate_title_quality(page_data.title)
        
        # 4. Heading Structure Score
        heading_structure_score = self._evaluate_heading_structure(page_data.headings_tree)
        
        # 5. Code Blocks Score
        code_blocks_score = min(100, len(page_data.code_blocks) * 25)
        
        # 6. Tables Score
        tables_score = min(100, len(page_data.tables) * 20)
        
        # 7. Links Score
        links_score = self._evaluate_links_quality(page_data)
        
        # 8. Readability Score
        readability_score = self._evaluate_readability(page_data.text_content)
        
        # 9. Information Density Score
        information_density_score = self._evaluate_information_density(page_data.text_content)
        
        # 10. Duplicate Content Penalty
        duplicate_content_penalty = self._detect_duplicate_content(page_data.text_content)
        
        # 11. Boilerplate Ratio Penalty
        boilerplate_ratio_penalty = self._estimate_boilerplate_ratio(page_data.text_content)
        
        return {
            "content_length_score": int(content_length_score),
            "word_count_score": int(word_count_score),
            "title_quality_score": title_quality_score,
            "heading_structure_score": heading_structure_score,
            "code_blocks_score": code_blocks_score,
            "tables_score": tables_score,
            "links_score": links_score,
            "readability_score": readability_score,
            "information_density_score": information_density_score,
            "duplicate_content_penalty": duplicate_content_penalty,
            "boilerplate_ratio_penalty": boilerplate_ratio_penalty,
        }
    
    def _evaluate_title_quality(self, title: str) -> int:
        """Avaliar qualidade do título."""
        if not title:
            return 0
        
        score = 50  # Base score
        
        # Comprimento ideal (30-60 caracteres)
        length = len(title)
        if 30 <= length <= 60:
            score += 30
        elif 20 <= length <= 80:
            score += 20
        elif length > 0:
            score += 10
        
        # Presença de palavras-chave importantes
        important_patterns = [
            r'\b(?:how|what|why|when|where|guide|tutorial|introduction)\b',
            r'\b(?:api|documentation|reference|manual)\b',
            r'\b(?:example|sample|demo)\b',
        ]
        
        for pattern in important_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                score += 5
        
        # Penalizar títulos genéricos
        generic_patterns = [
            r'^(?:home|index|main|default|untitled)$',
            r'^page \d+$',
            r'^welcome$',
        ]
        
        for pattern in generic_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                score -= 20
        
        return max(0, min(100, score))
    
    def _evaluate_heading_structure(self, headings_tree: List) -> int:
        """Avaliar estrutura de headings."""
        if not headings_tree:
            return 0
        
        score = 30  # Base score por ter headings
        
        # Número de headings
        total_headings = self._count_total_headings(headings_tree)
        if total_headings >= 3:
            score += 30
        elif total_headings >= 2:
            score += 20
        
        # Hierarquia lógica
        if self._has_logical_hierarchy(headings_tree):
            score += 25
        
        # Diversidade de níveis
        levels_used = self._get_heading_levels_used(headings_tree)
        if len(levels_used) >= 3:
            score += 15
        elif len(levels_used) >= 2:
            score += 10
        
        return min(100, score)
    
    def _evaluate_links_quality(self, page_data: PageData) -> int:
        """Avaliar qualidade dos links."""
        internal_count = len(page_data.internal_links)
        external_count = len(page_data.external_links)
        total_links = internal_count + external_count
        
        if total_links == 0:
            return 0
        
        score = 0
        
        # Links internos (navegação)
        if internal_count > 0:
            score += min(30, internal_count * 5)
        
        # Links externos (referências)
        if external_count > 0:
            score += min(20, external_count * 3)
        
        # Balanceamento
        if internal_count > 0 and external_count > 0:
            score += 10
        
        # Penalizar excesso de links
        if total_links > 50:
            score -= 10
        
        return min(100, score)
    
    def _evaluate_readability(self, text: str) -> int:
        """Avaliar legibilidade do texto."""
        if not text:
            return 0
        
        words = text.split()
        sentences = re.split(r'[.!?]+', text)
        
        if not words or not sentences:
            return 0
        
        # Flesch Reading Ease simplificado
        avg_sentence_length = len(words) / len(sentences)
        
        # Score baseado no comprimento médio das sentenças
        if 15 <= avg_sentence_length <= 20:
            readability_score = 100
        elif 10 <= avg_sentence_length <= 25:
            readability_score = 80
        elif 8 <= avg_sentence_length <= 30:
            readability_score = 60
        else:
            readability_score = 40
        
        # Ajustar baseado na complexidade das palavras
        long_words = sum(1 for word in words if len(word) > 6)
        long_word_ratio = long_words / len(words)
        
        if long_word_ratio > 0.3:
            readability_score -= 20
        elif long_word_ratio > 0.2:
            readability_score -= 10
        
        return max(0, min(100, readability_score))
    
    def _evaluate_information_density(self, text: str) -> int:
        """Avaliar densidade de informação."""
        if not text:
            return 0
        
        words = text.lower().split()
        if not words:
            return 0
        
        # Calcular densidade de informação
        unique_words = set(words)
        density = len(unique_words) / len(words)
        
        # Converter para score 0-100
        # Densidade ideal é entre 0.6-0.8
        if 0.6 <= density <= 0.8:
            score = 100
        elif 0.5 <= density <= 0.9:
            score = 80
        elif 0.4 <= density <= 0.95:
            score = 60
        else:
            score = 40
        
        return score
    
    def _detect_duplicate_content(self, text: str) -> int:
        """Detectar conteúdo duplicado (penalização)."""
        if not text:
            return 0
        
        # Detectar repetições de frases
        sentences = [s.strip() for s in re.split(r'[.!?]+', text) if s.strip()]
        
        if len(sentences) < 2:
            return 0
        
        duplicate_count = 0
        seen_sentences = set()
        
        for sentence in sentences:
            if sentence in seen_sentences:
                duplicate_count += 1
            else:
                seen_sentences.add(sentence)
        
        duplicate_ratio = duplicate_count / len(sentences)
        
        # Converter para penalização 0-100
        return min(100, int(duplicate_ratio * 200))
    
    def _estimate_boilerplate_ratio(self, text: str) -> int:
        """Estimar proporção de boilerplate (penalização)."""
        if not text:
            return 0
        
        # Padrões de boilerplate
        boilerplate_patterns = [
            r'\b(?:click here|read more|learn more|see more)\b',
            r'\b(?:copyright|all rights reserved)\b',
            r'\b(?:privacy policy|terms of service)\b',
            r'\b(?:follow us|social media)\b',
        ]
        
        boilerplate_matches = 0
        for pattern in boilerplate_patterns:
            boilerplate_matches += len(re.findall(pattern, text, re.IGNORECASE))
        
        words = text.split()
        if not words:
            return 0
        
        boilerplate_ratio = boilerplate_matches / len(words)
        
        # Converter para penalização 0-100
        return min(100, int(boilerplate_ratio * 500))
    
    def _calculate_weighted_score(self, metrics: Dict[str, int]) -> float:
        """Calcular score ponderado."""
        weighted_sum = (
            metrics["content_length_score"] * self.weights.content_length +
            metrics["word_count_score"] * self.weights.word_count +
            metrics["title_quality_score"] * self.weights.title_quality +
            metrics["heading_structure_score"] * self.weights.heading_structure +
            metrics["code_blocks_score"] * self.weights.code_blocks +
            metrics["tables_score"] * self.weights.tables +
            metrics["links_score"] * self.weights.links +
            metrics["readability_score"] * self.weights.readability +
            metrics["information_density_score"] * self.weights.information_density
        )
        
        return weighted_sum
    
    def _apply_penalties(self, base_score: float, metrics: Dict[str, int]) -> int:
        """Aplicar penalizações ao score."""
        penalty = (
            metrics["duplicate_content_penalty"] * abs(self.weights.duplicate_content) +
            metrics["boilerplate_ratio_penalty"] * abs(self.weights.boilerplate_ratio)
        )
        
        final_score = base_score - penalty
        return max(0, min(100, int(final_score)))
    
    def _determine_quality_tier(self, score: int) -> str:
        """Determinar tier de qualidade."""
        if score >= 80:
            return "excellent"
        elif score >= 60:
            return "good"
        elif score >= 40:
            return "fair"
        else:
            return "poor"
    
    def _calculate_confidence(self, page_data: PageData, metrics: Dict[str, int]) -> float:
        """Calcular confiança no score."""
        # Fatores que aumentam confiança
        confidence_factors = []
        
        # Quantidade de conteúdo
        if page_data.word_count > 100:
            confidence_factors.append(0.3)
        
        # Presença de estrutura
        if page_data.headings_tree:
            confidence_factors.append(0.2)
        
        # Presença de elementos ricos
        if page_data.code_blocks or page_data.tables:
            confidence_factors.append(0.2)
        
        # Título presente
        if page_data.title:
            confidence_factors.append(0.1)
        
        # Links presentes
        if page_data.internal_links or page_data.external_links:
            confidence_factors.append(0.1)
        
        # Baixa penalização
        total_penalty = metrics["duplicate_content_penalty"] + metrics["boilerplate_ratio_penalty"]
        if total_penalty < 20:
            confidence_factors.append(0.1)
        
        return min(1.0, sum(confidence_factors))
    
    # Métodos auxiliares
    def _count_total_headings(self, headings_tree: List) -> int:
        """Contar total de headings recursivamente."""
        count = len(headings_tree)
        for heading in headings_tree:
            count += self._count_total_headings(heading.children)
        return count
    
    def _has_logical_hierarchy(self, headings_tree: List) -> bool:
        """Verificar se há hierarquia lógica nos headings."""
        # Implementação simplificada
        return len(headings_tree) > 0 and any(h.children for h in headings_tree)
    
    def _get_heading_levels_used(self, headings_tree: List) -> set:
        """Obter níveis de heading utilizados."""
        levels = set()
        for heading in headings_tree:
            levels.add(heading.level)
            levels.update(self._get_heading_levels_used(heading.children))
        return levels
