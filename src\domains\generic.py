"""
Generic Parser - Parser genérico para qualquer site.

Este parser implementa estratégias de extração de conteúdo que funcionam
na maioria dos sites, servindo como fallback quando não há parser específico.
"""

import re
from typing import List, Optional
from urllib.parse import urlparse

import structlog
from selectolax.parser import HTMLParser

from ..core.parser_base import BaseParser
from ..core.validators import DomainConfig, HeadingNode, ScrapingConfig

logger = structlog.get_logger(__name__)


class GenericParser(BaseParser):
    """Parser genérico que funciona com qualquer site."""
    
    def can_parse(self, url: str, content_type: str) -> bool:
        """Este parser pode processar qualquer conteúdo HTML."""
        return "text/html" in content_type.lower()
    
    async def extract_title(self, parser: HTMLParser) -> str:
        """Extrair título usando múltiplas estratégias."""
        # Estratégia 1: Seletores configurados
        title = await super().extract_title(parser)
        if title:
            return title
        
        # Estratégia 2: Metadados Open Graph
        og_title = parser.css_first('meta[property="og:title"]')
        if og_title:
            content = og_title.attributes.get("content", "").strip()
            if content:
                return content
        
        # Estratégia 3: Meta title
        meta_title = parser.css_first('meta[name="title"]')
        if meta_title:
            content = meta_title.attributes.get("content", "").strip()
            if content:
                return content
        
        # Estratégia 4: Primeiro H1 visível
        for h1 in parser.css("h1"):
            text = h1.text(strip=True)
            if text and len(text) > 3:  # Evitar H1s muito curtos
                return text
        
        # Estratégia 5: Title tag (já tentado no parent)
        title_tag = parser.css_first("title")
        if title_tag:
            title = title_tag.text(strip=True)
            # Remover sufixos comuns do site
            title = re.sub(r'\s*[-|–]\s*[^-|–]*$', '', title)
            return title
        
        return "Untitled"
    
    async def extract_text_content(self, parser: HTMLParser) -> str:
        """Extrair texto principal usando heurísticas."""
        # Estratégia 1: Seletores configurados
        text = await super().extract_text_content(parser)
        if text and len(text) > 100:
            return text
        
        # Estratégia 2: Seletores semânticos comuns
        semantic_selectors = [
            "main",
            "article", 
            "[role='main']",
            ".main-content",
            ".content",
            "#content",
            ".post-content",
            ".entry-content",
            ".article-content",
            ".page-content",
        ]
        
        for selector in semantic_selectors:
            elements = parser.css(selector)
            if elements:
                content = elements[0].text(strip=True)
                if len(content) > 100:
                    return content
        
        # Estratégia 3: Maior bloco de texto
        return await self._extract_largest_text_block(parser)
    
    async def _extract_largest_text_block(self, parser: HTMLParser) -> str:
        """Encontrar o maior bloco de texto na página."""
        # Elementos que geralmente contêm conteúdo principal
        content_elements = [
            "div", "section", "article", "main", "p"
        ]
        
        largest_text = ""
        largest_length = 0
        
        for tag in content_elements:
            for element in parser.css(tag):
                # Pular elementos que são claramente navegação/sidebar
                classes = element.attributes.get("class", "").lower()
                element_id = element.attributes.get("id", "").lower()
                
                skip_patterns = [
                    "nav", "menu", "sidebar", "footer", "header",
                    "advertisement", "ad", "social", "share",
                    "comment", "related", "recommended"
                ]
                
                if any(pattern in classes or pattern in element_id 
                       for pattern in skip_patterns):
                    continue
                
                text = element.text(strip=True)
                if len(text) > largest_length:
                    largest_text = text
                    largest_length = len(text)
        
        return largest_text
    
    async def extract_headings_tree(self, parser: HTMLParser) -> List[HeadingNode]:
        """Extrair headings com limpeza adicional."""
        headings = await super().extract_headings_tree(parser)
        
        # Filtrar headings que são claramente navegação
        filtered_headings = []
        
        for heading in headings:
            # Pular headings muito curtos ou que parecem navegação
            if len(heading.text) < 3:
                continue
            
            # Pular headings que são claramente navegação
            nav_patterns = [
                r'^(menu|nav|navigation)$',
                r'^(home|about|contact|login|register)$',
                r'^(search|filter|sort)$',
            ]
            
            is_navigation = any(
                re.match(pattern, heading.text.lower()) 
                for pattern in nav_patterns
            )
            
            if not is_navigation:
                filtered_headings.append(heading)
        
        return self._build_heading_tree(filtered_headings)
    
    async def preprocess_html(self, html: str) -> str:
        """Pré-processar HTML com limpeza adicional."""
        html = await super().preprocess_html(html)
        
        parser = HTMLParser(html)
        
        # Remover elementos comuns que não são conteúdo
        noise_selectors = [
            # Navegação
            "nav", ".navigation", ".nav", "#nav",
            ".menu", "#menu", ".navbar", ".nav-bar",
            
            # Sidebar e widgets
            ".sidebar", "#sidebar", ".widget", ".widgets",
            ".aside", "aside",
            
            # Footer
            "footer", ".footer", "#footer",
            
            # Anúncios
            ".ad", ".ads", ".advertisement", ".adsense",
            "[class*='ad-']", "[id*='ad-']",
            
            # Social e compartilhamento
            ".social", ".share", ".sharing", ".social-share",
            
            # Comentários
            ".comments", "#comments", ".comment-section",
            
            # Elementos de tracking
            ".analytics", ".tracking", ".pixel",
            
            # Pop-ups e modais
            ".modal", ".popup", ".overlay",
            
            # Breadcrumbs (podem ser úteis, mas geralmente são ruído)
            ".breadcrumb", ".breadcrumbs",
        ]
        
        for selector in noise_selectors:
            for element in parser.css(selector):
                element.decompose()
        
        # Remover elementos vazios
        for element in parser.css("div, span, p"):
            if not element.text(strip=True) and not element.css("img, video, audio"):
                element.decompose()
        
        return str(parser.html)
    
    async def extract_code_blocks(self, parser: HTMLParser) -> List:
        """Extrair código com detecção melhorada de linguagem."""
        code_blocks = await super().extract_code_blocks(parser)
        
        # Tentar detectar linguagem por contexto se não foi detectada
        for block in code_blocks:
            if not block.language:
                block.language = self._detect_language_by_content(block.content)
        
        return code_blocks
    
    def _detect_language_by_content(self, content: str) -> Optional[str]:
        """Detectar linguagem de programação pelo conteúdo."""
        content_lower = content.lower()
        
        # Padrões simples para detecção de linguagem
        language_patterns = {
            "python": [
                r'\bdef\s+\w+\s*\(',
                r'\bimport\s+\w+',
                r'\bfrom\s+\w+\s+import',
                r'print\s*\(',
                r'if\s+__name__\s*==\s*["\']__main__["\']',
            ],
            "javascript": [
                r'\bfunction\s+\w+\s*\(',
                r'\bvar\s+\w+\s*=',
                r'\blet\s+\w+\s*=',
                r'\bconst\s+\w+\s*=',
                r'console\.log\s*\(',
            ],
            "java": [
                r'\bpublic\s+class\s+\w+',
                r'\bpublic\s+static\s+void\s+main',
                r'\bSystem\.out\.print',
                r'\bprivate\s+\w+\s+\w+',
            ],
            "csharp": [
                r'\bpublic\s+class\s+\w+',
                r'\busing\s+System',
                r'\bConsole\.Write',
                r'\bpublic\s+static\s+void\s+Main',
            ],
            "sql": [
                r'\bSELECT\s+',
                r'\bFROM\s+\w+',
                r'\bWHERE\s+',
                r'\bINSERT\s+INTO',
                r'\bUPDATE\s+\w+\s+SET',
            ],
            "html": [
                r'<html\b',
                r'<div\b',
                r'<p\b',
                r'<!DOCTYPE',
            ],
            "css": [
                r'\w+\s*\{[^}]*\}',
                r'@media\s+',
                r'#\w+\s*\{',
                r'\.\w+\s*\{',
            ],
        }
        
        for language, patterns in language_patterns.items():
            for pattern in patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    return language
        
        return None
