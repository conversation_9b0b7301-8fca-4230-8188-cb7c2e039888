"""
WebScraper - Sistema complexo de web scraping com observabilidade e tolerância a falhas.

Este pacote implementa um pipeline completo de coleta, parsing, validação e 
versionamento de conteúdos web, seguindo as melhores práticas de:

- Robustez: Rate limiting, retries com backoff, circuit breakers
- Observabilidade: Logs estruturados, métricas Prometheus
- Ética: Respeito a robots.txt e políticas de coleta
- Escalabilidade: Arquitetura assíncrona, storage distribuído
- Qualidade: Validação de dados, scores de qualidade

Módulos principais:
- core: Componentes fundamentais (HTTP client, browser, parsers, storage)
- crawl: Sistema de descoberta e crawling de URLs
- domains: Parsers específicos por domínio
- flows: Orquestração com Prefect
"""

__version__ = "0.1.0"
__author__ = "Pedro"
__email__ = "<EMAIL>"
__description__ = "Sistema complexo de web scraping com observabilidade e tolerância a falhas"

# Imports principais para facilitar o uso
from .core.http_client import AsyncHTTPClient
from .core.parser_base import BaseParser
from .core.validators import PageData, ScrapingConfig
from .core.storage import StorageManager

__all__ = [
    "AsyncHTTPClient",
    "BaseParser", 
    "PageData",
    "ScrapingConfig",
    "StorageManager",
]
