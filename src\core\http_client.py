"""
HTTP Client - Cliente HTTP assíncrono com retries e cache.

Este módulo implementa um cliente HTTP robusto com:
- Requisições assíncronas usando httpx
- Rate limiting por domínio
- Retries com backoff exponencial
- Cache condicional (ETag/Last-Modified)
- Circuit breaker para hosts problemáticos
- Observabilidade com métricas e logs
"""

import asyncio
import hashlib
import random
import time
from typing import Dict, Optional, Set
from urllib.parse import urlparse

import httpx
import structlog
from async_lru import alru_cache

from .validators import (
    DomainConfig,
    HTTPHeaders,
    HTTPResponse,
    ScrapingConfig,
    ScrapingStatus,
)

logger = structlog.get_logger(__name__)


class RateLimiter:
    """Rate limiter por domínio usando token bucket."""
    
    def __init__(self, requests_per_second: float, burst_limit: int):
        self.requests_per_second = requests_per_second
        self.burst_limit = burst_limit
        self.tokens = burst_limit
        self.last_update = time.time()
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """Adquirir permissão para fazer uma requisição."""
        async with self._lock:
            now = time.time()
            elapsed = now - self.last_update
            
            # Adicionar tokens baseado no tempo decorrido
            self.tokens = min(
                self.burst_limit,
                self.tokens + elapsed * self.requests_per_second
            )
            self.last_update = now
            
            if self.tokens >= 1:
                self.tokens -= 1
                return
            
            # Aguardar até ter tokens disponíveis
            wait_time = (1 - self.tokens) / self.requests_per_second
            await asyncio.sleep(wait_time)
            self.tokens = 0


class CircuitBreaker:
    """Circuit breaker para hosts problemáticos."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 300):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "closed"  # closed, open, half-open
    
    def can_execute(self) -> bool:
        """Verificar se pode executar uma requisição."""
        if self.state == "closed":
            return True
        
        if self.state == "open":
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "half-open"
                return True
            return False
        
        # half-open: permitir uma tentativa
        return True
    
    def record_success(self) -> None:
        """Registrar sucesso."""
        self.failure_count = 0
        self.state = "closed"
    
    def record_failure(self) -> None:
        """Registrar falha."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"
        elif self.state == "half-open":
            self.state = "open"


class AsyncHTTPClient:
    """Cliente HTTP assíncrono com recursos avançados."""
    
    def __init__(self, config: ScrapingConfig):
        self.config = config
        self.rate_limiters: Dict[str, RateLimiter] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.session: Optional[httpx.AsyncClient] = None
        self._cache: Dict[str, HTTPResponse] = {}
        self._cache_timestamps: Dict[str, float] = {}
        
    async def __aenter__(self):
        """Inicializar cliente assíncrono."""
        self.session = httpx.AsyncClient(
            timeout=httpx.Timeout(
                connect=self.config.request_timeout,
                read=self.config.request_timeout,
                pool=None,
                write=None,
            ),
            limits=httpx.Limits(
                max_keepalive_connections=20,
                max_connections=100,
                keepalive_expiry=30,
            ),
            headers={
                "User-Agent": self.config.user_agent,
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "pt-BR,pt;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate",
                "DNT": "1",
            },
            follow_redirects=True,
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Fechar cliente assíncrono."""
        if self.session:
            await self.session.aclose()
    
    def _get_domain(self, url: str) -> str:
        """Extrair domínio de uma URL."""
        return urlparse(url).netloc.lower()
    
    def _get_rate_limiter(self, domain: str) -> RateLimiter:
        """Obter rate limiter para um domínio."""
        if domain not in self.rate_limiters:
            domain_config = self.config.get_domain_config(domain)
            self.rate_limiters[domain] = RateLimiter(
                domain_config.rate_limit.requests_per_second,
                domain_config.rate_limit.burst_limit,
            )
        return self.rate_limiters[domain]
    
    def _get_circuit_breaker(self, domain: str) -> CircuitBreaker:
        """Obter circuit breaker para um domínio."""
        if domain not in self.circuit_breakers:
            self.circuit_breakers[domain] = CircuitBreaker()
        return self.circuit_breakers[domain]

    def _get_cache_key(self, url: str) -> str:
        """Gerar chave de cache para uma URL."""
        return hashlib.sha256(url.encode('utf-8')).hexdigest()

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Verificar se o cache é válido."""
        if not self.config.enable_cache:
            return False

        if cache_key not in self._cache_timestamps:
            return False

        age = time.time() - self._cache_timestamps[cache_key]
        return age < self.config.cache_ttl

    async def _calculate_backoff(self, attempt: int, base_delay: float = 1.0) -> float:
        """Calcular delay de backoff com jitter."""
        delay = base_delay * (2 ** attempt)
        jitter = random.uniform(0, delay * 0.1)  # 10% de jitter
        return min(delay + jitter, 60.0)  # Máximo 60 segundos

    async def _make_request(
        self,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        use_cache: bool = True
    ) -> HTTPResponse:
        """Fazer uma requisição HTTP com todas as proteções."""
        domain = self._get_domain(url)
        cache_key = self._get_cache_key(url)

        # Verificar cache primeiro
        if use_cache and self._is_cache_valid(cache_key):
            logger.debug("Cache hit", url=url, domain=domain)
            return self._cache[cache_key]

        # Verificar circuit breaker
        circuit_breaker = self._get_circuit_breaker(domain)
        if not circuit_breaker.can_execute():
            logger.warning("Circuit breaker open", domain=domain)
            raise httpx.RequestError(f"Circuit breaker open for {domain}")

        # Rate limiting
        rate_limiter = self._get_rate_limiter(domain)
        await rate_limiter.acquire()

        # Configuração do domínio
        domain_config = self.config.get_domain_config(domain)

        # Headers condicionais para cache
        request_headers = headers or {}
        if use_cache and cache_key in self._cache:
            cached_response = self._cache[cache_key]
            if cached_response.headers.etag:
                request_headers["If-None-Match"] = cached_response.headers.etag
            if cached_response.headers.last_modified:
                request_headers["If-Modified-Since"] = cached_response.headers.last_modified

        # Tentar requisição com retries
        last_exception = None
        for attempt in range(domain_config.rate_limit.max_retries + 1):
            try:
                start_time = time.time()

                response = await self.session.get(
                    url,
                    headers=request_headers,
                    timeout=httpx.Timeout(
                        connect=domain_config.timeouts.connect,
                        read=domain_config.timeouts.read,
                        pool=None,
                        write=None,
                    ),
                )

                duration_ms = int((time.time() - start_time) * 1000)

                # Processar resposta
                http_response = await self._process_response(
                    response, url, duration_ms
                )

                # Registrar sucesso no circuit breaker
                circuit_breaker.record_success()

                # Salvar no cache se apropriado
                if use_cache and response.status_code == 200:
                    self._cache[cache_key] = http_response
                    self._cache_timestamps[cache_key] = time.time()

                logger.info(
                    "Request successful",
                    url=url,
                    status_code=response.status_code,
                    duration_ms=duration_ms,
                    attempt=attempt + 1,
                )

                return http_response

            except (httpx.RequestError, httpx.HTTPStatusError) as e:
                last_exception = e
                circuit_breaker.record_failure()

                logger.warning(
                    "Request failed",
                    url=url,
                    attempt=attempt + 1,
                    error=str(e),
                    domain=domain,
                )

                # Se não é a última tentativa, aguardar backoff
                if attempt < domain_config.rate_limit.max_retries:
                    backoff_delay = await self._calculate_backoff(
                        attempt, domain_config.rate_limit.backoff_factor
                    )
                    await asyncio.sleep(backoff_delay)

        # Se chegou aqui, todas as tentativas falharam
        logger.error(
            "All retry attempts failed",
            url=url,
            max_retries=domain_config.rate_limit.max_retries,
            last_error=str(last_exception),
        )
        raise last_exception

    async def _process_response(
        self, response: httpx.Response, url: str, duration_ms: int
    ) -> HTTPResponse:
        """Processar resposta HTTP."""
        # Extrair headers importantes
        headers = HTTPHeaders(
            content_type=response.headers.get("content-type"),
            etag=response.headers.get("etag"),
            last_modified=response.headers.get("last-modified"),
            content_length=int(response.headers.get("content-length", 0)) or None,
        )

        # Detectar encoding
        encoding = response.encoding or "utf-8"

        # Ler conteúdo
        try:
            content = response.text
        except UnicodeDecodeError:
            # Fallback para latin-1 se UTF-8 falhar
            content = response.content.decode("latin-1", errors="replace")

        return HTTPResponse(
            url=url,
            status_code=response.status_code,
            headers=headers,
            content=content,
            encoding=encoding,
            duration_ms=duration_ms,
        )

    async def get(self, url: str, use_cache: bool = True) -> HTTPResponse:
        """Fazer requisição GET."""
        return await self._make_request(url, use_cache=use_cache)

    async def head(self, url: str) -> HTTPResponse:
        """Fazer requisição HEAD para verificar metadados."""
        domain = self._get_domain(url)
        rate_limiter = self._get_rate_limiter(domain)
        await rate_limiter.acquire()

        try:
            response = await self.session.head(url)
            return await self._process_response(response, url, 0)
        except Exception as e:
            logger.error("HEAD request failed", url=url, error=str(e))
            raise
