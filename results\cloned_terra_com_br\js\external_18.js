/*! zaz-app-t360-user-table - v1.0.0 - 19/08/2025 -- 7:32pm */
zaz.use(function appT360UserTable(pkg){"use strict";var console=pkg.console,appFactory,STATIC_PUBLIC=null,STATIC_PRIVATE={};pkg.factoryManager.get("app").create({name:"t360.userTable",version:"1.0.0",state:"ok",docs:"http://github.tpn.terra.com/pages/terra/t360-user-table",source:"http://github.tpn.terra.com/Terra/t360-user-table",description:"Just another app",tests:"http://s1.trrsf.com/fe/t360-user-table/tests/index.htm?zaz[env]=tests",dependencies:[],dictionaries:[],templates:{menu:'{% if device == \'mob\' %}<div class="table-news__header__menu-shadow"></div>{% endif %}{% if items %}<ul class="table-news__header__menu {{ \'color-\'+color }}">{% for menu_item in items %}<li class="table-news__header__menu--items bg-color-secondary-usertable"><a href="{{ menu_item.url }}" data-event-label="{{ menu_item.label }}">{{ menu_item.label }}</a></li>{% endfor %}</ul>{% endif %}'},expects:{},setup:function(STATIC_PUBLIC,PUBLIC,__shared){},init:function(data,__shared){var PRIVATE={},PUBLIC=this;return PRIVATE.container=data.container,PRIVATE.elemInfo=PRIVATE.container.querySelector(".table-news__info"),PRIVATE.loadFavoriteTeam=function(){PRIVATE.appFavoriteTeam=PRIVATE.container.querySelector(".app-fixo-ut-team"),PRIVATE.appFavoriteTeam&&pkg.require(["app.t360.favoriteTeam"],function(T360FavoriteTeam){PRIVATE.favoriteTeam=new T360FavoriteTeam({container:PRIVATE.appFavoriteTeam,type:"card"}),PRIVATE.appFavoriteTeam.classList.remove("loading-app")})},PRIVATE.loadWeather=function(){PRIVATE.appWeather=PRIVATE.container.querySelector(".app-t360-weather"),PRIVATE.appWeather&&pkg.require(["app.t360.weather"],function(T360Weather){var inst=new T360Weather({data:{configs:{size:"small"}},container:PRIVATE.appWeather})}).then(function(){PRIVATE.appWeather.classList.remove("loading-app")})},PRIVATE.loadGenericCardApp=function(){PRIVATE.elemGenericCards=PRIVATE.container.querySelector(".app-t360-generic-cards"),PRIVATE.elemGenericCards&&pkg.require(["app.t360.genericCards"],function(T360GenericCards){PRIVATE.genericApp=new T360GenericCards({container:PRIVATE.elemGenericCards})})},PRIVATE.loadLogin=function(){PRIVATE.elemHeaderAvatar=PRIVATE.container.querySelector(".table-news__header__avatar"),PRIVATE.elemHeaderTitleLabel=PRIVATE.container.querySelector(".table-news__header__title__label"),PRIVATE.elemHeaderMenu=PRIVATE.container.querySelector(".table-news__header__menu-wrapper"),PRIVATE.elemHeaderBtLogin=PRIVATE.container.querySelector(".table-news__header__title__bt-login"),pkg.context.user.get("usertable-info-read",function(val){val||(PRIVATE.elemInfo.classList.remove("hide"),PRIVATE.infoBindEvents())}),pkg.require(["mod.googleOneTap"],function(ModGOT){PRIVATE.modGOT=ModGOT,PRIVATE.userLoginStatus=!1,PRIVATE.modGOT.getLoginStatus()?PRIVATE.userLoginSuccess():PRIVATE.userLoginDefault(),pkg.context.user.on("googleSignInData",function(data){PRIVATE.modGOT.getLoginStatus()&&""!=data&&!PRIVATE.userLoginStatus?PRIVATE.userLoginSuccess():PRIVATE.modGOT.getLoginStatus()||PRIVATE.userLoginDefault()})})},PRIVATE.userLoginDefault=function(){PRIVATE.modGOT.renderButtonLogin(PRIVATE.elemHeaderBtLogin),PRIVATE.userLoginStatus=!1,PRIVATE.elemHeaderAvatar.innerHTML="",PRIVATE.elemHeaderMenu.innerHTML="",PRIVATE.elemHeaderTitleLabel.innerHTML="Seu Terra"},PRIVATE.userLoginSuccess=function(){PRIVATE.userLoginStatus=!0,PRIVATE.elemHeaderMenu.innerHTML="";var userData=PRIVATE.modGOT.getLoginData(),avatar,avatar;userData&&userData.given_name&&(pkg.context.user.get("segments",function(data){PRIVATE.renderMenu(data)}),PRIVATE.elemHeaderAvatar&&userData.picture&&(avatar='<img src="'+userData.picture+'" style="width: 60px; margin-right: 16px; height: auto; border-radius: 30px;'+(userData.picture?" border: 4px solid currentColor":"")+'">',PRIVATE.elemHeaderAvatar.innerHTML=avatar),PRIVATE.elemHeaderTitleLabel&&(avatar="Seu Terra, "+userData.given_name,PRIVATE.elemHeaderTitleLabel.innerHTML=avatar,PRIVATE.elemHeaderBtLogin.innerHTML="",PRIVATE.elemHeaderAvatar.addEventListener("click",function(){pkg.require(["app.t360.navbar"],function(Nav){var navbar;(new Nav).toggleModule("user-area")}),window.tga.send("send","event","USER-TABLE","click","User configs")})))},PRIVATE.renderMenu=function(data){if(data&&Array.isArray(data)){for(var channelIds=[],i=0;i<data.length&&i<5;i++)data[i]&&channelIds.push(Object.keys(data[i])[0]);0<channelIds.length&&PRIVATE.getChannelById(channelIds).then(function(channels){var channels;channels&&((channels=PUBLIC.templates.render("menu",{items:channels,device:pkg.context.platform.get("type"),color:"usertable"}))&&window.tga.event("menu-aberto","click",channels.querySelectorAll("li a"),{dimension14:"Scroll USER-TABLE"}),PRIVATE.elemHeaderMenu.appendChild(channels))})}},PRIVATE.getChannelById=function(channelIds){return new Promise(function(resolve,reject){fetch("https://www.terra.com.br/feeder/channels/list/"+channelIds.join(",")+".json").then(function(response){return response.json()}).then(function(data){var channels=[];if(data)for(var x in data)data.hasOwnProperty(x)&&channels.push(data[x]);resolve(channels)}).catch(function(){reject()})})},PRIVATE.infoBindEvents=function(){var elemInfoBt=PRIVATE.elemInfo.querySelector(".table-news__info--icon");elemInfoBt&&elemInfoBt.addEventListener("click",function(){pkg.context.user.set("usertable-info-read",!0),PRIVATE.elemInfo.classList.add("hide")})},PRIVATE.onClickTitle=function(){PRIVATE.elemHeaderTitleLabel.addEventListener("click",function(){pkg.require(["app.t360.navbar"],function(Nav){var navbar;(new Nav).toggleModule("user-area")}),window.tga.send("send","event","USER-TABLE","click","User configs")})},1!=pkg.context.page.get("isHome")&&(PRIVATE.prefetchNews=function(){pkg.require(["mod.prefetchURL"],function(prefetchPublic){var nodeListsArray=prefetchPublic.getDeepNestedElements(".app-t360-user-table.table-range-flex .table-range-grid-step",prefetchPublic.cardLinksQuery);nodeListsArray&&prefetchPublic.setElementsToObserve([nodeListsArray])})}),PRIVATE.loadFavoriteTeam(),PRIVATE.loadWeather(),PRIVATE.loadGenericCardApp(),PRIVATE.loadLogin(),PRIVATE.onClickTitle(),"mob"!==pkg.context.platform.get("type")&&PRIVATE.prefetchNews(),PUBLIC},teardown:function(why,__static,__proto,__shared){}})});