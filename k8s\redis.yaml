apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: webscraper
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: webscraper
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
data:
  redis.conf: |
    # Redis Configuration for WebScraper
    
    # Memory
    maxmemory 512mb
    maxmemory-policy allkeys-lru
    
    # Persistence
    appendonly yes
    appendfsync everysec
    
    # Network
    tcp-keepalive 300
    timeout 0
    
    # Logging
    loglevel notice
    
    # Performance
    tcp-backlog 511
    databases 16

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: webscraper
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app.kubernetes.io/name: redis
  template:
    metadata:
      labels:
        app.kubernetes.io/name: redis
        app.kubernetes.io/component: cache
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: redis
        command:
        - redis-server
        - /etc/redis/redis.conf
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
      - name: redis-config
        configMap:
          name: redis-config

---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: webscraper
  labels:
    app.kubernetes.io/name: redis
    app.kubernetes.io/component: cache
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
    name: redis
  selector:
    app.kubernetes.io/name: redis
