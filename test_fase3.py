#!/usr/bin/env python3
"""
Teste da Fase 3 - Produção & Escala.

Este script testa as funcionalidades de produção implementadas na Fase 3:
- Containerização Docker
- Redis cache e filas
- Elasticsearch analytics
- API REST FastAPI
- Dashboard web
- Kubernetes manifests
"""

import asyncio
import json
import sys
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.config import get_settings
from src.core.logging import configure_logging, get_logger


async def test_redis_integration():
    """Testar integração com Redis."""
    logger = get_logger(__name__)
    logger.info("Testing Redis integration")
    
    try:
        from src.core.redis_client import redis_client
        
        # Inicializar Redis
        await redis_client.initialize()
        
        # Testar cache
        test_key = "test_key"
        test_value = {"message": "Hello Redis!", "timestamp": "2025-01-01"}
        
        # Set cache
        success = await redis_client.cache_set(test_key, test_value, ttl=60)
        assert success, "Cache set should succeed"
        
        # Get cache
        cached_value = await redis_client.cache_get(test_key)
        assert cached_value == test_value, "Cached value should match"
        
        # Testar filas
        queue_name = "test_queue"
        queue_item = {"task": "test_task", "data": "test_data"}
        
        # Push to queue
        queue_length = await redis_client.queue_push(queue_name, queue_item, priority=5)
        assert queue_length > 0, "Queue should have items"
        
        # Pop from queue
        popped_item = await redis_client.queue_pop(queue_name, timeout=1)
        assert popped_item is not None, "Should pop item from queue"
        assert popped_item["task"] == "test_task", "Popped item should match"
        
        # Testar locks
        lock_name = "test_lock"
        lock_value = await redis_client.acquire_lock(lock_name, timeout=30)
        assert lock_value is not None, "Should acquire lock"
        
        released = await redis_client.release_lock(lock_name, lock_value)
        assert released, "Should release lock"
        
        # Health check
        health = await redis_client.health_check()
        assert health["status"] == "healthy", "Redis should be healthy"
        
        # Stats
        stats = await redis_client.get_stats()
        assert "redis_info" in stats, "Should have Redis info"
        
        logger.info("Redis integration test passed", health=health)
        return True
        
    except Exception as e:
        logger.error("Redis integration test failed", error=str(e), exc_info=True)
        return False


async def test_elasticsearch_integration():
    """Testar integração com Elasticsearch."""
    logger = get_logger(__name__)
    logger.info("Testing Elasticsearch integration")
    
    try:
        from src.core.elasticsearch_client import elasticsearch_client
        from src.core.validators import PageData
        from datetime import datetime
        
        # Tentar inicializar (pode falhar se ES não estiver rodando)
        try:
            await elasticsearch_client.initialize()
            es_available = True
        except Exception:
            logger.warning("Elasticsearch not available, skipping ES tests")
            es_available = False
        
        if es_available:
            # Criar dados de página de teste
            page_data = PageData(
                url="https://test.com/page1",
                title="Test Page for Elasticsearch",
                text_content="This is test content for Elasticsearch indexing and search.",
                word_count=10,
                quality_score=85,
                status="success",
                processed_at=datetime.utcnow()
            )
            
            # Indexar página
            indexed = await elasticsearch_client.index_page(page_data)
            assert indexed, "Page should be indexed successfully"
            
            # Aguardar um pouco para indexação
            await asyncio.sleep(2)
            
            # Buscar páginas
            search_results = await elasticsearch_client.search_pages(
                query="test content",
                size=10
            )
            
            assert "total" in search_results, "Search should return total count"
            assert "pages" in search_results, "Search should return pages"
            
            # Health check
            health = await elasticsearch_client.health_check()
            logger.info("Elasticsearch health", **health)
            
            # Stats
            stats = await elasticsearch_client.get_stats()
            assert "indices" in stats, "Should have indices stats"
            
            logger.info("Elasticsearch integration test passed")
        
        return True
        
    except Exception as e:
        logger.error("Elasticsearch integration test failed", error=str(e), exc_info=True)
        return False


async def test_api_endpoints():
    """Testar endpoints da API."""
    logger = get_logger(__name__)
    logger.info("Testing API endpoints")
    
    try:
        import aiohttp
        
        # Testar se a API está rodando (assumindo que está em localhost:8080)
        base_url = "http://localhost:8080"
        
        async with aiohttp.ClientSession() as session:
            # Testar endpoint raiz
            try:
                async with session.get(f"{base_url}/") as response:
                    if response.status == 200:
                        data = await response.json()
                        assert "service" in data, "Root endpoint should return service info"
                        logger.info("API root endpoint working", data=data)
                    else:
                        logger.warning("API not running on localhost:8080")
                        return True  # Não falhar se API não estiver rodando
            except aiohttp.ClientError:
                logger.warning("API not accessible, skipping API tests")
                return True
            
            # Testar health endpoint
            try:
                async with session.get(f"{base_url}/health") as response:
                    if response.status == 200:
                        health = await response.json()
                        assert "status" in health, "Health endpoint should return status"
                        logger.info("API health endpoint working", status=health.get("status"))
            except Exception as e:
                logger.debug("Health endpoint test failed", error=str(e))
            
            # Testar metrics endpoint
            try:
                async with session.get(f"{base_url}/metrics") as response:
                    if response.status == 200:
                        metrics = await response.text()
                        assert "webscraper_" in metrics, "Metrics should contain webscraper metrics"
                        logger.info("API metrics endpoint working", lines=len(metrics.split('\n')))
            except Exception as e:
                logger.debug("Metrics endpoint test failed", error=str(e))
        
        return True
        
    except Exception as e:
        logger.error("API endpoints test failed", error=str(e), exc_info=True)
        return False


def test_docker_files():
    """Testar arquivos Docker."""
    logger = get_logger(__name__)
    logger.info("Testing Docker files")
    
    try:
        # Verificar se Dockerfile existe
        dockerfile = Path("Dockerfile")
        assert dockerfile.exists(), "Dockerfile should exist"
        
        dockerfile_content = dockerfile.read_text()
        assert "FROM python:3.11-slim" in dockerfile_content, "Should use Python 3.11"
        assert "COPY" in dockerfile_content, "Should copy application files"
        assert "CMD" in dockerfile_content, "Should have CMD instruction"
        
        # Verificar docker-compose
        docker_compose = Path("docker-compose.fase3.yml")
        assert docker_compose.exists(), "Docker compose file should exist"
        
        compose_content = docker_compose.read_text()
        assert "webscraper:" in compose_content, "Should have webscraper service"
        assert "postgres:" in compose_content, "Should have postgres service"
        assert "redis:" in compose_content, "Should have redis service"
        assert "elasticsearch:" in compose_content, "Should have elasticsearch service"
        
        logger.info("Docker files test passed")
        return True
        
    except Exception as e:
        logger.error("Docker files test failed", error=str(e), exc_info=True)
        return False


def test_kubernetes_manifests():
    """Testar manifests Kubernetes."""
    logger = get_logger(__name__)
    logger.info("Testing Kubernetes manifests")
    
    try:
        k8s_dir = Path("k8s")
        assert k8s_dir.exists(), "k8s directory should exist"
        
        # Verificar arquivos essenciais
        required_files = [
            "namespace.yaml",
            "configmap.yaml",
            "postgres.yaml",
            "redis.yaml",
            "webscraper.yaml"
        ]
        
        for file_name in required_files:
            file_path = k8s_dir / file_name
            assert file_path.exists(), f"{file_name} should exist"
            
            content = file_path.read_text()
            assert "apiVersion:" in content, f"{file_name} should have apiVersion"
            assert "kind:" in content, f"{file_name} should have kind"
            assert "metadata:" in content, f"{file_name} should have metadata"
        
        # Verificar namespace
        namespace_content = (k8s_dir / "namespace.yaml").read_text()
        assert "name: webscraper" in namespace_content, "Should create webscraper namespace"
        
        # Verificar webscraper deployment
        webscraper_content = (k8s_dir / "webscraper.yaml").read_text()
        assert "Deployment" in webscraper_content, "Should have Deployment"
        assert "Service" in webscraper_content, "Should have Service"
        assert "HorizontalPodAutoscaler" in webscraper_content, "Should have HPA"
        
        logger.info("Kubernetes manifests test passed")
        return True
        
    except Exception as e:
        logger.error("Kubernetes manifests test failed", error=str(e), exc_info=True)
        return False


def test_dashboard_files():
    """Testar arquivos do dashboard."""
    logger = get_logger(__name__)
    logger.info("Testing dashboard files")
    
    try:
        # Verificar dashboard HTML
        dashboard_file = Path("src/web/dashboard.html")
        assert dashboard_file.exists(), "Dashboard HTML should exist"
        
        dashboard_content = dashboard_file.read_text()
        assert "WebScraper Enterprise Dashboard" in dashboard_content, "Should have correct title"
        assert "Chart.js" in dashboard_content, "Should include Chart.js"
        assert "tailwindcss" in dashboard_content, "Should include Tailwind CSS"
        assert "refreshDashboard" in dashboard_content, "Should have refresh function"
        
        # Verificar se tem elementos essenciais
        assert "health-status" in dashboard_content, "Should have health status"
        assert "total-pages" in dashboard_content, "Should have total pages metric"
        assert "qualityChart" in dashboard_content, "Should have quality chart"
        assert "search-input" in dashboard_content, "Should have search input"
        
        logger.info("Dashboard files test passed")
        return True
        
    except Exception as e:
        logger.error("Dashboard files test failed", error=str(e), exc_info=True)
        return False


async def test_integration_flow():
    """Testar flow de integração completo."""
    logger = get_logger(__name__)
    logger.info("Testing integration flow")
    
    try:
        # Testar se conseguimos importar e executar o flow empresarial
        from src.flows.enterprise_flow import enterprise_webscraper_flow
        
        # Executar flow em modo dry-run
        result = await enterprise_webscraper_flow(
            domain="httpbin.org",
            max_pages=1,
            incremental=False,
            dry_run=True
        )
        
        logger.info("Integration flow test completed", result=result)
        
        # Verificar resultado
        assert "domain" in result, "Result should contain domain"
        assert "status" in result, "Result should contain status"
        
        return True
        
    except Exception as e:
        logger.error("Integration flow test failed", error=str(e), exc_info=True)
        return False


async def main():
    """Função principal de teste da Fase 3."""
    print("🚀 Testando Fase 3 - Produção & Escala...")
    
    # Configurar logging
    configure_logging(level="INFO", structured=True)
    
    tests = [
        ("Redis Integration", test_redis_integration),
        ("Elasticsearch Integration", test_elasticsearch_integration),
        ("API Endpoints", test_api_endpoints),
        ("Docker Files", test_docker_files),
        ("Kubernetes Manifests", test_kubernetes_manifests),
        ("Dashboard Files", test_dashboard_files),
        ("Integration Flow", test_integration_flow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Executando: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                success = await test_func()
            else:
                success = test_func()
                
            if success:
                print(f"✅ {test_name}: PASSOU")
                results.append(True)
            else:
                print(f"❌ {test_name}: FALHOU")
                results.append(False)
        except Exception as e:
            print(f"💥 {test_name}: ERRO - {e}")
            results.append(False)
    
    # Resumo final
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Resumo dos Testes da Fase 3:")
    print(f"✅ Passou: {passed}/{total}")
    print(f"❌ Falhou: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 Fase 3 implementada com sucesso!")
        return 0
    elif passed >= total * 0.8:  # 80% ou mais
        print("⚠️ Fase 3 parcialmente implementada. Alguns serviços externos podem não estar disponíveis.")
        return 0
    else:
        print("⚠️ Alguns testes falharam. Verifique os logs.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
