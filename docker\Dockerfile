# =============================================================================
# Multi-stage Dockerfile para WebScraper
# =============================================================================

# Estágio 1: Base com Python e dependências do sistema
FROM python:3.11-slim as base

# Metadados
LABEL maintainer="<EMAIL>"
LABEL description="Sistema complexo de web scraping"
LABEL version="1.0.0"

# Variáveis de ambiente para Python
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \
    # Dependências básicas
    curl \
    wget \
    git \
    # Dependências para lxml e parsing
    libxml2-dev \
    libxslt1-dev \
    # Dependências para Playwright
    libnss3 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libgtk-3-0 \
    libgbm1 \
    libasound2 \
    # Limpeza
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Criar usuário não-root
RUN groupadd -r webscraper && useradd -r -g webscraper webscraper

# Criar diretórios
RUN mkdir -p /app /app/data /app/logs \
    && chown -R webscraper:webscraper /app

WORKDIR /app

# =============================================================================
# Estágio 2: Dependências Python
# =============================================================================

FROM base as dependencies

# Copiar arquivos de configuração
COPY pyproject.toml ./

# Instalar dependências Python
RUN pip install --upgrade pip setuptools wheel \
    && pip install -e ".[dev,s3,monitoring]"

# Instalar Playwright browsers
RUN playwright install chromium \
    && playwright install-deps chromium

# =============================================================================
# Estágio 3: Aplicação
# =============================================================================

FROM dependencies as application

# Copiar código fonte
COPY src/ ./src/
COPY configs/ ./configs/
COPY tests/ ./tests/
COPY alembic.ini ./
COPY alembic/ ./alembic/

# Copiar scripts de entrada
COPY docker/entrypoint.sh ./
COPY docker/healthcheck.sh ./

# Tornar scripts executáveis
RUN chmod +x entrypoint.sh healthcheck.sh

# Mudar para usuário não-root
USER webscraper

# Configurar volumes
VOLUME ["/app/data", "/app/logs"]

# Expor portas
EXPOSE 8080 8000

# Healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD ./healthcheck.sh

# Ponto de entrada
ENTRYPOINT ["./entrypoint.sh"]
CMD ["webscraper", "run"]

# =============================================================================
# Estágio 4: Desenvolvimento (opcional)
# =============================================================================

FROM application as development

# Voltar para root para instalar dependências de dev
USER root

# Instalar dependências adicionais para desenvolvimento
RUN apt-get update && apt-get install -y \
    vim \
    htop \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Instalar dependências de desenvolvimento
RUN pip install ipython jupyter

# Voltar para usuário webscraper
USER webscraper

# Comando padrão para desenvolvimento
CMD ["python", "-m", "ipython"]
