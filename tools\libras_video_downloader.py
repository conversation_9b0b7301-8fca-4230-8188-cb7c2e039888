#!/usr/bin/env python3
"""
🎬 LIBRAS VIDEO DOWNLOADER
Sistema completo para download de vídeos de Libras do site https://libras.cin.ufpe.br

Funcionalidades:
- Navega por todas as páginas (1-69)
- Extrai todos os links de vídeos
- Faz download organizado por sinal e articulador
- Sistema de retry e controle de rate limiting
- Relatórios detalhados de progresso
"""

import requests
from bs4 import BeautifulSoup
import re
import os
import time
import json
from pathlib import Path
from urllib.parse import urljoin, urlparse
from datetime import datetime
import asyncio
import aiohttp
import aiofiles
from typing import List, Dict, Optional
import logging

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('libras_downloader.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LibrasVideoDownloader:
    """Downloader principal para vídeos de Libras."""
    
    def __init__(self, output_dir: str = "libras_videos"):
        self.base_url = "https://libras.cin.ufpe.br"
        self.output_dir = Path(output_dir)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Estatísticas
        self.stats = {
            'pages_processed': 0,
            'videos_found': 0,
            'videos_downloaded': 0,
            'errors': 0,
            'start_time': datetime.now()
        }
        
        # Criar diretórios
        self.setup_directories()
        
    def setup_directories(self):
        """Criar estrutura de diretórios."""
        self.output_dir.mkdir(exist_ok=True)
        (self.output_dir / "videos").mkdir(exist_ok=True)
        (self.output_dir / "metadata").mkdir(exist_ok=True)
        (self.output_dir / "reports").mkdir(exist_ok=True)
        
        logger.info(f"📁 Diretórios criados em: {self.output_dir}")
    
    def get_all_pages(self) -> List[str]:
        """Gerar URLs de todas as páginas (1-69)."""
        pages = []
        for page_num in range(1, 70):  # 1 a 69
            pages.append(f"{self.base_url}/?page={page_num}")
        return pages
    
    def extract_video_links_from_page(self, page_url: str) -> List[Dict]:
        """Extrair todos os links de vídeo de uma página."""
        try:
            logger.info(f"🔍 Processando página: {page_url}")
            
            response = self.session.get(page_url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Encontrar todos os botões "Exibir"
            exibir_buttons = soup.find_all('a', string=re.compile(r'Exibir', re.IGNORECASE))
            
            video_links = []
            current_signal = None
            
            # Processar cada linha da tabela
            table_rows = soup.find_all('tr')
            
            for row in table_rows[1:]:  # Pular cabeçalho
                cells = row.find_all(['td', 'th'])
                
                if len(cells) >= 5:
                    # Primeira célula tem o nome do sinal
                    signal_name = cells[0].get_text().strip()
                    if signal_name and len(signal_name) > 1:
                        current_signal = signal_name
                    
                    # Células 3, 4, 5 têm os links dos articuladores
                    for i, articulador_num in enumerate([1, 2, 3], start=2):
                        if i < len(cells):
                            cell = cells[i]
                            link = cell.find('a')
                            if link and link.get('href'):
                                video_links.append({
                                    'signal_name': current_signal,
                                    'articulador': articulador_num,
                                    'page_url': link.get('href'),
                                    'full_url': urljoin(self.base_url, link.get('href')),
                                    'source_page': page_url
                                })
            
            logger.info(f"✅ Encontrados {len(video_links)} links de vídeo na página")
            self.stats['pages_processed'] += 1
            
            return video_links
            
        except Exception as e:
            logger.error(f"❌ Erro ao processar página {page_url}: {e}")
            self.stats['errors'] += 1
            return []
    
    def extract_video_url_from_sign_page(self, sign_page_url: str) -> Optional[str]:
        """Extrair URL real do vídeo de uma página de sinal."""
        try:
            response = self.session.get(sign_page_url, timeout=10)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Método 1: Procurar por tags source (mais confiável baseado no teste)
            source_tags = soup.find_all('source')
            if source_tags:
                # Pegar o primeiro source que corresponde ao intérprete
                # Extrair número do intérprete da URL
                interpreter_match = re.search(r'#interpreter_(\d+)', sign_page_url)
                interpreter_num = int(interpreter_match.group(1)) if interpreter_match else 1

                # Os vídeos parecem estar em ordem: interpreter_3, interpreter_2, interpreter_1
                # Então interpreter_3 = index 0, interpreter_2 = index 1, interpreter_1 = index 2
                source_index = 3 - interpreter_num  # Converter para índice correto

                if 0 <= source_index < len(source_tags):
                    video_url = source_tags[source_index].get('src')
                    if video_url:
                        return urljoin(self.base_url, video_url)

                # Fallback: pegar o primeiro source disponível
                for source in source_tags:
                    video_url = source.get('src')
                    if video_url:
                        return urljoin(self.base_url, video_url)

            # Método 2: Procurar por links diretos para .mp4
            mp4_links = soup.find_all('a', href=re.compile(r'\.mp4$'))
            if mp4_links:
                interpreter_match = re.search(r'#interpreter_(\d+)', sign_page_url)
                interpreter_num = int(interpreter_match.group(1)) if interpreter_match else 1

                link_index = 3 - interpreter_num
                if 0 <= link_index < len(mp4_links):
                    video_url = mp4_links[link_index].get('href')
                    if video_url:
                        return urljoin(self.base_url, video_url)

                # Fallback: primeiro link
                video_url = mp4_links[0].get('href')
                if video_url:
                    return urljoin(self.base_url, video_url)

            # Método 3: Procurar no texto da página por URLs .mp4
            page_text = str(soup)
            video_urls = re.findall(r'["\']([^"\']*storage/videos/[^"\']*\.mp4[^"\']*)["\']', page_text)

            if video_urls:
                interpreter_match = re.search(r'#interpreter_(\d+)', sign_page_url)
                interpreter_num = int(interpreter_match.group(1)) if interpreter_match else 1

                url_index = 3 - interpreter_num
                if 0 <= url_index < len(video_urls):
                    return urljoin(self.base_url, video_urls[url_index])

                # Fallback: primeira URL
                return urljoin(self.base_url, video_urls[0])

            return None

        except Exception as e:
            logger.error(f"❌ Erro ao extrair vídeo de {sign_page_url}: {e}")
            return None
    
    def download_video(self, video_url: str, signal_name: str, articulador: int) -> bool:
        """Fazer download de um vídeo específico."""
        try:
            # Criar nome de arquivo seguro
            safe_signal_name = re.sub(r'[^\w\s-]', '', signal_name).strip()
            safe_signal_name = re.sub(r'[-\s]+', '_', safe_signal_name)
            
            filename = f"{safe_signal_name}_articulador_{articulador}.mp4"
            filepath = self.output_dir / "videos" / filename
            
            # Verificar se já existe
            if filepath.exists():
                logger.info(f"⏭️ Arquivo já existe: {filename}")
                return True
            
            logger.info(f"⬇️ Baixando: {filename}")
            
            response = self.session.get(video_url, timeout=30, stream=True)
            response.raise_for_status()
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            file_size = filepath.stat().st_size
            logger.info(f"✅ Download concluído: {filename} ({file_size:,} bytes)")
            
            self.stats['videos_downloaded'] += 1
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro ao baixar vídeo {video_url}: {e}")
            self.stats['errors'] += 1
            return False
    
    def save_metadata(self, video_links: List[Dict]):
        """Salvar metadados dos vídeos encontrados."""
        metadata_file = self.output_dir / "metadata" / "video_links.json"
        
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(video_links, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Metadados salvos: {metadata_file}")
    
    def generate_report(self):
        """Gerar relatório final."""
        duration = datetime.now() - self.stats['start_time']
        
        report = f"""
🎬 RELATÓRIO DE DOWNLOAD - LIBRAS VIDEOS
{'='*60}

📊 ESTATÍSTICAS:
- Páginas processadas: {self.stats['pages_processed']}
- Vídeos encontrados: {self.stats['videos_found']}
- Vídeos baixados: {self.stats['videos_downloaded']}
- Erros: {self.stats['errors']}
- Tempo total: {duration}

📁 ARQUIVOS:
- Diretório: {self.output_dir}
- Vídeos: {self.output_dir}/videos/
- Metadados: {self.output_dir}/metadata/
- Relatórios: {self.output_dir}/reports/

🎯 TAXA DE SUCESSO: {(self.stats['videos_downloaded']/max(self.stats['videos_found'], 1)*100):.1f}%
"""
        
        report_file = self.output_dir / "reports" / f"download_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        logger.info(f"📋 Relatório salvo: {report_file}")
    
    def run(self):
        """Executar o processo completo de download."""
        logger.info("🚀 INICIANDO DOWNLOAD DE VÍDEOS DE LIBRAS")
        logger.info("="*60)
        
        # 1. Obter todas as páginas
        pages = self.get_all_pages()
        logger.info(f"📄 Total de páginas para processar: {len(pages)}")
        
        # 2. Extrair todos os links de vídeo
        all_video_links = []
        
        for i, page_url in enumerate(pages, 1):
            logger.info(f"📄 Processando página {i}/{len(pages)}")
            
            video_links = self.extract_video_links_from_page(page_url)
            all_video_links.extend(video_links)
            
            # Rate limiting
            time.sleep(1)
            
            # Salvar progresso a cada 10 páginas
            if i % 10 == 0:
                self.save_metadata(all_video_links)
        
        self.stats['videos_found'] = len(all_video_links)
        logger.info(f"🎯 Total de vídeos encontrados: {len(all_video_links)}")
        
        # 3. Salvar metadados completos
        self.save_metadata(all_video_links)
        
        # 4. Fazer download dos vídeos
        logger.info("⬇️ INICIANDO DOWNLOADS")
        
        for i, video_link in enumerate(all_video_links, 1):
            logger.info(f"🎬 Processando vídeo {i}/{len(all_video_links)}")
            
            # Extrair URL real do vídeo
            video_url = self.extract_video_url_from_sign_page(video_link['full_url'])
            
            if video_url:
                success = self.download_video(
                    video_url, 
                    video_link['signal_name'], 
                    video_link['articulador']
                )
                
                if success:
                    logger.info(f"✅ Vídeo baixado com sucesso")
                else:
                    logger.error(f"❌ Falha no download")
            else:
                logger.warning(f"⚠️ URL de vídeo não encontrada para {video_link['signal_name']}")
            
            # Rate limiting
            time.sleep(2)
        
        # 5. Gerar relatório final
        self.generate_report()
        
        logger.info("🎉 PROCESSO CONCLUÍDO!")

def main():
    """Função principal."""
    print("🎬 LIBRAS VIDEO DOWNLOADER")
    print("="*50)
    print("Sistema para download completo de vídeos de Libras")
    print("Site: https://libras.cin.ufpe.br")
    print()
    
    # Criar downloader
    downloader = LibrasVideoDownloader()
    
    # Executar processo
    downloader.run()

if __name__ == "__main__":
    main()
