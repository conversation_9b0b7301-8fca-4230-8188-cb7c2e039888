"""
Logging - Sistema de logging estruturado.

Este módulo configura logging estruturado usando structlog
para observabilidade e debugging do WebScraper.
"""

import logging
import sys
from pathlib import Path
from typing import Any, Dict, Optional

import structlog
from structlog.stdlib import LoggerFactory


def configure_logging(
    level: str = "INFO",
    structured: bool = True,
    log_file: Optional[str] = None,
    json_format: bool = False,
) -> None:
    """
    Configurar sistema de logging.
    
    Args:
        level: Nível de log (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        structured: Se True, usa logging estruturado
        log_file: Arquivo para salvar logs (opcional)
        json_format: Se True, usa formato JSON
    """
    # Configurar nível de log
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    # Configurar handlers
    handlers = []
    
    # Handler para console
    console_handler = logging.StreamHandler(sys.stdout)
    handlers.append(console_handler)
    
    # Handler para arquivo se especificado
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        handlers.append(file_handler)
    
    # Configurar logging básico
    logging.basicConfig(
        level=log_level,
        handlers=handlers,
        format="%(message)s",  # structlog vai formatar
    )
    
    # Configurar processadores do structlog
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="ISO"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]
    
    if structured:
        if json_format:
            # Formato JSON para produção
            processors.append(structlog.processors.JSONRenderer())
        else:
            # Formato colorido para desenvolvimento
            processors.append(
                structlog.dev.ConsoleRenderer(colors=True)
            )
    else:
        # Formato simples
        processors.append(
            structlog.processors.KeyValueRenderer(
                key_order=['timestamp', 'level', 'logger', 'event']
            )
        )
    
    # Configurar structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Configurar loggers de bibliotecas externas
    _configure_external_loggers(log_level)
    
    logger = structlog.get_logger(__name__)
    logger.info(
        "Logging configured",
        level=level,
        structured=structured,
        json_format=json_format,
        log_file=log_file,
    )


def _configure_external_loggers(level: int) -> None:
    """Configurar loggers de bibliotecas externas."""
    # Reduzir verbosidade de bibliotecas externas
    external_loggers = [
        'httpx',
        'httpcore',
        'urllib3',
        'requests',
        'asyncio',
        'prefect',
        'sqlalchemy',
        'alembic',
    ]
    
    for logger_name in external_loggers:
        logger = logging.getLogger(logger_name)
        # Usar WARNING para bibliotecas externas, exceto se DEBUG global
        external_level = logging.WARNING if level > logging.DEBUG else level
        logger.setLevel(external_level)


def get_logger(name: str) -> structlog.BoundLogger:
    """Obter logger estruturado."""
    return structlog.get_logger(name)


class LogContext:
    """Context manager para adicionar contexto aos logs."""
    
    def __init__(self, logger: structlog.BoundLogger, **context):
        self.logger = logger
        self.context = context
        self.bound_logger = None
    
    def __enter__(self) -> structlog.BoundLogger:
        self.bound_logger = self.logger.bind(**self.context)
        return self.bound_logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass


def log_function_call(logger: structlog.BoundLogger):
    """Decorator para logar chamadas de função."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_name = func.__name__
            logger.debug(
                "Function called",
                function=func_name,
                args_count=len(args),
                kwargs_keys=list(kwargs.keys()),
            )
            
            try:
                result = func(*args, **kwargs)
                logger.debug(
                    "Function completed",
                    function=func_name,
                    result_type=type(result).__name__,
                )
                return result
            except Exception as e:
                logger.error(
                    "Function failed",
                    function=func_name,
                    error=str(e),
                    exc_info=True,
                )
                raise
        
        return wrapper
    return decorator


def log_async_function_call(logger: structlog.BoundLogger):
    """Decorator para logar chamadas de função assíncrona."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            func_name = func.__name__
            logger.debug(
                "Async function called",
                function=func_name,
                args_count=len(args),
                kwargs_keys=list(kwargs.keys()),
            )
            
            try:
                result = await func(*args, **kwargs)
                logger.debug(
                    "Async function completed",
                    function=func_name,
                    result_type=type(result).__name__,
                )
                return result
            except Exception as e:
                logger.error(
                    "Async function failed",
                    function=func_name,
                    error=str(e),
                    exc_info=True,
                )
                raise
        
        return wrapper
    return decorator


class MetricsLogger:
    """Logger especializado para métricas."""
    
    def __init__(self, logger: structlog.BoundLogger):
        self.logger = logger.bind(component="metrics")
    
    def log_request_metrics(
        self,
        url: str,
        status_code: int,
        duration_ms: int,
        content_length: int = 0,
    ) -> None:
        """Logar métricas de requisição HTTP."""
        self.logger.info(
            "HTTP request metrics",
            url=url,
            status_code=status_code,
            duration_ms=duration_ms,
            content_length=content_length,
            metric_type="http_request",
        )
    
    def log_parsing_metrics(
        self,
        url: str,
        quality_score: int,
        word_count: int,
        headings_count: int,
        processing_time_ms: int,
    ) -> None:
        """Logar métricas de parsing."""
        self.logger.info(
            "Parsing metrics",
            url=url,
            quality_score=quality_score,
            word_count=word_count,
            headings_count=headings_count,
            processing_time_ms=processing_time_ms,
            metric_type="parsing",
        )
    
    def log_crawl_metrics(
        self,
        domain: str,
        urls_discovered: int,
        urls_processed: int,
        success_rate: float,
        duration_seconds: float,
    ) -> None:
        """Logar métricas de crawling."""
        self.logger.info(
            "Crawl metrics",
            domain=domain,
            urls_discovered=urls_discovered,
            urls_processed=urls_processed,
            success_rate=success_rate,
            duration_seconds=duration_seconds,
            metric_type="crawl",
        )


class PerformanceLogger:
    """Logger para métricas de performance."""
    
    def __init__(self, logger: structlog.BoundLogger):
        self.logger = logger.bind(component="performance")
    
    def log_memory_usage(self, memory_mb: float, component: str = "system") -> None:
        """Logar uso de memória."""
        self.logger.debug(
            "Memory usage",
            memory_mb=memory_mb,
            component=component,
            metric_type="memory",
        )
    
    def log_queue_size(self, queue_size: int, queue_type: str = "url_frontier") -> None:
        """Logar tamanho de filas."""
        self.logger.debug(
            "Queue size",
            queue_size=queue_size,
            queue_type=queue_type,
            metric_type="queue",
        )
    
    def log_cache_stats(
        self,
        cache_hits: int,
        cache_misses: int,
        cache_size: int,
        cache_type: str = "http",
    ) -> None:
        """Logar estatísticas de cache."""
        hit_rate = cache_hits / (cache_hits + cache_misses) * 100 if (cache_hits + cache_misses) > 0 else 0
        
        self.logger.info(
            "Cache statistics",
            cache_hits=cache_hits,
            cache_misses=cache_misses,
            cache_size=cache_size,
            hit_rate=hit_rate,
            cache_type=cache_type,
            metric_type="cache",
        )


def setup_default_logging() -> None:
    """Configurar logging padrão baseado em variáveis de ambiente."""
    import os
    
    level = os.getenv("LOG_LEVEL", "INFO")
    structured = os.getenv("STRUCTURED_LOGGING", "true").lower() == "true"
    json_format = os.getenv("LOG_FORMAT", "").lower() == "json"
    log_file = os.getenv("LOG_FILE")
    
    configure_logging(
        level=level,
        structured=structured,
        log_file=log_file,
        json_format=json_format,
    )


# Configurar logging automaticamente na importação
if not structlog.is_configured():
    setup_default_logging()
