"""
Elasticsearch Client - Sistema de busca e analytics.

Este módulo implementa integração com Elasticsearch para busca avançada,
analytics de conteúdo e dashboards de dados.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

import structlog
from elasticsearch import AsyncElasticsearch
try:
    from elasticsearch.exceptions import ElasticsearchException, NotFoundError
except ImportError:
    # Fallback para versões mais novas do elasticsearch
    from elasticsearch import ConnectionError as ElasticsearchException
    from elasticsearch import NotFoundError

from .config import get_settings
from .metrics import metrics_collector
from .validators import PageData

logger = structlog.get_logger(__name__)


class ElasticsearchClient:
    """Cliente Elasticsearch assíncrono para busca e analytics."""
    
    def __init__(self, elasticsearch_url: Optional[str] = None):
        self.settings = get_settings()
        self.elasticsearch_url = elasticsearch_url or getattr(
            self.settings, 'elasticsearch_url', 'http://localhost:9200'
        )
        
        # Cliente Elasticsearch
        self.client = AsyncElasticsearch(
            [self.elasticsearch_url],
            timeout=30,
            max_retries=3,
            retry_on_timeout=True,
        )
        
        self._initialized = False
        
        # Índices
        self.pages_index = "webscraper-pages"
        self.metrics_index = "webscraper-metrics"
        self.sessions_index = "webscraper-sessions"
        
        logger.info("Elasticsearch client initialized", url=self.elasticsearch_url)
    
    async def initialize(self) -> None:
        """Inicializar conexão e índices."""
        if self._initialized:
            return
        
        try:
            # Testar conexão
            health = await self.client.cluster.health()
            logger.info("Elasticsearch cluster health", status=health["status"])
            
            # Criar índices se não existirem
            await self._create_indices()
            
            self._initialized = True
            logger.info("Elasticsearch initialized successfully")
            
        except Exception as e:
            logger.error("Failed to initialize Elasticsearch", error=str(e))
            raise
    
    async def _create_indices(self) -> None:
        """Criar índices com mappings otimizados."""
        
        # Mapping para páginas
        pages_mapping = {
            "mappings": {
                "properties": {
                    "url": {"type": "keyword"},
                    "domain": {"type": "keyword"},
                    "title": {
                        "type": "text",
                        "analyzer": "standard",
                        "fields": {"keyword": {"type": "keyword"}}
                    },
                    "content": {
                        "type": "text",
                        "analyzer": "standard"
                    },
                    "content_hash": {"type": "keyword"},
                    "semantic_hash": {"type": "keyword"},
                    "word_count": {"type": "integer"},
                    "quality_score": {"type": "integer"},
                    "quality_tier": {"type": "keyword"},
                    "status": {"type": "keyword"},
                    "headings": {
                        "type": "nested",
                        "properties": {
                            "level": {"type": "integer"},
                            "text": {"type": "text"}
                        }
                    },
                    "links": {
                        "type": "nested",
                        "properties": {
                            "url": {"type": "keyword"},
                            "text": {"type": "text"},
                            "type": {"type": "keyword"}
                        }
                    },
                    "code_blocks": {
                        "type": "nested",
                        "properties": {
                            "language": {"type": "keyword"},
                            "code": {"type": "text"}
                        }
                    },
                    "tables": {
                        "type": "nested",
                        "properties": {
                            "headers": {"type": "keyword"},
                            "rows": {"type": "integer"}
                        }
                    },
                    "fetched_at": {"type": "date"},
                    "processed_at": {"type": "date"},
                    "indexed_at": {"type": "date"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0,
                "analysis": {
                    "analyzer": {
                        "content_analyzer": {
                            "type": "standard",
                            "stopwords": "_english_"
                        }
                    }
                }
            }
        }
        
        # Mapping para métricas
        metrics_mapping = {
            "mappings": {
                "properties": {
                    "metric_name": {"type": "keyword"},
                    "metric_type": {"type": "keyword"},
                    "value": {"type": "double"},
                    "labels": {"type": "object"},
                    "component": {"type": "keyword"},
                    "domain": {"type": "keyword"},
                    "timestamp": {"type": "date"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }
        
        # Mapping para sessões
        sessions_mapping = {
            "mappings": {
                "properties": {
                    "session_id": {"type": "keyword"},
                    "domain": {"type": "keyword"},
                    "session_type": {"type": "keyword"},
                    "status": {"type": "keyword"},
                    "urls_discovered": {"type": "integer"},
                    "urls_processed": {"type": "integer"},
                    "urls_successful": {"type": "integer"},
                    "urls_failed": {"type": "integer"},
                    "avg_quality_score": {"type": "double"},
                    "duration_seconds": {"type": "double"},
                    "started_at": {"type": "date"},
                    "completed_at": {"type": "date"}
                }
            },
            "settings": {
                "number_of_shards": 1,
                "number_of_replicas": 0
            }
        }
        
        # Criar índices
        indices = [
            (self.pages_index, pages_mapping),
            (self.metrics_index, metrics_mapping),
            (self.sessions_index, sessions_mapping)
        ]
        
        for index_name, mapping in indices:
            try:
                exists = await self.client.indices.exists(index=index_name)
                if not exists:
                    await self.client.indices.create(index=index_name, body=mapping)
                    logger.info("Index created", index=index_name)
                else:
                    logger.debug("Index already exists", index=index_name)
            except Exception as e:
                logger.error("Failed to create index", index=index_name, error=str(e))
    
    # ================================
    # Page Operations
    # ================================
    
    async def index_page(self, page_data: PageData) -> bool:
        """Indexar página no Elasticsearch."""
        try:
            # Preparar documento
            doc = {
                "url": str(page_data.url),
                "domain": str(page_data.url).split("//")[1].split("/")[0],
                "title": page_data.title,
                "content": page_data.text_content,
                "content_hash": page_data.content_hash,
                "semantic_hash": getattr(page_data, 'semantic_hash', ''),
                "word_count": page_data.word_count,
                "quality_score": page_data.quality_score,
                "quality_tier": getattr(page_data, 'quality_tier', 'unknown'),
                "status": page_data.status.value,
                "headings": [
                    {"level": h.level, "text": h.text}
                    for h in page_data.headings_tree
                ],
                "links": [
                    {"url": str(link.url), "text": link.text, "type": "internal"}
                    for link in page_data.internal_links
                ] + [
                    {"url": str(link.url), "text": link.text, "type": "external"}
                    for link in page_data.external_links
                ],
                "code_blocks": [
                    {"language": cb.language, "code": cb.code}
                    for cb in page_data.code_blocks
                ],
                "tables": [
                    {"headers": table.headers, "rows": len(table.rows)}
                    for table in page_data.tables
                ],
                "fetched_at": getattr(page_data, 'fetched_at', datetime.utcnow()).isoformat(),
                "processed_at": page_data.processed_at.isoformat(),
                "indexed_at": datetime.utcnow().isoformat()
            }
            
            # Usar URL hash como ID do documento
            doc_id = page_data.content_hash
            
            # Indexar documento
            await self.client.index(
                index=self.pages_index,
                id=doc_id,
                body=doc
            )
            
            metrics_collector.storage_operations_total.labels(
                operation="index_page",
                backend="elasticsearch",
                status="success"
            ).inc()
            
            logger.debug("Page indexed", url=page_data.url, doc_id=doc_id)
            return True
            
        except Exception as e:
            logger.error("Failed to index page", url=page_data.url, error=str(e))
            metrics_collector.record_error("elasticsearch", "index_page_error")
            return False
    
    async def search_pages(
        self,
        query: str,
        filters: Optional[Dict[str, Any]] = None,
        size: int = 20,
        from_: int = 0,
        sort: Optional[List[Dict]] = None
    ) -> Dict[str, Any]:
        """Buscar páginas."""
        try:
            # Construir query
            search_body = {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["title^3", "content^1", "headings.text^2"],
                                    "type": "best_fields",
                                    "fuzziness": "AUTO"
                                }
                            }
                        ]
                    }
                },
                "size": size,
                "from": from_,
                "highlight": {
                    "fields": {
                        "title": {},
                        "content": {"fragment_size": 150, "number_of_fragments": 3}
                    }
                }
            }
            
            # Adicionar filtros
            if filters:
                filter_clauses = []
                
                if "domain" in filters:
                    filter_clauses.append({"term": {"domain": filters["domain"]}})
                
                if "quality_tier" in filters:
                    filter_clauses.append({"term": {"quality_tier": filters["quality_tier"]}})
                
                if "min_quality_score" in filters:
                    filter_clauses.append({"range": {"quality_score": {"gte": filters["min_quality_score"]}}})
                
                if "date_range" in filters:
                    date_filter = {"range": {"processed_at": {}}}
                    if "from" in filters["date_range"]:
                        date_filter["range"]["processed_at"]["gte"] = filters["date_range"]["from"]
                    if "to" in filters["date_range"]:
                        date_filter["range"]["processed_at"]["lte"] = filters["date_range"]["to"]
                    filter_clauses.append(date_filter)
                
                if filter_clauses:
                    search_body["query"]["bool"]["filter"] = filter_clauses
            
            # Adicionar ordenação
            if sort:
                search_body["sort"] = sort
            else:
                search_body["sort"] = [{"_score": {"order": "desc"}}]
            
            # Executar busca
            response = await self.client.search(
                index=self.pages_index,
                body=search_body
            )
            
            # Processar resultados
            hits = response["hits"]
            results = {
                "total": hits["total"]["value"],
                "pages": [],
                "aggregations": response.get("aggregations", {})
            }
            
            for hit in hits["hits"]:
                page = {
                    "id": hit["_id"],
                    "score": hit["_score"],
                    "source": hit["_source"],
                    "highlight": hit.get("highlight", {})
                }
                results["pages"].append(page)
            
            logger.debug("Page search completed", query=query, total=results["total"])
            return results
            
        except Exception as e:
            logger.error("Page search failed", query=query, error=str(e))
            return {"total": 0, "pages": [], "error": str(e)}
    
    async def get_page_analytics(
        self,
        domain: Optional[str] = None,
        days: int = 30
    ) -> Dict[str, Any]:
        """Obter analytics de páginas."""
        try:
            # Data de início
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Query base
            query = {
                "bool": {
                    "filter": [
                        {"range": {"processed_at": {"gte": start_date.isoformat()}}}
                    ]
                }
            }
            
            if domain:
                query["bool"]["filter"].append({"term": {"domain": domain}})
            
            # Agregações
            aggs = {
                "total_pages": {"value_count": {"field": "url"}},
                "avg_quality": {"avg": {"field": "quality_score"}},
                "quality_distribution": {
                    "terms": {"field": "quality_tier"}
                },
                "domains": {
                    "terms": {"field": "domain", "size": 20}
                },
                "pages_over_time": {
                    "date_histogram": {
                        "field": "processed_at",
                        "calendar_interval": "day"
                    }
                },
                "word_count_stats": {
                    "stats": {"field": "word_count"}
                },
                "top_domains_by_quality": {
                    "terms": {"field": "domain"},
                    "aggs": {
                        "avg_quality": {"avg": {"field": "quality_score"}}
                    }
                }
            }
            
            # Executar agregação
            response = await self.client.search(
                index=self.pages_index,
                body={
                    "query": query,
                    "size": 0,
                    "aggs": aggs
                }
            )
            
            # Processar resultados
            aggregations = response["aggregations"]
            
            analytics = {
                "period_days": days,
                "total_pages": aggregations["total_pages"]["value"],
                "avg_quality_score": round(aggregations["avg_quality"]["value"] or 0, 2),
                "quality_distribution": {
                    bucket["key"]: bucket["doc_count"]
                    for bucket in aggregations["quality_distribution"]["buckets"]
                },
                "top_domains": [
                    {"domain": bucket["key"], "pages": bucket["doc_count"]}
                    for bucket in aggregations["domains"]["buckets"]
                ],
                "pages_timeline": [
                    {
                        "date": bucket["key_as_string"],
                        "pages": bucket["doc_count"]
                    }
                    for bucket in aggregations["pages_over_time"]["buckets"]
                ],
                "word_count_stats": {
                    "min": aggregations["word_count_stats"]["min"],
                    "max": aggregations["word_count_stats"]["max"],
                    "avg": round(aggregations["word_count_stats"]["avg"] or 0, 2),
                    "sum": aggregations["word_count_stats"]["sum"]
                },
                "domain_quality_ranking": [
                    {
                        "domain": bucket["key"],
                        "pages": bucket["doc_count"],
                        "avg_quality": round(bucket["avg_quality"]["value"] or 0, 2)
                    }
                    for bucket in aggregations["top_domains_by_quality"]["buckets"]
                ]
            }
            
            logger.debug("Page analytics generated", domain=domain, days=days)
            return analytics
            
        except Exception as e:
            logger.error("Failed to get page analytics", domain=domain, error=str(e))
            return {"error": str(e)}
    
    # ================================
    # Metrics Operations
    # ================================
    
    async def index_metric(
        self,
        metric_name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None,
        component: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ) -> bool:
        """Indexar métrica."""
        try:
            doc = {
                "metric_name": metric_name,
                "metric_type": "gauge",  # Pode ser expandido
                "value": value,
                "labels": labels or {},
                "component": component,
                "domain": labels.get("domain") if labels else None,
                "timestamp": (timestamp or datetime.utcnow()).isoformat()
            }
            
            await self.client.index(
                index=self.metrics_index,
                body=doc
            )
            
            return True
            
        except Exception as e:
            logger.error("Failed to index metric", metric=metric_name, error=str(e))
            return False
    
    # ================================
    # Health & Stats
    # ================================
    
    async def health_check(self) -> Dict[str, Any]:
        """Verificar saúde do Elasticsearch."""
        try:
            start_time = asyncio.get_event_loop().time()
            
            # Health check
            health = await self.client.cluster.health()
            
            response_time = (asyncio.get_event_loop().time() - start_time) * 1000
            
            return {
                "status": "healthy" if health["status"] in ["green", "yellow"] else "unhealthy",
                "cluster_status": health["status"],
                "response_time_ms": round(response_time, 2),
                "number_of_nodes": health["number_of_nodes"],
                "active_shards": health["active_shards"],
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
            }
    
    async def get_stats(self) -> Dict[str, Any]:
        """Obter estatísticas do Elasticsearch."""
        try:
            # Stats dos índices
            indices_stats = await self.client.indices.stats(index=f"{self.pages_index},*")
            
            # Contar documentos
            pages_count = await self.client.count(index=self.pages_index)
            metrics_count = await self.client.count(index=self.metrics_index)
            sessions_count = await self.client.count(index=self.sessions_index)
            
            return {
                "indices": {
                    "pages": pages_count["count"],
                    "metrics": metrics_count["count"],
                    "sessions": sessions_count["count"]
                },
                "storage": {
                    "total_size": indices_stats["_all"]["total"]["store"]["size_in_bytes"],
                    "total_docs": indices_stats["_all"]["total"]["docs"]["count"]
                }
            }
            
        except Exception as e:
            logger.error("Failed to get Elasticsearch stats", error=str(e))
            return {"error": str(e)}
    
    async def close(self) -> None:
        """Fechar conexão Elasticsearch."""
        try:
            await self.client.close()
            logger.info("Elasticsearch connection closed")
        except Exception as e:
            logger.error("Error closing Elasticsearch connection", error=str(e))


# Instância global do cliente Elasticsearch
elasticsearch_client = ElasticsearchClient()


async def init_elasticsearch() -> None:
    """Inicializar cliente Elasticsearch."""
    await elasticsearch_client.initialize()


async def close_elasticsearch() -> None:
    """Fechar cliente Elasticsearch."""
    await elasticsearch_client.close()
