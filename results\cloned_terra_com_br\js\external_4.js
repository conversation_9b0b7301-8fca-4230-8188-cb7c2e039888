
// Copyright 2012 Google Inc. All rights reserved.
 
 (function(w,g){w[g]=w[g]||{};
 w[g].e=function(s){return eval(s);};})(window,'google_tag_manager');
 
(function(){

var data = {
"resource": {
  "version":"176",
  
  "macros":[{"function":"__e"},{"function":"__v","vtp_name":"gtm.elementClasses","vtp_dataLayerVersion":1},{"function":"__u","vtp_component":"FRAGMENT","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__v","vtp_name":"gtm.elementUrl","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.triggers","vtp_dataLayerVersion":2,"vtp_setDefaultValue":true,"vtp_defaultValue":""},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"video_provider"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"video_title"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"player_id"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"player_source_id"},{"function":"__v","vtp_dataLayerVersion":2,"vtp_setDefaultValue":false,"vtp_name":"event_name"},{"function":"__u","vtp_component":"URL","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__jsm","convert_undefined_to":"0","vtp_javascript":["template","(function(){if(window.performance\u0026\u0026window.performance.getEntriesByType){var a=window.performance.getEntriesByType(\"navigation\");if(a.length\u003E0)return a=a[0],a=a.loadEventEnd-a.startTime,Math.round((a\/1E3+Number.EPSILON)*100)\/100}})();"]},{"function":"__gtes","vtp_eventSettingsTable":["list",["map","parameter","loading_time_sec","parameterValue",["macro",11]]]},{"function":"__c","vtp_value":"G-XXXXXXXXXX"},{"function":"__u","vtp_component":"PATH","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__remm","vtp_setDefaultValue":false,"vtp_input":["macro",14],"vtp_fullMatch":true,"vtp_replaceAfterMatch":true,"vtp_ignoreCase":true,"vtp_map":["list",["map","key","Page Path","value","dailymotion"]]},{"function":"__u","vtp_component":"HOST","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__f","vtp_component":"URL"},{"function":"__e"},{"function":"__v","vtp_name":"gtm.element","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.elementId","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.elementTarget","vtp_dataLayerVersion":1},{"function":"__aev","vtp_varType":"TEXT"},{"function":"__v","vtp_name":"gtm.element","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.elementClasses","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.elementId","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.elementTarget","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.elementUrl","vtp_dataLayerVersion":1},{"function":"__aev","vtp_varType":"TEXT"},{"function":"__v","vtp_name":"gtm.errorMessage","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.errorUrl","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.errorLineNumber","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.newUrlFragment","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.oldUrlFragment","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.newHistoryState","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.oldHistoryState","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.historyChangeSource","vtp_dataLayerVersion":1},{"function":"__ctv"},{"function":"__dbg"},{"function":"__r"},{"function":"__cid"},{"function":"__hid"},{"function":"__v","vtp_name":"gtm.videoProvider","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoUrl","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoTitle","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoDuration","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoPercent","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoVisible","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoStatus","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.videoCurrentTime","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.scrollThreshold","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.scrollUnits","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.scrollDirection","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.visibleRatio","vtp_dataLayerVersion":1},{"function":"__v","vtp_name":"gtm.visibleTime","vtp_dataLayerVersion":1}],
  "tags":[{"function":"__googtag","metadata":["map"],"once_per_event":true,"vtp_tagId":"G-FJBH1GT2QG","vtp_configSettingsTable":["list",["map","parameter","send_page_view","parameterValue","false"],["map","parameter","cookie_flags","parameterValue","secure;samesite=none"]],"tag_id":8},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"Clique_BotaoJogoHoroscopo","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":11},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"portal_event_click_botão_aprendamais","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":14},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"anuncie_click_menu_contato","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":17},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_banner_curso_andre","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":25},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"web_interstitial_displayed","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":28},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_terra_mail_ds","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":32},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_menu_central_assinante","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":36},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_menu_curso_online","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":38},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_meu_terra_perfil","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":42},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_whatsapp","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":44},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_gmail_login","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":46},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_ads_banner_topo_home","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":48},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_hamburguer","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":54},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_logo","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":56},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_loja_virtual_ds","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":58},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_vale_saude","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":62},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_vivae","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":64},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_terra_meu_negocio","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":67},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"tabela_brasileirao_home","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":71},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"scroll_etapa_1","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":73},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"scroll_etapa_2","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":75},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"scroll_etapa_3","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":77},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"scroll_etapa_4","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":79},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"scroll_etapa_5","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":81},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"scroll_etapa_6","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":83},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_open_menu_horoscopo","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":88},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_open_menu_entrete","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":89},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_open_menu_noticias","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":92},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_open_menu_esportes","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":94},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_open_menu_apostas","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":96},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_open_menu_mobilidade","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":98},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_open_menu_nos","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":101},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_open_menu_voce","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":103},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_open_menu_degusta","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":105},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_open_menu_planeta","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":107},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_open_menu_dinheiros","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":109},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_open_menu_educar","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":111},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_open_menu_visao","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":113},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_vivo_fibra","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":122},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_terra_astral_login","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":124},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_terra_astral_assinatura","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":126},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"login_terra_astral_membro","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":128},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_eventSettingsTable":["list",["map","parameter","video_provider","parameterValue",["macro",5]],["map","parameter","video_title","parameterValue",["macro",6]],["map","parameter","player_id","parameterValue",["macro",7]],["map","parameter","player_source_id","parameterValue",["macro",8]]],"vtp_enhancedUserId":false,"vtp_eventName":["macro",9],"vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":137},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"modulo_player_daily_motion","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":142},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"clique_compartilhar_redes","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":146},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"clique_compartilhar_facebook","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":148},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"clique_compartilhar_TW","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":150},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"clique_compartilhar_pinterest","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":152},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"exibir_comentarios_rodape","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":154},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"clique_compartilhar_whatsapp","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":157},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"push_notification_pop_up","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":158},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_smart_checkout_terra_astral","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":164},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_cta1_vitrineDS","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":171},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_cta2_vitrineDS","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":176},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"login_email_terra","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":177},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_combo_cursos_ds","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":180},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"click_header_amazon_prime_ds","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":181},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"leia_mais","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":190},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"page_load_time","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_eventSettingsVariable":["macro",12],"vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":197},{"function":"__gaawe","metadata":["map"],"once_per_event":true,"vtp_sendEcommerceData":false,"vtp_enhancedUserId":false,"vtp_eventName":"seu_terra_login","vtp_measurementIdOverride":"G-FJBH1GT2QG","vtp_enableUserProperties":true,"vtp_enableEuid":true,"vtp_migratedToV2":true,"vtp_demoV2":false,"tag_id":203},{"function":"__googtag","metadata":["map"],"once_per_event":true,"vtp_tagId":"G-7Y8JPEF9M3","vtp_configSettingsTable":["list",["map","parameter","send_page_view","parameterValue","false"],["map","parameter","cookie_flags","parameterValue","secure;samesite=none"]],"tag_id":211},{"function":"__cl","tag_id":212},{"function":"__cl","tag_id":213},{"function":"__cl","tag_id":214},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_15","tag_id":215},{"function":"__cl","tag_id":216},{"function":"__cl","tag_id":217},{"function":"__hl","tag_id":218},{"function":"__cl","tag_id":219},{"function":"__cl","tag_id":220},{"function":"__cl","tag_id":221},{"function":"__cl","tag_id":222},{"function":"__cl","tag_id":223},{"function":"__cl","tag_id":224},{"function":"__cl","tag_id":225},{"function":"__cl","tag_id":226},{"function":"__cl","tag_id":227},{"function":"__cl","tag_id":228},{"function":"__cl","tag_id":229},{"function":"__cl","tag_id":230},{"function":"__cl","tag_id":231},{"function":"__cl","tag_id":232},{"function":"__cl","tag_id":233},{"function":"__cl","tag_id":234},{"function":"__cl","tag_id":235},{"function":"__cl","tag_id":236},{"function":"__cl","tag_id":237},{"function":"__cl","tag_id":238},{"function":"__cl","tag_id":239},{"function":"__cl","tag_id":240},{"function":"__sdl","vtp_verticalThresholdUnits":"PERCENT","vtp_verticalThresholdsPercent":"10","vtp_verticalThresholdOn":true,"vtp_triggerStartOption":"WINDOW_LOAD","vtp_horizontalThresholdOn":false,"vtp_uniqueTriggerId":"193160782_72","vtp_enableTriggerStartOption":true,"tag_id":241},{"function":"__sdl","vtp_verticalThresholdUnits":"PERCENT","vtp_verticalThresholdsPercent":"25","vtp_verticalThresholdOn":true,"vtp_triggerStartOption":"WINDOW_LOAD","vtp_horizontalThresholdOn":false,"vtp_uniqueTriggerId":"193160782_74","vtp_enableTriggerStartOption":true,"tag_id":242},{"function":"__sdl","vtp_verticalThresholdUnits":"PERCENT","vtp_verticalThresholdsPercent":"50","vtp_verticalThresholdOn":true,"vtp_triggerStartOption":"WINDOW_LOAD","vtp_horizontalThresholdOn":false,"vtp_uniqueTriggerId":"193160782_76","vtp_enableTriggerStartOption":true,"tag_id":243},{"function":"__sdl","vtp_verticalThresholdUnits":"PERCENT","vtp_verticalThresholdsPercent":"75","vtp_verticalThresholdOn":true,"vtp_triggerStartOption":"WINDOW_LOAD","vtp_horizontalThresholdOn":false,"vtp_uniqueTriggerId":"193160782_78","vtp_enableTriggerStartOption":true,"tag_id":244},{"function":"__sdl","vtp_verticalThresholdUnits":"PERCENT","vtp_verticalThresholdsPercent":"90","vtp_verticalThresholdOn":true,"vtp_triggerStartOption":"WINDOW_LOAD","vtp_horizontalThresholdOn":false,"vtp_uniqueTriggerId":"193160782_80","vtp_enableTriggerStartOption":true,"tag_id":245},{"function":"__sdl","vtp_verticalThresholdUnits":"PERCENT","vtp_verticalThresholdsPercent":"100","vtp_verticalThresholdOn":true,"vtp_triggerStartOption":"WINDOW_LOAD","vtp_horizontalThresholdOn":false,"vtp_uniqueTriggerId":"193160782_82","vtp_enableTriggerStartOption":true,"tag_id":246},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_84","tag_id":247},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_86","tag_id":248},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_87","tag_id":249},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_90","tag_id":250},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_91","tag_id":251},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_93","tag_id":252},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_95","tag_id":253},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_97","tag_id":254},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_100","tag_id":255},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_102","tag_id":256},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_104","tag_id":257},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_106","tag_id":258},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_108","tag_id":259},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_110","tag_id":260},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_112","tag_id":261},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_114","tag_id":262},{"function":"__cl","tag_id":263},{"function":"__cl","tag_id":264},{"function":"__cl","tag_id":265},{"function":"__cl","tag_id":266},{"function":"__cl","tag_id":267},{"function":"__cl","tag_id":268},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_141","tag_id":269},{"function":"__cl","tag_id":270},{"function":"__cl","tag_id":271},{"function":"__cl","tag_id":272},{"function":"__cl","tag_id":273},{"function":"__cl","tag_id":274},{"function":"__cl","tag_id":275},{"function":"__cl","tag_id":276},{"function":"__cl","tag_id":277},{"function":"__lcl","vtp_waitForTags":false,"vtp_checkValidation":false,"vtp_uniqueTriggerId":"193160782_161","tag_id":278},{"function":"__cl","tag_id":279},{"function":"__evl","vtp_useOnScreenDuration":false,"vtp_useDomChangeListener":false,"vtp_firingFrequency":"ONCE","vtp_selectorType":"CSS","vtp_onScreenRatio":"50","vtp_uniqueTriggerId":"193160782_166","tag_id":280},{"function":"__cl","tag_id":281},{"function":"__cl","tag_id":282},{"function":"__cl","tag_id":283},{"function":"__cl","tag_id":284},{"function":"__cl","tag_id":285},{"function":"__cl","tag_id":286},{"function":"__cl","tag_id":287},{"function":"__cl","tag_id":288},{"function":"__cl","tag_id":289},{"function":"__cl","tag_id":290},{"function":"__cl","tag_id":291},{"function":"__cl","tag_id":292},{"function":"__cl","tag_id":293},{"function":"__cl","tag_id":294},{"function":"__html","metadata":["map"],"once_per_event":true,"vtp_html":"\u003Cscript type=\"text\/gtmscript\" id=\"gtm-dailymotion-tracking\"\u003E(function(){if(window.dailymotion){var h=dailymotion.getAllPlayers();h.forEach(function(a){a.then(function(c){DM_GTM_INIT(c)},function(c){console.error(\"GTM:Dailymotion\",c)})})}var g=[],k=function(a,c,f){if(!g.includes(c)){g.push(c);var b={event:\"dm_video\",video_provider:\"Dailymotion player (client-side)\",player_source_id:c,player_id:f};a.on(dailymotion.events.PLAYER_VIDEOCHANGE,function(d){b.event_name=\"player_videochange\";b.video_title=d.videoTitle;e(b)});a.on(dailymotion.events.PLAYER_START,function(d){b.event_name=\n\"player_start\";b.video_title=d.videoTitle;e(b)});a.on(dailymotion.events.VIDEO_START,function(d){b.event_name=\"video_start\";e(b)});a.on(dailymotion.events.VIDEO_PAUSE,function(d){b.event_name=\"video_pause\";e(b)});a.on(dailymotion.events.AD_START,function(d){b.event_name=\"ad_start\";e(b)});a.on(dailymotion.events.AD_PAUSE,function(d){b.event_name=\"ad_pause\";e(b)})}},e=function(a){dataLayer.push(JSON.parse(JSON.stringify(a)))};window.DM_GTM_INIT=function(a){a.getState().then(function(c){c=c.id;var f=\na.getSettings().id;k(a,c,f)},function(c){console.error(\"GTM:Dailymotion\",c)})}})();\u003C\/script\u003E","vtp_supportDocumentWrite":false,"vtp_enableIframeMode":false,"vtp_enableEditJsMacroBehavior":false,"tag_id":139}],
  "predicates":[{"function":"_eq","arg0":["macro",0],"arg1":"gtm.init"},{"function":"_cn","arg0":["macro",1],"arg1":"app-tarot-home__content--button"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.click"},{"function":"_eq","arg0":["macro",1],"arg1":"ad-course-tarot__link"},{"function":"_eq","arg0":["macro",1],"arg1":"nav-link"},{"function":"_eq","arg0":["macro",1],"arg1":"app-tarot-card-chosen__course-tarot"},{"function":"_cn","arg0":["macro",2],"arg1":"google_vignette"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.historyChange"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/servicos.terra.com.br\/para-voce\/terra-mail"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/central.terra.com.br"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/servicos.terra.com.br\/cursos-online\/?utm_source=portal-terra\u0026utm_medium=espaco-fixo\u0026utm_campaign=header\u0026utm_content=pg\u0026utm_term=cursos-online_pos-02\u0026cdConvenio=CVTR00001907"},{"function":"_eq","arg0":["macro",1],"arg1":"icon-solid icon-color-auto icon-16 icon-user-account"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/whatsapp.com\/channel\/0029VaDGPXE0rGiN2XvTl03k"},{"function":"_cn","arg0":["macro",1],"arg1":"nsm7Bb-HzV7m-LgbsSe-BPrWId"},{"function":"_cn","arg0":["macro",1],"arg1":"table-ad ad-get-size table-ad__fixed-size big-ad-970"},{"function":"_cn","arg0":["macro",1],"arg1":"header-full-ad--wrapper"},{"function":"_cn","arg0":["macro",1],"arg1":"header-full-ad"},{"function":"_cn","arg0":["macro",1],"arg1":"navbar__left--menu icon-solid icon-20 icon-color-auto icon-bars"},{"function":"_cn","arg0":["macro",1],"arg1":"navbar__left--logo icon"},{"function":"_eq","arg0":["macro",3],"arg1":"https:\/\/servicos.terra.com.br\/para-seu-negocio\/loja-virtual"},{"function":"_cn","arg0":["macro",1],"arg1":"t360-sva-table-cards__wrapper__list__item__oferta__cta1"},{"function":"_cn","arg0":["macro",1],"arg1":"t360-sva-table-cards__wrapper__list__item__oferta__cta2"},{"function":"_sw","arg0":["macro",3],"arg1":"https:\/\/www.valesaude.com.br\/"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/www.vivae.com.br\/"},{"function":"_eq","arg0":["macro",3],"arg1":"https:\/\/servicos.terra.com.br\/para-seu-negocio"},{"function":"_eq","arg0":["macro",1],"arg1":"t360-sva-table-cards__wrapper__list__item"},{"function":"_eq","arg0":["macro",1],"arg1":"t360-sva-table-cards__wrapper"},{"function":"_cn","arg0":["macro",1],"arg1":"page-nav container-carousel-esportes-previous previous esportes"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.scrollDepth"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_72($|,)))"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_74($|,)))"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_76($|,)))"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_78($|,)))"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_80($|,)))"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_82($|,)))"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/vida-e-estilo\/horoscopo\/"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.linkClick"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_87($|,)))"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/diversao\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_90($|,)))"},{"function":"_eq","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/noticias\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_91($|,)))"},{"function":"_eq","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/noticias\/educacao\/enem\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_86($|,)))"},{"function":"_eq","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/noticias\/educacao\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_110($|,)))"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/esportes\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_93($|,)))"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/apostas\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_95($|,)))"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/mobilidade\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_97($|,)))"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/nos\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_100($|,)))"},{"function":"_eq","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/vida-e-estilo\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_102($|,)))"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/vida-e-estilo\/degusta\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_104($|,)))"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/planeta\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_106($|,)))"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/economia\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_108($|,)))"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/visao-do-corre\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_112($|,)))"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/internet.vivo.com.br\/ofertas\/fibra-e-pos\/"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/www.terra.com.br\/vida-e-estilo\/horoscopo\/terra-astral\/login\/"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/servicos.terra.com.br\/para-voce\/terra-astral\/"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/central.terra.com.br\/login?next=https:\/\/www.terra.com.br\/vida-e-estilo\/horoscopo\/terra-astral\/"},{"function":"_cn","arg0":["macro",1],"arg1":"widget-checkout__login-button"},{"function":"_eq","arg0":["macro",0],"arg1":"dm_video"},{"function":"_cn","arg0":["macro",1],"arg1":"related-videos bg-color-noticias"},{"function":"_sw","arg0":["macro",10],"arg1":"https:\/\/www.terra.com.br\/noticias\/"},{"function":"_re","arg0":["macro",4],"arg1":"(^$|((^|,)193160782_141($|,)))"},{"function":"_eq","arg0":["macro",1],"arg1":"t360-sharebar__wrapper"},{"function":"_eq","arg0":["macro",1],"arg1":"t360-sharebar active"},{"function":"_eq","arg0":["macro",1],"arg1":"icon icon-24 icon-facebook-color"},{"function":"_eq","arg0":["macro",1],"arg1":"icon icon-24 icon-twitter-color"},{"function":"_eq","arg0":["macro",1],"arg1":"icon icon-24 icon-pinterest-color"},{"function":"_eq","arg0":["macro",1],"arg1":"article__header__info__comments"},{"function":"_eq","arg0":["macro",1],"arg1":"icon icon-24 icon-whatsapp-color"},{"function":"_eq","arg0":["macro",1],"arg1":"push-notification--button push-notification--accept-button"},{"function":"_cn","arg0":["macro",1],"arg1":"ssales__btn ssales__widget__summary__btn"},{"function":"_sw","arg0":["macro",10],"arg1":"https:\/\/www.terra.com.br\/vida-e-estilo\/horoscopo\/signos\/"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.load"},{"function":"_cn","arg0":["macro",1],"arg1":"navbar__right--mail icon-solid icon-color-auto icon-16 icon-envelope"},{"function":"_sw","arg0":["macro",3],"arg1":"https:\/\/servicos.terra.com.br\/para-voce\/cursos-online"},{"function":"_cn","arg0":["macro",3],"arg1":"https:\/\/servicos.terra.com.br\/para-voce\/amazon-prime"},{"function":"_eq","arg0":["macro",1],"arg1":"btn_read_more"},{"function":"_sw","arg0":["macro",1],"arg1":"readmore-content__button"},{"function":"_lt","arg0":["macro",11],"arg1":"0"},{"function":"_re","arg0":["macro",0],"arg1":".*"},{"function":"_cn","arg0":["macro",1],"arg1":"qJTHM"},{"function":"_eq","arg0":["macro",0],"arg1":"gtm.js"}],
  "rules":[[["if",0],["add",0,61]],[["if",1,2],["add",1]],[["if",2,3],["add",2]],[["if",2,4],["add",3]],[["if",2,5],["add",4]],[["if",6,7],["add",5]],[["if",2,8],["add",6]],[["if",2,9],["add",7]],[["if",2,10],["add",8]],[["if",2,11],["add",9]],[["if",2,12],["add",10]],[["if",2,13],["add",11]],[["if",2,14],["add",12]],[["if",2,15],["add",12]],[["if",2,16],["add",12]],[["if",2,17],["add",13]],[["if",2,18],["add",14]],[["if",2,19],["add",15],["block",18]],[["if",2,22],["add",16]],[["if",2,23],["add",17]],[["if",2,24],["add",18]],[["if",2,27],["add",19]],[["if",28,29],["add",20]],[["if",28,30],["add",21]],[["if",28,31],["add",22]],[["if",28,32],["add",23]],[["if",28,33],["add",24]],[["if",28,34],["add",25]],[["if",35,36,37],["add",26],["block",33,34]],[["if",36,38,39],["add",27]],[["if",36,40,41],["add",28],["block",37]],[["if",36,46,47],["add",29]],[["if",36,48,49],["add",30]],[["if",36,50,51],["add",31]],[["if",36,52,53],["add",32]],[["if",36,54,55],["add",33],["block",34]],[["if",36,56,57],["add",34],["block",33]],[["if",36,58,59],["add",35]],[["if",36,60,61],["add",36]],[["if",36,44,45],["add",37],["block",28]],[["if",36,62,63],["add",38]],[["if",2,64],["add",39]],[["if",2,65],["add",40]],[["if",2,66],["add",41]],[["if",2,67,68],["add",42]],[["if",69],["add",43]],[["if",2,70],["add",44]],[["if",36,71,72],["add",44]],[["if",2,73],["add",45]],[["if",2,74],["add",45]],[["if",2,75],["add",46]],[["if",2,76],["add",47]],[["if",2,77],["add",48]],[["if",2,78],["add",49]],[["if",2,79],["add",50]],[["if",2,80],["add",51]],[["if",2,81],["add",52]],[["if",82,83],["add",52]],[["if",2,20],["add",53],["block",15,18]],[["if",2,21],["add",54],["block",15,18]],[["if",2,84],["add",55]],[["if",2,85],["add",56]],[["if",2,86],["add",57]],[["if",2,87],["add",58]],[["if",2,88],["add",58]],[["if",83],["add",59,91,92,93,94,95,96]],[["if",2,91],["add",60]],[["if",92],["add",62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145]],[["if",2,25],["block",18]],[["if",2,26],["block",18]],[["if",36,42,43],["block",28,37]],[["if",89,90],["block",59]]]
},
"runtime":[ [50,"__aev",[46,"a"],[50,"aC",[46,"aJ"],[22,[2,[15,"v"],"hasOwnProperty",[7,[15,"aJ"]]],[46,[53,[36,[16,[15,"v"],[15,"aJ"]]]]]],[52,"aK",[16,[15,"z"],"element"]],[22,[28,[15,"aK"]],[46,[36,[44]]]],[52,"aL",["g",[15,"aK"]]],["aD",[15,"aJ"],[15,"aL"]],[36,[15,"aL"]]],[50,"aD",[46,"aJ","aK"],[43,[15,"v"],[15,"aJ"],[15,"aK"]],[2,[15,"w"],"push",[7,[15,"aJ"]]],[22,[18,[17,[15,"w"],"length"],[15,"s"]],[46,[53,[52,"aL",[2,[15,"w"],"shift",[7]]],[2,[15,"b"],"delete",[7,[15,"v"],[15,"aL"]]]]]]],[50,"aE",[46,"aJ","aK"],[52,"aL",["n",[30,[30,[16,[15,"z"],"elementUrl"],[15,"aJ"]],""]]],[52,"aM",["n",[30,[17,[15,"aK"],"component"],"URL"]]],[38,[15,"aM"],[46,"URL","IS_OUTBOUND","PROTOCOL","HOST","PORT","PATH","EXTENSION","QUERY","FRAGMENT"],[46,[5,[46,[36,[15,"aL"]]]],[5,[46,[36,["aG",[15,"aL"],[17,[15,"aK"],"affiliatedDomains"]]]]],[5,[46,[36,[2,[15,"l"],"B",[7,[15,"aL"]]]]]],[5,[46,[36,[2,[15,"l"],"C",[7,[15,"aL"],[17,[15,"aK"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"l"],"D",[7,[15,"aL"]]]]]],[5,[46,[36,[2,[15,"l"],"E",[7,[15,"aL"],[17,[15,"aK"],"defaultPages"]]]]]],[5,[46,[36,[2,[15,"l"],"F",[7,[15,"aL"]]]]]],[5,[46,[22,[17,[15,"aK"],"queryKey"],[46,[53,[36,[2,[15,"l"],"H",[7,[15,"aL"],[17,[15,"aK"],"queryKey"]]]]]],[46,[53,[36,[2,[17,["m",[15,"aL"]],"search"],"replace",[7,"?",""]]]]]]]],[5,[46,[36,[2,[15,"l"],"G",[7,[15,"aL"]]]]]],[9,[46,[36,[17,["m",[15,"aL"]],"href"]]]]]]],[50,"aF",[46,"aJ","aK"],[52,"aL",[8,"ATTRIBUTE","elementAttribute","CLASSES","elementClasses","ELEMENT","element","ID","elementId","HISTORY_CHANGE_SOURCE","historyChangeSource","HISTORY_NEW_STATE","newHistoryState","HISTORY_NEW_URL_FRAGMENT","newUrlFragment","HISTORY_OLD_STATE","oldHistoryState","HISTORY_OLD_URL_FRAGMENT","oldUrlFragment","TARGET","elementTarget"]],[52,"aM",[16,[15,"z"],[16,[15,"aL"],[15,"aJ"]]]],[36,[39,[21,[15,"aM"],[44]],[15,"aM"],[15,"aK"]]]],[50,"aG",[46,"aJ","aK"],[22,[28,[15,"aJ"]],[46,[53,[36,false]]]],[52,"aL",["aI",[15,"aJ"]]],[22,["aH",[15,"aL"],["k"]],[46,[53,[36,false]]]],[22,[28,["q",[15,"aK"]]],[46,[53,[3,"aK",[2,[2,["n",[30,[15,"aK"],""]],"replace",[7,["c","\\s+","g"],""]],"split",[7,","]]]]]],[65,"aM",[15,"aK"],[46,[53,[22,[20,["j",[15,"aM"]],"object"],[46,[53,[22,[16,[15,"aM"],"is_regex"],[46,[53,[52,"aN",["c",[16,[15,"aM"],"domain"]]],[22,[20,[15,"aN"],[45]],[46,[6]]],[22,["p",[15,"aN"],[15,"aL"]],[46,[53,[36,false]]]]]],[46,[53,[22,["aH",[15,"aL"],[16,[15,"aM"],"domain"]],[46,[53,[36,false]]]]]]]]],[46,[22,[20,["j",[15,"aM"]],"RegExp"],[46,[53,[22,["p",[15,"aM"],[15,"aL"]],[46,[53,[36,false]]]]]],[46,[53,[22,["aH",[15,"aL"],[15,"aM"]],[46,[53,[36,false]]]]]]]]]]]],[36,true]],[50,"aH",[46,"aJ","aK"],[22,[28,[15,"aK"]],[46,[36,false]]],[22,[19,[2,[15,"aJ"],"indexOf",[7,[15,"aK"]]],0],[46,[36,true]]],[3,"aK",["aI",[15,"aK"]]],[22,[28,[15,"aK"]],[46,[36,false]]],[3,"aK",[2,[15,"aK"],"toLowerCase",[7]]],[41,"aL"],[3,"aL",[37,[17,[15,"aJ"],"length"],[17,[15,"aK"],"length"]]],[22,[1,[18,[15,"aL"],0],[29,[2,[15,"aK"],"charAt",[7,0]],"."]],[46,[53,[34,[3,"aL",[37,[15,"aL"],1]]],[3,"aK",[0,".",[15,"aK"]]]]]],[36,[1,[19,[15,"aL"],0],[12,[2,[15,"aJ"],"indexOf",[7,[15,"aK"],[15,"aL"]]],[15,"aL"]]]]],[50,"aI",[46,"aJ"],[22,[28,["p",[15,"r"],[15,"aJ"]]],[46,[53,[3,"aJ",[0,"http://",[15,"aJ"]]]]]],[36,[2,[15,"l"],"C",[7,[15,"aJ"],true]]]],[52,"b",["require","Object"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","internal.getElementAttribute"]],[52,"e",["require","internal.getElementValue"]],[52,"f",["require","internal.getEventData"]],[52,"g",["require","internal.getElementInnerText"]],[52,"h",["require","internal.getElementProperty"]],[52,"i",["require","internal.copyFromDataLayerCache"]],[52,"j",["require","getType"]],[52,"k",["require","getUrl"]],[52,"l",[15,"__module_legacyUrls"]],[52,"m",["require","internal.legacyParseUrl"]],[52,"n",["require","makeString"]],[52,"o",["require","templateStorage"]],[52,"p",["require","internal.testRegex"]],[52,"q",[51,"",[7,"aJ"],[36,[20,["j",[15,"aJ"]],"array"]]]],[52,"r",["c","^https?:\\/\\/","i"]],[52,"s",35],[52,"t","eq"],[52,"u","evc"],[52,"v",[30,[2,[15,"o"],"getItem",[7,[15,"u"]]],[8]]],[2,[15,"o"],"setItem",[7,[15,"u"],[15,"v"]]],[52,"w",[30,[2,[15,"o"],"getItem",[7,[15,"t"]]],[7]]],[2,[15,"o"],"setItem",[7,[15,"t"],[15,"w"]]],[52,"x",[17,[15,"a"],"defaultValue"]],[52,"y",[17,[15,"a"],"varType"]],[52,"z",["i","gtm"]],[38,[15,"y"],[46,"TAG_NAME","TEXT","URL","ATTRIBUTE"],[46,[5,[46,[52,"aA",[16,[15,"z"],"element"]],[52,"aB",[1,[15,"aA"],["h",[15,"aA"],"tagName"]]],[36,[30,[15,"aB"],[15,"x"]]]]],[5,[46,[36,[30,["aC",["f","gtm\\.uniqueEventId"]],[15,"x"]]]]],[5,[46,[36,["aE",[15,"x"],[15,"a"]]]]],[5,[46,[22,[20,[17,[15,"a"],"attribute"],[44]],[46,[53,[36,["aF",[15,"y"],[15,"x"]]]]],[46,[53,[52,"aJ",[16,[15,"z"],"element"]],[52,"aK",[1,[15,"aJ"],[39,[20,[17,[15,"a"],"attribute"],"value"],["e",[15,"aJ"]],["d",[15,"aJ"],[17,[15,"a"],"attribute"]]]]],[36,[30,[30,[15,"aK"],[15,"x"]],""]]]]]]],[9,[46,[36,["aF",[15,"y"],[15,"x"]]]]]]]]
 ,[50,"__c",[46,"a"],[36,[17,[15,"a"],"value"]]]
 ,[50,"__cid",[46,"a"],[36,[17,[13,[41,"$0"],[3,"$0",["require","getContainerVersion"]],["$0"]],"containerId"]]]
 ,[50,"__cl",[46,"a"],[52,"b",["require","internal.enableAutoEventOnClick"]],["b"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__ctv",[46,"a"],[36,[17,[13,[41,"$0"],[3,"$0",["require","getContainerVersion"]],["$0"]],"version"]]]
 ,[50,"__dbg",[46,"a"],[36,[17,[13,[41,"$0"],[3,"$0",["require","getContainerVersion"]],["$0"]],"debugMode"]]]
 ,[50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__evl",[46,"a"],[52,"b",["require","internal.enableAutoEventOnElementVisibility"]],[52,"c",["require","makeNumber"]],[52,"d",[8,"selectorType",[17,[15,"a"],"selectorType"],"id",[17,[15,"a"],"elementId"],"selector",[17,[15,"a"],"elementSelector"],"useDomChangeListener",[28,[28,[17,[15,"a"],"useDomChangeListener"]]],"onScreenRatio",["c",[17,[15,"a"],"onScreenRatio"]],"firingFrequency",[17,[15,"a"],"firingFrequency"]]],[22,[17,[15,"a"],"useOnScreenDuration"],[46,[53,[43,[15,"d"],"onScreenDuration",["c",[17,[15,"a"],"onScreenDuration"]]]]]],["b",[15,"d"],[17,[15,"a"],"uniqueTriggerId"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__f",[46,"a"],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","getReferrerUrl"]],[52,"d",["require","makeString"]],[52,"e",["require","parseUrl"]],[52,"f",[15,"__module_legacyUrls"]],[52,"g",[30,["b","gtm.referrer",1],["c"]]],[22,[28,[15,"g"]],[46,[36,["d",[15,"g"]]]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"f"],"B",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"C",[7,[15,"g"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"f"],"D",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"E",[7,[15,"g"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[22,[17,[15,"a"],"queryKey"],[46,[53,[36,[2,[15,"f"],"H",[7,[15,"g"],[17,[15,"a"],"queryKey"]]]]]]],[52,"h",["e",[15,"g"]]],[36,[2,[17,[15,"h"],"search"],"replace",[7,"?",""]]]]],[5,[46,[36,[2,[15,"f"],"G",[7,[15,"g"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"f"],"A",[7,["d",[15,"g"]]]]]]]]]]
 ,[50,"__googtag",[46,"a"],[50,"m",[46,"v","w"],[66,"x",[2,[15,"b"],"keys",[7,[15,"w"]]],[46,[53,[43,[15,"v"],[15,"x"],[16,[15,"w"],[15,"x"]]]]]]],[50,"n",[46],[36,[7,[17,[15,"f"],"HS"],[17,[15,"f"],"IJ"]]]],[50,"o",[46,"v"],[52,"w",["n"]],[65,"x",[15,"w"],[46,[53,[52,"y",[16,[15,"v"],[15,"x"]]],[22,[15,"y"],[46,[36,[15,"y"]]]]]]],[36,[44]]],[52,"b",["require","Object"]],[52,"c",["require","createArgumentsQueue"]],[52,"d",[15,"__module_gtag"]],[52,"e",["require","internal.gtagConfig"]],[52,"f",[15,"__module_gtagSchema"]],[52,"g",["require","getType"]],[52,"h",["require","internal.loadGoogleTag"]],[52,"i",["require","logToConsole"]],[52,"j",["require","makeNumber"]],[52,"k",["require","makeString"]],[52,"l",["require","makeTableMap"]],[52,"p",[30,[17,[15,"a"],"tagId"],""]],[22,[30,[21,["g",[15,"p"]],"string"],[24,[2,[15,"p"],"indexOf",[7,"-"]],0]],[46,[53,["i",[0,"Invalid Measurement ID for the GA4 Configuration tag: ",[15,"p"]]],[2,[15,"a"],"gtmOnFailure",[7]],[36]]]],[52,"q",[30,[17,[15,"a"],"configSettingsVariable"],[8]]],[52,"r",[30,["l",[30,[17,[15,"a"],"configSettingsTable"],[7]],"parameter","parameterValue"],[8]]],["m",[15,"q"],[15,"r"]],[52,"s",[30,[17,[15,"a"],"eventSettingsVariable"],[8]]],[52,"t",[30,["l",[30,[17,[15,"a"],"eventSettingsTable"],[7]],"parameter","parameterValue"],[8]]],["m",[15,"s"],[15,"t"]],[52,"u",[15,"q"]],["m",[15,"u"],[15,"s"]],[22,[30,[2,[15,"u"],"hasOwnProperty",[7,[17,[15,"f"],"JF"]]],[17,[15,"a"],"userProperties"]],[46,[53,[52,"v",[30,[16,[15,"u"],[17,[15,"f"],"JF"]],[8]]],["m",[15,"v"],[30,["l",[30,[17,[15,"a"],"userProperties"],[7]],"name","value"],[8]]],[43,[15,"u"],[17,[15,"f"],"JF"],[15,"v"]]]]],[2,[15,"d"],"E",[7,[15,"u"],[17,[15,"d"],"B"],[51,"",[7,"v"],[36,[39,[20,"false",[2,["k",[15,"v"]],"toLowerCase",[7]]],false,[28,[28,[15,"v"]]]]]]]],[2,[15,"d"],"E",[7,[15,"u"],[17,[15,"d"],"D"],[51,"",[7,"v"],[36,["j",[15,"v"]]]]]],["h",[15,"p"],[8,"firstPartyUrl",["o",[15,"u"]]]],["e",[15,"p"],[15,"u"],[8,"noTargetGroup",true]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__gtes",[46,"a"],[50,"f",[46,"h","i"],[66,"j",[2,[15,"b"],"keys",[7,[15,"i"]]],[46,[53,[43,[15,"h"],[15,"j"],[16,[15,"i"],[15,"j"]]]]]]],[52,"b",["require","Object"]],[52,"c",["require","getType"]],[52,"d",[15,"__module_gtagSchema"]],[52,"e",["require","makeTableMap"]],[52,"g",[30,["e",[30,[17,[15,"a"],"eventSettingsTable"],[7]],"parameter","parameterValue"],[8]]],[22,[17,[15,"a"],"userProperties"],[46,[53,[41,"h"],[3,"h",[30,[16,[15,"g"],[17,[15,"d"],"JF"]],[8]]],[22,[29,["c",[15,"h"]],"object"],[46,[53,[3,"h",[8]]]]],["f",[15,"h"],[30,["e",[30,[17,[15,"a"],"userProperties"],[7]],"name","value"],[8]]],[43,[15,"g"],[17,[15,"d"],"JF"],[15,"h"]]]]],[36,[15,"g"]]]
 ,[50,"__hid",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getHtmlId"]],["$0"]]]]
 ,[50,"__hl",[46,"a"],[52,"b",["require","internal.enableAutoEventOnHistoryChange"]],["b"],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__html",[46,"a"],[52,"b",["require","internal.injectHtml"]],["b",[17,[15,"a"],"html"],[17,[15,"a"],"gtmOnSuccess"],[17,[15,"a"],"gtmOnFailure"],[17,[15,"a"],"useIframe"],[17,[15,"a"],"supportDocumentWrite"]]]
 ,[50,"__jsm",[46,"a"],[52,"b",["require","internal.executeJavascriptString"]],[22,[20,[17,[15,"a"],"javascript"],[44]],[46,[36]]],[36,["b",[17,[15,"a"],"javascript"]]]]
 ,[50,"__lcl",[46,"a"],[52,"b",["require","makeInteger"]],[52,"c",["require","makeString"]],[52,"d",["require","internal.enableAutoEventOnLinkClick"]],[52,"e",[15,"__module_featureFlags"]],[52,"f",["require","queryPermission"]],[52,"g",["require","internal.isFeatureEnabled"]],[52,"h",["require","internal.isOgt"]],[52,"i",[8]],[22,[17,[15,"a"],"waitForTags"],[46,[53,[43,[15,"i"],"waitForTags",true],[43,[15,"i"],"waitForTagsTimeout",["b",[17,[15,"a"],"waitForTagsTimeout"]]]]]],[22,[17,[15,"a"],"checkValidation"],[46,[53,[43,[15,"i"],"checkValidation",true]]]],[52,"j",[30,[17,[15,"a"],"uniqueTriggerId"],"0"]],[22,[1,[1,["g",[17,[15,"e"],"EA"]],["h"]],[28,["f","detect_link_click_events",[15,"i"]]]],[46,[53,[43,[15,"i"],"waitForTags",false]]]],["d",[15,"i"],[15,"j"]],[2,[15,"a"],"gtmOnSuccess",[7]]]
 ,[50,"__r",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","generateRandom"]],["$0",[30,[17,[15,"a"],"min"],0],[30,[17,[15,"a"],"max"],2.147483647E9]]]]]
 ,[50,"__sdl",[46,"a"],[50,"f",[46,"h"],[2,[15,"h"],"gtmOnSuccess",[7]],[52,"i",[17,[15,"h"],"horizontalThresholdUnits"]],[52,"j",[17,[15,"h"],"verticalThresholdUnits"]],[52,"k",[8]],[43,[15,"k"],"horizontalThresholdUnits",[15,"i"]],[38,[15,"i"],[46,"PIXELS","PERCENT"],[46,[5,[46,[43,[15,"k"],"horizontalThresholds",["g",[17,[15,"h"],"horizontalThresholdsPixels"]]],[4]]],[5,[46,[43,[15,"k"],"horizontalThresholds",["g",[17,[15,"h"],"horizontalThresholdsPercent"]]],[4]]],[9,[46,[4]]]]],[43,[15,"k"],"verticalThresholdUnits",[15,"j"]],[38,[15,"j"],[46,"PIXELS","PERCENT"],[46,[5,[46,[43,[15,"k"],"verticalThresholds",["g",[17,[15,"h"],"verticalThresholdsPixels"]]],[4]]],[5,[46,[43,[15,"k"],"verticalThresholds",["g",[17,[15,"h"],"verticalThresholdsPercent"]]],[4]]],[9,[46,[4]]]]],["c",[15,"k"],[17,[15,"h"],"uniqueTriggerId"]]],[50,"g",[46,"h"],[52,"i",[7]],[52,"j",[2,["e",[15,"h"]],"split",[7,","]]],[53,[41,"k"],[3,"k",0],[63,[7,"k"],[23,[15,"k"],[17,[15,"j"],"length"]],[33,[15,"k"],[3,"k",[0,[15,"k"],1]]],[46,[53,[52,"l",["d",[16,[15,"j"],[15,"k"]]]],[22,[29,[15,"l"],[15,"l"]],[46,[53,[36,[7]]]],[46,[22,[29,[17,[2,[16,[15,"j"],[15,"k"]],"trim",[7]],"length"],0],[46,[53,[2,[15,"i"],"push",[7,[15,"l"]]]]]]]]]]]],[36,[15,"i"]]],[52,"b",["require","callOnWindowLoad"]],[52,"c",["require","internal.enableAutoEventOnScroll"]],[52,"d",["require","makeNumber"]],[52,"e",["require","makeString"]],[22,[17,[15,"a"],"triggerStartOption"],[46,[53,["f",[15,"a"]]]],[46,[53,["b",[51,"",[7],[36,["f",[15,"a"]]]]]]]]]
 ,[50,"__u",[46,"a"],[50,"k",[46,"l","m"],[52,"n",[17,[15,"m"],"multiQueryKeys"]],[52,"o",[30,[17,[15,"m"],"queryKey"],""]],[52,"p",[17,[15,"m"],"ignoreEmptyQueryParam"]],[22,[20,[15,"o"],""],[46,[53,[52,"r",[2,[17,["i",[15,"l"]],"search"],"replace",[7,"?",""]]],[36,[39,[1,[28,[15,"r"]],[15,"p"]],[44],[15,"r"]]]]]],[41,"q"],[22,[15,"n"],[46,[53,[22,[20,["e",[15,"o"]],"array"],[46,[53,[3,"q",[15,"o"]]]],[46,[53,[52,"r",["c","\\s+","g"]],[3,"q",[2,[2,["f",[15,"o"]],"replace",[7,[15,"r"],""]],"split",[7,","]]]]]]]],[46,[53,[3,"q",[7,["f",[15,"o"]]]]]]],[65,"r",[15,"q"],[46,[53,[52,"s",[2,[15,"h"],"H",[7,[15,"l"],[15,"r"]]]],[22,[29,[15,"s"],[44]],[46,[53,[22,[1,[15,"p"],[20,[15,"s"],""]],[46,[53,[6]]]],[36,[15,"s"]]]]]]]],[36,[44]]],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","getUrl"]],[52,"e",["require","getType"]],[52,"f",["require","makeString"]],[52,"g",["require","parseUrl"]],[52,"h",[15,"__module_legacyUrls"]],[52,"i",["require","internal.legacyParseUrl"]],[41,"j"],[22,[17,[15,"a"],"customUrlSource"],[46,[53,[3,"j",[17,[15,"a"],"customUrlSource"]]]],[46,[53,[3,"j",["b","gtm.url",1]]]]],[3,"j",[30,[15,"j"],["d"]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","EXTENSION","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"h"],"B",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"C",[7,[15,"j"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"h"],"D",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"E",[7,[15,"j"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[36,[2,[15,"h"],"F",[7,[15,"j"]]]]]],[5,[46,[36,["k",[15,"j"],[15,"a"]]]]],[5,[46,[36,[2,[15,"h"],"G",[7,[15,"j"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"h"],"A",[7,["f",[15,"j"]]]]]]]]]]
 ,[50,"__v",[46,"a"],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","getType"]],[52,"e",[17,[15,"a"],"name"]],[22,[30,[28,[15,"e"]],[21,["d",[15,"e"]],"string"]],[46,[36,false]]],[52,"f",[2,[15,"e"],"replace",[7,["c","\\\\.","g"],"."]]],[52,"g",["b",[15,"f"],[30,[17,[15,"a"],"dataLayerVersion"],1]]],[36,[39,[21,[15,"g"],[44]],[15,"g"],[17,[15,"a"],"defaultValue"]]]]
 ,[52,"__module_gtagSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","ad_personalization"],[52,"c","ad_storage"],[52,"d","ad_user_data"],[52,"e","consent_updated"],[52,"f","app_remove"],[52,"g","app_store_refund"],[52,"h","app_store_subscription_cancel"],[52,"i","app_store_subscription_convert"],[52,"j","app_store_subscription_renew"],[52,"k","purchase"],[52,"l","first_open"],[52,"m","first_visit"],[52,"n","gtag.config"],[52,"o","in_app_purchase"],[52,"p","page_view"],[52,"q","session_start"],[52,"r","user_engagement"],[52,"s","ads_data_redaction"],[52,"t","allow_ad_personalization_signals"],[52,"u","allow_custom_scripts"],[52,"v","allow_direct_google_requests"],[52,"w","allow_enhanced_conversions"],[52,"x","allow_google_signals"],[52,"y","auid"],[52,"z","aw_remarketing_only"],[52,"aA","discount"],[52,"aB","aw_feed_country"],[52,"aC","aw_feed_language"],[52,"aD","items"],[52,"aE","aw_merchant_id"],[52,"aF","aw_basket_type"],[52,"aG","client_id"],[52,"aH","conversion_cookie_prefix"],[52,"aI","conversion_id"],[52,"aJ","conversion_linker"],[52,"aK","conversion_api"],[52,"aL","cookie_deprecation"],[52,"aM","cookie_expires"],[52,"aN","cookie_prefix"],[52,"aO","cookie_update"],[52,"aP","country"],[52,"aQ","currency"],[52,"aR","customer_buyer_stage"],[52,"aS","customer_lifetime_value"],[52,"aT","customer_loyalty"],[52,"aU","customer_ltv_bucket"],[52,"aV","debug_mode"],[52,"aW","developer_id"],[52,"aX","shipping"],[52,"aY","engagement_time_msec"],[52,"aZ","estimated_delivery_date"],[52,"bA","event_developer_id_string"],[52,"bB","event"],[52,"bC","event_timeout"],[52,"bD","first_party_collection"],[52,"bE","match_id"],[52,"bF","gdpr_applies"],[52,"bG","google_analysis_params"],[52,"bH","_google_ng"],[52,"bI","gpp_sid"],[52,"bJ","gpp_string"],[52,"bK","gsa_experiment_id"],[52,"bL","gtag_event_feature_usage"],[52,"bM","iframe_state"],[52,"bN","ignore_referrer"],[52,"bO","is_passthrough"],[52,"bP","_lps"],[52,"bQ","language"],[52,"bR","merchant_feed_label"],[52,"bS","merchant_feed_language"],[52,"bT","merchant_id"],[52,"bU","new_customer"],[52,"bV","page_hostname"],[52,"bW","page_path"],[52,"bX","page_referrer"],[52,"bY","page_title"],[52,"bZ","_platinum_request_status"],[52,"cA","quantity"],[52,"cB","restricted_data_processing"],[52,"cC","screen_resolution"],[52,"cD","send_page_view"],[52,"cE","server_container_url"],[52,"cF","session_duration"],[52,"cG","session_engaged_time"],[52,"cH","session_id"],[52,"cI","_shared_user_id"],[52,"cJ","delivery_postal_code"],[52,"cK","topmost_url"],[52,"cL","transaction_id"],[52,"cM","transport_url"],[52,"cN","update"],[52,"cO","_user_agent_architecture"],[52,"cP","_user_agent_bitness"],[52,"cQ","_user_agent_full_version_list"],[52,"cR","_user_agent_mobile"],[52,"cS","_user_agent_model"],[52,"cT","_user_agent_platform"],[52,"cU","_user_agent_platform_version"],[52,"cV","_user_agent_wow64"],[52,"cW","user_data"],[52,"cX","user_data_auto_latency"],[52,"cY","user_data_auto_meta"],[52,"cZ","user_data_auto_multi"],[52,"dA","user_data_auto_selectors"],[52,"dB","user_data_auto_status"],[52,"dC","user_data_mode"],[52,"dD","user_id"],[52,"dE","user_properties"],[52,"dF","us_privacy_string"],[52,"dG","value"],[52,"dH","_fpm_parameters"],[52,"dI","_host_name"],[52,"dJ","_in_page_command"],[52,"dK","non_personalized_ads"],[52,"dL","conversion_label"],[52,"dM","page_location"],[52,"dN","global_developer_id_string"],[52,"dO","tc_privacy_string"],[36,[8,"A",[15,"b"],"B",[15,"c"],"C",[15,"d"],"F",[15,"e"],"H",[15,"f"],"I",[15,"g"],"J",[15,"h"],"K",[15,"i"],"L",[15,"j"],"X",[15,"k"],"AC",[15,"l"],"AD",[15,"m"],"AE",[15,"n"],"AG",[15,"o"],"AH",[15,"p"],"AJ",[15,"q"],"AN",[15,"r"],"AX",[15,"s"],"BE",[15,"t"],"BF",[15,"u"],"BG",[15,"v"],"BI",[15,"w"],"BJ",[15,"x"],"BP",[15,"y"],"BS",[15,"z"],"BT",[15,"aA"],"BU",[15,"aB"],"BV",[15,"aC"],"BW",[15,"aD"],"BX",[15,"aE"],"BY",[15,"aF"],"CG",[15,"aG"],"CL",[15,"aH"],"CM",[15,"aI"],"JT",[15,"dL"],"CN",[15,"aJ"],"CP",[15,"aK"],"CQ",[15,"aL"],"CS",[15,"aM"],"CW",[15,"aN"],"CX",[15,"aO"],"CY",[15,"aP"],"CZ",[15,"aQ"],"DA",[15,"aR"],"DB",[15,"aS"],"DC",[15,"aT"],"DD",[15,"aU"],"DH",[15,"aV"],"DI",[15,"aW"],"DU",[15,"aX"],"DW",[15,"aY"],"EA",[15,"aZ"],"EE",[15,"bA"],"EG",[15,"bB"],"EI",[15,"bC"],"EN",[15,"bD"],"EW",[15,"bE"],"FG",[15,"bF"],"JV",[15,"dN"],"FK",[15,"bG"],"FL",[15,"bH"],"FO",[15,"bI"],"FP",[15,"bJ"],"FR",[15,"bK"],"FS",[15,"bL"],"FU",[15,"bM"],"FV",[15,"bN"],"GA",[15,"bO"],"GB",[15,"bP"],"GC",[15,"bQ"],"GJ",[15,"bR"],"GK",[15,"bS"],"GL",[15,"bT"],"GP",[15,"bU"],"GS",[15,"bV"],"JU",[15,"dM"],"GT",[15,"bW"],"GU",[15,"bX"],"GV",[15,"bY"],"HD",[15,"bZ"],"HF",[15,"cA"],"HJ",[15,"cB"],"HN",[15,"cC"],"HQ",[15,"cD"],"HS",[15,"cE"],"HU",[15,"cF"],"HW",[15,"cG"],"HX",[15,"cH"],"HZ",[15,"cI"],"IA",[15,"cJ"],"JW",[15,"dO"],"IF",[15,"cK"],"II",[15,"cL"],"IJ",[15,"cM"],"IL",[15,"cN"],"IO",[15,"cO"],"IP",[15,"cP"],"IQ",[15,"cQ"],"IR",[15,"cR"],"IS",[15,"cS"],"IT",[15,"cT"],"IU",[15,"cU"],"IV",[15,"cV"],"IW",[15,"cW"],"IX",[15,"cX"],"IY",[15,"cY"],"IZ",[15,"cZ"],"JA",[15,"dA"],"JB",[15,"dB"],"JC",[15,"dC"],"JE",[15,"dD"],"JF",[15,"dE"],"JH",[15,"dF"],"JI",[15,"dG"],"JK",[15,"dH"],"JL",[15,"dI"],"JM",[15,"dJ"],"JQ",[15,"dK"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_metadataSchema",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b","accept_by_default"],[52,"c","add_tag_timing"],[52,"d","consent_state"],[52,"e","consent_updated"],[52,"f","conversion_linker_enabled"],[52,"g","cookie_options"],[52,"h","em_event"],[52,"i","event_start_timestamp_ms"],[52,"j","event_usage"],[52,"k","ga4_collection_subdomain"],[52,"l","handle_internally"],[52,"m","hit_type"],[52,"n","hit_type_override"],[52,"o","is_conversion"],[52,"p","is_external_event"],[52,"q","is_first_visit"],[52,"r","is_first_visit_conversion"],[52,"s","is_fpm_encryption"],[52,"t","is_fpm_split"],[52,"u","is_gcp_conversion"],[52,"v","is_google_signals_allowed"],[52,"w","is_server_side_destination"],[52,"x","is_session_start"],[52,"y","is_session_start_conversion"],[52,"z","is_sgtm_ga_ads_conversion_study_control_group"],[52,"aA","is_sgtm_prehit"],[52,"aB","is_split_conversion"],[52,"aC","is_syn"],[52,"aD","prehit_for_retry"],[52,"aE","redact_ads_data"],[52,"aF","redact_click_ids"],[52,"aG","send_ccm_parallel_ping"],[52,"aH","send_user_data_hit"],[52,"aI","speculative"],[52,"aJ","syn_or_mod"],[52,"aK","transient_ecsid"],[52,"aL","transmission_type"],[52,"aM","user_data"],[52,"aN","user_data_from_automatic"],[52,"aO","user_data_from_automatic_getter"],[52,"aP","user_data_from_code"],[52,"aQ","user_data_from_manual"],[36,[8,"A",[15,"b"],"B",[15,"c"],"H",[15,"d"],"I",[15,"e"],"J",[15,"f"],"K",[15,"g"],"Q",[15,"h"],"V",[15,"i"],"W",[15,"j"],"AE",[15,"k"],"AG",[15,"l"],"AH",[15,"m"],"AI",[15,"n"],"AM",[15,"o"],"AO",[15,"p"],"AQ",[15,"q"],"AR",[15,"r"],"AT",[15,"s"],"AU",[15,"t"],"AV",[15,"u"],"AW",[15,"v"],"BA",[15,"w"],"BB",[15,"x"],"BC",[15,"y"],"BD",[15,"z"],"BE",[15,"aA"],"BG",[15,"aB"],"BH",[15,"aC"],"BM",[15,"aD"],"BP",[15,"aE"],"BQ",[15,"aF"],"BS",[15,"aG"],"BW",[15,"aH"],"BY",[15,"aI"],"CB",[15,"aJ"],"CC",[15,"aK"],"CD",[15,"aL"],"CE",[15,"aM"],"CF",[15,"aN"],"CG",[15,"aO"],"CH",[15,"aP"],"CI",[15,"aQ"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_featureFlags",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[52,"b",30],[52,"c",32],[52,"d",33],[52,"e",42],[52,"f",43],[52,"g",44],[52,"h",45],[52,"i",46],[52,"j",47],[52,"k",113],[52,"l",129],[52,"m",142],[52,"n",156],[52,"o",168],[52,"p",174],[52,"q",178],[52,"r",212],[52,"s",240],[52,"t",241],[52,"u",242],[52,"v",243],[52,"w",246],[52,"x",247],[52,"y",248],[52,"z",252],[52,"aA",253],[52,"aB",254],[36,[8,"FD",[15,"z"],"DG",[15,"o"],"V",[15,"b"],"W",[15,"c"],"X",[15,"d"],"AF",[15,"e"],"AG",[15,"f"],"AH",[15,"g"],"AI",[15,"h"],"AJ",[15,"i"],"AK",[15,"j"],"DK",[15,"p"],"DN",[15,"q"],"ER",[15,"s"],"BR",[15,"k"],"EU",[15,"v"],"EX",[15,"w"],"EZ",[15,"y"],"ET",[15,"u"],"EY",[15,"x"],"CD",[15,"l"],"ES",[15,"t"],"FF",[15,"aB"],"FE",[15,"aA"],"CQ",[15,"m"],"EA",[15,"r"],"CZ",[15,"n"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_legacyUrls",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"h",[46,"p"],[52,"q",[2,[15,"p"],"indexOf",[7,"#"]]],[36,[39,[23,[15,"q"],0],[15,"p"],[2,[15,"p"],"substring",[7,0,[15,"q"]]]]]],[50,"i",[46,"p"],[52,"q",[17,["e",[15,"p"]],"protocol"]],[36,[39,[15,"q"],[2,[15,"q"],"replace",[7,":",""]],""]]],[50,"j",[46,"p","q"],[41,"r"],[3,"r",[17,["e",[15,"p"]],"hostname"]],[22,[28,[15,"r"]],[46,[36,""]]],[52,"s",["b",":[0-9]+"]],[3,"r",[2,[15,"r"],"replace",[7,[15,"s"],""]]],[22,[15,"q"],[46,[53,[52,"t",["b","^www\\d*\\."]],[52,"u",[2,[15,"r"],"match",[7,[15,"t"]]]],[22,[1,[15,"u"],[16,[15,"u"],0]],[46,[3,"r",[2,[15,"r"],"substring",[7,[17,[16,[15,"u"],0],"length"]]]]]]]]],[36,[15,"r"]]],[50,"k",[46,"p"],[52,"q",["e",[15,"p"]]],[41,"r"],[3,"r",["f",[17,[15,"q"],"port"]]],[22,[28,[15,"r"]],[46,[53,[22,[20,[17,[15,"q"],"protocol"],"http:"],[46,[53,[3,"r",80]]],[46,[22,[20,[17,[15,"q"],"protocol"],"https:"],[46,[53,[3,"r",443]]],[46,[53,[3,"r",""]]]]]]]]],[36,["g",[15,"r"]]]],[50,"l",[46,"p","q"],[52,"r",["e",[15,"p"]]],[41,"s"],[3,"s",[39,[20,[2,[17,[15,"r"],"pathname"],"indexOf",[7,"/"]],0],[17,[15,"r"],"pathname"],[0,"/",[17,[15,"r"],"pathName"]]]],[22,[20,["d",[15,"q"]],"array"],[46,[53,[52,"t",[2,[15,"s"],"split",[7,"/"]]],[22,[19,[2,[15,"q"],"indexOf",[7,[16,[15,"t"],[37,[17,[15,"t"],"length"],1]]]],0],[46,[53,[43,[15,"t"],[37,[17,[15,"t"],"length"],1],""],[3,"s",[2,[15,"t"],"join",[7,"/"]]]]]]]]],[36,[15,"s"]]],[50,"m",[46,"p"],[52,"q",[17,["e",[15,"p"]],"pathname"]],[52,"r",[2,[15,"q"],"split",[7,"."]]],[41,"s"],[3,"s",[39,[18,[17,[15,"r"],"length"],1],[16,[15,"r"],[37,[17,[15,"r"],"length"],1]],""]],[36,[16,[2,[15,"s"],"split",[7,"/"]],0]]],[50,"n",[46,"p"],[52,"q",[17,["e",[15,"p"]],"hash"]],[36,[2,[15,"q"],"replace",[7,"#",""]]]],[50,"o",[46,"p","q"],[50,"s",[46,"t"],[36,["c",[2,[15,"t"],"replace",[7,["b","\\+","g"]," "]]]]],[52,"r",[2,[17,["e",[15,"p"]],"search"],"replace",[7,"?",""]]],[65,"t",[2,[15,"r"],"split",[7,"&"]],[46,[53,[52,"u",[2,[15,"t"],"split",[7,"="]]],[22,[21,["s",[16,[15,"u"],0]],[15,"q"]],[46,[6]]],[36,["s",[2,[2,[15,"u"],"slice",[7,1]],"join",[7,"="]]]]]]],[36]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","decodeUriComponent"]],[52,"d",["require","getType"]],[52,"e",["require","internal.legacyParseUrl"]],[52,"f",["require","makeNumber"]],[52,"g",["require","makeString"]],[36,[8,"F",[15,"m"],"H",[15,"o"],"G",[15,"n"],"C",[15,"j"],"E",[15,"l"],"D",[15,"k"],"B",[15,"i"],"A",[15,"h"]]]],[36,["a"]]]],["$0"]]]
 ,[52,"__module_gtag",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"n",[46,"r","s","t"],[65,"u",[15,"s"],[46,[53,[22,[2,[15,"r"],"hasOwnProperty",[7,[15,"u"]]],[46,[53,[43,[15,"r"],[15,"u"],["t",[16,[15,"r"],[15,"u"]]]]]]]]]]],[50,"o",[46,"r","s"],["n",[15,"r"],[15,"s"],[51,"",[7,"t"],[36,[39,[20,"false",[2,["e",[15,"t"]],"toLowerCase",[7]]],false,[28,[28,[15,"t"]]]]]]]],[50,"p",[46,"r","s"],["n",[15,"r"],[15,"s"],[15,"d"]]],[50,"q",[46,"r","s"],[22,["f",[17,[15,"g"],"ER"]],[46,[53,[52,"t",["h"]],[22,[1,[15,"t"],[18,[2,[15,"t"],"indexOf",[7,[15,"s"]]],[27,1]]],[46,[53,[43,[15,"r"],[17,[15,"i"],"AG"],true]]]]]]]],[52,"b",["require","Object"]],[52,"c",[15,"__module_gtagSchema"]],[52,"d",["require","makeNumber"]],[52,"e",["require","makeString"]],[52,"f",["require","internal.isFeatureEnabled"]],[52,"g",[15,"__module_featureFlags"]],[52,"h",["require","internal.getDestinationIds"]],[52,"i",[15,"__module_metadataSchema"]],[52,"j",[2,[15,"b"],"freeze",[7,[7,[17,[15,"c"],"BE"],[17,[15,"c"],"BG"],[17,[15,"c"],"BJ"],[17,[15,"c"],"CX"],[17,[15,"c"],"FV"],[17,[15,"c"],"IL"],[17,[15,"c"],"EN"],[17,[15,"c"],"HQ"]]]]],[52,"k",[2,[15,"b"],"freeze",[7,[7,[17,[15,"c"],"BE"],[17,[15,"c"],"BG"],[17,[15,"c"],"BJ"],[17,[15,"c"],"CX"],[17,[15,"c"],"FV"],[17,[15,"c"],"IL"],[17,[15,"c"],"EN"],[17,[15,"c"],"HQ"]]]]],[52,"l",[2,[15,"b"],"freeze",[7,[7,[17,[15,"c"],"CS"],[17,[15,"c"],"EI"],[17,[15,"c"],"HU"],[17,[15,"c"],"HW"],[17,[15,"c"],"DW"]]]]],[52,"m",[2,[15,"b"],"freeze",[7,[7,[17,[15,"c"],"CS"],[17,[15,"c"],"EI"],[17,[15,"c"],"HU"],[17,[15,"c"],"HW"],[17,[15,"c"],"DW"]]]]],[36,[8,"B",[15,"k"],"D",[15,"m"],"A",[15,"j"],"C",[15,"l"],"F",[15,"o"],"G",[15,"p"],"E",[15,"n"],"H",[15,"q"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__aev":{"2":true,"5":true}
,
"__c":{"2":true,"5":true}
,
"__cid":{"2":true,"3":true,"5":true}
,
"__ctv":{"2":true,"3":true,"5":true}
,
"__dbg":{"2":true,"5":true}
,
"__e":{"2":true,"5":true}
,
"__f":{"2":true,"5":true}
,
"__googtag":{"1":10,"5":true}
,
"__gtes":{"5":true}
,
"__hid":{"5":true}
,
"__hl":{"5":true}
,
"__lcl":{"5":true}
,
"__r":{"2":true,"5":true}
,
"__sdl":{"5":true}
,
"__u":{"2":true,"5":true}
,
"__v":{"2":true,"5":true}


}
,"blob":{"1":"176","10":"GTM-T4ZBMQJ5","12":"","13":"Vznn970uiEGlqOX9kuioxCG1U6pvll7ug3RhSBKua9I,EQmhtVhbsM2pgCBGO0QZ6selKQaSRN_2sMvfb4FIlQg,3NFpfVCWj10rYEHrZK8GkasOO687Q96UA-fszbuKk1I,_flJvJH2u4CzQhUhP2Buhej6CtEEzNixYvbAFSmMLeg,EEmThdj1FXWiyGt4V2J__F6ClYZtn44QPsIIPTunVks","14":"5931","15":"0","16":"ChAI8Nr0xQYQrOn60pyi3fwWEiUADGxl875QkYE2nVHijZ+fAeOD//HdLxfKBzes8fQddPG+CrwiGgKPCg==","19":"dataLayer","20":"","21":"www.googletagmanager.com","22":"eyIwIjoiQlIiLCIxIjoiQlItUlMiLCIyIjpmYWxzZSwiMyI6Imdvb2dsZS5jb20uYnIiLCI0IjoiIiwiNSI6ZmFsc2UsIjYiOmZhbHNlLCI3IjoiYWRfc3RvcmFnZXxhbmFseXRpY3Nfc3RvcmFnZXxhZF91c2VyX2RhdGF8YWRfcGVyc29uYWxpemF0aW9uIn0","23":"google.tagmanager.debugui2.queue","24":"tagassistant.google.com","27":0.005,"3":"www.googletagmanager.com","30":"BR","31":"BR-RS","32":false,"36":"https://adservice.google.com/pagead/regclk","37":"__TAGGY_INSTALLED","38":"cct.google","39":"googTaggyReferrer","40":"https://cct.google/taggy/agent.js","41":"google.tagmanager.ta.prodqueue","42":0.01,"43":"{\"keys\":[{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BJlFZo1PLIDIEBtsGEdfkWR4ZCcviP2y+dqfwvtWFNjNYttgOKelwTaMUwDjc4u1vbVnCQhKBR3Ud3oXWze2eV4=\",\"version\":0},\"id\":\"e79a77ad-43af-44a5-bcca-e57a1f93b790\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BDw7DFfOeZAAWAqNIyQfysi0/JA10sR+FPZhR6dMiRh4sDLkXZ+q5KoIer8OYTaPCHAZDjlF4UCz/ecoB262OG0=\",\"version\":0},\"id\":\"90bb4489-4e6e-41bc-9502-3e9d807aa4dd\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BC//NaSUVUe7TL7gUUpav1au+A9txHuJj5FmEkTOnsN0kBHmVr42c4PoznnLpQHkIK6mHi4tfTzCe888FbA2mCE=\",\"version\":0},\"id\":\"63d5d28f-3fce-4587-986e-285cc423dfa1\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BB0vQ526NMz7UA93caIqPgXdDxDAVWQZndpQCFqrbGC9e/6V4njP+/9b14wWL2ZgZ65/wfrndtBmKkenASmKl/0=\",\"version\":0},\"id\":\"4aadf648-f1fb-4a07-bbd3-09468280293c\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BLZSd5uiWWgL76/7QnIswmgZarlkx5CYp0WjRYlmOlij9R1fcj5aGKOXVUsG1faHH5wRBRoWbgbG2ma6T3kkNP0=\",\"version\":0},\"id\":\"468470fe-89e8-44ed-821a-c25902de0bd0\"}]}","44":"101509157~103116026~103200004~103233427~104684208~104684211","46":{"1":"1000","10":"5940","11":"5840","12":"0.01","14":"1000","16":"US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD","17":"US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD","2":"9","20":"5000","21":"5000","22":"3.2.0","23":"0.0.0","25":"1","26":"4000","27":"100","3":"5","4":"ad_storage|analytics_storage|ad_user_data|ad_personalization","44":"15000","48":"30000","5":"ad_storage|analytics_storage|ad_user_data","6":"1","60":"0","7":"10","8":"20","9":"https://publickeyservice.keys.adm-services.goog/v1alpha/publicKeys:raw"},"5":"GTM-T4ZBMQJ5","6":"193160782","8":"res_ts:1754487674463669,srv_cl:802928595,ds:live,cv:176","9":"GTM-T4ZBMQJ5"}
,"permissions":{
"__aev":{"read_data_layer":{"allowedKeys":"specific","keyPatterns":["gtm"]},"read_event_data":{"eventDataAccess":"any"},"read_dom_element_text":{},"get_element_attributes":{"allowedAttributes":"any"},"get_url":{"urlParts":"any"},"access_dom_element_properties":{"properties":[{"property":"tagName","read":true}]},"access_template_storage":{},"access_element_values":{"allowRead":[true],"allowWrite":[false]}}
,
"__c":{}
,
"__cid":{"read_container_data":{}}
,
"__cl":{"detect_click_events":{}}
,
"__ctv":{"read_container_data":{}}
,
"__dbg":{"read_container_data":{}}
,
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__evl":{"detect_element_visibility_events":{}}
,
"__f":{"read_data_layer":{"keyPatterns":["gtm.referrer"]},"get_referrer":{"urlParts":"any"}}
,
"__googtag":{"logging":{"environments":"debug"},"access_globals":{"keys":[{"key":"gtag","read":true,"write":true,"execute":true},{"key":"dataLayer","read":true,"write":true,"execute":false}]},"configure_google_tags":{"allowedTagIds":"any"},"load_google_tags":{"allowedTagIds":"any","allowFirstPartyUrls":true,"allowedFirstPartyUrls":"any"}}
,
"__gtes":{}
,
"__hid":{}
,
"__hl":{"detect_history_change_events":{}}
,
"__html":{"unsafe_inject_arbitrary_html":{}}
,
"__jsm":{"unsafe_run_arbitrary_javascript":{}}
,
"__lcl":{"detect_link_click_events":{"allowWaitForTags":true}}
,
"__r":{}
,
"__sdl":{"process_dom_events":{"targets":[{"targetType":"window","eventName":"load"}]},"detect_scroll_events":{}}
,
"__u":{"read_data_layer":{"keyPatterns":["gtm.url"]},"get_url":{"urlParts":"any"}}
,
"__v":{"read_data_layer":{"allowedKeys":"any"}}


}



,"security_groups":{
"customScripts":[
"__html"
,
"__jsm"

]
,
"google":[
"__aev"
,
"__c"
,
"__cid"
,
"__cl"
,
"__ctv"
,
"__dbg"
,
"__e"
,
"__evl"
,
"__f"
,
"__googtag"
,
"__gtes"
,
"__hid"
,
"__hl"
,
"__r"
,
"__sdl"
,
"__u"
,
"__v"

]


}



};




var k,ba=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ca=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},da=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},fa=da(this),ha=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ka={},ma={},na=function(a,b,c){if(!c||a!=null){var d=ma[b];if(d==null)return a[b];var e=a[d];return e!==void 0?e:a[b]}},oa=function(a,b,c){if(b)a:{var d=a.split("."),e=d.length===1,f=d[0],g;!e&&f in ka?g=ka:g=fa;for(var h=0;h<d.length-1;h++){var l=d[h];if(!(l in g))break a;g=g[l]}var n=d[d.length-1],p=ha&&c==="es6"?g[n]:null,q=b(p);if(q!=null)if(e)ca(ka,n,{configurable:!0,writable:!0,value:q});else if(q!==p){if(ma[n]===void 0){var r=
Math.random()*1E9>>>0;ma[n]=ha?fa.Symbol(n):"$jscp$"+r+"$"+n}ca(g,ma[n],{configurable:!0,writable:!0,value:q})}}};oa("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ca(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6");
var pa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},qa;if(ha&&typeof Object.setPrototypeOf=="function")qa=Object.setPrototypeOf;else{var ra;a:{var sa={a:!0},va={};try{va.__proto__=sa;ra=va.a;break a}catch(a){}ra=!1}qa=ra?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var wa=qa,xa=function(a,b){a.prototype=pa(b.prototype);a.prototype.constructor=a;if(wa)wa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Ar=b.prototype},m=function(a){var b=typeof ka.Symbol!="undefined"&&ka.Symbol.iterator&&a[ka.Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:ba(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},za=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},Ba=function(a){return a instanceof Array?a:za(m(a))},Da=function(a){return Ca(a,a)},Ca=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},Ea=ha&&typeof na(Object,"assign")=="function"?na(Object,"assign"):function(a,b){if(a==null)throw new TypeError("No nullish arg");a=Object(a);for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};
oa("Object.assign",function(a){return a||Ea},"es6");var Fa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Ga=this||self,Ha=function(a,b){function c(){}c.prototype=b.prototype;a.Ar=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Ds=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ia=function(a,b){this.type=a;this.data=b};var Ja=function(){this.map={};this.C={}};Ja.prototype.get=function(a){return this.map["dust."+a]};Ja.prototype.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};Ja.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ja.prototype.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ka=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ja.prototype.ya=function(){return Ka(this,1)};Ja.prototype.sc=function(){return Ka(this,2)};Ja.prototype.ac=function(){return Ka(this,3)};var La=function(){};La.prototype.reset=function(){};var Ma=function(a,b){this.R=a;this.parent=b;this.M=this.C=void 0;this.Bb=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=new Ja};Ma.prototype.add=function(a,b){Na(this,a,b,!1)};Ma.prototype.ph=function(a,b){Na(this,a,b,!0)};var Na=function(a,b,c,d){if(!a.Bb)if(d){var e=a.values;e.set(b,c);e.C["dust."+b]=!0}else a.values.set(b,c)};k=Ma.prototype;k.set=function(a,b){this.Bb||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.pb=function(){var a=new Ma(this.R,this);this.C&&a.Mb(this.C);a.Sc(this.H);a.Md(this.M);return a};k.Fd=function(){return this.R};k.Mb=function(a){this.C=a};k.Nm=function(){return this.C};k.Sc=function(a){this.H=a};k.gj=function(){return this.H};k.Ra=function(){this.Bb=!0};k.Md=function(a){this.M=a};k.rb=function(){return this.M};var Oa=function(){this.value={};this.prefix="gtm."};Oa.prototype.set=function(a,b){this.value[this.prefix+String(a)]=b};Oa.prototype.get=function(a){return this.value[this.prefix+String(a)]};Oa.prototype.has=function(a){return this.value.hasOwnProperty(this.prefix+String(a))};function Pa(){try{if(Map)return new Map}catch(a){}return new Oa};var Qa=function(){this.values=[]};Qa.prototype.add=function(a){this.values.indexOf(a)===-1&&this.values.push(a)};Qa.prototype.has=function(a){return this.values.indexOf(a)>-1};var Ra=function(a,b){this.ia=a;this.parent=b;this.R=this.H=void 0;this.Bb=!1;this.M=function(d,e,f){return d.apply(e,f)};this.C=Pa();var c;a:{try{if(Set){c=new Set;break a}}catch(d){}c=new Qa}this.U=c};Ra.prototype.add=function(a,b){Sa(this,a,b,!1)};Ra.prototype.ph=function(a,b){Sa(this,a,b,!0)};var Sa=function(a,b,c,d){a.Bb||a.U.has(b)||(d&&a.U.add(b),a.C.set(b,c))};k=Ra.prototype;
k.set=function(a,b){this.Bb||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.U.has(a)||this.C.set(a,b))};k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.pb=function(){var a=new Ra(this.ia,this);this.H&&a.Mb(this.H);a.Sc(this.M);a.Md(this.R);return a};k.Fd=function(){return this.ia};k.Mb=function(a){this.H=a};k.Nm=function(){return this.H};
k.Sc=function(a){this.M=a};k.gj=function(){return this.M};k.Ra=function(){this.Bb=!0};k.Md=function(a){this.R=a};k.rb=function(){return this.R};var Ta=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.Xm=a;this.Gm=c===void 0?!1:c;this.debugInfo=[];this.C=b};xa(Ta,Error);var Ua=function(a){return a instanceof Ta?a:new Ta(a,void 0,!0)};var Wa=[],Xa={};function Ya(a){return Wa[a]===void 0?!1:Wa[a]};var Za=Pa();function ab(a,b){for(var c,d=m(b),e=d.next();!e.done&&!(c=bb(a,e.value),c instanceof Ia);e=d.next());return c}
function bb(a,b){try{if(Ya(17)){var c=b[0],d=b.slice(1),e=String(c),f=Za.has(e)?Za.get(e):a.get(e);if(!f||typeof f.invoke!=="function")throw Ua(Error("Attempting to execute non-function "+b[0]+"."));return f.apply(a,d)}var g=m(b),h=g.next().value,l=za(g),n=a.get(String(h));if(!n||typeof n.invoke!=="function")throw Ua(Error("Attempting to execute non-function "+b[0]+"."));return n.invoke.apply(n,[a].concat(Ba(l)))}catch(q){var p=a.Nm();p&&p(q,b.context?{id:b[0],line:b.context.line}:null);throw q;}}
;var cb=function(){this.H=new La;this.C=Ya(17)?new Ra(this.H):new Ma(this.H)};k=cb.prototype;k.Fd=function(){return this.H};k.Mb=function(a){this.C.Mb(a)};k.Sc=function(a){this.C.Sc(a)};k.execute=function(a){return this.Gj([a].concat(Ba(Fa.apply(1,arguments))))};k.Gj=function(){for(var a,b=m(Fa.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=bb(this.C,c.value);return a};
k.Zo=function(a){var b=Fa.apply(1,arguments),c=this.C.pb();c.Md(a);for(var d,e=m(b),f=e.next();!f.done;f=e.next())d=bb(c,f.value);return d};k.Ra=function(){this.C.Ra()};var db=function(){this.Fa=!1;this.da=new Ja};k=db.prototype;k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Fa||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Fa||this.da.remove(a)};k.ya=function(){return this.da.ya()};k.sc=function(){return this.da.sc()};k.ac=function(){return this.da.ac()};k.Ra=function(){this.Fa=!0};k.Bb=function(){return this.Fa};function eb(){for(var a=fb,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function gb(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var fb,hb;function ib(a){fb=fb||gb();hb=hb||eb();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,l=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push(fb[l],fb[n],fb[p],fb[q])}return b.join("")}
function jb(a){function b(l){for(;d<a.length;){var n=a.charAt(d++),p=hb[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return l}fb=fb||gb();hb=hb||eb();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var kb={};function lb(a,b){kb[a]=kb[a]||[];kb[a][b]=!0}function mb(){delete kb.GA4_EVENT}function nb(){kb.GTAG_EVENT_FEATURE_CHANNEL=ob}function pb(a){var b=kb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return ib(c.join("")).replace(/\.+$/,"")};function rb(){}function sb(a){return typeof a==="function"}function tb(a){return typeof a==="string"}function ub(a){return typeof a==="number"&&!isNaN(a)}function vb(a){return Array.isArray(a)?a:[a]}function wb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function xb(a,b){if(!ub(a)||!ub(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function yb(a,b){for(var c=new zb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function Ab(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function Bb(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function Cb(a){return Math.round(Number(a))||0}function Db(a){return"false"===String(a).toLowerCase()?!1:!!a}
function Eb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function Fb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function Gb(){return new Date(Date.now())}function Hb(){return Gb().getTime()}var zb=function(){this.prefix="gtm.";this.values={}};zb.prototype.set=function(a,b){this.values[this.prefix+a]=b};zb.prototype.get=function(a){return this.values[this.prefix+a]};zb.prototype.contains=function(a){return this.get(a)!==void 0};
function Ib(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Jb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Kb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Lb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Mb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Nb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Ob(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Pb=/^\w{1,9}$/;function Qb(a,b){a=a||{};b=b||",";var c=[];Ab(a,function(d,e){Pb.test(d)&&e&&c.push(d)});return c.join(b)}function Rb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Sb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Tb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var l=""+f+g+h;l[l.length-1]==="/"&&(l=l.substring(0,l.length-1));return l}
function Ub(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Vb(){var a=w,b;a:{var c=a.crypto||a.msCrypto;if(c&&c.getRandomValues)try{var d=new Uint8Array(25);c.getRandomValues(d);b=btoa(String.fromCharCode.apply(String,Ba(d))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"");break a}catch(e){}b=void 0}return b};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Wb=globalThis.trustedTypes,Yb;function Zb(){var a=null;if(!Wb)return a;try{var b=function(c){return c};a=Wb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function $b(){Yb===void 0&&(Yb=Zb());return Yb};var ac=function(a){this.C=a};ac.prototype.toString=function(){return this.C+""};function bc(a){var b=a,c=$b(),d=c?c.createScriptURL(b):b;return new ac(d)}function dc(a){if(a instanceof ac)return a.C;throw Error("");};var ec=Da([""]),fc=Ca(["\x00"],["\\0"]),hc=Ca(["\n"],["\\n"]),ic=Ca(["\x00"],["\\u0000"]);function jc(a){return a.toString().indexOf("`")===-1}jc(function(a){return a(ec)})||jc(function(a){return a(fc)})||jc(function(a){return a(hc)})||jc(function(a){return a(ic)});var kc=function(a){this.C=a};kc.prototype.toString=function(){return this.C};var lc=function(a){this.Lq=a};function mc(a){return new lc(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var nc=[mc("data"),mc("http"),mc("https"),mc("mailto"),mc("ftp"),new lc(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function oc(a){var b;b=b===void 0?nc:b;if(a instanceof kc)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof lc&&d.Lq(a))return new kc(a)}}var pc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function qc(a){var b;if(a instanceof kc)if(a instanceof kc)b=a.C;else throw Error("");else b=pc.test(a)?a:void 0;return b};function rc(a,b){var c=qc(b);c!==void 0&&(a.action=c)};function sc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var tc=function(a){this.C=a};tc.prototype.toString=function(){return this.C+""};var vc=function(){this.C=uc[0].toLowerCase()};vc.prototype.toString=function(){return this.C};function wc(a,b){var c=[new vc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof vc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var xc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function yc(a){return a===null?"null":a===void 0?"undefined":a};var w=window,zc=window.history,z=document,Ac=navigator;function Bc(){var a;try{a=Ac.serviceWorker}catch(b){return}return a}var Cc=z.currentScript,Dc=Cc&&Cc.src;function Ec(a,b){var c=w,d=c[a];c[a]=d===void 0?b:d;return c[a]}function Fc(a){return(Ac.userAgent||"").indexOf(a)!==-1}function Gc(){return Fc("Firefox")||Fc("FxiOS")}function Hc(){return(Fc("GSA")||Fc("GoogleApp"))&&(Fc("iPhone")||Fc("iPad"))}function Ic(){return Fc("Edg/")||Fc("EdgA/")||Fc("EdgiOS/")}
var Jc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Kc={height:1,onload:1,src:1,style:1,width:1};function Lc(a,b,c){b&&Ab(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Mc(a,b,c,d,e){var f=z.createElement("script");Lc(f,d,Jc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=bc(yc(a));f.src=dc(g);var h,l=f.ownerDocument;l=l===void 0?document:l;var n,p,q=(p=(n=l).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=z.getElementsByTagName("script")[0]||z.body||z.head;r.parentNode.insertBefore(f,r)}return f}
function Nc(){if(Dc){var a=Dc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Oc(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=z.createElement("iframe"),h=!0);Lc(g,c,Kc);d&&Ab(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var l=z.body&&z.body.lastChild||z.body||z.head;l.parentNode.insertBefore(g,l)}b&&(g.onload=b);return g}
function Pc(a,b,c,d){return Qc(a,b,c,d)}function Rc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Sc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Tc(a){w.setTimeout(a,0)}function Uc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Vc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Wc(a){var b=z.createElement("div"),c=b,d,e=yc("A<div>"+a+"</div>"),f=$b(),g=f?f.createHTML(e):e;d=new tc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof tc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var l=[];b&&b.firstChild;)l.push(b.removeChild(b.firstChild));return l}
function Xc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Yc(a,b,c){var d;try{d=Ac.sendBeacon&&Ac.sendBeacon(a)}catch(e){lb("TAGGING",15)}d?b==null||b():Qc(a,b,c)}function Zc(a,b){try{return Ac.sendBeacon(a,b)}catch(c){lb("TAGGING",15)}return!1}var $c={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function ad(a,b,c,d,e){if(bd()){var f=na(Object,"assign").call(Object,{},$c);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=w.fetch(a,f);if(g)return g.then(function(l){l&&(l.ok||l.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(l){}}if(c&&c.Ch)return e==null||e(),
!1;if(b){var h=Zc(a,b);h?d==null||d():e==null||e();return h}cd(a,d,e);return!0}function bd(){return typeof w.fetch==="function"}function fd(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function gd(){var a=w.performance;if(a&&sb(a.now))return a.now()}
function hd(){var a,b=w.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function id(){return w.performance||void 0}function jd(){var a=w.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Qc=function(a,b,c,d){var e=new Image(1,1);Lc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},cd=Yc;function kd(a,b){return this.evaluate(a)&&this.evaluate(b)}function ld(a,b){return this.evaluate(a)===this.evaluate(b)}function md(a,b){return this.evaluate(a)||this.evaluate(b)}function nd(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function od(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function pd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=w.location.href;d instanceof db&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var qd=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,rd=function(a){if(a==null)return String(a);var b=qd.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},sd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},td=function(a){if(!a||rd(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!sd(a,"constructor")&&!sd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
sd(a,b)},ud=function(a,b){var c=b||(rd(a)=="array"?[]:{}),d;for(d in a)if(sd(a,d)){var e=a[d];rd(e)=="array"?(rd(c[d])!="array"&&(c[d]=[]),c[d]=ud(e,c[d])):td(e)?(td(c[d])||(c[d]={}),c[d]=ud(e,c[d])):c[d]=e}return c};function vd(a){if(a==void 0||Array.isArray(a)||td(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function wd(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var xd=function(a){a=a===void 0?[]:a;this.da=new Ja;this.values=[];this.Fa=!1;for(var b in a)a.hasOwnProperty(b)&&(wd(b)?this.values[Number(b)]=a[Number(b)]:this.da.set(b,a[b]))};k=xd.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof xd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Fa)if(a==="length"){if(!wd(b))throw Ua(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else wd(a)?this.values[Number(a)]=b:this.da.set(a,b)};k.get=function(a){return a==="length"?this.length():wd(a)?this.values[Number(a)]:this.da.get(a)};k.length=function(){return this.values.length};k.ya=function(){for(var a=this.da.ya(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.sc=function(){for(var a=this.da.sc(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.ac=function(){for(var a=this.da.ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){wd(a)?delete this.values[Number(a)]:this.Fa||this.da.remove(a)};k.pop=function(){return this.values.pop()};k.push=function(){return this.values.push.apply(this.values,Ba(Fa.apply(0,arguments)))};k.shift=function(){return this.values.shift()};
k.splice=function(a,b){var c=Fa.apply(2,arguments);return b===void 0&&c.length===0?new xd(this.values.splice(a)):new xd(this.values.splice.apply(this.values,[a,b||0].concat(Ba(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,Ba(Fa.apply(0,arguments)))};k.has=function(a){return wd(a)&&this.values.hasOwnProperty(a)||this.da.has(a)};k.Ra=function(){this.Fa=!0;Object.freeze(this.values)};k.Bb=function(){return this.Fa};
function yd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var zd=function(a,b){this.functionName=a;this.Ed=b;this.da=new Ja;this.Fa=!1};k=zd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new xd(this.ya())};k.invoke=function(a){return this.Ed.call.apply(this.Ed,[new Ad(this,a)].concat(Ba(Fa.apply(1,arguments))))};k.apply=function(a,b){return this.Ed.apply(new Ad(this,a),b)};k.Kb=function(a){var b=Fa.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(Ba(b)))}catch(c){}};
k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Fa||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Fa||this.da.remove(a)};k.ya=function(){return this.da.ya()};k.sc=function(){return this.da.sc()};k.ac=function(){return this.da.ac()};k.Ra=function(){this.Fa=!0};k.Bb=function(){return this.Fa};var Bd=function(a,b){zd.call(this,a,b)};xa(Bd,zd);var Cd=function(a,b){zd.call(this,a,b)};xa(Cd,zd);var Ad=function(a,b){this.Ed=a;this.J=b};
Ad.prototype.evaluate=function(a){var b=this.J;return Array.isArray(a)?bb(b,a):a};Ad.prototype.getName=function(){return this.Ed.getName()};Ad.prototype.Fd=function(){return this.J.Fd()};var Dd=function(){this.map=new Map};Dd.prototype.set=function(a,b){this.map.set(a,b)};Dd.prototype.get=function(a){return this.map.get(a)};var Ed=function(){this.keys=[];this.values=[]};Ed.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};Ed.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function Fd(){try{return Map?new Dd:new Ed}catch(a){return new Ed}};var Gd=function(a){if(a instanceof Gd)return a;if(vd(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};Gd.prototype.getValue=function(){return this.value};Gd.prototype.toString=function(){return String(this.value)};var Id=function(a){this.promise=a;this.Fa=!1;this.da=new Ja;this.da.set("then",Hd(this));this.da.set("catch",Hd(this,!0));this.da.set("finally",Hd(this,!1,!0))};k=Id.prototype;k.get=function(a){return this.da.get(a)};k.set=function(a,b){this.Fa||this.da.set(a,b)};k.has=function(a){return this.da.has(a)};k.remove=function(a){this.Fa||this.da.remove(a)};k.ya=function(){return this.da.ya()};k.sc=function(){return this.da.sc()};k.ac=function(){return this.da.ac()};
var Hd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new Bd("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof Bd||(d=void 0);e instanceof Bd||(e=void 0);var f=this.J.pb(),g=function(l){return function(n){try{return c?(l.invoke(f),a.promise):l.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new Gd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new Id(h)})};Id.prototype.Ra=function(){this.Fa=!0};Id.prototype.Bb=function(){return this.Fa};function A(a,b,c){var d=Fd(),e=function(g,h){for(var l=g.ya(),n=0;n<l.length;n++)h[l[n]]=f(g.get(l[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof xd){var l=[];d.set(g,l);for(var n=g.ya(),p=0;p<n.length;p++)l[n[p]]=f(g.get(n[p]));return l}if(g instanceof Id)return g.promise.then(function(u){return A(u,b,1)},function(u){return Promise.reject(A(u,b,1))});if(g instanceof db){var q={};d.set(g,q);e(g,q);return q}if(g instanceof Bd){var r=function(){for(var u=
[],v=0;v<arguments.length;v++)u[v]=Jd(arguments[v],b,c);var x=new Ma(b?b.Fd():new La);b&&x.Md(b.rb());return f(Ya(17)?g.apply(x,u):g.invoke.apply(g,[x].concat(Ba(u))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof Gd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Jd(a,b,c){var d=Fd(),e=function(g,h){for(var l in g)g.hasOwnProperty(l)&&h.set(l,f(g[l]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||Bb(g)){var l=new xd;d.set(g,l);for(var n in g)g.hasOwnProperty(n)&&l.set(n,f(g[n]));return l}if(td(g)){var p=new db;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new Bd("",function(){for(var u=Fa.apply(0,arguments),v=[],x=0;x<u.length;x++)v[x]=A(this.evaluate(u[x]),b,c);return f(this.J.gj()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new Gd(g)};return f(a)};var Kd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof xd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new xd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new xd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new xd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
Ba(Fa.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Ua(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Ua(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Ua(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Ua(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=yd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new xd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=yd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(Ba(Fa.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,Ba(Fa.apply(1,arguments)))}};var Ld={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Md=new Ia("break"),Nd=new Ia("continue");function Od(a,b){return this.evaluate(a)+this.evaluate(b)}function Pd(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Qd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof xd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Ua(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=A(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Ua(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Ld.hasOwnProperty(e)){var l=2;l=1;var n=A(f,void 0,l);return Jd(d[e].apply(d,n),this.J)}throw Ua(Error("TypeError: "+e+" is not a function"));}if(d instanceof xd){if(d.has(e)){var p=d.get(String(e));if(p instanceof Bd){var q=yd(f);return Ya(17)?p.apply(this.J,q):p.invoke.apply(p,[this.J].concat(Ba(q)))}throw Ua(Error("TypeError: "+e+" is not a function"));
}if(Kd.supportedMethods.indexOf(e)>=0){var r=yd(f);return Kd[e].call.apply(Kd[e],[d,this.J].concat(Ba(r)))}}if(d instanceof Bd||d instanceof db||d instanceof Id){if(d.has(e)){var t=d.get(e);if(t instanceof Bd){var u=yd(f);return Ya(17)?t.apply(this.J,u):t.invoke.apply(t,[this.J].concat(Ba(u)))}throw Ua(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof Bd?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof Gd&&e==="toString")return d.toString();
throw Ua(Error("TypeError: Object has no '"+e+"' property."));}function Rd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.J;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Sd(){var a=Fa.apply(0,arguments),b=this.J.pb(),c=ab(b,a);if(c instanceof Ia)return c}function Td(){return Md}
function Ud(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Ia)return d}}function Vd(){for(var a=this.J,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.ph(c,d)}}}function Wd(){return Nd}function Xd(a,b){return new Ia(a,this.evaluate(b))}
function Yd(a,b){var c=Fa.apply(2,arguments),d;d=new xd;for(var e=this.evaluate(b),f=0;f<e.length;f++)d.push(e[f]);var g=[51,a,d].concat(Ba(c));this.J.add(a,this.evaluate(g))}function Zd(a,b){return this.evaluate(a)/this.evaluate(b)}function $d(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof Gd,f=d instanceof Gd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}function ae(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}
function be(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=ab(f,d);if(g instanceof Ia){if(g.type==="break")break;if(g.type==="return")return g}}}function ce(a,b,c){if(typeof b==="string")return be(a,function(){return b.length},function(f){return f},c);if(b instanceof db||b instanceof Id||b instanceof xd||b instanceof Bd){var d=b.ya(),e=d.length;return be(a,function(){return e},function(f){return d[f]},c)}}
function de(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ce(function(h){g.set(d,h);return g},e,f)}function ee(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ce(function(h){var l=g.pb();l.ph(d,h);return l},e,f)}function fe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return ce(function(h){var l=g.pb();l.add(d,h);return l},e,f)}
function ge(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return he(function(h){g.set(d,h);return g},e,f)}function ie(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return he(function(h){var l=g.pb();l.ph(d,h);return l},e,f)}function je(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.J;return he(function(h){var l=g.pb();l.add(d,h);return l},e,f)}
function he(a,b,c){if(typeof b==="string")return be(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof xd)return be(a,function(){return b.length()},function(d){return b.get(d)},c);throw Ua(Error("The value is not iterable."));}
function ke(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof xd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.J,h=this.evaluate(d),l=g.pb();for(e(g,l);bb(l,b);){var n=ab(l,h);if(n instanceof Ia){if(n.type==="break")break;if(n.type==="return")return n}var p=g.pb();e(l,p);bb(p,c);l=p}}
function le(a,b){var c=Fa.apply(2,arguments),d=this.J,e=this.evaluate(b);if(!(e instanceof xd))throw Error("Error: non-List value given for Fn argument names.");return new Bd(a,function(){return function(){var f=Fa.apply(0,arguments),g=d.pb();g.rb()===void 0&&g.Md(this.J.rb());for(var h=[],l=0;l<f.length;l++){var n=this.evaluate(f[l]);h[l]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new xd(h));var r=ab(g,c);if(r instanceof Ia)return r.type===
"return"?r.data:r}}())}function me(a){var b=this.evaluate(a),c=this.J;if(ne&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function oe(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Ua(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof db||d instanceof Id||d instanceof xd||d instanceof Bd)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:wd(e)&&(c=d[e]);else if(d instanceof Gd)return;return c}function pe(a,b){return this.evaluate(a)>this.evaluate(b)}function qe(a,b){return this.evaluate(a)>=this.evaluate(b)}
function re(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof Gd&&(c=c.getValue());d instanceof Gd&&(d=d.getValue());return c===d}function se(a,b){return!re.call(this,a,b)}function te(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=ab(this.J,d);if(e instanceof Ia)return e}var ne=!1;
function ue(a,b){return this.evaluate(a)<this.evaluate(b)}function ve(a,b){return this.evaluate(a)<=this.evaluate(b)}function we(){for(var a=new xd,b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return a}function xe(){for(var a=new db,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}function ye(a,b){return this.evaluate(a)%this.evaluate(b)}
function ze(a,b){return this.evaluate(a)*this.evaluate(b)}function Ae(a){return-this.evaluate(a)}function Be(a){return!this.evaluate(a)}function Ce(a,b){return!$d.call(this,a,b)}function De(){return null}function Ee(a,b){return this.evaluate(a)||this.evaluate(b)}function Fe(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function Ge(a){return this.evaluate(a)}function He(){return Fa.apply(0,arguments)}function Ie(a){return new Ia("return",this.evaluate(a))}
function Je(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Ua(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof Bd||d instanceof xd||d instanceof db)&&d.set(String(e),f);return f}function Ke(a,b){return this.evaluate(a)-this.evaluate(b)}
function Le(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,l=0;l<e.length;l++)if(h||d===this.evaluate(e[l]))if(g=this.evaluate(f[l]),g instanceof Ia){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Ia&&(g.type==="return"||g.type==="continue")))return g}
function Me(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function Ne(a){var b=this.evaluate(a);return b instanceof Bd?"function":typeof b}function Oe(){for(var a=this.J,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Pe(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=ab(this.J,e);if(f instanceof Ia){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=ab(this.J,e);if(g instanceof Ia){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Qe(a){return~Number(this.evaluate(a))}function Re(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Se(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Te(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Ue(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Ve(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function We(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Xe(){}
function Ye(a,b,c){try{var d=this.evaluate(b);if(d instanceof Ia)return d}catch(h){if(!(h instanceof Ta&&h.Gm))throw h;var e=this.J.pb();a!==""&&(h instanceof Ta&&(h=h.Xm),e.add(a,new Gd(h)));var f=this.evaluate(c),g=ab(e,f);if(g instanceof Ia)return g}}function Ze(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Ta&&f.Gm))throw f;c=f}var e=this.evaluate(b);if(e instanceof Ia)return e;if(c)throw c;if(d instanceof Ia)return d};var af=function(){this.C=new cb;$e(this)};af.prototype.execute=function(a){return this.C.Gj(a)};var $e=function(a){var b=function(c,d){var e=new Cd(String(c),d);e.Ra();var f=String(c);a.C.C.set(f,e);Za.set(f,e)};b("map",xe);b("and",kd);b("contains",nd);b("equals",ld);b("or",md);b("startsWith",od);b("variable",pd)};af.prototype.Mb=function(a){this.C.Mb(a)};var cf=function(){this.H=!1;this.C=new cb;bf(this);this.H=!0};cf.prototype.execute=function(a){return df(this.C.Gj(a))};var ef=function(a,b,c){return df(a.C.Zo(b,c))};cf.prototype.Ra=function(){this.C.Ra()};
var bf=function(a){var b=function(c,d){var e=String(c),f=new Cd(e,d);f.Ra();a.C.C.set(e,f);Za.set(e,f)};b(0,Od);b(1,Pd);b(2,Qd);b(3,Rd);b(56,Ue);b(57,Re);b(58,Qe);b(59,We);b(60,Se);b(61,Te);b(62,Ve);b(53,Sd);b(4,Td);b(5,Ud);b(68,Ye);b(52,Vd);b(6,Wd);b(49,Xd);b(7,we);b(8,xe);b(9,Ud);b(50,Yd);b(10,Zd);b(12,$d);b(13,ae);b(67,Ze);b(51,le);b(47,de);b(54,ee);b(55,fe);b(63,ke);b(64,ge);b(65,ie);b(66,je);b(15,me);b(16,oe);b(17,oe);b(18,pe);b(19,qe);b(20,re);b(21,se);b(22,te);b(23,ue);b(24,ve);b(25,ye);b(26,
ze);b(27,Ae);b(28,Be);b(29,Ce);b(45,De);b(30,Ee);b(32,Fe);b(33,Fe);b(34,Ge);b(35,Ge);b(46,He);b(36,Ie);b(43,Je);b(37,Ke);b(38,Le);b(39,Me);b(40,Ne);b(44,Xe);b(41,Oe);b(42,Pe)};cf.prototype.Fd=function(){return this.C.Fd()};cf.prototype.Mb=function(a){this.C.Mb(a)};cf.prototype.Sc=function(a){this.C.Sc(a)};
function df(a){if(a instanceof Ia||a instanceof Bd||a instanceof xd||a instanceof db||a instanceof Id||a instanceof Gd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var ff=function(a){this.message=a};function gf(a){a.Js=!0;return a};var hf=gf(function(a){return typeof a==="string"});function jf(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new ff("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function kf(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var lf=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function mf(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+jf(e)+c}a<<=2;d||(a|=32);return c=""+jf(a|b)+c}
function nf(a,b){var c;var d=a.Eh,e=a.Rm;d===void 0?c="":(e||(e=0),c=""+mf(1,1)+jf(d<<2|e));var f=a.Ep,g=a.Kp,h="4"+c+(f?""+mf(2,1)+jf(f):"")+(g?""+mf(12,1)+jf(g):""),l,n=a.ln;l=n&&lf.test(n)?""+mf(3,2)+n:"";var p,q=a.hn;p=q?""+mf(4,1)+jf(q):"";var r;var t=a.ctid;if(t&&b){var u=mf(5,3),v=t.split("-"),x=v[0].toUpperCase();if(x!=="GTM"&&x!=="OPT")r="";else{var y=v[1];r=""+u+jf(1+y.length)+(a.Mq||0)+y}}else r="";var B=a.yr,D=a.canonicalId,E=a.Na,L=a.Ns,G=h+l+p+r+(B?""+mf(6,1)+jf(B):"")+(D?""+mf(7,3)+
jf(D.length)+D:"")+(E?""+mf(8,3)+jf(E.length)+E:"")+(L?""+mf(9,3)+jf(L.length)+L:""),N;var U=a.Lp;U=U===void 0?{}:U;for(var ia=[],S=m(Object.keys(U)),aa=S.next();!aa.done;aa=S.next()){var ta=aa.value;ia[Number(ta)]=U[ta]}if(ia.length){var la=mf(10,3),ea;if(ia.length===0)ea=jf(0);else{for(var Z=[],ja=0,ya=!1,ua=0;ua<ia.length;ua++){ya=!0;var Va=ua%6;ia[ua]&&(ja|=1<<Va);Va===5&&(Z.push(jf(ja)),ja=0,ya=!1)}ya&&Z.push(jf(ja));ea=Z.join("")}var $a=ea;N=""+la+jf($a.length)+$a}else N="";var cc=a.Tq,Xb=a.lr,
qb=a.zr;return G+N+(cc?""+mf(11,3)+jf(cc.length)+cc:"")+(Xb?""+mf(13,3)+jf(Xb.length)+Xb:"")+(qb?""+mf(14,1)+jf(qb):"")};var of=function(){function a(b){return{toString:function(){return b}}}return{En:a("consent"),ek:a("convert_case_to"),fk:a("convert_false_to"),gk:a("convert_null_to"),hk:a("convert_true_to"),ik:a("convert_undefined_to"),Mr:a("debug_mode_metadata"),Qa:a("function"),ah:a("instance_name"),cp:a("live_only"),ep:a("malware_disabled"),METADATA:a("metadata"),jp:a("original_activity_id"),ks:a("original_vendor_template_id"),hs:a("once_on_load"),hp:a("once_per_event"),Zl:a("once_per_load"),ns:a("priority_override"),
us:a("respected_consent_types"),jm:a("setup_tags"),oh:a("tag_id"),xm:a("teardown_tags")}}();var Kf;var Lf=[],Mf=[],Nf=[],Of=[],Pf=[],Qf,Rf,Sf;function Tf(a){Sf=Sf||a}
function Uf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Lf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Of.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)Nf.push(f[g]);for(var h=a.rules||[],l=0;l<h.length;l++){for(var n=h[l],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Vf(p[r])}Mf.push(p)}}
function Vf(a){}var Wf,Xf=[],Yf=[];function Zf(a,b){var c={};c[of.Qa]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function $f(a,b,c){try{return Rf(ag(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var ag=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=bg(a[e],b,c));return d},bg=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(bg(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Lf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[of.ah]);try{var l=ag(g,b,c);l.vtp_gtmEventId=b.id;b.priorityId&&(l.vtp_gtmPriorityId=b.priorityId);d=cg(l,{event:b,index:f,type:2,
name:h});Wf&&(d=Wf.Mp(d,l))}catch(B){b.logMacroError&&b.logMacroError(B,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[bg(a[n],b,c)]=bg(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=bg(a[q],b,c);Sf&&(p=p||Sf.Iq(r));d.push(r)}return Sf&&p?Sf.Rp(d):d.join("");case "escape":d=bg(a[1],b,c);if(Sf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Sf.Jq(a))return Sf.Zq(d);d=String(d);for(var t=2;t<a.length;t++)vf[a[t]]&&(d=vf[a[t]](d));return d;
case "tag":var u=a[1];if(!Of[u])throw Error("Unable to resolve tag reference "+u+".");return{Km:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[of.Qa]=a[1];var x=$f(v,b,c),y=!!a[4];return y||x!==2?y!==(x===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},cg=function(a,b){var c=a[of.Qa],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Qf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Xf.indexOf(c)!==-1,g={},h={},l;for(l in a)a.hasOwnProperty(l)&&Mb(l,"vtp_")&&(e&&(g[l]=a[l]),!e||f)&&(h[l.substring(4)]=a[l]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Lf[q];break;case 1:r=Of[q];break;default:n="";break a}var t=r&&r[of.ah];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,x;if(f&&Yf.indexOf(c)===-1){Yf.push(c);
var y=Hb();u=e(g);var B=Hb()-y,D=Hb();v=Kf(c,h,b);x=B-(Hb()-D)}else if(e&&(u=e(g)),!e||f)v=Kf(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),vd(u)?(Array.isArray(u)?Array.isArray(v):td(u)?td(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),x!==void 0&&d.reportMacroDiscrepancy(d.id,c,x));return e?u:v};var dg=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};xa(dg,Error);dg.prototype.getMessage=function(){return this.message};function eg(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)eg(a[c],b[c])}};function fg(){return function(a,b){var c;var d=gg;a instanceof Ta?(a.C=d,c=a):c=new Ta(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function gg(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)ub(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function hg(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=ig(a),f=0;f<Mf.length;f++){var g=Mf[f],h=jg(g,e);if(h){for(var l=g.add||[],n=0;n<l.length;n++)c[l[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Of.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function jg(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function ig(a){var b=[];return function(c){b[c]===void 0&&(b[c]=$f(Nf[c],a));return b[c]}};function kg(a,b){b[of.ek]&&typeof a==="string"&&(a=b[of.ek]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(of.gk)&&a===null&&(a=b[of.gk]);b.hasOwnProperty(of.ik)&&a===void 0&&(a=b[of.ik]);b.hasOwnProperty(of.hk)&&a===!0&&(a=b[of.hk]);b.hasOwnProperty(of.fk)&&a===!1&&(a=b[of.fk]);return a};var lg=function(){this.C={}},ng=function(a,b){var c=mg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,Ba(Fa.apply(0,arguments)))})};function og(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new dg(c,d,g);}}
function pg(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(Ba(Fa.apply(1,arguments))));og(e,b,d,g);og(f,b,d,g)}}}};var sg=function(a,b){var c=this;this.H={};this.C=new lg;var d={},e={},f=pg(this.C,a,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(Ba(Fa.apply(1,arguments)))):{}});Ab(b,function(g,h){function l(p){var q=Fa.apply(1,arguments);if(!n[p])throw qg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(Ba(q)))}var n={};Ab(h,function(p,q){var r=rg(p,q);n[p]=r.assert;d[p]||(d[p]=r.V);r.Em&&!e[p]&&(e[p]=r.Em)});c.H[g]=function(p,q){var r=n[p];if(!r)throw qg(p,
{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[l].concat(Ba(t.slice(1))))}})},tg=function(a){return mg.H[a]||function(){}};function rg(a,b){var c=Zf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=qg;try{return cg(c)}catch(d){return{assert:function(e){throw new dg(e,{},"Permission "+e+" is unknown.");},V:function(){throw new dg(a,{},"Permission "+a+" is unknown.");}}}}
function qg(a,b,c){return new dg(a,b,c)};var ug=!1;var vg={};vg.rn=Db('');vg.Yp=Db('');var Ag=[];function Bg(a){switch(a){case 1:return 0;case 216:return 16;case 235:return 18;case 38:return 13;case 256:return 11;case 257:return 12;case 219:return 9;case 220:return 10;case 53:return 1;case 54:return 2;case 52:return 6;case 203:return 17;case 75:return 3;case 103:return 14;case 197:return 15;case 116:return 4;case 135:return 8;case 136:return 5}}function Cg(a,b){Ag[a]=b;var c=Bg(a);c!==void 0&&(Wa[c]=b)}function C(a){Cg(a,!0)}C(39);
C(145);C(153);C(144);C(120);C(5);C(111);C(139);
C(87);C(92);C(159);
C(132);C(20);C(72);
C(113);C(154);C(116);Cg(23,!1),C(24);C(29);Dg(26,25);
C(37);C(9);C(91);
C(123);C(158);C(71);C(136);C(127);C(27);C(69);C(135);C(95);C(38);
C(103);C(112);
C(101);
C(122);C(121);
C(21);C(134);
C(22);

C(141);
C(90);C(59);
C(175);C(177);
C(185);
C(197);C(200);
C(206);
C(231);C(242);C(246);
C(247);C(248);C(238);C(241);
function F(a){return!!Ag[a]}
function Dg(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?C(b):C(a)};var Fg={P:{Nn:1,Qn:2,ym:3,bm:4,pk:5,qk:6,Uo:7,Rn:8,To:9,Mn:10,Ln:11,qm:12,km:13,Wj:14,yn:15,An:16,Xl:17,rk:18,Wl:19,On:20,fp:21,Dn:22,zn:23,Bn:24,nk:25,Uj:26,op:27,Fl:28,Ol:29,Nl:30,Ml:31,Il:32,Gl:33,Hl:34}};Fg.P[Fg.P.Nn]="CREATE_EVENT_SOURCE";Fg.P[Fg.P.Qn]="EDIT_EVENT";Fg.P[Fg.P.ym]="TRAFFIC_TYPE";Fg.P[Fg.P.bm]="REFERRAL_EXCLUSION";Fg.P[Fg.P.pk]="ECOMMERCE_FROM_GTM_TAG";Fg.P[Fg.P.qk]="ECOMMERCE_FROM_GTM_UA_SCHEMA";Fg.P[Fg.P.Uo]="GA_SEND";Fg.P[Fg.P.Rn]="EM_FORM";Fg.P[Fg.P.To]="GA_GAM_LINK";
Fg.P[Fg.P.Mn]="CREATE_EVENT_AUTO_PAGE_PATH";Fg.P[Fg.P.Ln]="CREATED_EVENT";Fg.P[Fg.P.qm]="SIDELOADED";Fg.P[Fg.P.km]="SGTM_LEGACY_CONFIGURATION";Fg.P[Fg.P.Wj]="CCD_EM_EVENT";Fg.P[Fg.P.yn]="AUTO_REDACT_EMAIL";Fg.P[Fg.P.An]="AUTO_REDACT_QUERY_PARAM";Fg.P[Fg.P.Xl]="MULTIPLE_PAGEVIEW_FROM_CONFIG";Fg.P[Fg.P.rk]="EM_EVENT_SENT_BEFORE_CONFIG";Fg.P[Fg.P.Wl]="LOADED_VIA_CST_OR_SIDELOADING";Fg.P[Fg.P.On]="DECODED_PARAM_MATCH";Fg.P[Fg.P.fp]="NON_DECODED_PARAM_MATCH";Fg.P[Fg.P.Dn]="CCD_EVENT_SGTM";
Fg.P[Fg.P.zn]="AUTO_REDACT_EMAIL_SGTM";Fg.P[Fg.P.Bn]="AUTO_REDACT_QUERY_PARAM_SGTM";Fg.P[Fg.P.nk]="DAILY_LIMIT_REACHED";Fg.P[Fg.P.Uj]="BURST_LIMIT_REACHED";Fg.P[Fg.P.op]="SHARED_USER_ID_SET_AFTER_REQUEST";Fg.P[Fg.P.Fl]="GA4_MULTIPLE_SESSION_COOKIES";Fg.P[Fg.P.Ol]="INVALID_GA4_SESSION_COUNT";Fg.P[Fg.P.Nl]="INVALID_GA4_LAST_EVENT_TIMESTAMP";Fg.P[Fg.P.Ml]="INVALID_GA4_JOIN_TIMER";Fg.P[Fg.P.Il]="GA4_STALE_SESSION_COOKIE_SELECTED";Fg.P[Fg.P.Gl]="GA4_SESSION_COOKIE_GS1_READ";Fg.P[Fg.P.Hl]="GA4_SESSION_COOKIE_GS2_READ";var Gg={},Hg=(Gg.uaa=!0,Gg.uab=!0,Gg.uafvl=!0,Gg.uamb=!0,Gg.uam=!0,Gg.uap=!0,Gg.uapv=!0,Gg.uaw=!0,Gg);
var Pg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Ng.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,l;a:if(d.length===0)l=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Og.exec(n[p])){l=!1;break a}l=!0}if(!l||h.length>d.length||!g&&d.length!==e.length?0:g?Mb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Og=/^[a-z$_][\w-$]*$/i,Ng=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Qg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Rg(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Sg(a,b){return String(a).split(",").indexOf(String(b))>=0}var Tg=new zb;function Ug(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Tg.get(e);f||(f=new RegExp(b,d),Tg.set(e,f));return f.test(a)}catch(g){return!1}}function Vg(a,b){return String(a).indexOf(String(b))>=0}
function Wg(a,b){return String(a)===String(b)}function Xg(a,b){return Number(a)>=Number(b)}function Yg(a,b){return Number(a)<=Number(b)}function Zg(a,b){return Number(a)>Number(b)}function $g(a,b){return Number(a)<Number(b)}function ah(a,b){return Mb(String(a),String(b))};var hh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,ih={Fn:"function",PixieMap:"Object",List:"Array"};
function jh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=hh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],l=b[d];if(l==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof l;l instanceof Bd?n="Fn":l instanceof xd?n="List":l instanceof db?n="PixieMap":l instanceof Id?n="PixiePromise":l instanceof Gd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((ih[n]||n)+", which does not match required type ")+
((ih[h]||h)+"."));}}}function H(a,b,c){for(var d=[],e=m(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof Bd?d.push("function"):g instanceof xd?d.push("Array"):g instanceof db?d.push("Object"):g instanceof Id?d.push("Promise"):g instanceof Gd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function kh(a){return a instanceof db}function lh(a){return kh(a)||a===null||mh(a)}
function nh(a){return a instanceof Bd}function oh(a){return nh(a)||a===null||mh(a)}function ph(a){return a instanceof xd}function qh(a){return a instanceof Gd}function I(a){return typeof a==="string"}function rh(a){return I(a)||a===null||mh(a)}function sh(a){return typeof a==="boolean"}function th(a){return sh(a)||mh(a)}function uh(a){return sh(a)||a===null||mh(a)}function vh(a){return typeof a==="number"}function mh(a){return a===void 0};function wh(a){return""+a}
function xh(a,b){var c=[];return c};function yh(a,b){var c=new Bd(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Ua(g);}});c.Ra();return c}
function zh(a,b){var c=new db,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];sb(e)?c.set(d,yh(a+"_"+d,e)):td(e)?c.set(d,zh(a+"_"+d,e)):(ub(e)||tb(e)||typeof e==="boolean")&&c.set(d,e)}c.Ra();return c};function Ah(a,b){if(!I(a))throw H(this.getName(),["string"],arguments);if(!rh(b))throw H(this.getName(),["string","undefined"],arguments);var c={},d=new db;return d=zh("AssertApiSubject",
c)};function Bh(a,b){if(!rh(b))throw H(this.getName(),["string","undefined"],arguments);if(a instanceof Id)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new db;return d=zh("AssertThatSubject",c)};function Ch(a){return function(){for(var b=Fa.apply(0,arguments),c=[],d=this.J,e=0;e<b.length;++e)c.push(A(b[e],d));return Jd(a.apply(null,c))}}function Dh(){for(var a=Math,b=Eh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=Ch(a[e].bind(a)))}return c};function Fh(a){return a!=null&&Mb(a,"__cvt_")};function Gh(a){var b;return b};function Hh(a){var b;if(!I(a))throw H(this.getName(),["string"],arguments);try{b=decodeURIComponent(a)}catch(c){}return b};function Ih(a){try{return encodeURI(a)}catch(b){}};function Jh(a){try{return encodeURIComponent(String(a))}catch(b){}};function Oh(a){if(!rh(a))throw H(this.getName(),["string|undefined"],arguments);};function Ph(a,b){if(!vh(a)||!vh(b))throw H(this.getName(),["number","number"],arguments);return xb(a,b)};function Qh(){return(new Date).getTime()};function Rh(a){if(a===null)return"null";if(a instanceof xd)return"array";if(a instanceof Bd)return"function";if(a instanceof Gd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Sh(a){function b(c){return function(d){try{return c(d)}catch(e){(ug||vg.rn)&&a.call(this,e.message)}}}return{parse:b(function(c){return Jd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(A(c))}),publicName:"JSON"}};function Th(a){return Cb(A(a,this.J))};function Vh(a){return Number(A(a,this.J))};function Wh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Xh(a,b,c){var d=null,e=!1;if(!ph(a)||!I(b)||!I(c))throw H(this.getName(),["Array","string","string"],arguments);d=new db;for(var f=0;f<a.length();f++){var g=a.get(f);g instanceof db&&g.has(b)&&g.has(c)&&(d.set(g.get(b),g.get(c)),e=!0)}return e?d:null};var Eh="floor ceil round max min abs pow sqrt".split(" ");function Yh(){var a={};return{kq:function(b){return a.hasOwnProperty(b)?a[b]:void 0},on:function(b,c){a[b]=c},reset:function(){a={}}}}function Zh(a,b){return function(){return Bd.prototype.invoke.apply(a,[b].concat(Ba(Fa.apply(0,arguments))))}}
function $h(a,b){if(!I(a))throw H(this.getName(),["string","any"],arguments);}
function ai(a,b){if(!I(a)||!kh(b))throw H(this.getName(),["string","PixieMap"],arguments);};var bi={};var ci=function(a){var b=new db;if(a instanceof xd)for(var c=a.ya(),d=0;d<c.length;d++){var e=c[d];a.has(e)&&b.set(e,a.get(e))}else if(a instanceof Bd)for(var f=a.ya(),g=0;g<f.length;g++){var h=f[g];b.set(h,a.get(h))}else for(var l=0;l<a.length;l++)b.set(l,a[l]);return b};
bi.keys=function(a){jh(this.getName(),arguments);if(a instanceof xd||a instanceof Bd||typeof a==="string")a=ci(a);if(a instanceof db||a instanceof Id)return new xd(a.ya());return new xd};
bi.values=function(a){jh(this.getName(),arguments);if(a instanceof xd||a instanceof Bd||typeof a==="string")a=ci(a);if(a instanceof db||a instanceof Id)return new xd(a.sc());return new xd};
bi.entries=function(a){jh(this.getName(),arguments);if(a instanceof xd||a instanceof Bd||typeof a==="string")a=ci(a);if(a instanceof db||a instanceof Id)return new xd(a.ac().map(function(b){return new xd(b)}));return new xd};
bi.freeze=function(a){(a instanceof db||a instanceof Id||a instanceof xd||a instanceof Bd)&&a.Ra();return a};bi.delete=function(a,b){if(a instanceof db&&!a.Bb())return a.remove(b),!0;return!1};function J(a,b){var c=Fa.apply(2,arguments),d=a.J.rb();if(!d)throw Error("Missing program state.");if(d.jr){try{d.Fm.apply(null,[b].concat(Ba(c)))}catch(e){throw lb("TAGGING",21),e;}return}d.Fm.apply(null,[b].concat(Ba(c)))};var di=function(){this.H={};this.C={};this.M=!0;};di.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};di.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
di.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:sb(b)?yh(a,b):zh(a,b)};function ei(a,b){var c=void 0;return c};function fi(){var a={};
return a};var K={m:{Ka:"ad_personalization",W:"ad_storage",X:"ad_user_data",ka:"analytics_storage",jc:"region",ja:"consent_updated",tg:"wait_for_update",Tn:"app_remove",Un:"app_store_refund",Vn:"app_store_subscription_cancel",Wn:"app_store_subscription_convert",Xn:"app_store_subscription_renew",Yn:"consent_update",tk:"add_payment_info",uk:"add_shipping_info",Td:"add_to_cart",Ud:"remove_from_cart",vk:"view_cart",Uc:"begin_checkout",Vd:"select_item",kc:"view_item_list",yc:"select_promotion",mc:"view_promotion",
ub:"purchase",Wd:"refund",Ob:"view_item",wk:"add_to_wishlist",Zn:"exception",ao:"first_open",bo:"first_visit",oa:"gtag.config",nc:"gtag.get",co:"in_app_purchase",Vc:"page_view",eo:"screen_view",fo:"session_start",ho:"source_update",io:"timing_complete",jo:"track_social",Xd:"user_engagement",ko:"user_id_update",Pe:"gclid_link_decoration_source",Qe:"gclid_storage_source",oc:"gclgb",wb:"gclid",xk:"gclid_len",Yd:"gclgs",Zd:"gcllp",ae:"gclst",Ha:"ads_data_redaction",Re:"gad_source",Se:"gad_source_src",
Wc:"gclid_url",yk:"gclsrc",Te:"gbraid",be:"wbraid",Pb:"allow_ad_personalization_signals",zg:"allow_custom_scripts",Ue:"allow_direct_google_requests",Ag:"allow_display_features",Bg:"allow_enhanced_conversions",Qb:"allow_google_signals",Ph:"allow_interest_groups",lo:"app_id",mo:"app_installer_id",no:"app_name",oo:"app_version",Xc:"auid",po:"auto_detection_enabled",zk:"aw_remarketing",Qh:"aw_remarketing_only",Cg:"discount",Dg:"aw_feed_country",Eg:"aw_feed_language",wa:"items",Fg:"aw_merchant_id",Ak:"aw_basket_type",
Ve:"campaign_content",We:"campaign_id",Xe:"campaign_medium",Ye:"campaign_name",Ze:"campaign",af:"campaign_source",bf:"campaign_term",Rb:"client_id",Bk:"rnd",Rh:"consent_update_type",qo:"content_group",ro:"content_type",lb:"conversion_cookie_prefix",Gg:"conversion_id",eb:"conversion_linker",Sh:"conversion_linker_disabled",Yc:"conversion_api",Hg:"cookie_deprecation",xb:"cookie_domain",yb:"cookie_expires",Cb:"cookie_flags",Zc:"cookie_name",Sb:"cookie_path",Ua:"cookie_prefix",Ac:"cookie_update",bd:"country",
fb:"currency",Th:"customer_buyer_stage",cf:"customer_lifetime_value",Uh:"customer_loyalty",Vh:"customer_ltv_bucket",df:"custom_map",Ig:"gcldc",dd:"dclid",Ck:"debug_mode",Ba:"developer_id",so:"disable_merchant_reported_purchases",ed:"dc_custom_params",uo:"dc_natural_search",Dk:"dynamic_event_settings",Ek:"affiliation",Jg:"checkout_option",Wh:"checkout_step",Fk:"coupon",ef:"item_list_name",Xh:"list_name",vo:"promotions",ce:"shipping",Gk:"tax",Kg:"engagement_time_msec",Lg:"enhanced_client_id",Yh:"enhanced_conversions",
Hk:"enhanced_conversions_automatic_settings",ff:"estimated_delivery_date",Zh:"euid_logged_in_state",hf:"event_callback",wo:"event_category",Bc:"event_developer_id_string",xo:"event_label",fd:"event",Mg:"event_settings",Ng:"event_timeout",yo:"description",zo:"fatal",Ao:"experiments",ai:"firebase_id",de:"first_party_collection",Og:"_x_20",qc:"_x_19",Ik:"flight_error_code",Jk:"flight_error_message",Kk:"fl_activity_category",Lk:"fl_activity_group",bi:"fl_advertiser_id",Mk:"fl_ar_dedupe",jf:"match_id",
Nk:"fl_random_number",Ok:"tran",Pk:"u",Pg:"gac_gclid",ee:"gac_wbraid",Qk:"gac_wbraid_multiple_conversions",Rk:"ga_restrict_domain",Sk:"ga_temp_client_id",Bo:"ga_temp_ecid",fe:"gdpr_applies",Tk:"geo_granularity",kf:"value_callback",lf:"value_key",Dc:"google_analysis_params",he:"_google_ng",ie:"google_signals",Uk:"google_tld",nf:"gpp_sid",pf:"gpp_string",Qg:"groups",Vk:"gsa_experiment_id",qf:"gtag_event_feature_usage",Wk:"gtm_up",Ec:"iframe_state",rf:"ignore_referrer",di:"internal_traffic_results",
Xk:"_is_fpm",Fc:"is_legacy_converted",Gc:"is_legacy_loaded",ei:"is_passthrough",gd:"_lps",zb:"language",Rg:"legacy_developer_id_string",Va:"linker",tf:"accept_incoming",Hc:"decorate_forms",na:"domains",hd:"url_position",jd:"merchant_feed_label",kd:"merchant_feed_language",ld:"merchant_id",Yk:"method",Co:"name",Zk:"navigation_type",uf:"new_customer",Sg:"non_interaction",Do:"optimize_id",al:"page_hostname",vf:"page_path",Wa:"page_referrer",Db:"page_title",bl:"passengers",fl:"phone_conversion_callback",
Eo:"phone_conversion_country_code",il:"phone_conversion_css_class",Fo:"phone_conversion_ids",jl:"phone_conversion_number",kl:"phone_conversion_options",Go:"_platinum_request_status",Ho:"_protected_audience_enabled",je:"quantity",Tg:"redact_device_info",fi:"referral_exclusion_definition",Pr:"_request_start_time",Tb:"restricted_data_processing",Io:"retoken",Jo:"sample_rate",gi:"screen_name",Ic:"screen_resolution",ml:"_script_source",Ko:"search_term",md:"send_page_view",nd:"send_to",od:"server_container_url",
Lo:"session_attributes_encoded",wf:"session_duration",Ug:"session_engaged",hi:"session_engaged_time",Ub:"session_id",Vg:"session_number",xf:"_shared_user_id",ke:"delivery_postal_code",Qr:"_tag_firing_delay",Rr:"_tag_firing_time",Sr:"temporary_client_id",ii:"_timezone",ji:"topmost_url",Mo:"tracking_id",ki:"traffic_type",Pa:"transaction_id",rc:"transport_url",nl:"trip_type",pd:"update",Eb:"url_passthrough",ol:"uptgs",yf:"_user_agent_architecture",zf:"_user_agent_bitness",Af:"_user_agent_full_version_list",
Bf:"_user_agent_mobile",Cf:"_user_agent_model",Df:"_user_agent_platform",Ef:"_user_agent_platform_version",Ff:"_user_agent_wow64",mb:"user_data",li:"user_data_auto_latency",mi:"user_data_auto_meta",ni:"user_data_auto_multi",oi:"user_data_auto_selectors",ri:"user_data_auto_status",Ab:"user_data_mode",pl:"user_data_settings",La:"user_id",Vb:"user_properties",ql:"_user_region",Gf:"us_privacy_string",Da:"value",rl:"wbraid_multiple_conversions",sd:"_fpm_parameters",zi:"_host_name",Pl:"_in_page_command",
Bi:"_ip_override",Tl:"_is_passthrough_cid",Hi:"_measurement_type",zd:"non_personalized_ads",Oi:"_sst_parameters",np:"sgtm_geo_user_country",zc:"conversion_label",Ca:"page_location",Cc:"global_developer_id_string",me:"tc_privacy_string"}};var gi={},hi=(gi[K.m.ja]="gcu",gi[K.m.oc]="gclgb",gi[K.m.wb]="gclaw",gi[K.m.xk]="gclid_len",gi[K.m.Yd]="gclgs",gi[K.m.Zd]="gcllp",gi[K.m.ae]="gclst",gi[K.m.Xc]="auid",gi[K.m.Cg]="dscnt",gi[K.m.Dg]="fcntr",gi[K.m.Eg]="flng",gi[K.m.Fg]="mid",gi[K.m.Ak]="bttype",gi[K.m.Rb]="gacid",gi[K.m.zc]="label",gi[K.m.Yc]="capi",gi[K.m.Hg]="pscdl",gi[K.m.fb]="currency_code",gi[K.m.Th]="clobs",gi[K.m.cf]="vdltv",gi[K.m.Uh]="clolo",gi[K.m.Vh]="clolb",gi[K.m.Ck]="_dbg",gi[K.m.ff]="oedeld",gi[K.m.Bc]="edid",gi[K.m.Pg]=
"gac",gi[K.m.ee]="gacgb",gi[K.m.Qk]="gacmcov",gi[K.m.fe]="gdpr",gi[K.m.Cc]="gdid",gi[K.m.he]="_ng",gi[K.m.nf]="gpp_sid",gi[K.m.pf]="gpp",gi[K.m.Vk]="gsaexp",gi[K.m.qf]="_tu",gi[K.m.Ec]="frm",gi[K.m.ei]="gtm_up",gi[K.m.gd]="lps",gi[K.m.Rg]="did",gi[K.m.jd]="fcntr",gi[K.m.kd]="flng",gi[K.m.ld]="mid",gi[K.m.uf]=void 0,gi[K.m.Db]="tiba",gi[K.m.Tb]="rdp",gi[K.m.Ub]="ecsid",gi[K.m.xf]="ga_uid",gi[K.m.ke]="delopc",gi[K.m.me]="gdpr_consent",gi[K.m.Pa]="oid",gi[K.m.ol]="uptgs",gi[K.m.yf]="uaa",gi[K.m.zf]=
"uab",gi[K.m.Af]="uafvl",gi[K.m.Bf]="uamb",gi[K.m.Cf]="uam",gi[K.m.Df]="uap",gi[K.m.Ef]="uapv",gi[K.m.Ff]="uaw",gi[K.m.li]="ec_lat",gi[K.m.mi]="ec_meta",gi[K.m.ni]="ec_m",gi[K.m.oi]="ec_sel",gi[K.m.ri]="ec_s",gi[K.m.Ab]="ec_mode",gi[K.m.La]="userId",gi[K.m.Gf]="us_privacy",gi[K.m.Da]="value",gi[K.m.rl]="mcov",gi[K.m.zi]="hn",gi[K.m.Pl]="gtm_ee",gi[K.m.Bi]="uip",gi[K.m.Hi]="mt",gi[K.m.zd]="npa",gi[K.m.np]="sg_uc",gi[K.m.Gg]=null,gi[K.m.Ic]=null,gi[K.m.zb]=null,gi[K.m.wa]=null,gi[K.m.Ca]=null,gi[K.m.Wa]=
null,gi[K.m.ji]=null,gi[K.m.sd]=null,gi[K.m.Pe]=null,gi[K.m.Qe]=null,gi[K.m.Dc]=null,gi);function ii(a,b){if(a){var c=a.split("x");c.length===2&&(ji(b,"u_w",c[0]),ji(b,"u_h",c[1]))}}
function ki(a){var b=li;b=b===void 0?mi:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var l=c;if(l){for(var n=[],p=0;p<l.length;p++){var q=l[p],r=[];q&&(r.push(ni(q.value)),r.push(ni(q.quantity)),r.push(ni(q.item_id)),r.push(ni(q.start_date)),r.push(ni(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function mi(a){return oi(a.item_id,a.id,a.item_name)}function oi(){for(var a=m(Fa.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function pi(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function ji(a,b,c){c===void 0||c===null||c===""&&!Hg[b]||(a[b]=c)}function ni(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var qi={},ri=function(){for(var a=!1,b=!1,c=0;a===b;)if(a=xb(0,1)===0,b=xb(0,1)===0,c++,c>30)return;return a},ti={nr:si};function si(a,b){var c=qi[b];if(!(xb(0,9999)<c.probability*(c.controlId2?4:2)*1E4))return a;var d=c.studyId,e=c.experimentId,f=c.controlId,g=c.controlId2;if(!((a.exp||{})[e]||(a.exp||{})[f]||g&&(a.exp||{})[g])){var h=ri()?0:1;g&&(h|=(ri()?0:1)<<1);h===0?ui(a,e,d):h===1?ui(a,f,d):h===2&&ui(a,g,d)}return a}
function vi(a,b){for(var c=a.exp||{},d=m(Object.keys(c).map(Number)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c[f]===b)return f}}function ui(a,b,c){var d=a.exp||{};d[b]=c;a.exp=d};var M={N:{Vj:"call_conversion",Qd:"ccm_conversion",sa:"conversion",No:"floodlight",If:"ga_conversion",pe:"gcp_remarketing",Fi:"landing_page",Ya:"page_view",ue:"fpm_test_hit",Gb:"remarketing",Hb:"user_data_lead",nb:"user_data_web"}};function yi(a){return zi?z.querySelector(a):null}
function Ai(a,b){if(!zi)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!z.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var Bi=!1;
if(z.querySelectorAll)try{var Ci=z.querySelectorAll(":root");Ci&&Ci.length==1&&Ci[0]==z.documentElement&&(Bi=!0)}catch(a){}var zi=Bi;var Di="email sha256_email_address phone_number sha256_phone_number first_name last_name".split(" "),Ei="first_name sha256_first_name last_name sha256_last_name street sha256_street city region country postal_code".split(" ");function Fi(a,b){if(!b._tag_metadata){for(var c={},d=0,e=0;e<a.length;e++)d+=Gi(a[e],b,c)?1:0;d>0&&(b._tag_metadata=c)}}function Gi(a,b,c){var d=b[a];if(d===void 0)return!1;c[a]=Array.isArray(d)?d.map(function(){return{mode:"c"}}):{mode:"c"};return!0}
function Hi(a){if(F(178)&&a){Fi(Di,a);for(var b=vb(a.address),c=0;c<b.length;c++){var d=b[c];d&&Fi(Ei,d)}var e=a.home_address;e&&Fi(Ei,e)}}
function Ii(a,b,c){function d(f,g){g=String(g).substring(0,100);e.push(""+f+encodeURIComponent(g))}if(!c)return"";var e=[];d("i",String(a));d("f",b);c.mode&&d("m",c.mode);c.isPreHashed&&d("p","1");c.rawLength&&d("r",String(c.rawLength));c.normalizedLength&&d("n",String(c.normalizedLength));c.location&&d("l",c.location);c.selector&&d("s",c.selector);return e.join(".")};function Ji(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function Ki(){this.blockSize=-1};function Li(a,b){this.blockSize=-1;this.blockSize=64;this.M=Ga.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.R=this.H=0;this.C=[];this.ia=a;this.U=b;this.ma=Ga.Int32Array?new Int32Array(64):Array(64);Mi===void 0&&(Ga.Int32Array?Mi=new Int32Array(Ni):Mi=Ni);this.reset()}Ha(Li,Ki);for(var Oi=[],Pi=0;Pi<63;Pi++)Oi[Pi]=0;var Qi=[].concat(128,Oi);
Li.prototype.reset=function(){this.R=this.H=0;var a;if(Ga.Int32Array)a=new Int32Array(this.U);else{var b=this.U,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Ri=function(a){for(var b=a.M,c=a.ma,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var l=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,u=a.C[6]|0,v=a.C[7]|0,x=0;x<64;x++){var y=((l>>>2|l<<30)^(l>>>13|l<<19)^(l>>>22|l<<10))+(l&n^l&p^n&p)|0,B=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(Mi[x]|0)|0)+(c[x]|0)|0)|0;v=u;u=t;t=r;r=q+B|0;q=p;p=n;n=l;l=B+y|0}a.C[0]=a.C[0]+l|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+u|0;a.C[7]=a.C[7]+v|0};
Li.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.M[d++]=a.charCodeAt(c++),d==this.blockSize&&(Ri(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.M[d++]=g;d==this.blockSize&&(Ri(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.R+=b};Li.prototype.digest=function(){var a=[],b=this.R*8;this.H<56?this.update(Qi,56-this.H):this.update(Qi,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.M[c]=b&255,b/=256;Ri(this);for(var d=0,e=0;e<this.ia;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Ni=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Mi;function Si(){Li.call(this,8,Ti)}Ha(Si,Li);var Ti=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Ui=/^[0-9A-Fa-f]{64}$/;function Vi(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Wi(a){var b=w;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Ui.test(a))return Promise.resolve(a);try{var d=Vi(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return Xi(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Xi(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};function Yi(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var Zi=[],$i=[],aj,bj;function cj(a,b){var c=dj(a,!1);return c!==b?(aj?aj(a):Zi.push(a),b):c}function dj(a,b){b=b===void 0?!1:b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?!!data.blob[a]:b}function ej(a){var b;b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}function fj(){var a=gj.M,b=hj(54);return b===a||isNaN(b)&&isNaN(a)?b:(aj?aj(54):Zi.push(54),a)}
function hj(a){var b,c;return((b=data)==null?0:(c=b.blob)==null?0:c.hasOwnProperty(a))?Number(data.blob[a]):0}function ij(a,b){var c;c=c===void 0?"":c;if(!F(225))return b;var d,e,f,g=(d=(e=data)==null?void 0:(f=e.blob)==null?void 0:f[46])&&(d==null?0:d.hasOwnProperty(a))?String(d[a]):c;return g!==b?(bj?bj(a):$i.push(a),b):g}
function jj(){var a=kj,b=lj;aj=a;for(var c=m(Zi),d=c.next();!d.done;d=c.next())a(d.value);Zi.length=0;if(F(225)){bj=b;for(var e=m($i),f=e.next();!f.done;f=e.next())b(f.value);$i.length=0}}function mj(){var a=Yi(ij(6,'1'),6E4);Xa[1]=a;var b=Yi(ij(7,'10'),1);Xa[3]=b;var c=Yi(ij(35,''),50);Xa[2]=c};var nj={wn:ij(20,'5000'),xn:ij(21,'5000'),Pn:ij(15,''),Sn:ij(14,'1000'),Vo:ij(16,'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD'),Wo:ij(17,'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD')},oj={Bp:Number(nj.wn)||-1,Cp:Number(nj.xn)||-1,Hs:Number(nj.Pn)||0,Xp:Number(nj.Sn)||0,oq:nj.Vo.split("~"),qq:nj.Wo.split("~")};
na(Object,"assign").call(Object,{},oj);function O(a){lb("GTM",a)};var Yj={T:{Sj:1,Ni:2,Nj:3,lk:4,Pj:5,Tc:6,kk:7,Gi:8,gm:9,Qj:10,Rj:11,Zg:12,Dl:13,Al:14,Cl:15,zl:16,Bl:17,yl:18,Oj:19,Po:20,Qo:21}};Yj.T[Yj.T.Sj]="ALLOW_INTEREST_GROUPS";Yj.T[Yj.T.Ni]="SERVER_CONTAINER_URL";Yj.T[Yj.T.Nj]="ADS_DATA_REDACTION";Yj.T[Yj.T.lk]="CUSTOMER_LIFETIME_VALUE";Yj.T[Yj.T.Pj]="ALLOW_CUSTOM_SCRIPTS";Yj.T[Yj.T.Tc]="ANY_COOKIE_PARAMS";Yj.T[Yj.T.kk]="COOKIE_EXPIRES";Yj.T[Yj.T.Gi]="LEGACY_ENHANCED_CONVERSION_JS_VARIABLE";Yj.T[Yj.T.gm]="RESTRICTED_DATA_PROCESSING";Yj.T[Yj.T.Qj]="ALLOW_DISPLAY_FEATURES";
Yj.T[Yj.T.Rj]="ALLOW_GOOGLE_SIGNALS";Yj.T[Yj.T.Zg]="GENERATED_TRANSACTION_ID";Yj.T[Yj.T.Dl]="FLOODLIGHT_COUNTING_METHOD_UNKNOWN";Yj.T[Yj.T.Al]="FLOODLIGHT_COUNTING_METHOD_STANDARD";Yj.T[Yj.T.Cl]="FLOODLIGHT_COUNTING_METHOD_UNIQUE";Yj.T[Yj.T.zl]="FLOODLIGHT_COUNTING_METHOD_PER_SESSION";Yj.T[Yj.T.Bl]="FLOODLIGHT_COUNTING_METHOD_TRANSACTIONS";Yj.T[Yj.T.yl]="FLOODLIGHT_COUNTING_METHOD_ITEMS_SOLD";Yj.T[Yj.T.Oj]="ADS_OGT_V1_USAGE";Yj.T[Yj.T.Po]="FORM_INTERACTION_PERMISSION_DENIED";Yj.T[Yj.T.Qo]="FORM_SUBMIT_PERMISSION_DENIED";var Zj={},ak=(Zj[K.m.Ph]=Yj.T.Sj,Zj[K.m.od]=Yj.T.Ni,Zj[K.m.rc]=Yj.T.Ni,Zj[K.m.Ha]=Yj.T.Nj,Zj[K.m.cf]=Yj.T.lk,Zj[K.m.zg]=Yj.T.Pj,Zj[K.m.Ac]=Yj.T.Tc,Zj[K.m.Ua]=Yj.T.Tc,Zj[K.m.xb]=Yj.T.Tc,Zj[K.m.Zc]=Yj.T.Tc,Zj[K.m.Sb]=Yj.T.Tc,Zj[K.m.Cb]=Yj.T.Tc,Zj[K.m.yb]=Yj.T.kk,Zj[K.m.Tb]=Yj.T.gm,Zj[K.m.Ag]=Yj.T.Qj,Zj[K.m.Qb]=Yj.T.Rj,Zj),bk={},ck=(bk.unknown=Yj.T.Dl,bk.standard=Yj.T.Al,bk.unique=Yj.T.Cl,bk.per_session=Yj.T.zl,bk.transactions=Yj.T.Bl,bk.items_sold=Yj.T.yl,bk);var ob=[];function dk(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=m(Object.keys(ak)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=ak[f],h=b;h=h===void 0?!1:h;lb("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(ob[g]=!0)}}};var ek=new zb,fk={},gk={},jk={name:ej(19),set:function(a,b){ud(Ob(a,b),fk);hk()},get:function(a){return ik(a,2)},reset:function(){ek=new zb;fk={};hk()}};function ik(a,b){return b!=2?ek.get(a):kk(a)}function kk(a,b){var c=a.split(".");b=b||[];for(var d=fk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function lk(a,b){gk.hasOwnProperty(a)||(ek.set(a,b),ud(Ob(a,b),fk),hk())}
function mk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=ik(c,1);if(Array.isArray(d)||td(d))d=ud(d,null);gk[c]=d}}function hk(a){Ab(gk,function(b,c){ek.set(b,c);ud(Ob(b),fk);ud(Ob(b,c),fk);a&&delete gk[b]})}function nk(a,b){var c,d=(b===void 0?2:b)!==1?kk(a):ek.get(a);rd(d)==="array"||rd(d)==="object"?c=ud(d,null):c=d;return c};var wk=function(){this.C=new Set;this.H=new Set},xk=function(a){var b=gj.U;a=a===void 0?[]:a;var c=[].concat(Ba(b.C)).concat([].concat(Ba(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},yk=function(){var a=[].concat(Ba(gj.U.C));a.sort(function(b,c){return b-c});return a},zk=function(){var a=gj.U,b=ej(44);a.C=new Set;if(b!=="")for(var c=m(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Ak={},Bk={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Ck={__paused:1,__tg:1},Dk;for(Dk in Bk)Bk.hasOwnProperty(Dk)&&(Ck[Dk]=1);var Ek=!1;function Fk(){var a=!1;return a}var Gk=F(218)?cj(45,Fk()):Fk(),Hk,Ik=!1;Hk=Ik;var Jk=null,Kk=null,Lk={},Mk={},Nk="";Ak.Pi=Nk;var gj=new function(){this.U=new wk;this.H=this.C=!1;this.M=0;this.ma=this.Ia=this.Xa="";this.ia=this.R=!1};function Ok(){var a=ej(18),b=a.length;return a[b-1]==="/"?a.substring(0,b-1):a}function Pk(){return gj.H?F(84)?gj.M===0:gj.M!==1:!1}function Qk(a){for(var b={},c=m(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var Rk=/:[0-9]+$/,Sk=/^\d+\.fls\.doubleclick\.net$/;function Tk(a,b,c,d){var e=Uk(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Uk(a,b,c){for(var d={},e=m(a.split("&")),f=e.next();!f.done;f=e.next()){var g=m(f.value.split("=")),h=g.next().value,l=za(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=l.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Vk(a){try{return decodeURIComponent(a)}catch(b){}}function Wk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Xk(a.protocol)||Xk(w.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:w.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||w.location.hostname).replace(Rk,"").toLowerCase());return Yk(a,b,c,d,e)}
function Yk(a,b,c,d,e){var f,g=Xk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Zk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Rk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||lb("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var l=f.split("/");(d||[]).indexOf(l[l.length-
1])>=0&&(l[l.length-1]="");f=l.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Tk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Xk(a){return a?a.replace(":","").toLowerCase():""}function Zk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var $k={},al=0;
function bl(a){var b=$k[a];if(!b){var c=z.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||lb("TAGGING",1),d="/"+d);var e=c.hostname.replace(Rk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};al<5&&($k[a]=b,al++)}return b}function cl(a,b,c){var d=bl(a);return Tb(b,d,c)}
function dl(a){var b=bl(w.location.href),c=Wk(b,"host",!1);if(c&&c.match(Sk)){var d=Wk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var el=/gtag[.\/]js/,fl=/gtm[.\/]js/,gl=!1;function hl(a){if(gl)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(el.test(c))return"3";if(fl.test(c))return"2"}return"0"};function il(a,b){var c=jl();c.pending||(c.pending=[]);wb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function kl(){var a=w.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=m(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var ll=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=kl()};function jl(){var a=Ec("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new ll,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=kl());return c};function ml(){return dj(7)&&nl().some(function(a){return a===ej(5)})}function ol(){return ej(6)||"_"+ej(5)}function pl(){var a=ej(10);return a?a.split("|"):[ej(5)]}function nl(){var a=ej(9);return a?a.split("|").filter(function(b){return b.indexOf("GTM-")!==0}):[]}function ql(){var a=rl(sl()),b=a&&a.parent;if(b)return rl(b)}function tl(){var a=rl(sl());if(a){for(;a.parent;){var b=rl(a.parent);if(!b)break;a=b}return a}}
function rl(a){var b=jl();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}function ul(){var a=jl();if(a.pending){for(var b,c=[],d=!1,e=pl(),f=nl(),g={},h=0;h<a.pending.length;g={mg:void 0},h++)g.mg=a.pending[h],wb(g.mg.target.isDestination?f:e,function(l){return function(n){return n===l.mg.target.ctid}}(g))?d||(b=g.mg.onLoad,d=!0):c.push(g.mg);a.pending=c;if(b)try{b(ol())}catch(l){}}}
function vl(){for(var a=ej(5),b=pl(),c=nl(),d=function(n,p){var q={canonicalContainerId:ej(6),scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};Cc&&(q.scriptElement=Cc);Dc&&(q.scriptSource=Dc);if(ql()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var x=gj.H,y=bl(v),B=x?y.pathname:""+y.hostname+y.pathname,D=z.scripts,E="",L=0;L<D.length;++L){var G=D[L];if(!(G.innerHTML.length===0||!x&&G.innerHTML.indexOf(q.scriptContainerId||
"SHOULD_NOT_BE_SET")<0||G.innerHTML.indexOf(B)<0)){if(G.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(L);break b}E=String(L)}}if(E){t=E;break b}}t=void 0}var N=t;if(N){gl=!0;r=N;break a}}var U=[].slice.call(z.scripts);r=q.scriptElement?String(U.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=hl(q)}var ia=p?e.destination:e.container,S=ia[n];S?(p&&S.state===0&&O(93),na(Object,"assign").call(Object,S,q)):ia[n]=q},e=jl(),f=m(b),g=f.next();!g.done;g=f.next())d(g.value,!1);for(var h=
m(c),l=h.next();!l.done;l=h.next())d(l.value,!0);e.canonical[ol()]={};ul()}function wl(){var a=ol();return!!jl().canonical[a]}function xl(a){return!!jl().container[a]}function yl(a){var b=jl().destination[a];return!!b&&!!b.state}function sl(){return{ctid:ej(5),isDestination:dj(7)}}function zl(a,b,c){var d=sl(),e=jl().container[a];e&&e.state!==3||(jl().container[a]={state:1,context:b,parent:d},il({ctid:a,isDestination:!1},c))}
function Al(){var a=jl().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}function Bl(){var a={};Ab(jl().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function Cl(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Dl(){for(var a=jl(),b=m(pl()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};function El(a){a=a===void 0?[]:a;return xk(a).join("~")}function Fl(){if(!F(118))return"";var a,b;return(((a=rl(sl()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};var Gl={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Hl=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function Il(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return bl(""+c+b).href}}function Jl(a,b){if(Pk()||gj.C)return Il(a,b)}
function Kl(){return!!Ak.Pi&&Ak.Pi.split("@@").join("")!=="SGTM_TOKEN"}function Ll(a){for(var b=m([K.m.od,K.m.rc]),c=b.next();!c.done;c=b.next()){var d=P(a,c.value);if(d)return d}}function Ml(a,b,c){c=c===void 0?"":c;if(!Pk())return a;var d=b?Gl[a]||"":"";d==="/gs"&&(c="");return""+Ok()+d+c}function Nl(a){if(!Pk())return a;for(var b=m(Hl),c=b.next();!c.done;c=b.next()){var d=c.value;if(Mb(a,""+Ok()+d))return a+"&_uip="+encodeURIComponent("::")}return a};function Ol(a){var b=String(a[of.Qa]||"").replace(/_/g,"");return Mb(b,"cvt")?"cvt":b}var Pl=w.location.search.indexOf("?gtm_latency=")>=0||w.location.search.indexOf("&gtm_latency=")>=0;var Ql=Math.random(),Rl,Sl=hj(27);Rl=Pl||Ql<Sl;var Tl,Ul=hj(42);Tl=Pl||Ql>=1-Ul;var Vl=function(a){Vl[" "](a);return a};Vl[" "]=function(){};function Wl(a){var b=a.location.href;if(a===a.top)return{url:b,Kq:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1],g;f&&((g=b)==null?void 0:g.indexOf(f))===-1&&(c=!1,b=f)}return{url:b,Kq:c}}function Xl(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Vl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}}function Yl(){for(var a=w,b=a;a&&a!=a.parent;)a=a.parent,Xl(a)&&(b=a);return b};var Zl=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},$l=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var am,bm;a:{for(var cm=["CLOSURE_FLAGS"],dm=Ga,em=0;em<cm.length;em++)if(dm=dm[cm[em]],dm==null){bm=null;break a}bm=dm}var fm=bm&&bm[610401301];am=fm!=null?fm:!1;function gm(){var a=Ga.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var hm,im=Ga.navigator;hm=im?im.userAgentData||null:null;function jm(a){if(!am||!hm)return!1;for(var b=0;b<hm.brands.length;b++){var c=hm.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function km(a){return gm().indexOf(a)!=-1};function lm(){return am?!!hm&&hm.brands.length>0:!1}function mm(){return lm()?!1:km("Opera")}function nm(){return km("Firefox")||km("FxiOS")}function om(){return lm()?jm("Chromium"):(km("Chrome")||km("CriOS"))&&!(lm()?0:km("Edge"))||km("Silk")};var pm=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function qm(){return am?!!hm&&!!hm.platform:!1}function rm(){return km("iPhone")&&!km("iPod")&&!km("iPad")}function sm(){rm()||km("iPad")||km("iPod")};mm();lm()||km("Trident")||km("MSIE");km("Edge");!km("Gecko")||gm().toLowerCase().indexOf("webkit")!=-1&&!km("Edge")||km("Trident")||km("MSIE")||km("Edge");gm().toLowerCase().indexOf("webkit")!=-1&&!km("Edge")&&km("Mobile");qm()||km("Macintosh");qm()||km("Windows");(qm()?hm.platform==="Linux":km("Linux"))||qm()||km("CrOS");qm()||km("Android");rm();km("iPad");km("iPod");sm();gm().toLowerCase().indexOf("kaios");var tm=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},um=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},vm=function(a){var b=w;if(b.top==b)return 0;if(a===void 0?0:a){var c=b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Xl(b.top)?1:2},wm=function(a){a=a===void 0?
document:a;return a.createElement("img")};function xm(){var a;a=a===void 0?document:a;var b;return!((b=a.featurePolicy)==null||!b.allowedFeatures().includes("attribution-reporting"))};function ym(a,b,c){var d=Xa[3]===void 0?1:Xa[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=z.querySelector(e);g&&(f=[g])}else f=Array.from(z.querySelectorAll(e))}catch(r){}var h;a:{try{h=z.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var l=h,n=((l==null?void 0:l.length)||0)>=(Xa[2]===void 0?50:Xa[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&Hb()-q<(Xa[1]===void 0?6E4:Xa[1])?(lb("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)zm(f[0]);else{if(n)return lb("TAGGING",10),!1}else f.length>=d?zm(f[0]):n&&zm(l[0]);Oc(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:Hb()});return!0}function zm(a){try{a.parentNode.removeChild(a)}catch(b){}};function Am(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Bm=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};nm();rm()||km("iPod");km("iPad");!km("Android")||om()||nm()||mm()||km("Silk");om();!km("Safari")||om()||(lm()?0:km("Coast"))||mm()||(lm()?0:km("Edge"))||(lm()?jm("Microsoft Edge"):km("Edg/"))||(lm()?jm("Opera"):km("OPR"))||nm()||km("Silk")||km("Android")||sm();var Cm={},Dm=null,Em=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Dm){Dm={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],l=0;l<5;l++){var n=g.concat(h[l].split(""));Cm[l]=n;for(var p=0;p<n.length;p++){var q=n[p];Dm[q]===void 0&&(Dm[q]=p)}}}for(var r=Cm[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,x=0;v<b.length-2;v+=3){var y=b[v],
B=b[v+1],D=b[v+2],E=r[y>>2],L=r[(y&3)<<4|B>>4],G=r[(B&15)<<2|D>>6],N=r[D&63];t[x++]=""+E+L+G+N}var U=0,ia=u;switch(b.length-v){case 2:U=b[v+1],ia=r[(U&15)<<2]||u;case 1:var S=b[v];t[x]=""+r[S>>2]+r[(S&3)<<4|U>>4]+ia+u}return t.join("")};var Fm=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Gm=/#|$/,Hm=function(a,b){var c=a.search(Gm),d=Fm(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return pm(a.slice(d,e!==-1?e:0))},Im=/[?&]($|#)/,Jm=function(a,b,c){for(var d,e=a.search(Gm),f=0,g,h=[];(g=Fm(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Im,"$1");var l,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;l=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else l=d;return l};function Km(a,b,c,d,e,f,g){var h=Hm(c,"fmt");if(d){var l=Hm(c,"random"),n=Hm(c,"label")||"";if(!l)return!1;var p=Em(pm(n)+":"+pm(l));if(!Am(a,p,d))return!1}h&&Number(h)!==4&&(c=Jm(c,"rfmt",h));var q=Jm(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||Lm(g);Mc(q,function(){g==null||Mm(g);a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||Mm(g);e==null||e()},f,r||void 0);return!0};var Nm={},Om=(Nm[1]={},Nm[2]={},Nm[3]={},Nm[4]={},Nm);function Pm(a,b,c){var d=Qm(b,c);if(d){var e=Om[b][d];e||(e=Om[b][d]=[]);e.push(na(Object,"assign").call(Object,{},a))}}function Rm(a,b){var c=Qm(a,b);if(c){var d=Om[a][c];d&&(Om[a][c]=d.filter(function(e){return!e.jn}))}}function Sm(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function Qm(a,b){var c=b;if(b[0]==="/"){var d;c=((d=w.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function Tm(a){var b=Fa.apply(1,arguments);Tl&&(Pm(a,2,b[0]),Pm(a,3,b[0]));Yc.apply(null,Ba(b))}function Um(a){var b=Fa.apply(1,arguments);Tl&&Pm(a,2,b[0]);return Zc.apply(null,Ba(b))}function Vm(a){var b=Fa.apply(1,arguments);Tl&&Pm(a,3,b[0]);Pc.apply(null,Ba(b))}
function Wm(a){var b=Fa.apply(1,arguments),c=b[0];Tl&&(Pm(a,2,c),Pm(a,3,c));return ad.apply(null,Ba(b))}function Xm(a){var b=Fa.apply(1,arguments);Tl&&Pm(a,1,b[0]);Mc.apply(null,Ba(b))}function Ym(a){var b=Fa.apply(1,arguments);b[0]&&Tl&&Pm(a,4,b[0]);Oc.apply(null,Ba(b))}function Zm(a){var b=Fa.apply(1,arguments);Tl&&Pm(a,1,b[2]);return Km.apply(null,Ba(b))}function $m(a){var b=Fa.apply(1,arguments);Tl&&Pm(a,4,b[0]);ym.apply(null,Ba(b))};var an={Ja:{oe:0,te:1,Ii:2}};an.Ja[an.Ja.oe]="FULL_TRANSMISSION";an.Ja[an.Ja.te]="LIMITED_TRANSMISSION";an.Ja[an.Ja.Ii]="NO_TRANSMISSION";var bn={Z:{Fb:0,Ga:1,xc:2,Jc:3}};bn.Z[bn.Z.Fb]="NO_QUEUE";bn.Z[bn.Z.Ga]="ADS";bn.Z[bn.Z.xc]="ANALYTICS";bn.Z[bn.Z.Jc]="MONITORING";function cn(){var a=Ec("google_tag_data",{});return a.ics=a.ics||new dn}var dn=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
dn.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;lb("TAGGING",19);b==null?lb("TAGGING",18):en(this,a,b==="granted",c,d,e,f,g)};dn.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)en(this,a[d],void 0,void 0,"","",b,c)};
var en=function(a,b,c,d,e,f,g,h){var l=a.entries,n=l[b]||{},p=n.region,q=d&&tb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)l[b]=t;r&&w.setTimeout(function(){l[b]===t&&t.quiet&&(lb("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=dn.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var l=m(d),n=l.next();!n.done;n=l.next())fn(this,n.value)}else if(b!==void 0&&h!==b)for(var p=m(d),q=p.next();!q.done;q=p.next())fn(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,l=c&&tb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||l===e||(l===d?h!==e:!l&&!h)){var n={region:g.region,declare_region:l,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var l=b.containerScopedDefaults[g];if(l===3)return 1;if(l===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Ed:b})};var fn=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.Zm=!0)}};dn.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.Zm){d.Zm=!1;try{d.Ed({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var gn=!1,hn=!1,jn={},kn={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:(jn.ad_storage=1,jn.analytics_storage=1,jn.ad_user_data=1,jn.ad_personalization=1,jn),usedContainerScopedDefaults:!1};function ln(a){var b=cn();b.accessedAny=!0;return(tb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,kn)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function mn(a){var b=cn();b.accessedAny=!0;return b.getConsentState(a,kn)}function nn(a){var b=cn();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function on(){if(!Ya(7))return!1;var a=cn();a.accessedAny=!0;if(a.active)return!0;if(!kn.usedContainerScopedDefaults)return!1;for(var b=m(Object.keys(kn.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(kn.containerScopedDefaults[c.value]!==1)return!0;return!1}function pn(a,b){cn().addListener(a,b)}
function qn(a,b){cn().notifyListeners(a,b)}function rn(a,b){function c(){for(var e=0;e<b.length;e++)if(!nn(b[e]))return!0;return!1}if(c()){var d=!1;pn(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function sn(a,b){function c(){for(var h=[],l=0;l<e.length;l++){var n=e[l];ln(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var l=0;l<h.length;l++)f[h[l]]=!0}var e=tb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),pn(e,function(h){function l(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?l(n):w.setTimeout(function(){l(c())},500)}}))};var tn={},un=(tn[bn.Z.Fb]=an.Ja.oe,tn[bn.Z.Ga]=an.Ja.oe,tn[bn.Z.xc]=an.Ja.oe,tn[bn.Z.Jc]=an.Ja.oe,tn),vn=function(a,b){this.C=a;this.consentTypes=b};vn.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return ln(a)});case 1:return this.consentTypes.some(function(a){return ln(a)});default:sc(this.C,"consentsRequired had an unknown type")}};
var wn={},xn=(wn[bn.Z.Fb]=new vn(0,[]),wn[bn.Z.Ga]=new vn(0,["ad_storage"]),wn[bn.Z.xc]=new vn(0,["analytics_storage"]),wn[bn.Z.Jc]=new vn(1,["ad_storage","analytics_storage"]),wn);var zn=function(a){var b=this;this.type=a;this.C=[];pn(xn[a].consentTypes,function(){yn(b)||b.flush()})};zn.prototype.flush=function(){for(var a=m(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var yn=function(a){return un[a.type]===an.Ja.Ii&&!xn[a.type].isConsentGranted()},An=function(a,b){yn(a)?a.C.push(b):b()},Bn=new Map;function Cn(a){Bn.has(a)||Bn.set(a,new zn(a));return Bn.get(a)};var Dn={aa:{vn:"aw_user_data_cache",Lh:"cookie_deprecation_label",yg:"diagnostics_page_id",si:"eab",Oo:"fl_user_data_cache",So:"ga4_user_data_cache",qe:"ip_geo_data_cache",Ai:"ip_geo_fetch_in_progress",Yl:"nb_data",Ji:"page_experiment_ids",ve:"pt_data",am:"pt_listener_set",im:"service_worker_endpoint",lm:"shared_user_id",om:"shared_user_id_requested",nh:"shared_user_id_source"}};var En=function(a){return gf(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(Dn.aa);
function Fn(a,b){b=b===void 0?!1:b;if(En(a)){var c,d,e=(d=(c=Ec("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},l={set:function(n){f=n;l.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=m(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=l}}}
function Gn(a,b){var c=Fn(a,!0);c&&c.set(b)}function Hn(a){var b;return(b=Fn(a))==null?void 0:b.get()}function In(a,b){var c=Fn(a);if(!c){c=Fn(a,!0);if(!c)return;c.set(b)}return c.get()}function Jn(a,b){if(typeof b==="function"){var c;return(c=Fn(a,!0))==null?void 0:c.subscribe(b)}}function Kn(a,b){var c=Fn(a);return c?c.unsubscribe(b):!1};var Ln={},Mn=(Ln.tdp=1,Ln.exp=1,Ln.pid=1,Ln.dl=1,Ln.seq=1,Ln.t=1,Ln.v=1,Ln),Nn=["mcc"],On={},Pn={},Un=!1;function Vn(a,b,c){Pn[a]=b;(c===void 0||c)&&Wn(a)}function Wn(a,b){On[a]!==void 0&&(b===void 0||!b)||Mb(ej(5),"GTM-")&&a==="mcc"||(On[a]=!0)}
function Xn(a){a=a===void 0?!1:a;var b=Object.keys(On).filter(function(f){return On[f]===!0&&Pn[f]!==void 0&&(a||!Nn.includes(f))});Yn(b);var c=b.map(function(f){var g=Pn[f];typeof g==="function"&&(g=g());return g?"&"+f+"="+g:""}).join(""),d="https://"+ej(21),e="/td?id="+ej(5);return""+Ml(d)+e+(""+c+"&z=0")}function Yn(a){a.forEach(function(b){Mn[b]||(On[b]=!1)})}
function Zn(a){a=a===void 0?!1:a;if(gj.ia&&Tl&&ej(5)){var b=Cn(bn.Z.Jc);if(yn(b))Un||(Un=!0,An(b,Zn));else{var c=Xn(a),d={destinationId:ej(5),endpoint:61};a?Wm(d,c,void 0,{Ch:!0},void 0,function(){Vm(d,c+"&img=1")}):Vm(d,c);Un=!1}}}function $n(){Object.keys(On).filter(function(a){return On[a]&&!Mn[a]}).length>0&&Zn(!0)}var ao;function bo(){if(Hn(Dn.aa.yg)===void 0){var a=function(){Gn(Dn.aa.yg,xb());ao=0};a();w.setInterval(a,864E5)}else Jn(Dn.aa.yg,function(){ao=0});ao=0}
function co(){bo();Vn("v","3");Vn("t","t");Vn("pid",function(){return String(Hn(Dn.aa.yg))});Vn("seq",function(){return String(++ao)});Vn("exp",El());Rc(w,"pagehide",$n)};var eo=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],fo=[K.m.od,K.m.rc,K.m.de,K.m.Rb,K.m.Ub,K.m.La,K.m.Va,K.m.Ua,K.m.xb,K.m.Sb],go=!1,ho=!1,io={},jo={};function ko(){!ho&&go&&(eo.some(function(a){return kn.containerScopedDefaults[a]!==1})||lo("mbc"));ho=!0}function lo(a){Tl&&(Vn(a,"1"),Zn())}function mo(a,b){if(!io[b]&&(io[b]=!0,jo[b]))for(var c=m(fo),d=c.next();!d.done;d=c.next())if(P(a,d.value)){lo("erc");break}};function no(a){lb("HEALTH",a)};var oo={},po=!1;function qo(){function a(){c!==void 0&&Kn(Dn.aa.qe,c);try{var e=Hn(Dn.aa.qe);oo=JSON.parse(e)}catch(f){O(123),no(2),oo={}}po=!0;b()}var b=ro,c=void 0,d=Hn(Dn.aa.qe);d?a(d):(c=Jn(Dn.aa.qe,a),so())}
function so(){function a(b){Gn(Dn.aa.qe,b||"{}");Gn(Dn.aa.Ai,!1)}if(!Hn(Dn.aa.Ai)){Gn(Dn.aa.Ai,!0);try{w.fetch("https://www.google.com/ccm/geo",{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(b){b.ok?b.text().then(function(c){a(c)},function(){a()}):a()},function(){a()})}catch(b){a()}}}function to(){var a=ej(22);try{return JSON.parse(jb(a))}catch(b){return O(123),no(2),{}}}function uo(){return oo["0"]||""}function vo(){return oo["1"]||""}
function wo(){var a=!1;return a}function xo(){return oo["6"]!==!1}function yo(){var a="";return a}function zo(){var a=!1;return a}function Ao(){var a="";return a};var Bo={},Co=Object.freeze((Bo[K.m.Pb]=1,Bo[K.m.Ag]=1,Bo[K.m.Bg]=1,Bo[K.m.Qb]=1,Bo[K.m.wa]=1,Bo[K.m.xb]=1,Bo[K.m.yb]=1,Bo[K.m.Cb]=1,Bo[K.m.Zc]=1,Bo[K.m.Sb]=1,Bo[K.m.Ua]=1,Bo[K.m.Ac]=1,Bo[K.m.df]=1,Bo[K.m.Ba]=1,Bo[K.m.Dk]=1,Bo[K.m.hf]=1,Bo[K.m.Mg]=1,Bo[K.m.Ng]=1,Bo[K.m.de]=1,Bo[K.m.Rk]=1,Bo[K.m.Dc]=1,Bo[K.m.ie]=1,Bo[K.m.Uk]=1,Bo[K.m.Qg]=1,Bo[K.m.di]=1,Bo[K.m.Fc]=1,Bo[K.m.Gc]=1,Bo[K.m.Va]=1,Bo[K.m.fi]=1,Bo[K.m.Tb]=1,Bo[K.m.md]=1,Bo[K.m.nd]=1,Bo[K.m.od]=1,Bo[K.m.wf]=1,Bo[K.m.hi]=1,Bo[K.m.ke]=1,Bo[K.m.rc]=
1,Bo[K.m.pd]=1,Bo[K.m.pl]=1,Bo[K.m.Vb]=1,Bo[K.m.sd]=1,Bo[K.m.Oi]=1,Bo));Object.freeze([K.m.Ca,K.m.Wa,K.m.Db,K.m.zb,K.m.gi,K.m.La,K.m.ai,K.m.qo]);
var Do={},Eo=Object.freeze((Do[K.m.Tn]=1,Do[K.m.Un]=1,Do[K.m.Vn]=1,Do[K.m.Wn]=1,Do[K.m.Xn]=1,Do[K.m.ao]=1,Do[K.m.bo]=1,Do[K.m.co]=1,Do[K.m.fo]=1,Do[K.m.Xd]=1,Do)),Fo={},Go=Object.freeze((Fo[K.m.tk]=1,Fo[K.m.uk]=1,Fo[K.m.Td]=1,Fo[K.m.Ud]=1,Fo[K.m.vk]=1,Fo[K.m.Uc]=1,Fo[K.m.Vd]=1,Fo[K.m.kc]=1,Fo[K.m.yc]=1,Fo[K.m.mc]=1,Fo[K.m.ub]=1,Fo[K.m.Wd]=1,Fo[K.m.Ob]=1,Fo[K.m.wk]=1,Fo)),Ho=Object.freeze([K.m.Pb,K.m.Ue,K.m.Qb,K.m.Ac,K.m.de,K.m.rf,K.m.md,K.m.pd]),Io=Object.freeze([].concat(Ba(Ho))),Jo=Object.freeze([K.m.yb,
K.m.Ng,K.m.wf,K.m.hi,K.m.Kg]),Ko=Object.freeze([].concat(Ba(Jo))),Lo={},Mo=(Lo[K.m.W]="1",Lo[K.m.ka]="2",Lo[K.m.X]="3",Lo[K.m.Ka]="4",Lo),No={},Oo=Object.freeze((No.search="s",No.youtube="y",No.playstore="p",No.shopping="h",No.ads="a",No.maps="m",No));function Po(a){return typeof a!=="object"||a===null?{}:a}function Qo(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Ro(a){if(a!==void 0&&a!==null)return Qo(a)}function So(a){return typeof a==="number"?a:Ro(a)};function To(a){return a&&a.indexOf("pending:")===0?Uo(a.substr(8)):!1}function Uo(a){if(a==null||a.length===0)return!1;var b=Number(a),c=Hb();return b<c+3E5&&b>c-9E5};var Vo=!1,Wo=!1,Xo=!1,Yo=0,Zo=!1,$o=[];function ap(a){if(Yo===0)Zo&&$o&&($o.length>=100&&$o.shift(),$o.push(a));else if(bp()){var b=ej(41),c=Ec(b,[]);c.length>=50&&c.shift();c.push(a)}}function cp(){dp();Sc(z,"TAProdDebugSignal",cp)}function dp(){if(!Wo){Wo=!0;ep();var a=$o;$o=void 0;a==null||a.forEach(function(b){ap(b)})}}
function ep(){var a=z.documentElement.getAttribute("data-tag-assistant-prod-present");Uo(a)?Yo=1:!To(a)||Vo||Xo?Yo=2:(Xo=!0,Rc(z,"TAProdDebugSignal",cp,!1),w.setTimeout(function(){dp();Vo=!0},200))}function bp(){if(!Zo)return!1;switch(Yo){case 1:case 0:return!0;case 2:return!1;default:return!1}};var fp=!1;function gp(a,b){var c=pl(),d=nl();if(bp()){var e=hp("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;ap(e)}}
function ip(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.jb;e=a.isBatched;var f;if(f=bp()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=hp("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);ap(h)}}function jp(a){bp()&&ip(a())}
function hp(a,b){b=b===void 0?{}:b;b.groupId=kp;var c,d=b,e={publicId:lp};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'176',messageType:a};c.containerProduct=fp?"OGT":"GTM";c.key.targetRef=mp;return c}var lp="",mp={ctid:"",isDestination:!1},kp;
function np(a){var b=ej(5),c=ml(),d=ej(6);Yo=0;Zo=!0;ep();kp=a;lp=b;fp=Gk;mp={ctid:b,isDestination:c,canonicalId:d}};var op=[K.m.W,K.m.ka,K.m.X,K.m.Ka],pp,qp;function rp(a){var b=a[K.m.jc];b||(b=[""]);for(var c={eg:0};c.eg<b.length;c={eg:c.eg},++c.eg)Ab(a,function(d){return function(e,f){if(e!==K.m.jc){var g=Qo(f),h=b[d.eg],l=uo(),n=vo();hn=!0;gn&&lb("TAGGING",20);cn().declare(e,g,h,l,n)}}}(c))}
function sp(a){ko();!qp&&pp&&lo("crc");qp=!0;var b=a[K.m.tg];b&&O(41);var c=a[K.m.jc];c?O(40):c=[""];for(var d={fg:0};d.fg<c.length;d={fg:d.fg},++d.fg)Ab(a,function(e){return function(f,g){if(f!==K.m.jc&&f!==K.m.tg){var h=Ro(g),l=c[e.fg],n=Number(b),p=uo(),q=vo();n=n===void 0?0:n;gn=!0;hn&&lb("TAGGING",20);cn().default(f,h,l,p,q,n,kn)}}}(d))}
function tp(a){kn.usedContainerScopedDefaults=!0;var b=a[K.m.jc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(vo())&&!c.includes(uo()))return}Ab(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}kn.usedContainerScopedDefaults=!0;kn.containerScopedDefaults[d]=e==="granted"?3:2})}
function up(a,b){ko();pp=!0;Ab(a,function(c,d){var e=Qo(d);gn=!0;hn&&lb("TAGGING",20);cn().update(c,e,kn)});qn(b.eventId,b.priorityId)}function vp(a){a.hasOwnProperty("all")&&(kn.selectedAllCorePlatformServices=!0,Ab(Oo,function(b){kn.corePlatformServices[b]=a.all==="granted";kn.usedCorePlatformServices=!0}));Ab(a,function(b,c){b!=="all"&&(kn.corePlatformServices[b]=c==="granted",kn.usedCorePlatformServices=!0)})}function Q(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return ln(b)})}
function wp(a,b){pn(a,b)}function xp(a,b){sn(a,b)}function yp(a,b){rn(a,b)}function zp(){var a=[K.m.W,K.m.Ka,K.m.X];cn().waitForUpdate(a,500,kn)}function Ap(a){for(var b=m(a),c=b.next();!c.done;c=b.next()){var d=c.value;cn().clearTimeout(d,void 0,kn)}qn()}function Bp(){if(!Hk)for(var a=xo()?Qk(gj.Ia):Qk(gj.Xa),b=0;b<op.length;b++){var c=op[b],d=c,e=a[c]?"granted":"denied";cn().implicit(d,e)}};var Cp=!1;F(218)&&(Cp=cj(49,Cp));var Dp=!1,Ep=[];function Fp(){if(!Dp){Dp=!0;for(var a=Ep.length-1;a>=0;a--)Ep[a]();Ep=[]}};var Gp=w.google_tag_manager=w.google_tag_manager||{};function Hp(a,b){return Gp[a]=Gp[a]||b()}function Ip(){var a=ej(5),b=Jp;Gp[a]=Gp[a]||b}function Kp(){var a=ej(19);return Gp[a]=Gp[a]||{}}function Lp(){var a=ej(19);return Gp[a]}function Mp(){var a=Gp.sequence||1;Gp.sequence=a+1;return a}w.google_tag_data=w.google_tag_data||{};function Np(){if(Gp.pscdl!==void 0)Hn(Dn.aa.Lh)===void 0&&Gn(Dn.aa.Lh,Gp.pscdl);else{var a=function(c){Gp.pscdl=c;Gn(Dn.aa.Lh,c)},b=function(){a("error")};try{Ac.cookieDeprecationLabel?(a("pending"),Ac.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var Op=0;function Pp(a){Tl&&a===void 0&&Op===0&&(Vn("mcc","1"),Op=1)};function Qp(){var a=[],b=Number('')||0,c=Number('')||0;c||(c=b/100);var d=function(){var Aa=!1;Aa=!0;return Aa}();a.push({Oa:21,studyId:21,experimentId:105102050,controlId:105102051,controlId2:105102052,probability:c,active:d,Ea:0});var e=
Number('')||0,f=Number('')||0;f||(f=e/100);var g=function(){var Aa=!1;return Aa}();a.push({Oa:228,studyId:228,experimentId:105177154,controlId:105177155,controlId2:105255245,probability:f,active:g,Ea:0});var h=Number('')||
0,l=Number('')||0;l||(l=h/100);var n=function(){var Aa=!1;return Aa}();a.push({Oa:256,studyId:256,experimentId:115495938,controlId:115495939,controlId2:115495940,probability:l,active:n,Ea:0});var p=Number('')||
0,q=Number('')||0;q||(q=p/100);var r=function(){var Aa=!1;return Aa}();a.push({Oa:257,studyId:257,experimentId:115495941,controlId:115495942,controlId2:115495943,probability:q,
active:r,Ea:0});var t=Number('')||0,u=Number('')||0;u||(u=t/100);var v=function(){var Aa=!1;Aa=!0;return Aa}();a.push({Oa:219,studyId:219,experimentId:104948811,
controlId:104948812,controlId2:0,probability:u,active:v,Ea:0});var x=Number('')||0,y=Number('1')||0;y||(y=x/100);var B=function(){var Aa=!1;
return Aa}();a.push({Oa:220,studyId:220,experimentId:104948813,controlId:104948814,controlId2:0,probability:y,active:B,Ea:0});var D=Number('')||0,E=Number('')||0;E||(E=D/100);var L=function(){var Aa=!1;
return Aa}();a.push({Oa:255,studyId:255,experimentId:105391252,controlId:105391253,controlId2:105446120,probability:E,active:L,Ea:0});var G=Number('')||0,N=Number('')||0;N||(N=G/100);var U=function(){var Aa=!1;return Aa}();a.push({Oa:235,studyId:235,experimentId:105357150,controlId:105357151,
controlId2:0,probability:N,active:U,Ea:1});var ia=Number('')||0,S=Number('0.1')||0;S||(S=ia/100);var aa=function(){var Aa=!1;return Aa}();a.push({Oa:203,studyId:203,experimentId:115480710,controlId:115480709,controlId2:115489982,
probability:S,active:aa,Ea:0});var ta=Number('')||0,la=Number('')||0;la||(la=ta/100);var ea=function(){var Aa=!1;Aa=!0;return Aa}();a.push({Oa:197,studyId:197,experimentId:105113532,controlId:105113531,controlId2:0,probability:la,active:ea,Ea:0});var Z=Number('')||
0,ja=Number('')||0;ja||(ja=Z/100);var ya=function(){var Aa=!1;return Aa}();a.push({Oa:254,studyId:254,experimentId:115583767,controlId:115583768,controlId2:115583769,probability:ja,active:ya,Ea:0});var ua=Number('')||
0,Va=Number('')||0;Va||(Va=ua/100);var $a=function(){var Aa=!1;return Aa}();a.push({Oa:253,studyId:253,experimentId:115583770,controlId:115583771,controlId2:115583772,probability:Va,active:$a,Ea:0});var cc=Number('')||
0,Xb=Number('')||0;Xb||(Xb=cc/100);var qb=function(){var Aa=!1;return Aa}();a.push({Oa:259,studyId:259,experimentId:105322302,controlId:105322303,controlId2:105322304,probability:Xb,active:qb,Ea:0});var dd=Number('')||
0,ed=Number('')||0;ed||(ed=dd/100);var Uh=function(){var Aa=!1;return Aa}();a.push({Oa:249,studyId:249,experimentId:105440521,controlId:105440522,controlId2:0,focused:!0,probability:ed,active:Uh,Ea:0});var DH=Number('')||0,Qn=Number('0.5')||
0;Qn||(Qn=DH/100);var EH=function(){var Aa=!1;return Aa}();a.push({Oa:195,studyId:195,experimentId:104527906,controlId:104527907,controlId2:104898015,probability:Qn,active:EH,Ea:1});var FH=Number('')||0,Rn=Number('0.5')||0;Rn||(Rn=FH/100);var GH=function(){var Aa=!1;return Aa}();a.push({Oa:196,studyId:196,experimentId:104528500,controlId:104528501,controlId2:104898016,probability:Rn,active:GH,Ea:0});var HH=Number('')||0,Sn=Number('0.001')||0;Sn||(Sn=HH/100);var IH=function(){var Aa=!1;return Aa}();a.push({Oa:229,studyId:229,experimentId:105359938,controlId:105359937,controlId2:105359936,probability:Sn,active:IH,Ea:0});var JH=Number('')||0,Tn=Number('')||0;Tn||(Tn=JH/100);var KH=function(){var Aa=!1;return Aa}();a.push({Oa:225,studyId:225,experimentId:105476338,controlId:105476339,controlId2:105476599,probability:Tn,active:KH,Ea:0});return a};var R={A:{Hh:"accept_by_default",sg:"add_tag_timing",Ke:"allow_ad_personalization",Tj:"batch_on_navigation",Xj:"client_id_source",Le:"consent_event_id",Me:"consent_priority_id",Lr:"consent_state",ja:"consent_updated",Rd:"conversion_linker_enabled",Aa:"cookie_options",vg:"create_dc_join",wg:"create_fpm_geo_join",xg:"create_fpm_signals_join",Sd:"create_google_join",Nh:"dc_random",Oe:"em_event",Or:"endpoint_for_debug",sk:"enhanced_client_id_source",Oh:"enhanced_match_result",ne:"euid_mode_enabled",hb:"event_start_timestamp_ms",
wl:"event_usage",wi:"extra_tag_experiment_ids",Vr:"add_parameter",xi:"attribution_reporting_experiment",yi:"counting_method",Xg:"send_as_iframe",Wr:"parameter_order",Yg:"parsed_target",Ro:"ga4_collection_subdomain",Jl:"gbraid_cookie_marked",Ll:"handle_internally",ba:"hit_type",ud:"hit_type_override",Jf:"ignore_hit_success_failure",Zr:"is_config_command",bh:"is_consent_update",Kf:"is_conversion",Ql:"is_ecommerce",vd:"is_external_event",eh:"is_fallback_aw_conversion_ping_allowed",Lf:"is_first_visit",
Rl:"is_first_visit_conversion",fh:"is_fl_fallback_conversion_flow_allowed",wd:"is_fpm_encryption",gh:"is_fpm_split",Wb:"is_gcp_conversion",Sl:"is_google_signals_allowed",xd:"is_merchant_center",hh:"is_new_to_site",Ci:"is_personalization",ih:"is_server_side_destination",se:"is_session_start",Ul:"is_session_start_conversion",bs:"is_sgtm_ga_ads_conversion_study_control_group",ds:"is_sgtm_prehit",Vl:"is_sgtm_service_worker",Di:"is_split_conversion",Yo:"is_syn",Mf:"join_id",Ei:"join_elapsed",Nf:"join_timer_sec",
we:"tunnel_updated",ls:"prehit_for_retry",qs:"promises",rs:"record_aw_latency",Cd:"redact_ads_data",xe:"redact_click_ids",fm:"remarketing_only",Li:"send_ccm_parallel_ping",vs:"send_ccm_parallel_test_ping",Rf:"send_to_destinations",Mi:"send_to_targets",hm:"send_user_data_hit",Za:"source_canonical_id",xa:"speculative",rm:"speculative_in_message",sm:"suppress_script_load",tm:"syn_or_mod",zm:"transient_ecsid",Sf:"transmission_type",Ma:"user_data",ys:"user_data_from_automatic",zs:"user_data_from_automatic_getter",
Bm:"user_data_from_code",qp:"user_data_from_manual",Cm:"user_data_mode",Tf:"user_id_updated"}};var Rp={};function Sp(a,b){var c=qi[b],d=c.experimentId,e=c.probability;if(!(a.studies||{})[b]){var f=a.studies||{};f[b]=!0;a.studies=f;qi[b].active||(qi[b].probability>.5?ui(a,d,b):e<=0||e>1||ti.nr(a,b))}if(!Rp[b]){var g=vi(a,b);g&&gj.U.H.add(g)}}var Tp={};function Up(a){var b=In(Dn.aa.Ji,{});return!!qi[a].active||qi[a].probability>.5||!!(b.exp||{})[qi[a].experimentId]||!!qi[a].active||qi[a].probability>.5||!!(Tp.exp||{})[qi[a].experimentId]}function Vp(a){var b=T(a,R.A.wi)||[];return El(b)}
var Wp={};
function Xp(){Wp={};var a,b,c=((a=w)==null?void 0:(b=a.location)==null?void 0:b.hash)||"";if(c.indexOf("_te=")!==0){var d=c.substring(5);if(d)for(var e=m(d.split("~")),f=e.next();!f.done;f=e.next()){var g=Number(f.value);g&&(Wp[g]=!0,C(g))}}for(var h=m(Qp()),l=h.next();!l.done;l=h.next()){var n=l.value,p=n,q=n=Wp[p.studyId]?na(Object,"assign").call(Object,{},p,{active:!0}):p;q.controlId2&&q.probability<=.25||(q=na(Object,"assign").call(Object,{},q,{controlId2:0}));qi[q.studyId]=q;n.focused&&(Rp[n.studyId]=
!0);if(n.Ea===1){var r=n.studyId;Sp(In(Dn.aa.Ji,{}),r);Up(r)&&C(r)}else if(n.Ea===0){var t=n.studyId;Sp(Tp,t);Up(t)&&C(t)}}};var Yp={Hf:{Gn:"cd",Hn:"ce",In:"cf",Jn:"cpf",Kn:"cu"}};var Zp=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,$p=/\s/;
function aq(a,b){if(tb(a)){a=Fb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Zp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var l=0;l<f.length;l++)if(!f[l]||$p.test(f[l])&&(d!=="AW"||l!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function bq(a,b){for(var c={},d=0;d<a.length;++d){var e=aq(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[cq[1]]&&f.push(h.destinationId)}for(var l=0;l<f.length;++l)delete c[f[l]];for(var n=[],p=m(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var dq={},cq=(dq[0]=0,dq[1]=1,dq[2]=2,dq[3]=0,dq[4]=1,dq[5]=0,dq[6]=0,dq[7]=0,dq);var eq=Number(ij(34,''))||500,fq={},gq={},hq={initialized:11,complete:12,interactive:13},iq={},jq=Object.freeze((iq[K.m.md]=!0,iq)),kq=void 0;function lq(a,b){if(b.length&&Tl){var c;(c=fq)[a]!=null||(c[a]=[]);gq[a]!=null||(gq[a]=[]);var d=b.filter(function(e){return!gq[a].includes(e)});fq[a].push.apply(fq[a],Ba(d));gq[a].push.apply(gq[a],Ba(d));!kq&&d.length>0&&(Wn("tdc",!0),kq=w.setTimeout(function(){Zn();fq={};kq=void 0},eq))}}
function mq(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function nq(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;rd(t)==="object"?u=t[r]:rd(t)==="array"&&(u=t[r]);return u===void 0?jq[r]:u},f=mq(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,l=e(g,a),n=e(g,b),p=rd(l)==="object"||rd(l)==="array",q=rd(n)==="object"||rd(n)==="array";if(p&&q)nq(l,n,c,h);else if(p||q||l!==n)c[h]=!0}return Object.keys(c)}
function oq(){Vn("tdc",function(){kq&&(w.clearTimeout(kq),kq=void 0);var a=[],b;for(b in fq)fq.hasOwnProperty(b)&&a.push(b+"*"+fq[b].join("."));return a.length?a.join("!"):void 0},!1)};var pq=function(a,b,c,d,e,f,g,h,l,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.U=d;this.H=e;this.R=f;this.M=g;this.eventMetadata=h;this.onSuccess=l;this.onFailure=n;this.isGtmEvent=p},qq=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.U);c.push(a.H);c.push(a.R);c.push(a.M);break;case 2:c.push(a.C);break;case 1:c.push(a.U);c.push(a.H);c.push(a.R);c.push(a.M);break;case 4:c.push(a.C),c.push(a.U),c.push(a.H),c.push(a.R)}return c},P=function(a,b,c,d){for(var e=m(qq(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},rq=function(a){for(var b={},c=qq(a,4),d=m(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=m(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
pq.prototype.getMergedValues=function(a,b,c){function d(n){td(n)&&Ab(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=qq(this,b);g.reverse();for(var h=m(g),l=h.next();!l.done;l=h.next())d(l.value[a]);return f?e:void 0};
var sq=function(a){for(var b=[K.m.Ze,K.m.Ve,K.m.We,K.m.Xe,K.m.Ye,K.m.af,K.m.bf],c=qq(a,3),d=m(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,l=m(b),n=l.next();!n.done;n=l.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},tq=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.U={};this.C={};this.M={};this.ia={};this.R={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},uq=function(a,
b){a.H=b;return a},vq=function(a,b){a.U=b;return a},wq=function(a,b){a.C=b;return a},xq=function(a,b){a.M=b;return a},yq=function(a,b){a.ia=b;return a},zq=function(a,b){a.R=b;return a},Aq=function(a,b){a.eventMetadata=b||{};return a},Bq=function(a,b){a.onSuccess=b;return a},Cq=function(a,b){a.onFailure=b;return a},Dq=function(a,b){a.isGtmEvent=b;return a},Eq=function(a){return new pq(a.eventId,a.priorityId,a.H,a.U,a.C,a.M,a.R,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var Fq={un:Number(ij(3,'5')),Ps:Number(ij(33,""))},Gq=[],Hq=!1;function Iq(a){Gq.push(a)}var Jq=void 0,Kq={},Lq=void 0,Mq=new function(){var a=5;Fq.un>0&&(a=Fq.un);this.H=a;this.C=0;this.M=[]},Nq=1E3;
function Oq(a,b){var c=Jq;if(c===void 0)if(b)c=Mp();else return"";for(var d=[Ml("https://"+ej(21)),"/a","?id="+ej(5)],e=m(Gq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Pd:!!a}),l=m(h),n=l.next();!n.done;n=l.next()){var p=m(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function Pq(){if(gj.ia&&(Lq&&(w.clearTimeout(Lq),Lq=void 0),Jq!==void 0&&Qq)){var a=Cn(bn.Z.Jc);if(yn(a))Hq||(Hq=!0,An(a,Pq));else{var b;if(!(b=Kq[Jq])){var c=Mq;b=c.C<c.H?!1:Hb()-c.M[c.C%c.H]<1E3}if(b||Nq--<=0)O(1),Kq[Jq]=!0;else{var d=Mq,e=d.C++%d.H;d.M[e]=Hb();var f=Oq(!0);Vm({destinationId:ej(5),endpoint:56,eventId:Jq},f);Hq=Qq=!1}}}}function Rq(){if(Rl&&gj.ia){var a=Oq(!0,!0);Vm({destinationId:ej(5),endpoint:56,eventId:Jq},a)}}var Qq=!1;
function Sq(a){Kq[a]||(a!==Jq&&(Pq(),Jq=a),Qq=!0,Lq||(Lq=w.setTimeout(Pq,500)),Oq().length>=2022&&Pq())}var Tq=xb();function Uq(){Tq=xb()}function Vq(){return[["v","3"],["t","t"],["pid",String(Tq)]]};var Wq={};function Xq(a,b,c){Rl&&a!==void 0&&(Wq[a]=Wq[a]||[],Wq[a].push(c+b),Sq(a))}function Yq(a){var b=a.eventId,c=a.Pd,d=[],e=Wq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Wq[b];return d};function Zq(a,b,c,d){var e=aq(a,!0);e&&$q.register(e,b,c,d)}function ar(a,b,c,d){var e=aq(c,d.isGtmEvent);e&&(Ek&&(d.deferrable=!0),$q.push("event",[b,a],e,d))}function br(a,b,c,d){var e=aq(c,d.isGtmEvent);e&&$q.push("get",[a,b],e,d)}function cr(a){var b=aq(a,!0),c;b?c=dr($q,b).C:c={};return c}function er(a,b){var c=aq(a,!0);c&&fr($q,c,b)}
var gr=function(){this.U={};this.C={};this.H={};this.ia=null;this.R={};this.M=!1;this.status=1},hr=function(a,b,c,d){this.H=Hb();this.C=b;this.args=c;this.messageContext=d;this.type=a},ir=function(){this.destinations={};this.C={};this.commands=[]},dr=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new gr},jr=function(a,b,c,d){if(d.C){var e=dr(a,d.C),f=e.ia;if(f){var g=ud(c,null),h=ud(e.U[d.C.id],null),l=ud(e.R,null),n=ud(e.C,null),p=ud(a.C,null),q={};if(Rl)try{q=
ud(fk,null)}catch(x){O(72)}var r=d.C.prefix,t=function(x){Xq(d.messageContext.eventId,r,x)},u=Eq(Dq(Cq(Bq(Aq(yq(xq(zq(wq(vq(uq(new tq(d.messageContext.eventId,d.messageContext.priorityId),g),h),l),n),p),q),d.messageContext.eventMetadata),function(){if(t){var x=t;t=void 0;x("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var x=t;t=void 0;x("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Xq(d.messageContext.eventId,
r,"1");var x=d.type,y=d.C.id;if(Tl&&x==="config"){var B,D=(B=aq(y))==null?void 0:B.ids;if(!(D&&D.length>1)){var E,L=Ec("google_tag_data",{});L.td||(L.td={});E=L.td;var G=ud(u.R);ud(u.C,G);var N=[],U;for(U in E)E.hasOwnProperty(U)&&nq(E[U],G).length&&N.push(U);N.length&&(lq(y,N),lb("TAGGING",hq[z.readyState]||14));E[y]=G}}f(d.C.id,b,d.H,u)}catch(ia){Xq(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():An(e.ma,v)}}};
ir.prototype.register=function(a,b,c,d){var e=dr(this,a);e.status!==3&&(e.ia=b,e.status=3,e.ma=Cn(c),fr(this,a,d||{}),this.flush())};
ir.prototype.push=function(a,b,c,d){c!==void 0&&(dr(this,c).status===1&&(dr(this,c).status=2,this.push("require",[{}],c,{})),dr(this,c).M&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[R.A.Rf]||(d.eventMetadata[R.A.Rf]=[c.destinationId]),d.eventMetadata[R.A.Mi]||(d.eventMetadata[R.A.Mi]=[c.id]));this.commands.push(new hr(a,c,b,d));d.deferrable||this.flush()};
ir.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Mc:void 0,sh:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||dr(this,g).M?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(dr(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];Ab(h,function(t,u){ud(Ob(t,u),b.C)});dk(h,!0);break;case "config":var l=dr(this,g);
e.Mc={};Ab(f.args[0],function(t){return function(u,v){ud(Ob(u,v),t.Mc)}}(e));var n=!!e.Mc[K.m.pd];delete e.Mc[K.m.pd];var p=g.destinationId===g.id;dk(e.Mc,!0);n||(p?l.R={}:l.U[g.id]={});l.M&&n||jr(this,K.m.oa,e.Mc,f);l.M=!0;p?ud(e.Mc,l.R):(ud(e.Mc,l.U[g.id]),O(70));d=!0;break;case "event":e.sh={};Ab(f.args[0],function(t){return function(u,v){ud(Ob(u,v),t.sh)}}(e));dk(e.sh);jr(this,f.args[1],e.sh,f);break;case "get":var q={},r=(q[K.m.lf]=f.args[0],q[K.m.kf]=f.args[1],q);jr(this,K.m.nc,r,f)}this.commands.shift();
kr(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var kr=function(a,b){if(b.type!=="require")if(b.C)for(var c=dr(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},fr=function(a,b,c){var d=ud(c,null);ud(dr(a,b).C,d);dr(a,b).C=d},$q=new ir;function lr(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function mr(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function nr(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=wm(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=xc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}mr(e,"load",f);mr(e,"error",f)};lr(e,"load",f);lr(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function or(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";tm(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});pr(c,b)}
function pr(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else nr(c,a,b===void 0?!1:b,d===void 0?!1:d)};var qr=function(){this.ia=this.ia;this.R=this.R};qr.prototype.ia=!1;qr.prototype.dispose=function(){this.ia||(this.ia=!0,this.M())};qr.prototype[ka.Symbol.dispose]=function(){this.dispose()};qr.prototype.addOnDisposeCallback=function(a,b){this.ia?b!==void 0?a.call(b):a():(this.R||(this.R=[]),b&&(a=a.bind(b)),this.R.push(a))};qr.prototype.M=function(){if(this.R)for(;this.R.length;)this.R.shift()()};function rr(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var sr=function(a,b){b=b===void 0?{}:b;qr.call(this);this.C=null;this.ma={};this.Kc=0;this.U=null;this.H=a;var c;this.Xa=(c=b.timeoutMs)!=null?c:500;var d;this.Ia=(d=b.Es)!=null?d:!1};xa(sr,qr);sr.prototype.M=function(){this.ma={};this.U&&(mr(this.H,"message",this.U),delete this.U);delete this.ma;delete this.H;delete this.C;qr.prototype.M.call(this)};var ur=function(a){return typeof a.H.__tcfapi==="function"||tr(a)!=null};
sr.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Ia},d=$l(function(){return a(c)}),e=0;this.Xa!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Xa));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=rr(c),c.internalBlockOnErrors=b.Ia,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{vr(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};sr.prototype.removeEventListener=function(a){a&&a.listenerId&&vr(this,"removeEventListener",null,a.listenerId)};
var xr=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var l;if(h===0)if(a.purpose&&a.vendor){var n=wr(a.vendor.consents,d===void 0?"755":d);l=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&wr(a.purpose.consents,b)}else l=!0;else l=h===1?a.purpose&&a.vendor?wr(a.purpose.legitimateInterests,
b)&&wr(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return l},wr=function(a,b){return!(!a||!a[b])},vr=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(tr(a)){yr(a);var g=++a.Kc;a.ma[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},tr=function(a){if(a.C)return a.C;a.C=um(a.H,"__tcfapiLocator");return a.C},yr=function(a){if(!a.U){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ma[d.callId](d.returnValue,d.success)}catch(e){}};a.U=b;lr(a.H,"message",b)}},zr=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=rr(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(or({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var Ar={1:0,3:0,4:0,7:3,9:3,10:3};ij(32,'');function Br(){return Hp("tcf",function(){return{}})}var Cr=function(){return new sr(w,{timeoutMs:-1})};
function Dr(){var a=Br(),b=Cr();ur(b)&&!Er()&&!Fr()&&O(124);if(!a.active&&ur(b)){Er()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,cn().active=!0,a.tcString="tcunavailable");zp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)Gr(a),Ap([K.m.W,K.m.Ka,K.m.X]),cn().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,Fr()&&(a.active=!0),!Hr(c)||Er()||Fr()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in Ar)Ar.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(Hr(c)){var g={},h;for(h in Ar)if(Ar.hasOwnProperty(h))if(h==="1"){var l,n=c,p={jq:!0};p=p===void 0?{}:p;l=zr(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.jq)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?xr(n,"1",0):!0:!1;g["1"]=l}else g[h]=xr(c,h,Ar[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[K.m.W]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(Ap([K.m.W,K.m.Ka,K.m.X]),cn().active=!0):(r[K.m.Ka]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[K.m.X]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":Ap([K.m.X]),up(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:Ir()||""}))}}else Ap([K.m.W,K.m.Ka,K.m.X])})}catch(c){Gr(a),Ap([K.m.W,K.m.Ka,K.m.X]),cn().active=!0}}}
function Gr(a){a.type="e";a.tcString="tcunavailable"}function Hr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function Er(){return w.gtag_enable_tcf_support===!0}function Fr(){return Br().enableAdvertiserConsentMode===!0}function Ir(){var a=Br();if(a.active)return a.tcString}function Jr(){var a=Br();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function Kr(a){if(!Ar.hasOwnProperty(String(a)))return!0;var b=Br();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var Lr=[K.m.W,K.m.ka,K.m.X,K.m.Ka],Mr={},Nr=(Mr[K.m.W]=1,Mr[K.m.ka]=2,Mr);function Or(a){if(a===void 0)return 0;switch(P(a,K.m.Pb)){case void 0:return 1;case !1:return 3;default:return 2}}function Pr(){return(F(183)?oj.oq:oj.qq).indexOf(vo())!==-1&&Ac.globalPrivacyControl===!0}function Qr(a){if(Pr())return!1;var b=Or(a);if(b===3)return!1;switch(mn(K.m.Ka)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function Rr(){return on()||!ln(K.m.W)||!ln(K.m.ka)}function Sr(){var a={},b;for(b in Nr)Nr.hasOwnProperty(b)&&(a[Nr[b]]=mn(b));return"G1"+kf(a[1]||0)+kf(a[2]||0)}var Tr={},Ur=(Tr[K.m.W]=0,Tr[K.m.ka]=1,Tr[K.m.X]=2,Tr[K.m.Ka]=3,Tr);function Vr(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Wr(a){for(var b="1",c=0;c<Lr.length;c++){var d=b,e,f=Lr[c],g=kn.delegatedConsentTypes[f];e=g===void 0?0:Ur.hasOwnProperty(g)?12|Ur[g]:8;var h=cn();h.accessedAny=!0;var l=h.entries[f]||{};e=e<<2|Vr(l.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Vr(l.declare)<<4|Vr(l.default)<<2|Vr(l.update)])}var n=b,p=(Pr()?1:0)<<3,q=(on()?1:0)<<2,r=Or(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[kn.containerScopedDefaults.ad_storage<<4|kn.containerScopedDefaults.analytics_storage<<2|kn.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(kn.usedContainerScopedDefaults?1:0)<<2|kn.containerScopedDefaults.ad_personalization]}
function Xr(){if(!ln(K.m.X))return"-";for(var a=Object.keys(Oo),b={},c=m(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=kn.corePlatformServices[e]!==!1}for(var f="",g=m(a),h=g.next();!h.done;h=g.next()){var l=h.value;b[l]&&(f+=Oo[l])}(kn.usedCorePlatformServices?kn.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Yr(){return xo()||(Er()||Fr())&&Jr()==="1"?"1":"0"}function Zr(){return(xo()?!0:!(!Er()&&!Fr())&&Jr()==="1")||!ln(K.m.X)}
function $r(){var a="0",b="0",c;var d=Br();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=Br();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;xo()&&(h|=1);Jr()==="1"&&(h|=2);Er()&&(h|=4);var l;var n=Br();l=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;l==="1"&&(h|=8);cn().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function as(){return vo()==="US-CO"};var bs;function cs(){if(Dc===null)return 0;var a=id();if(!a)return 0;var b=a.getEntriesByName(Dc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var ds={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function es(a){a=a===void 0?{}:a;var b=ej(5).split("-")[0].toUpperCase(),c,d={ctid:ej(5),hn:hj(15),ln:ej(14),Mq:dj(7)?2:1,yr:a.nn,canonicalId:ej(6),lr:(c=tl())==null?void 0:c.canonicalContainerId,zr:a.Od===void 0?void 0:a.Od?10:12};if(F(204)){var e;d.Kp=(e=bs)!=null?e:bs=cs()}d.canonicalId!==a.Na&&(d.Na=a.Na);var f=ql();d.Tq=f?f.canonicalContainerId:void 0;Gk?(d.Eh=ds[b],d.Eh||(d.Eh=0)):d.Eh=Hk?13:10;gj.H?(d.Rm=0,d.Ep=2):d.Rm=gj.C?1:3;var g={6:!1};gj.M===2?g[7]=!0:gj.M===1&&(g[2]=!0);if(Dc){var h=
Wk(bl(Dc),"host");h&&(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}d.Lp=g;return nf(d,a.qh)};function fs(a,b,c,d){var e,f=Number(a.Qc!=null?a.Qc:void 0);f!==0&&(e=new Date((b||Hb())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,wc:d}};var gs=["ad_storage","ad_user_data"];function hs(a,b){if(!a)return lb("TAGGING",32),10;if(b===null||b===void 0||b==="")return lb("TAGGING",33),11;var c=is(!1);if(c.error!==0)return lb("TAGGING",34),c.error;if(!c.value)return lb("TAGGING",35),2;c.value[a]=b;var d=js(c);d!==0&&lb("TAGGING",36);return d}
function ks(a){if(!a)return lb("TAGGING",27),{error:10};var b=is();if(b.error!==0)return lb("TAGGING",29),b;if(!b.value)return lb("TAGGING",30),{error:2};if(!(a in b.value))return lb("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(lb("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function is(a){a=a===void 0?!0:a;if(!ln(gs))return lb("TAGGING",43),{error:3};try{if(!w.localStorage)return lb("TAGGING",44),{error:1}}catch(f){return lb("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=w.localStorage.getItem("_gcl_ls")}catch(f){return lb("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return lb("TAGGING",47),{error:12}}}catch(f){return lb("TAGGING",48),{error:8}}if(b.schema!=="gcl")return lb("TAGGING",49),{error:4};
if(b.version!==1)return lb("TAGGING",50),{error:5};try{var e=ls(b);a&&e&&js({value:b,error:0})}catch(f){return lb("TAGGING",48),{error:8}}return{value:b,error:0}}
function ls(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,lb("TAGGING",54),!0}else{for(var c=!1,d=m(Object.keys(a)),e=d.next();!e.done;e=d.next())c=ls(a[e.value])||c;return c}return!1}
function js(a){if(a.error)return a.error;if(!a.value)return lb("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return lb("TAGGING",52),6}try{w.localStorage.setItem("_gcl_ls",c)}catch(d){return lb("TAGGING",53),7}return 0};var ms={uj:"value",ob:"conversionCount"},ns={Qm:9,dn:10,uj:"timeouts",ob:"timeouts"},os=[ms,ns];function ps(a){if(!qs(a))return{};var b=rs(os),c=b[a.ob];if(c===void 0||c===-1)return b;var d={},e=na(Object,"assign").call(Object,{},b,(d[a.ob]=c+1,d));return ss(e)?e:b}
function rs(a){var b;a:{var c=ks("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=m(a),h=g.next();!h.done;h=g.next()){var l=h.value;if(e&&qs(l)){var n=e[l.uj];n===void 0||Number.isNaN(n)?f[l.ob]=-1:f[l.ob]=Number(n)}else f[l.ob]=-1}return f}
function ts(){var a=ps(ms),b=a[ms.ob];if(b===void 0||b<=0)return"";var c=a[ns.ob];return c===void 0||c<0?b.toString():[b.toString(),c.toString()].join("~")}function ss(a,b){b=b||{};for(var c=Hb(),d=fs(b,c,!0),e={},f=m(os),g=f.next();!g.done;g=f.next()){var h=g.value,l=a[h.ob];l!==void 0&&l!==-1&&(e[h.uj]=l)}e.creationTimeMs=c;return hs("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function qs(a){return ln(["ad_storage","ad_user_data"])?!a.dn||Ya(a.dn):!1}
function us(a){return ln(["ad_storage","ad_user_data"])?!a.Qm||Ya(a.Qm):!1};function vs(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var ws={O:{lp:0,Mj:1,ug:2,bk:3,Jh:4,Yj:5,Zj:6,dk:7,Kh:8,tl:9,sl:10,ui:11,vl:12,Wg:13,El:14,Pf:15,kp:16,ye:17,Ri:18,Si:19,Ti:20,wm:21,Ui:22,Mh:23,mk:24}};ws.O[ws.O.lp]="RESERVED_ZERO";ws.O[ws.O.Mj]="ADS_CONVERSION_HIT";ws.O[ws.O.ug]="CONTAINER_EXECUTE_START";ws.O[ws.O.bk]="CONTAINER_SETUP_END";ws.O[ws.O.Jh]="CONTAINER_SETUP_START";ws.O[ws.O.Yj]="CONTAINER_BLOCKING_END";ws.O[ws.O.Zj]="CONTAINER_EXECUTE_END";ws.O[ws.O.dk]="CONTAINER_YIELD_END";ws.O[ws.O.Kh]="CONTAINER_YIELD_START";ws.O[ws.O.tl]="EVENT_EXECUTE_END";
ws.O[ws.O.sl]="EVENT_EVALUATION_END";ws.O[ws.O.ui]="EVENT_EVALUATION_START";ws.O[ws.O.vl]="EVENT_SETUP_END";ws.O[ws.O.Wg]="EVENT_SETUP_START";ws.O[ws.O.El]="GA4_CONVERSION_HIT";ws.O[ws.O.Pf]="PAGE_LOAD";ws.O[ws.O.kp]="PAGEVIEW";ws.O[ws.O.ye]="SNIPPET_LOAD";ws.O[ws.O.Ri]="TAG_CALLBACK_ERROR";ws.O[ws.O.Si]="TAG_CALLBACK_FAILURE";ws.O[ws.O.Ti]="TAG_CALLBACK_SUCCESS";ws.O[ws.O.wm]="TAG_EXECUTE_END";ws.O[ws.O.Ui]="TAG_EXECUTE_START";ws.O[ws.O.Mh]="CUSTOM_PERFORMANCE_START";ws.O[ws.O.mk]="CUSTOM_PERFORMANCE_END";var xs=[],ys={},zs={};var As=["2"];function Bs(a){return a.origin!=="null"};var Cs;function Ds(a,b,c,d){var e;return(e=Es(function(f){return f===a},b,c,d)[a])!=null?e:[]}function Es(a,b,c,d){var e;if(Fs(d)){for(var f={},g=String(b||Gs()).split(";"),h=0;h<g.length;h++){var l=g[h].split("="),n=l[0].trim();if(n&&a(n)){var p=l.slice(1).join("=").trim();p&&c&&(p=decodeURIComponent(p));var q=void 0,r=void 0;((q=f)[r=n]||(q[r]=[])).push(p)}}e=f}else e={};return e}
function Hs(a,b,c,d,e){if(Fs(e)){var f=Is(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=Js(f,function(g){return g.Vp},b);if(f.length===1)return f[0];f=Js(f,function(g){return g.Vq},c);return f[0]}}}function Ks(a,b,c,d){var e=Gs(),f=window;Bs(f)&&(f.document.cookie=a);var g=Gs();return e!==g||c!==void 0&&Ds(b,g,!1,d).indexOf(c)>=0}
function Ls(a,b,c,d){function e(x,y,B){if(B==null)return delete h[y],x;h[y]=B;return x+"; "+y+"="+B}function f(x,y){if(y==null)return x;h[y]=!0;return x+"; "+y}if(!Fs(c.wc))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=Ms(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var l;c.expires instanceof Date?l=c.expires.toUTCString():c.expires!=null&&(l=""+c.expires);g=e(g,"expires",l);g=e(g,"max-age",c.Pq);g=e(g,"samesite",c.mr);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=Ns(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(x){q=x;continue}r=!0;if(!Os(u,c.path)&&Ks(v,a,b,c.wc))return Ya(16)&&(Cs=u),0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return Os(n,c.path)?1:Ks(g,a,b,c.wc)?0:1}
function Ps(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");if(xs.includes("2")){var d;(d=id())==null||d.mark("2-"+ws.O.Mh+"-"+(zs["2"]||0))}var e=Ls(a,b,c);if(xs.includes("2")){var f="2-"+ws.O.mk+"-"+(zs["2"]||0),g={start:"2-"+ws.O.Mh+"-"+(zs["2"]||0),end:f},h;(h=id())==null||h.mark(f);var l,n,p=(n=(l=id())==null?void 0:l.measure(f,g))==null?void 0:n.duration;p!==void 0&&(zs["2"]=(zs["2"]||0)+1,ys["2"]=p+(ys["2"]||0))}return e}
function Js(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],l=b(h);l===c?d.push(h):f===void 0||l<f?(e=[h],f=l):l===f&&e.push(h)}return d.length>0?d:e}function Is(a,b,c){for(var d=[],e=Ds(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var l=g.shift();if(l){var n=l.split("-");d.push({Np:e[f],Op:g.join("."),Vp:Number(n[0])||1,Vq:Number(n[1])||1})}}}return d}function Ms(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var Qs=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,Rs=/(^|\.)doubleclick\.net$/i;function Os(a,b){return a!==void 0&&(Rs.test(window.document.location.hostname)||b==="/"&&Qs.test(a))}function Ss(a){if(!a)return 1;var b=a;Ya(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function Ts(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function Us(a,b){var c=""+Ss(a),d=Ts(b);d>1&&(c+="-"+d);return c}
var Gs=function(){return Bs(window)?window.document.cookie:""},Fs=function(a){return a&&Ya(7)?(Array.isArray(a)?a:[a]).every(function(b){return nn(b)&&ln(b)}):!0},Ns=function(){var a=Cs,b=[];a&&b.push(a);var c=window.document.location.hostname.split(".");if(c.length===4){var d=c[c.length-1];if(Number(d).toString()===d)return["none"]}for(var e=c.length-2;e>=0;e--){var f=c.slice(e).join(".");f!==a&&b.push(f)}var g=window.document.location.hostname;Rs.test(g)||Qs.test(g)||b.push("none");return b};function Vs(a){var b=Math.round(Math.random()*2147483647);return a?String(b^vs(a)&2147483647):String(b)}function Ws(a){return[Vs(a),Math.round(Hb()/1E3)].join(".")}function Xs(a,b,c,d,e){var f=Ss(b),g;return(g=Hs(a,f,Ts(c),d,e))==null?void 0:g.Op};var Ys;function Zs(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=$s,d=at,e=bt();if(!e.init){Rc(z,"mousedown",a);Rc(z,"keyup",a);Rc(z,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function ct(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};bt().decorators.push(f)}
function dt(a,b,c){for(var d=bt().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var l=g.domains,n=a,p=!!g.sameHost;if(l&&(p||n!==z.location.hostname))for(var q=0;q<l.length;q++)if(l[q]instanceof RegExp){if(l[q].test(n)){h=!0;break a}}else if(n.indexOf(l[q])>=0||p&&l[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Kb(e,g.callback())}}return e}
function bt(){var a=Ec("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var et=/(.*?)\*(.*?)\*(.*)/,ft=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,gt=/^(?:www\.|m\.|amp\.)+/,ht=/([^?#]+)(\?[^#]*)?(#.*)?/;function it(a){var b=ht.exec(a);if(b)return{Aj:b[1],query:b[2],fragment:b[3]}}function jt(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function kt(a,b){var c=[Ac.userAgent,(new Date).getTimezoneOffset(),Ac.userLanguage||Ac.language,Math.floor(Hb()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Ys)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Ys=d;for(var l=4294967295,n=0;n<c.length;n++)l=l>>>8^Ys[(l^c.charCodeAt(n))&255];return((l^-1)>>>0).toString(36)}
function lt(a){return function(b){var c=bl(w.location.href),d=c.search.replace("?",""),e=Tk(d,"_gl",!1,!0)||"";b.query=mt(e)||{};var f=Wk(c,"fragment"),g;var h=-1;if(Mb(f,"_gl="))h=4;else{var l=f.indexOf("&_gl=");l>0&&(h=l+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=mt(g||"")||{};a&&nt(c,d,f)}}function ot(a,b){var c=jt(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function nt(a,b,c){function d(g,h){var l=ot("_gl",g);l.length&&(l=h+l);return l}if(zc&&zc.replaceState){var e=jt("_gl");if(e.test(b)||e.test(c)){var f=Wk(a,"path");b=d(b,"?");c=d(c,"#");zc.replaceState({},"",""+f+b+c)}}}function pt(a,b){var c=lt(!!b),d=bt();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Kb(e,f.query),a&&Kb(e,f.fragment));return e}
var mt=function(a){try{var b=qt(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=jb(d[e+1]);c[f]=g}lb("TAGGING",6);return c}}catch(h){lb("TAGGING",8)}};function qt(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=et.exec(d);if(f){c=f;break a}d=Vk(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],l;a:{for(var n=g[2],p=0;p<b;++p)if(n===kt(h,p)){l=!0;break a}l=!1}if(l)return h;lb("TAGGING",7)}}}
function rt(a,b,c,d,e){function f(p){p=ot(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=it(c);if(!g)return"";var h=g.query||"",l=g.fragment||"",n=a+"="+b;d?l.substring(1).length!==0&&e||(l="#"+f(l.substring(1))):h="?"+f(h.substring(1));return""+g.Aj+h+l}
function st(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],x;for(x in n)if(n.hasOwnProperty(x)){var y=n[x];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(x),v.push(ib(String(y))))}var B=v.join("*");u=["1",kt(B),B].join("*");d?(Ya(3)||Ya(1)||!p)&&tt("_gl",u,a,p,q):ut("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=dt(b,1,d),f=dt(b,2,d),g=dt(b,4,d),h=dt(b,3,d);c(e,!1,!1);c(f,!0,!1);Ya(1)&&c(g,!0,!0);for(var l in h)h.hasOwnProperty(l)&&
vt(l,h[l],a)}function vt(a,b,c){c.tagName.toLowerCase()==="a"?ut(a,b,c):c.tagName.toLowerCase()==="form"&&tt(a,b,c)}function ut(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ya(4)||d)){var h=w.location.href,l=it(c.href),n=it(h);g=!(l&&n&&l.Aj===n.Aj&&l.query===n.query&&l.fragment)}f=g}if(f){var p=rt(a,b,c.href,d,e);pc.test(p)&&(c.href=p)}}
function tt(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=rt(a,b,f,d,e);pc.test(h)&&(c.action=h)}}else{for(var l=c.childNodes||[],n=!1,p=0;p<l.length;p++){var q=l[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=z.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function $s(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||st(e,e.hostname)}}catch(g){}}function at(a){try{var b=a.getAttribute("action");if(b){var c=Wk(bl(b),"host");st(a,c)}}catch(d){}}function wt(a,b,c,d){Zs();var e=c==="fragment"?2:1;d=!!d;ct(a,b,e,d,!1);e===2&&lb("TAGGING",23);d&&lb("TAGGING",24)}
function xt(a,b){Zs();ct(a,[Yk(w.location,"host",!0)],b,!0,!0)}function zt(){var a=z.location.hostname,b=ft.exec(z.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Vk(f[2])||"":Vk(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(gt,""),l=e.replace(gt,""),n;if(!(n=h===l)){var p="."+l;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function At(a,b){return a===!1?!1:a||b||zt()};var Bt=["1"],Ct={},Dt={};function Et(a,b){b=b===void 0?!0:b;var c=Ft(a.prefix);if(Ct[c])Gt(a);else if(Ht(c,a.path,a.domain)){var d=Dt[Ft(a.prefix)]||{id:void 0,Ah:void 0};b&&It(a,d.id,d.Ah);Gt(a)}else{var e=dl("auiddc");if(e)lb("TAGGING",17),Ct[c]=e;else if(b){var f=Ft(a.prefix),g=Ws();Jt(f,g,a);Ht(c,a.path,a.domain);Gt(a,!0)}}}
function Gt(a,b){if((b===void 0?0:b)&&qs(ms)){var c=is(!1);c.error!==0?lb("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,js(c)!==0&&lb("TAGGING",41)):lb("TAGGING",40):lb("TAGGING",39)}if(us(ms)&&rs([ms])[ms.ob]===-1){for(var d={},e=(d[ms.ob]=0,d),f=m(os),g=f.next();!g.done;g=f.next()){var h=g.value;h!==ms&&us(h)&&(e[h.ob]=0)}ss(e,a)}}
function It(a,b,c){var d=Ft(a.prefix),e=Ct[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(Hb()/1E3)));Jt(d,h,a,g*1E3)}}}}function Jt(a,b,c,d){var e;e=["1",Us(c.domain,c.path),b].join(".");var f=fs(c,d);f.wc=Kt();Ps(a,e,f)}function Ht(a,b,c){var d=Xs(a,b,c,Bt,Kt());if(!d)return!1;Lt(a,d);return!0}
function Lt(a,b){var c=b.split(".");c.length===5?(Ct[a]=c.slice(0,2).join("."),Dt[a]={id:c.slice(2,4).join("."),Ah:Number(c[4])||0}):c.length===3?Dt[a]={id:c.slice(0,2).join("."),Ah:Number(c[2])||0}:Ct[a]=b}function Ft(a){return(a||"_gcl")+"_au"}function Mt(a){function b(){ln(c)&&a()}var c=Kt();rn(function(){b();ln(c)||sn(b,c)},c)}
function Nt(a){var b=pt(!0),c=Ft(a.prefix);Mt(function(){var d=b[c];if(d){Lt(c,d);var e=Number(Ct[c].split(".")[1])*1E3;if(e){lb("TAGGING",16);var f=fs(a,e);f.wc=Kt();var g=["1",Us(a.domain,a.path),d].join(".");Ps(c,g,f)}}})}function Ot(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Xs(a,e.path,e.domain,Bt,Kt());h&&(g[a]=h);return g};Mt(function(){wt(f,b,c,d)})}function Kt(){return["ad_storage","ad_user_data"]};function Pt(a){for(var b=[],c=z.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Kj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function Qt(a,b){var c=Pt(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Kj]||(d[c[e].Kj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Kj].push(g)}}return d};var Rt={},St=(Rt.k={fa:/^[\w-]+$/},Rt.b={fa:/^[\w-]+$/,Ej:!0},Rt.i={fa:/^[1-9]\d*$/},Rt.h={fa:/^\d+$/},Rt.t={fa:/^[1-9]\d*$/},Rt.d={fa:/^[A-Za-z0-9_-]+$/},Rt.j={fa:/^\d+$/},Rt.u={fa:/^[1-9]\d*$/},Rt.l={fa:/^[01]$/},Rt.o={fa:/^[1-9]\d*$/},Rt.g={fa:/^[01]$/},Rt.s={fa:/^.+$/},Rt);var Tt={},Xt=(Tt[5]={Gh:{2:Ut},tj:"2",rh:["k","i","b","u"]},Tt[4]={Gh:{2:Ut,GCL:Vt},tj:"2",rh:["k","i","b"]},Tt[2]={Gh:{GS2:Ut,GS1:Wt},tj:"GS2",rh:"sogtjlhd".split("")},Tt);function Yt(a,b,c){var d=Xt[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Gh[e];if(f)return f(a,b)}}}
function Ut(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=Xt[b];if(f){for(var g=f.rh,h=m(d.split("$")),l=h.next();!l.done;l=h.next()){var n=l.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=St[p];r&&(r.Ej?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Zt(a,b,c){var d=Xt[b];if(d)return[d.tj,c||"1",$t(a,b)].join(".")}
function $t(a,b){var c=Xt[b];if(c){for(var d=[],e=m(c.rh),f=e.next();!f.done;f=e.next()){var g=f.value,h=St[g];if(h){var l=a[g];if(l!==void 0)if(h.Ej&&Array.isArray(l))for(var n=m(l),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+l))}}return d.join("$")}}function Vt(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Wt(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var au=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function bu(a,b,c){if(Xt[b]){for(var d=[],e=Ds(a,void 0,void 0,au.get(b)),f=m(e),g=f.next();!g.done;g=f.next()){var h=Yt(g.value,b,c);h&&d.push(cu(h))}return d}}
function du(a){var b=eu;if(Xt[2]){for(var c={},d=Es(a,void 0,void 0,au.get(2)),e=Object.keys(d).sort(),f=m(e),g=f.next();!g.done;g=f.next())for(var h=g.value,l=m(d[h]),n=l.next();!n.done;n=l.next()){var p=Yt(n.value,2,b);p&&(c[h]||(c[h]=[]),c[h].push(cu(p)))}return c}}function fu(a,b,c,d,e){d=d||{};var f=Us(d.domain,d.path),g=Zt(b,c,f);if(!g)return 1;var h=fs(d,e,void 0,au.get(c));return Ps(a,g,h)}function gu(a,b){var c=b.fa;return typeof c==="function"?c(a):c.test(a)}
function cu(a){for(var b=m(Object.keys(a)),c=b.next(),d={};!c.done;d={Wf:void 0},c=b.next()){var e=c.value,f=a[e];d.Wf=St[e];d.Wf?d.Wf.Ej?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return gu(h,g.Wf)}}(d)):void 0:typeof f==="string"&&gu(f,d.Wf)||(a[e]=void 0):a[e]=void 0}return a};var hu=function(){this.value=0};hu.prototype.set=function(a){return this.value|=1<<a};var iu=function(a,b){b<=0||(a.value|=1<<b-1)};hu.prototype.get=function(){return this.value};hu.prototype.clear=function(a){this.value&=~(1<<a)};hu.prototype.clearAll=function(){this.value=0};hu.prototype.equals=function(a){return this.value===a.value};function ju(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function ku(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function lu(){var a=String,b=w.location.hostname,c=w.location.pathname,d=b=Ub(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Ub(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(vs((""+b+e).toLowerCase()))};var mu={},nu=(mu.gclid=!0,mu.dclid=!0,mu.gbraid=!0,mu.wbraid=!0,mu),ou=/^\w+$/,pu=/^[\w-]+$/,qu={},ru=(qu.aw="_aw",qu.dc="_dc",qu.gf="_gf",qu.gp="_gp",qu.gs="_gs",qu.ha="_ha",qu.ag="_ag",qu.gb="_gb",qu),su=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,tu=/^www\.googleadservices\.com$/;function uu(){return["ad_storage","ad_user_data"]}function vu(a){return!Ya(7)||ln(a)}function wu(a,b){function c(){var d=vu(b);d&&a();return d}rn(function(){c()||sn(c,b)},b)}
function xu(a){return yu(a).map(function(b){return b.gclid})}function zu(a){return Au(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function Au(a){var b=Bu(a.prefix),c=Cu("gb",b),d=Cu("ag",b);if(!d||!c)return[];var e=function(h){return function(l){l.type=h;return l}},f=yu(c).map(e("gb")),g=Du(d).map(e("ag"));return f.concat(g).sort(function(h,l){return l.timestamp-h.timestamp})}
function Eu(a,b,c,d,e){var f=wb(a,function(g){return g.gclid===b});f?(f.timestamp<c&&(f.timestamp=c,f.Pc=e),f.labels=Fu(f.labels||[],d||[])):a.push({version:"2",gclid:b,timestamp:c,labels:d,Pc:e})}function Du(a){for(var b=bu(a,5)||[],c=[],d=m(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=Gu(f);h&&Eu(c,g.k,h,g.b||[],f.u)}return c.sort(function(l,n){return n.timestamp-l.timestamp})}
function yu(a){for(var b=[],c=Ds(a,z.cookie,void 0,uu()),d=m(c),e=d.next();!e.done;e=d.next()){var f=Hu(e.value);f!=null&&(f.Pc=void 0,f.za=new hu,f.ab=[1],Iu(b,f))}b.sort(function(g,h){return h.timestamp-g.timestamp});return Ju(b)}function Ku(a,b){for(var c=[],d=m(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=m(b),h=g.next();!h.done;h=g.next()){var l=h.value;c.includes(l)||c.push(l)}return c}
function Iu(a,b,c){c=c===void 0?!1:c;for(var d,e,f=m(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.za&&b.za&&h.za.equals(b.za)&&(e=h)}if(d){var l,n,p=(l=d.za)!=null?l:new hu,q=(n=b.za)!=null?n:new hu;p.value|=q.value;d.za=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Pc=b.Pc);d.labels=Ku(d.labels||[],b.labels||[]);d.ab=Ku(d.ab||[],b.ab||[])}else c&&e?na(Object,"assign").call(Object,e,b):a.push(b)}
function Lu(a){if(!a)return new hu;var b=new hu;if(a===1)return iu(b,2),iu(b,3),b;iu(b,a);return b}
function Mu(){var a=ks("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(pu))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new hu;typeof e==="number"?g=Lu(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],za:g,ab:[2]}}catch(h){return null}}
function Nu(){var a=ks("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(pu))return b;var f=new hu,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],za:f,ab:[2]});return b},[])}catch(b){return null}}
function Ou(a){for(var b=[],c=Ds(a,z.cookie,void 0,uu()),d=m(c),e=d.next();!e.done;e=d.next()){var f=Hu(e.value);f!=null&&(f.Pc=void 0,f.za=new hu,f.ab=[1],Iu(b,f))}var g=Mu();g&&(g.Pc=void 0,g.ab=g.ab||[2],Iu(b,g));if(Ya(14)){var h=Nu();if(h)for(var l=m(h),n=l.next();!n.done;n=l.next()){var p=n.value;p.Pc=void 0;p.ab=p.ab||[2];Iu(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return Ju(b)}
function Fu(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function Bu(a){return a&&typeof a==="string"&&a.match(ou)?a:"_gcl"}function Pu(a,b){if(a){var c={value:a,za:new hu};iu(c.za,b);return c}}
function Qu(a,b,c){var d=bl(a),e=Wk(d,"query",!1,void 0,"gclsrc"),f=Pu(Wk(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=Pu(Tk(g,"gclid",!1),3));e||(e=Tk(g,"gclsrc",!1))}return!f||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function Ru(a,b){var c=bl(a),d=Wk(c,"query",!1,void 0,"gclid"),e=Wk(c,"query",!1,void 0,"gclsrc"),f=Wk(c,"query",!1,void 0,"wbraid");f=Sb(f);var g=Wk(c,"query",!1,void 0,"gbraid"),h=Wk(c,"query",!1,void 0,"gad_source"),l=Wk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Tk(n,"gclid",!1);e=e||Tk(n,"gclsrc",!1);f=f||Tk(n,"wbraid",!1);g=g||Tk(n,"gbraid",!1);h=h||Tk(n,"gad_source",!1)}return Su(d,e,l,f,g,h)}function Tu(){return Ru(w.location.href,!0)}
function Su(a,b,c,d,e,f){var g={},h=function(l,n){g[n]||(g[n]=[]);g[n].push(l)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(pu))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&pu.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&pu.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&pu.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function Uu(a){for(var b=Tu(),c=!0,d=m(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Ru(w.document.referrer,!1),b.gad_source=void 0);Vu(b,!1,a)}
function Wu(a){Uu(a);var b=Qu(w.location.href,!0,!1);b.length||(b=Qu(w.document.referrer,!1,!0));a=a||{};Xu(a);if(b.length){var c=b[0],d=Hb(),e=fs(a,d,!0),f=uu(),g=function(){vu(f)&&e.expires!==void 0&&hs("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.za.get()},expires:Number(e.expires)})};rn(function(){g();vu(f)||sn(g,f)},f)}}
function Xu(a){var b;if(b=Ya(15)){var c=Yu();b=su.test(c)||tu.test(c)||Zu()}if(b){var d;a:{for(var e=bl(w.location.href),f=Uk(Wk(e,"query")),g=m(Object.keys(f)),h=g.next();!h.done;h=g.next()){var l=h.value;if(!nu[l]){var n=f[l][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=ju(n),r;if(q)c:{var t=q;if(t&&t.length!==0){var u=0;try{for(var v=10;u<t.length&&!(v--<=0);){var x=ku(t,u);if(x===void 0)break;var y=m(x),B=y.next().value,D=y.next().value,E=B,L=D,G=E&7;if(E>>3===16382){if(G!==0)break;
var N=ku(t,L);if(N===void 0)break;r=m(N).next().value===1;break c}var U;d:{var ia=void 0,S=t,aa=L;switch(G){case 0:U=(ia=ku(S,aa))==null?void 0:ia[1];break d;case 1:U=aa+8;break d;case 2:var ta=ku(S,aa);if(ta===void 0)break;var la=m(ta),ea=la.next().value;U=la.next().value+ea;break d;case 5:U=aa+4;break d}U=void 0}if(U===void 0||U>t.length||U<=u)break;u=U}}catch(ja){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var Z=d;Z&&$u(Z,7,a)}}
function $u(a,b,c){c=c||{};var d=Hb(),e=fs(c,d,!0),f=uu(),g=function(){if(vu(f)&&e.expires!==void 0){var h=Nu()||[];Iu(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),za:Lu(b)},!0);hs("gcl_aw",h.map(function(l){return{value:{value:l.gclid,creationTimeMs:l.timestamp,linkDecorationSources:l.za?l.za.get():0},expires:Number(l.expires)}}))}};rn(function(){vu(f)?g():sn(g,f)},f)}
function Vu(a,b,c,d,e){c=c||{};e=e||[];var f=Bu(c.prefix),g=d||Hb(),h=Math.round(g/1E3),l=uu(),n=!1,p=!1,q=function(){if(vu(l)){var r=fs(c,g,!0);r.wc=l;for(var t=function(U,ia){var S=Cu(U,f);S&&(Ps(S,ia,r),U!=="gb"&&(n=!0))},u=function(U){var ia=["GCL",h,U];e.length>0&&ia.push(e.join("."));return ia.join(".")},v=m(["aw","dc","gf","ha","gp"]),x=v.next();!x.done;x=v.next()){var y=x.value;a[y]&&t(y,u(a[y][0]))}if(!n&&a.gb){var B=a.gb[0],D=Cu("gb",f);!b&&yu(D).some(function(U){return U.gclid===B&&U.labels&&
U.labels.length>0})||t("gb",u(B))}}if(!p&&a.gbraid&&vu("ad_storage")&&(p=!0,!n)){var E=a.gbraid,L=Cu("ag",f);if(b||!Du(L).some(function(U){return U.gclid===E&&U.labels&&U.labels.length>0})){var G={},N=(G.k=E,G.i=""+h,G.b=e,G);fu(L,N,5,c,g)}}av(a,f,g,c)};rn(function(){q();vu(l)||sn(q,l)},l)}
function av(a,b,c,d){if(a.gad_source!==void 0&&vu("ad_storage")){var e=hd();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=Cu("gs",b);if(g){var h=Math.floor((Hb()-(gd()||0))/1E3),l,n=lu(),p={};l=(p.k=f,p.i=""+h,p.u=n,p);fu(g,l,5,d,c)}}}}
function bv(a,b){var c=pt(!0);wu(function(){for(var d=Bu(b.prefix),e=0;e<a.length;++e){var f=a[e];if(ru[f]!==void 0){var g=Cu(f,d),h=c[g];if(h){var l=Math.min(cv(h),Hb()),n;b:{for(var p=l,q=Ds(g,z.cookie,void 0,uu()),r=0;r<q.length;++r)if(cv(q[r])>p){n=!0;break b}n=!1}if(!n){var t=fs(b,l,!0);t.wc=uu();Ps(g,h,t)}}}}Vu(Su(c.gclid,c.gclsrc),!1,b)},uu())}
function dv(a){var b=["ag"],c=pt(!0),d=Bu(a.prefix);wu(function(){for(var e=0;e<b.length;++e){var f=Cu(b[e],d);if(f){var g=c[f];if(g){var h=Yt(g,5);if(h){var l=Gu(h);l||(l=Hb());var n;a:{for(var p=l,q=bu(f,5),r=0;r<q.length;++r)if(Gu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(l/1E3);fu(f,h,5,a,l)}}}}},["ad_storage"])}function Cu(a,b){var c=ru[a];if(c!==void 0)return b+c}function cv(a){return ev(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function Gu(a){return a?(Number(a.i)||0)*1E3:0}function Hu(a){var b=ev(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function ev(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!pu.test(a[2])?[]:a}
function fv(a,b,c,d,e){if(Array.isArray(b)&&Bs(w)){var f=Bu(e),g=function(){for(var h={},l=0;l<a.length;++l){var n=Cu(a[l],f);if(n){var p=Ds(n,z.cookie,void 0,uu());p.length&&(h[n]=p.sort()[p.length-1])}}return h};wu(function(){wt(g,b,c,d)},uu())}}
function gv(a,b,c,d){if(Array.isArray(a)&&Bs(w)){var e=["ag"],f=Bu(d),g=function(){for(var h={},l=0;l<e.length;++l){var n=Cu(e[l],f);if(!n)return{};var p=bu(n,5);if(p.length){var q=p.sort(function(r,t){return Gu(t)-Gu(r)})[0];h[n]=Zt(q,5)}}return h};wu(function(){wt(g,a,b,c)},["ad_storage"])}}function Ju(a){return a.filter(function(b){return pu.test(b.gclid)})}
function hv(a,b){if(Bs(w)){for(var c=Bu(b.prefix),d={},e=0;e<a.length;e++)ru[a[e]]&&(d[a[e]]=ru[a[e]]);wu(function(){Ab(d,function(f,g){var h=Ds(c+g,z.cookie,void 0,uu());h.sort(function(t,u){return cv(u)-cv(t)});if(h.length){var l=h[0],n=cv(l),p=ev(l.split(".")).length!==0?l.split(".").slice(3):[],q={},r;r=ev(l.split(".")).length!==0?l.split(".")[2]:void 0;q[f]=[r];Vu(q,!0,b,n,p)}})},uu())}}
function iv(a){var b=["ag"],c=["gbraid"];wu(function(){for(var d=Bu(a.prefix),e=0;e<b.length;++e){var f=Cu(b[e],d);if(!f)break;var g=bu(f,5);if(g.length){var h=g.sort(function(q,r){return Gu(r)-Gu(q)})[0],l=Gu(h),n=h.b,p={};p[c[e]]=h.k;Vu(p,!0,a,l,n)}}},["ad_storage"])}function jv(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function kv(a){function b(h,l,n){n&&(h[l]=n)}if(on()){var c=Tu(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:pt(!1)._gs);if(jv(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);xt(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);xt(function(){return g},1)}}}function Zu(){var a=bl(w.location.href);return Wk(a,"query",!1,void 0,"gad_source")}
function lv(a){if(!Ya(1))return null;var b=pt(!0).gad_source;if(b!=null)return w.location.hash="",b;if(Ya(2)){b=Zu();if(b!=null)return b;var c=Tu();if(jv(c,a))return"0"}return null}function mv(a){var b=lv(a);b!=null&&xt(function(){var c={};return c.gad_source=b,c},4)}function nv(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function ov(a,b,c,d){var e=[];c=c||{};if(!vu(uu()))return e;var f=yu(a),g=nv(e,f,b);if(g.length&&!d)for(var h=m(g),l=h.next();!l.done;l=h.next()){var n=l.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=fs(c,p,!0);r.wc=uu();Ps(a,q,r)}return e}
function pv(a,b){var c=[];b=b||{};var d=Au(b),e=nv(c,d,a);if(e.length)for(var f=m(e),g=f.next();!g.done;g=f.next()){var h=g.value,l=Bu(b.prefix),n=Cu(h.type,l);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var x={},y=(x.k=r,x.i=""+v,x.b=(t||[]).concat([a]),x);fu(n,y,5,b,u)}else if(h.type==="gb"){var B=[q,v,r].concat(t||[],[a]).join("."),D=fs(b,u,!0);D.wc=uu();Ps(n,B,D)}}return c}
function qv(a,b){var c=Bu(b),d=Cu(a,c);if(!d)return 0;var e;e=a==="ag"?Du(d):yu(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function rv(a){for(var b=0,c=m(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function sv(a){var b=Math.max(qv("aw",a),rv(vu(uu())?Qt():{})),c=Math.max(qv("gb",a),rv(vu(uu())?Qt("_gac_gb",!0):{}));c=Math.max(c,qv("ag",a));return c>b}
function Yu(){return z.referrer?Wk(bl(z.referrer),"host"):""};function Ev(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function Fv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function Gv(){return["ad_storage","ad_user_data"]}function Hv(a){if(F(38)&&!Hn(Dn.aa.Yl)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{Ev(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(Gn(Dn.aa.Yl,function(d){d.gclid&&$u(d.gclid,5,a)}),Fv(c)||O(178))})}catch(c){O(177)}};rn(function(){vu(Gv())?b():sn(b,Gv())},Gv())}};var Iv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function Jv(a){return a.data.action!=="gcl_transfer"?(O(173),!0):a.data.gadSource?a.data.gclid?!1:(O(181),!0):(O(180),!0)}
function Kv(a,b){if(F(a)){if(Hn(Dn.aa.ve))return O(176),Dn.aa.ve;if(Hn(Dn.aa.am))return O(170),Dn.aa.ve;var c=Yl();if(!c)O(171);else if(c.opener){var d=function(g){if(!Iv.includes(g.origin))O(172);else if(!Jv(g)){var h={gadSource:g.data.gadSource};F(229)&&(h.gclid=g.data.gclid);Gn(Dn.aa.ve,h);a===200&&g.data.gclid&&$u(String(g.data.gclid),6,b);var l;(l=g.stopImmediatePropagation)==null||l.call(g);mr(c,"message",d)}};if(lr(c,"message",d)){Gn(Dn.aa.am,!0);for(var e=m(Iv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},
f.value);O(174);return Dn.aa.ve}O(175)}}};function Uv(a){var b=P(a.D,K.m.Gc),c=P(a.D,K.m.Fc);b&&!c?(a.eventName!==K.m.oa&&a.eventName!==K.m.Xd&&O(131),a.isAborted=!0):!b&&c&&(O(132),a.isAborted=!0)}
function Vv(a){var b=Q(K.m.W)?Gp.pscdl:"denied";b!=null&&W(a,K.m.Hg,b)}function Wv(a){var b=vm(!0);W(a,K.m.Ec,b)}function Xv(a){as()&&W(a,K.m.he,1)}function Yv(){var a=z.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Vk(a.substring(0,b))===void 0;)b--;return Vk(a.substring(0,b))||""}function Zv(a){$v(a,Yp.Hf.Hn,P(a.D,K.m.yb))}function $v(a,b,c){Ov(a,K.m.sd)||W(a,K.m.sd,{});Ov(a,K.m.sd)[b]=c}function aw(a){V(a,R.A.Sf,bn.Z.Ga)}
function bw(a){var b=pb("GTAG_EVENT_FEATURE_CHANNEL");b&&(W(a,K.m.qf,b),nb())}function cw(a){var b=a.D.getMergedValues(K.m.Dc);b&&a.mergeHitDataForKey(K.m.Dc,b)}function dw(a,b){b=b===void 0?!1:b;var c=T(a,R.A.Rf),d=ew(a,"custom_event_accept_rules",!1)&&!b;if(c){var e=c.indexOf(a.target.destinationId)>=0,f=!0;F(240)&&T(a,R.A.Ll)&&(f=T(a,R.A.Za)===ol());e&&f?V(a,R.A.Hh,!0):(V(a,R.A.Hh,!1),d||(a.isAborted=!0));F(240)&&(a.hasBeenAccepted()?a.isAborted=!0:T(a,R.A.Hh)&&a.accept())}}
function fw(a){Tl&&(go=!0,a.eventName===K.m.oa?mo(a.D,a.target.id):(T(a,R.A.Oe)||(jo[a.target.id]=!0),Pp(T(a,R.A.Za))))}function gw(a){};var hw=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),iw=/^~?[\w-]+(?:\.~?[\w-]+)*$/,jw=/^\d+\.fls\.doubleclick\.net$/,kw=/;gac=([^;?]+)/,lw=/;gacgb=([^;?]+)/;
function mw(a,b){if(jw.test(z.location.host)){var c=z.location.href.match(b);return c&&c.length===2&&c[1].match(hw)?Vk(c[1])||"":""}for(var d=[],e=m(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],l=a[g],n=0;n<l.length;n++)h.push(l[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function nw(a,b,c){for(var d=vu(uu())?Qt("_gac_gb",!0):{},e=[],f=!1,g=m(Object.keys(d)),h=g.next();!h.done;h=g.next()){var l=h.value,n=ov("_gac_gb_"+l,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(l+":"+n.join(","))}return{hq:f?e.join(";"):"",gq:mw(d,lw)}}function ow(a){var b=z.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(iw)?b[1]:void 0}
function pw(a){var b={},c,d,e;jw.test(z.location.host)&&(c=ow("gclgs"),d=ow("gclst"),e=ow("gcllp"));if(c&&d&&e)b.cg=c,b.wh=d,b.uh=e;else{var f=Hb(),g=Du((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),l=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Pc});h.length>0&&l.length>0&&n.length>0&&(b.cg=h.join("."),b.wh=l.join("."),b.uh=n.join("."))}return b}
function qw(a,b,c,d){d=d===void 0?!1:d;if(jw.test(z.location.host)){var e=ow(c);if(e){if(d){var f=new hu;iu(f,2);iu(f,3);return e.split(".").map(function(h){return{gclid:h,za:f,ab:[1]}})}return e.split(".").map(function(h){return{gclid:h,za:new hu,ab:[1]}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?Ou(g):yu(g)}if(b==="wbraid")return yu((a||"_gcl")+"_gb");if(b==="braids")return Au({prefix:a})}return[]}function rw(a){return jw.test(z.location.host)?!(ow("gclaw")||ow("gac")):sv(a)}
function sw(a,b,c){var d;d=c?pv(a,b):ov((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function yw(){return Hp("dedupe_gclid",function(){return Ws()})};function Ew(a,b,c,d){var e=Nc(),f;if(e===1)a:{var g=ej(3);g=g.toLowerCase();for(var h="https://"+g,l="http://"+g,n=1,p=z.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(l)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==w.location.protocol?a:b)+c};function Rw(a,b){return arguments.length===1?Sw("set",a):Sw("set",a,b)}function Tw(a,b){return arguments.length===1?Sw("config",a):Sw("config",a,b)}function Uw(a,b,c){c=c||{};c[K.m.nd]=a;return Sw("event",b,c)}function Sw(){return arguments};var Xw=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,Yw=/^www.googleadservices.com$/;function Zw(a){a||(a=$w());return a.Ir?!1:a.yq||a.zq||a.Bq||a.Aq||a.Be||a.th||a.iq||a.Zb==="aw.ds"||F(235)&&a.Zb==="aw.dv"||a.mq?!0:!1}
function $w(){var a={},b=pt(!0);a.Ir=!!b._up;var c=Tu(),d=vv();a.yq=c.aw!==void 0;a.zq=c.dc!==void 0;a.Bq=c.wbraid!==void 0;a.Aq=c.gbraid!==void 0;a.Zb=typeof c.gclsrc==="string"?c.gclsrc:void 0;a.Be=d.Be;a.th=d.th;var e=z.referrer?Wk(bl(z.referrer),"host"):"";a.mq=Xw.test(e);a.iq=Yw.test(e);return a};var ax=function(){this.messages=[];this.C=[]};ax.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=na(Object,"assign").call(Object,{},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};ax.prototype.listen=function(a){this.C.push(a)};
ax.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};ax.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function bx(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[R.A.Za]=ej(6);cx().enqueue(a,b,c)}function dx(){var a=ex;cx().listen(a)}
function cx(){return Hp("mb",function(){return new ax})};var fx,gx=!1;function hx(){gx=!0;if(F(218)&&cj(52,!1))fx=productSettings,productSettings=void 0;else{}fx=fx||{}}function ix(a){gx||hx();return fx[a]};function jx(){var a=w.screen;return{width:a?a.width:0,height:a?a.height:0}}
function kx(a){if(z.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!w.getComputedStyle)return!0;var c=w.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=w.getComputedStyle(d,null))}return!1}
var mx=function(a){var b=lx(),c=b.height,d=b.width,e=a.getBoundingClientRect(),f=e.bottom-e.top,g=e.right-e.left;return f&&g?(1-Math.min((Math.max(0-e.left,0)+Math.max(e.right-d,0))/g,1))*(1-Math.min((Math.max(0-e.top,0)+Math.max(e.bottom-c,0))/f,1)):0},lx=function(){var a=z.body,b=z.documentElement||a&&a.parentElement,c,d;if(z.compatMode&&z.compatMode!=="BackCompat")c=b?b.clientHeight:0,d=b?b.clientWidth:0;else{var e=function(f,g){return f&&g?Math.min(f,g):Math.max(f,g)};c=e(b?b.clientHeight:0,a?
a.clientHeight:0);d=e(b?b.clientWidth:0,a?a.clientWidth:0)}return{width:d,height:c}};var px=function(a){if(nx){if(a>=0&&a<ox.length&&ox[a]){var b;(b=ox[a])==null||b.disconnect();ox[a]=void 0}}else w.clearInterval(a)},sx=function(a,b,c){for(var d=0;d<c.length;d++)c[d]>1?c[d]=1:c[d]<0&&(c[d]=0);if(nx){var e=!1;Tc(function(){e||qx(a,b,c)()});return rx(function(f){e=!0;for(var g={gg:0};g.gg<f.length;g={gg:g.gg},g.gg++)Tc(function(h){return function(){a(f[h.gg])}}(g))},
b,c)}return w.setInterval(qx(a,b,c),1E3)},qx=function(a,b,c){function d(h,l){var n={top:0,bottom:0,right:0,left:0,width:0,height:0},p={boundingClientRect:h.getBoundingClientRect(),intersectionRatio:l,intersectionRect:n,isIntersecting:l>0,rootBounds:n,target:h,time:Hb()};Tc(function(){a(p)})}for(var e=[],f=[],g=0;g<b.length;g++)e.push(0),f.push(-1);c.sort(function(h,l){return h-l});return function(){for(var h=0;h<b.length;h++){var l=mx(b[h]);if(l>e[h])for(;f[h]<c.length-1&&l>=c[f[h]+1];)d(b[h],l),
f[h]++;else if(l<e[h])for(;f[h]>=0&&l<=c[f[h]];)d(b[h],l),f[h]--;e[h]=l}}},rx=function(a,b,c){for(var d=new w.IntersectionObserver(a,{threshold:c}),e=0;e<b.length;e++)d.observe(b[e]);for(var f=0;f<ox.length;f++)if(!ox[f])return ox[f]=d,f;return ox.push(d)-1},ox=[],nx=!(!w.IntersectionObserver||!w.IntersectionObserverEntry);var mg;function wy(){var a=data.permissions||{};mg=new sg(ej(5),a)};var xy=Number(ij(57,''))||5,yy=Number(ij(58,''))||50,zy=xb();
var By=function(a,b){a&&(Ay("sid",a.targetId,b),Ay("cc",a.clientCount,b),Ay("tl",a.totalLifeMs,b),Ay("hc",a.heartbeatCount,b),Ay("cl",a.clientLifeMs,b))},Ay=function(a,b,c){b!=null&&c.push(a+"="+b)},Cy=function(){var a=z.referrer;if(a){var b;return Wk(bl(a),"host")===((b=w.location)==null?void 0:b.host)?1:2}return 0},Dy="https://"+ej(21)+"/a?",Fy=function(){this.U=Ey;this.M=0};Fy.prototype.H=function(a,b,c,d){var e=Cy(),f,g=[];f=w===w.top&&e!==0&&
b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&Ay("si",a.ig,g);Ay("m",0,g);Ay("iss",f,g);Ay("if",c,g);By(b,g);d&&Ay("fm",encodeURIComponent(d.substring(0,yy)),g);this.R(g);};Fy.prototype.C=function(a,b,c,d,e){var f=[];Ay("m",1,f);Ay("s",a,f);Ay("po",Cy(),f);b&&(Ay("st",b.state,f),Ay("si",b.ig,f),Ay("sm",b.og,f));By(c,f);Ay("c",d,f);e&&Ay("fm",encodeURIComponent(e.substring(0,yy)),f);this.R(f);
};Fy.prototype.R=function(a){a=a===void 0?[]:a;!Rl||this.M>=xy||(Ay("pid",zy,a),Ay("bc",++this.M,a),a.unshift("ctid="+ej(5)+"&t=s"),this.U(""+Dy+a.join("&")))};function Gy(a){return a.performance&&a.performance.now()||Date.now()}
var Hy=function(a,b){var c=w,d;var e=function(f,g,h){h=h===void 0?{Vm:function(){},Wm:function(){},Um:function(){},onFailure:function(){}}:h;this.up=f;this.C=g;this.M=h;this.ia=this.ma=this.heartbeatCount=this.tp=0;this.kh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.ig=Gy(this.C);this.og=Gy(this.C);this.U=10};e.prototype.init=function(){this.R(1);this.Ia()};e.prototype.getState=function(){return{state:this.state,
ig:Math.round(Gy(this.C)-this.ig),og:Math.round(Gy(this.C)-this.og)}};e.prototype.R=function(f){this.state!==f&&(this.state=f,this.og=Gy(this.C))};e.prototype.vm=function(){return String(this.tp++)};e.prototype.Ia=function(){var f=this;this.heartbeatCount++;this.Xa({type:0,clientId:this.id,requestId:this.vm(),maxDelay:this.mh()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.ia++,g.isDead||f.ia>20){var l=g.isDead&&g.failure.failureType;
f.U=l||10;f.R(4);f.rp();var n,p;(p=(n=f.M).Um)==null||p.call(n,{failureType:l||10,data:g.failure.data})}else f.R(3),f.Am();else{if(f.heartbeatCount>g.stats.heartbeatCount+20){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.M).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var t=f.state;f.R(2);if(t!==2)if(f.kh){var u,v;(v=(u=f.M).Wm)==null||v.call(u)}else{f.kh=!0;var x,y;(y=(x=f.M).Vm)==null||y.call(x)}f.ia=0;f.vp();f.Am()}}})};e.prototype.mh=function(){return this.state===2?
5E3:500};e.prototype.Am=function(){var f=this;this.C.setTimeout(function(){f.Ia()},Math.max(0,this.mh()-(Gy(this.C)-this.ma)))};e.prototype.zp=function(f,g,h){var l=this;this.Xa({type:1,clientId:this.id,requestId:this.vm(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,t={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},u,v;(v=(u=l.M).onFailure)==null||v.call(u,t);h(t)}})};e.prototype.Xa=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.U},g(f);else{var l=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var t=h.H[n];t&&h.Of(t,7)},(p=f.maxDelay)!=null?p:5E3),r={request:f,kn:g,bn:l,Oq:q};this.H[n]=r;l||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.ma=Gy(this.C);f.bn=!1;this.up(f.request)};e.prototype.vp=function(){for(var f=m(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.bn&&this.sendRequest(h)}};e.prototype.rp=function(){for(var f=
m(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Of(this.H[g.value],this.U)};e.prototype.Of=function(f,g){this.Kc(f);var h=f.request;h.failure={failureType:g};f.kn(h)};e.prototype.Kc=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.Oq)};e.prototype.wq=function(f){this.ma=Gy(this.C);var g=this.H[f.requestId];if(g)this.Kc(g),g.kn(f);else{var h,l;(l=(h=this.M).onFailure)==null||l.call(h,{failureType:14})}};d=new e(a,c,b);return d};var Iy;
var Jy=function(){Iy||(Iy=new Fy);return Iy},Ey=function(a){An(Cn(bn.Z.Jc),function(){Qc(a)})},Ky=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Ly=function(a){var b=a,c=gj.ma;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},My=function(a){var b=Hn(Dn.aa.im);return b&&b[a]},Ny=function(a,
b,c,d,e){var f=this;this.H=d;this.U=this.R=!1;this.ia=null;this.initTime=c;this.C=15;this.M=this.Qp(a);w.setTimeout(function(){f.initialize()},1E3);Tc(function(){f.Fq(a,b,e)})};k=Ny.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),ig:this.initTime,og:Math.round(Hb())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.M.zp(a,b,c)};k.getState=function(){return this.M.getState().state};k.Fq=function(a,b,c){var d=w.location.origin,e=this,
f=Oc();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,l=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?Ky(h):"",p;F(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Oc(l+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.ia=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.M.wq(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.Qp=function(a){var b=this,c=Hy(function(d){var e;(e=b.ia)==null||e.postMessage(d,a.origin)},{Vm:function(){b.R=!0;b.H.H(c.getState(),c.stats)},Wm:function(){},Um:function(d){b.R?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.U||this.M.init();this.U=!0};function Oy(){var a=pg(mg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Py(a,b){var c=Math.round(Hb());b=b===void 0?!1:b;var d=w.location.origin;if(!d||!Oy()||F(168))return;var e=Pk();F(238)&&(e=e&&!a);e&&(a=""+d+Ok()+"/_/service_worker");var f=Ly(a);if(f===null||My(f.origin))return;if(!Bc()){Jy().H(void 0,void 0,6);return}var g=new Ny(f,!!a,c||Math.round(Hb()),Jy(),b);In(Dn.aa.im,{})[f.origin]=g;}
var Qy=function(a,b,c,d){var e;if((e=My(a))==null||!e.delegate){var f=Bc()?16:6;Jy().C(f,void 0,void 0,b.commandType);d({failureType:f});return}My(a).delegate(b,c,d);};
function Ry(a,b,c,d,e){var f=Ly();if(f===null){d(Bc()?16:6);return}var g,h=(g=My(f.origin))==null?void 0:g.initTime,l=Math.round(Hb()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?l-h:void 0}};e&&(n.params.encryptionKeyString=e);Qy(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Sy(a,b,c,d){var e=Ly(a);if(e===null){d("_is_sw=f"+(Bc()?16:6)+"te");return}var f=b?1:0,g=Math.round(Hb()),h,l=(h=My(e.origin))==null?void 0:h.initTime,n=l?g-l:void 0,p=!1;F(169)&&(p=!0);Qy(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:w.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=My(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function Ty(a){if(F(10))return;var b=Pk()||gj.C||!!Ll(a.D);F(245)&&(b=gj.C||!!Ll(a.D));if(b||F(168))return;Py(void 0,F(131));};var pz=function(a){this.C=1;this.C>0||(this.C=1);this.onSuccess=a.D.onSuccess},qz=function(a,b){return Rb(function(){a.C--;if(sb(a.onSuccess)&&a.C===0)a.onSuccess()},b>0?b:1)};function tz(a,b){var c=!!Pk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?Ok()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?F(90)&&yo()?rz():""+Ok()+"/ag/g/c":rz();case 16:return c?F(90)&&yo()?sz():""+Ok()+"/ga/g/c":sz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?
Ok()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?Ok()+"/d/pagead/form-data":F(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Ap+".fls.doubleclick.net/activityi;";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?Ok()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";
case 8:return"https://www.google.com/pagead/1p-conversion";case 63:return"https://www.googleadservices.com/pagead/conversion";case 64:return c?Ok()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";case 65:return"https://www.google.com/pagead/1p-conversion";case 22:return c?Ok()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?Ok()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return c?Ok()+
"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";case 55:return c?Ok()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return F(205)?"https://www.google.com/measurement/conversion/":c?Ok()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return c?Ok()+"/d/ccm/form-data":F(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 62:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:sc(a,"Unknown endpoint")}};function uz(a,b){b&&Ab(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};function vz(a,b){var c=Ov(a,K.m.Dc);if(c&&typeof c==="object")for(var d=m(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value,g=c[f];g!==void 0&&(g===null&&(g=""),b["gap."+f]=String(g))}};var Dz={};Dz.O=ws.O;var Ez={es:"L",mp:"S",As:"Y",Kr:"B",Ur:"E",Yr:"I",xs:"TC",Xr:"HTC"},Fz={mp:"S",Tr:"V",Nr:"E",ws:"tag"},Gz={},Hz=(Gz[Dz.O.Si]="6",Gz[Dz.O.Ti]="5",Gz[Dz.O.Ri]="7",Gz);function Iz(){function a(c,d){var e=pb(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var Jz=!1;
function bA(a){}function cA(a){}
function dA(){}function eA(a){}
function fA(a){}function gA(a){}
function hA(){}
function iA(a,b){}
function jA(a,b,c){}
function kA(){};var lA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function mA(a,b,c,d,e,f,g,h){var l=na(Object,"assign").call(Object,{},lA);c&&(l.body=c,l.method="POST");na(Object,"assign").call(Object,l,e);h==null||Lm(h);w.fetch(b,l).then(function(n){h==null||Mm(h);if(!n.ok)g==null||g();else if(n.body){var p=n.body.getReader(),q=new TextDecoder;return new Promise(function(r){function t(){p.read().then(function(u){var v;v=u.done;var x=q.decode(u.value,{stream:!v});nA(d,x);v?(f==null||f(),r()):t()}).catch(function(){r()})}t()})}}).catch(function(){h==null||Mm(h);
g?g():F(128)&&(b+="&_z=retryFetch",c?Um(a,b,c):Tm(a,b))})};var oA=function(a){this.R=a;this.C=""},pA=function(a,b){a.H=b;return a},qA=function(a,b){a.M=b;return a},nA=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=m(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(l){}e=void 0}rA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},sA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};rA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},rA=function(a,b){b&&(tA(b.send_pixel,b.options,a.R),tA(b.create_iframe,b.options,a.H),tA(b.fetch,b.options,a.M))};function uA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function tA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=td(b)?b:{},f=m(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var vA=function(a,b){this.Sq=a;this.timeoutMs=b;this.Sa=void 0},Lm=function(a){a.Sa||(a.Sa=setTimeout(function(){a.Sq();a.Sa=void 0},a.timeoutMs))},Mm=function(a){a.Sa&&(clearTimeout(a.Sa),a.Sa=void 0)};var dB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),eB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},fB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},gB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function hB(){var a=ik("gtm.allowlist")||ik("gtm.whitelist");a&&O(9);Gk&&!F(212)?a=["google","gtagfl","lcl","zone","cmpPartners"]:F(212)&&(a=void 0);dB.test(w.location&&w.location.hostname)&&(Gk?O(116):(O(117),iB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Lb(Eb(a),eB),c=ik("gtm.blocklist")||ik("gtm.blacklist");c||(c=ik("tagTypeBlacklist"))&&O(3);c?O(8):c=[];dB.test(w.location&&w.location.hostname)&&(c=Eb(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));Eb(c).indexOf("google")>=0&&O(2);var d=c&&Lb(Eb(c),fB),e={};return function(f){var g=f&&f[of.Qa];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=Mk[g]||[],l=!0;if(a){var n;if(n=l)a:{if(b.indexOf(g)<0){if(Gk&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){O(11);n=!1;break a}}else{n=!1;break a}}n=!0}l=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=yb(d,h||[]);t&&
O(10);q=t}}var u=!l||q;!u&&(h.indexOf("sandboxedScripts")===-1?0:Gk&&h.indexOf("cmpPartners")>=0?!jB():b&&b.indexOf("sandboxedScripts")!==-1?0:yb(d,gB))&&(u=!0);return e[g]=u}}function jB(){var a=pg(mg.C,ej(5),function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var iB=!1;iB=!0;F(218)&&(iB=cj(48,iB));function kB(a,b,c,d,e){if(!xl(a)){d.loadExperiments=yk();zl(a,d,e);var f=lB(a),g=function(){jl().container[a]&&(jl().container[a].state=3);mB()},h={destinationId:a,endpoint:0};if(Pk())Xm(h,Ok()+"/"+f,void 0,g);else{var l=Mb(a,"GTM-"),n=Kl(),p=c?"/gtag/js":"/gtm.js",q=Jl(b,p+f);if(!q){var r=ej(3)+p;n&&Dc&&l&&(r=Dc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=Ew("https://","http://",r+f)}Xm(h,q,void 0,g)}}}function mB(){Al()||Ab(Bl(),function(a,b){nB(a,b.transportUrl,b.context);O(92)})}
function nB(a,b,c,d){if(!yl(a))if(c.loadExperiments||(c.loadExperiments=yk()),Al()){var e;(e=jl().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:sl()});jl().destination[a].state=0;il({ctid:a,isDestination:!0},d);O(91)}else{var f;(f=jl().destination)[a]!=null||(f[a]={context:c,state:1,parent:sl()});jl().destination[a].state=1;il({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(Pk())Xm(g,Ok()+("/gtd"+lB(a,!0)));else{var h="/gtag/destination"+lB(a,!0),l=Jl(b,
h);l||(l=Ew("https://","http://",ej(3)+h));Xm(g,l)}}}function lB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a),d=ej(19);d!=="dataLayer"&&(c+="&l="+d);if(!Mb(a,"GTM-")||b)c=F(130)?c+(Pk()?"&sc=1":"&cx=c"):c+"&cx=c";var e=c,f,g={hn:hj(15),ln:ej(14)};f=nf(g);c=e+("&gtm="+f);Kl()&&(c+="&sign="+Ak.Pi);var h=gj.M;h===1?c+="&fps=fc":h===2&&(c+="&fps=fe");return c};var oB=function(){this.H=0;this.C={}};oB.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Ie:c};return d};oB.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var qB=function(a,b){var c=[];Ab(pB.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Ie===void 0||b.indexOf(e.Ie)>=0)&&c.push(e.listener)});return c};function rB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:ej(5)}};function sB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var uB=function(a,b){this.C=!1;this.R=[];this.eventData={tags:[]};this.U=!1;this.H=this.M=0;tB(this,a,b)},vB=function(a,b,c,d){if(Ck.hasOwnProperty(b)||b==="__zone")return-1;var e={};td(d)&&(e=ud(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},wB=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},xB=function(a){if(!a.C){for(var b=a.R,c=0;c<b.length;c++)b[c]();a.C=!0;a.R.length=0}},tB=function(a,b,c){b!==void 0&&a.Uf(b);c&&w.setTimeout(function(){xB(a)},
Number(c))};uB.prototype.Uf=function(a){var b=this,c=Jb(function(){Tc(function(){a(ej(5),b.eventData)})});this.C?c():this.R.push(c)};var yB=function(a){a.M++;return Jb(function(){a.H++;a.U&&a.H>=a.M&&xB(a)})},zB=function(a){a.U=!0;a.H>=a.M&&xB(a)};var AB={};function BB(){return w[CB()]}
function CB(){return w.GoogleAnalyticsObject||"ga"}function FB(){var a=ej(5);}
function GB(a,b){return function(){var c=BB(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),l=g.indexOf("&tid="+b)<0;l&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);l&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var MB=["es","1"],NB={},OB={};function PB(a,b){if(Rl){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";NB[a]=[["e",c],["eid",a]];Sq(a)}}function QB(a){var b=a.eventId,c=a.Pd;if(!NB[b])return[];var d=[];OB[b]||d.push(MB);d.push.apply(d,Ba(NB[b]));c&&(OB[b]=!0);return d};var RB={},SB={},TB={};function UB(a,b,c,d){Rl&&F(120)&&((d===void 0?0:d)?(TB[b]=TB[b]||0,++TB[b]):c!==void 0?(SB[a]=SB[a]||{},SB[a][b]=Math.round(c)):(RB[a]=RB[a]||{},RB[a][b]=(RB[a][b]||0)+1))}function VB(a){var b=a.eventId,c=a.Pd,d=RB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete RB[b];return e.length?[["md",e.join(".")]]:[]}
function WB(a){var b=a.eventId,c=a.Pd,d=SB[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete SB[b];return e.length?[["mtd",e.join(".")]]:[]}function XB(){for(var a=[],b=m(Object.keys(TB)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+TB[d])}return a.length?[["mec",a.join(".")]]:[]};var YB={},ZB={};function $B(a,b,c){if(Rl&&b){var d=Ol(b);YB[a]=YB[a]||[];YB[a].push(c+d);var e=b[of.Qa];if(!e)throw Error("Error: No function name given for function call.");var f=(Qf[e]?"1":"2")+d;ZB[a]=ZB[a]||[];ZB[a].push(f);Sq(a)}}function aC(a){var b=a.eventId,c=a.Pd,d=[],e=YB[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=ZB[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete YB[b],delete ZB[b]);return d};function bC(a,b,c){c=c===void 0?!1:c;cC().addRestriction(0,a,b,c)}function dC(a,b,c){c=c===void 0?!1:c;cC().addRestriction(1,a,b,c)}function eC(){var a=ol();return cC().getRestrictions(1,a)}var fC=function(){this.container={};this.C={}},gC=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
fC.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=gC(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
fC.prototype.getRestrictions=function(a,b){var c=gC(this,b);if(a===0){var d,e;return[].concat(Ba((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),Ba((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(Ba((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),Ba((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
fC.prototype.getExternalRestrictions=function(a,b){var c=gC(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};fC.prototype.removeExternalRestrictions=function(a){var b=gC(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function cC(){return Hp("r",function(){return new fC})};function hC(a,b,c,d){var e=Of[a],f=iC(a,b,c,d);if(!f)return null;var g=bg(e[of.jm],c,[]);if(g&&g.length){var h=g[0];f=hC(h.index,{onSuccess:f,onFailure:h.Km===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function iC(a,b,c,d){function e(){function x(){no(3);var N=Hb()-G;rB(1,a,Of[a][of.ah]);$B(c.id,f,"7");wB(c.Lc,E,"exception",N);F(109)&&jA(c,f,Dz.O.Ri);L||(L=!0,h())}if(f[of.ep])h();else{var y=ag(f,c,[]),B=y[of.En];if(B!=null)for(var D=0;D<B.length;D++)if(!Q(B[D])){h();return}var E=vB(c.Lc,String(f[of.Qa]),Number(f[of.oh]),y[of.METADATA]),L=!1;y.vtp_gtmOnSuccess=function(){if(!L){L=!0;var N=Hb()-G;$B(c.id,Of[a],"5");wB(c.Lc,E,"success",N);F(109)&&jA(c,f,Dz.O.Ti);g()}};y.vtp_gtmOnFailure=function(){if(!L){L=
!0;var N=Hb()-G;$B(c.id,Of[a],"6");wB(c.Lc,E,"failure",N);F(109)&&jA(c,f,Dz.O.Si);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);$B(c.id,f,"1");F(109)&&iA(c,f);var G=Hb();try{cg(y,{event:c,index:a,type:1})}catch(N){x(N)}F(109)&&jA(c,f,Dz.O.wm)}}var f=Of[a],g=b.onSuccess,h=b.onFailure,l=b.terminate;if(c.isBlocked(f))return null;var n=bg(f[of.xm],c,[]);if(n&&n.length){var p=n[0],q=hC(p.index,{onSuccess:g,onFailure:h,terminate:l},c,d);if(!q)return null;
g=q;h=p.Km===2?l:q}if(f[of.Zl]||f[of.hp]){var r=f[of.Zl]?Pf:c.Cr,t=g,u=h;if(!r[a]){var v=jC(a,r,Jb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function jC(a,b,c){var d=[],e=[];b[a]=kC(d,e,c);return{onSuccess:function(){b[a]=lC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=mC;for(var f=0;f<e.length;f++)e[f]()}}}function kC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function lC(a){a()}function mC(a,b){b()};var pC=function(a,b){for(var c=[],d=0;d<Of.length;d++)if(a[d]){var e=Of[d];var f=yB(b.Lc);try{var g=hC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[of.Qa];if(!h)throw Error("Error: No function name given for function call.");var l=Qf[h];c.push({qn:d,priorityOverride:(l?l.priorityOverride||0:0)||sB(e[of.Qa],1)||0,execute:g})}else nC(d,b),f()}catch(p){f()}}c.sort(oC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function qC(a,b){if(!pB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=qB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=yB(b);try{d[e](a,f)}catch(g){f()}}return!0}function oC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.qn,h=b.qn;f=g>h?1:g<h?-1:0}return f}
function nC(a,b){if(Rl){var c=function(d){var e=b.isBlocked(Of[d])?"3":"4",f=bg(Of[d][of.jm],b,[]);f&&f.length&&c(f[0].index);$B(b.id,Of[d],e);var g=bg(Of[d][of.xm],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var rC=!1,pB;function sC(){pB||(pB=new oB);return pB}
function tC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(F(109)){}if(d==="gtm.js"){if(rC)return!1;rC=!0}var e=!1,f=eC(),g=ud(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}PB(b,d);var h=a.eventCallback,l=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:uC(g,e),Cr:[],logMacroError:function(t,u,v){O(6);no(0);rB(2,u,v)},cachedModelValues:vC(),Lc:new uB(function(){if(F(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,
0))},l),originalEventData:g};F(120)&&Rl&&(n.reportMacroDiscrepancy=UB);F(109)&&fA(n.id);var p=hg(n);F(109)&&gA(n.id);e&&(p=wC(p));F(109)&&eA(b);var q=pC(p,n),r=qC(a,n.Lc);zB(n.Lc);d!=="gtm.js"&&d!=="gtm.sync"||FB();return xC(p,q)||r}function vC(){var a={};a.event=nk("event",1);a.ecommerce=nk("ecommerce",1);a.gtm=nk("gtm");a.eventModel=nk("eventModel");return a}
function uC(a,b){var c=hB();return function(d){if(c(d))return!0;var e=d&&d[of.Qa];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=ol();f=cC().getRestrictions(0,g);var h=a;b&&(h=ud(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var l=Mk[e]||[],n=m(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:l,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function wC(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Of[c][of.Qa]);if(Bk[d]||Of[c][of.jp]!==void 0||sB(d,2))b[c]=!0}return b}function xC(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Of[c]&&!Ck[String(Of[c][of.Qa])])return!0;return!1};function yC(){sC().addListener("gtm.init",function(a,b){gj.ia=!0;Zn();b()})};var zC=!1,AC=0,BC=[];function CC(a){if(!zC){var b=z.createEventObject,c=z.readyState==="complete",d=z.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){zC=!0;for(var e=0;e<BC.length;e++)Tc(BC[e])}BC.push=function(){for(var f=Fa.apply(0,arguments),g=0;g<f.length;g++)Tc(f[g]);return 0}}}function DC(){if(!zC&&AC<140){AC++;try{var a,b;(b=(a=z.documentElement).doScroll)==null||b.call(a,"left");CC()}catch(c){w.setTimeout(DC,50)}}}
function EC(){var a=w;zC=!1;AC=0;if(z.readyState==="interactive"&&!z.createEventObject||z.readyState==="complete")CC();else{Rc(z,"DOMContentLoaded",CC);Rc(z,"readystatechange",CC);if(z.createEventObject&&z.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&DC()}Rc(a,"load",CC)}}function FC(a){zC?a():BC.push(a)};var GC={},HC={};function IC(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={Cj:void 0,jj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.Cj=aq(g,b),e.Cj){var h=nl();wb(h,function(r){return function(t){return r.Cj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var l=GC[g]||[];e.jj={};l.forEach(function(r){return function(t){r.jj[t]=!0}}(e));for(var n=pl(),p=0;p<n.length;p++)if(e.jj[n[p]]){c=c.concat(nl());break}var q=HC[g]||[];q.length&&(c=c.concat(q))}}return{wj:c,Qq:d}}
function JC(a){Ab(GC,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function KC(a){Ab(HC,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var LC=!1,MC=!1;function NC(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=ud(b,null),b[K.m.hf]&&(d.eventCallback=b[K.m.hf]),b[K.m.Ng]&&(d.eventTimeout=b[K.m.Ng]));return d}function OC(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Mp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function PC(a,b){var c=a&&a[K.m.nd];c===void 0&&(c=ik(K.m.nd,2),c===void 0&&(c="default"));if(tb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?tb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=IC(d,b.isGtmEvent),f=e.wj,g=e.Qq;if(g.length)for(var h=QC(a),l=0;l<g.length;l++){var n=aq(g[l],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=jl().destination[q];r&&r.state===0||nB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{wj:bq(f,b.isGtmEvent),
Dp:bq(t,b.isGtmEvent)}}}var RC=void 0,SC=void 0;function TC(a,b,c){var d=ud(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&O(136);var e=ud(b,null);ud(c,e);bx(Tw(pl()[0],e),a.eventId,d)}function QC(a){for(var b=m([K.m.od,K.m.rc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||$q.C[d];if(e)return e}}
var UC={config:function(a,b){var c=OC(a,b);if(!(a.length<2)&&tb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!td(a[2])||a.length>3)return;d=a[2]}var e=aq(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!dj(7)){var l=rl(sl());if(Cl(l)){var n=l.parent,p=n.isDestination;h={Uq:rl(n),Nq:p};break a}}h=void 0}var q=h;q&&(f=q.Uq,g=q.Nq);PB(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?nl().indexOf(r)===-1:pl().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[K.m.Gc]){var u=QC(d);if(t)nB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;RC?TC(b,v,RC):SC||(SC=ud(v,null))}else kB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(O(128),g&&O(130),b.inheritParentConfig)){var x;var y=d;SC?(TC(b,SC,y),x=!1):(!y[K.m.pd]&&dj(11)&&RC||(RC=ud(y,null)),x=!0);x&&f.containers&&f.containers.join(",");return}Tl&&(Op===1&&(On.mcc=!1),Op=2);if(dj(11)&&!t&&!d[K.m.pd]){var B=MC;MC=!0;if(B)return}LC||O(43);if(!b.noTargetGroup)if(t){KC(e.id);
var D=e.id,E=d[K.m.Qg]||"default";E=String(E).split(",");for(var L=0;L<E.length;L++){var G=HC[E[L]]||[];HC[E[L]]=G;G.indexOf(D)<0&&G.push(D)}}else{JC(e.id);var N=e.id,U=d[K.m.Qg]||"default";U=U.toString().split(",");for(var ia=0;ia<U.length;ia++){var S=GC[U[ia]]||[];GC[U[ia]]=S;S.indexOf(N)<0&&S.push(N)}}delete d[K.m.Qg];var aa=b.eventMetadata||{};aa.hasOwnProperty(R.A.vd)||(aa[R.A.vd]=!b.fromContainerExecution);b.eventMetadata=aa;delete d[K.m.hf];for(var ta=t?[e.id]:nl(),la=0;la<ta.length;la++){var ea=
d,Z=ta[la],ja=ud(b,null),ya=aq(Z,ja.isGtmEvent);ya&&$q.push("config",[ea],ya,ja)}}}}},consent:function(a,b){if(a.length===3){O(39);var c=OC(a,b),d=a[1],e={},f=Po(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===K.m.tg?Array.isArray(h)?NaN:Number(h):g===K.m.jc?(Array.isArray(h)?h:[h]).map(Qo):Ro(h)}b.fromContainerExecution||(e[K.m.X]&&O(139),e[K.m.Ka]&&O(140));d==="default"?sp(e):d==="update"?up(e,c):d==="declare"&&b.fromContainerExecution&&rp(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&tb(c)){var d=void 0;if(a.length>2){if(!td(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=NC(c,d),f=OC(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var l=PC(d,b);if(l){for(var n=l.wj,p=l.Dp,q=p.map(function(N){return N.id}),r=p.map(function(N){return N.destinationId}),t=n.map(function(N){return N.id}),u=m(nl()),v=u.next();!v.done;v=u.next()){var x=v.value;r.indexOf(x)<0&&t.push(x)}PB(g,
c);for(var y=m(t),B=y.next();!B.done;B=y.next()){var D=B.value,E=ud(b,null),L=ud(d,null);delete L[K.m.hf];var G=E.eventMetadata||{};G.hasOwnProperty(R.A.vd)||(G[R.A.vd]=!E.fromContainerExecution);G[R.A.Mi]=q.slice();G[R.A.Rf]=r.slice();E.eventMetadata=G;ar(c,L,D,E)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[K.m.nd]=q.join(","):delete e.eventModel[K.m.nd];LC||O(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[R.A.tm]&&(b.noGtmEvent=!0);e.eventModel[K.m.Fc]&&(b.noGtmEvent=!0);
return b.noGtmEvent?void 0:e}}},get:function(a,b){O(53);if(a.length===4&&tb(a[1])&&tb(a[2])&&sb(a[3])){var c=aq(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){LC||O(43);var f=QC();if(wb(nl(),function(h){return c.destinationId===h})){OC(a,b);var g={};ud((g[K.m.lf]=d,g[K.m.kf]=e,g),null);br(d,function(h){Tc(function(){e(h)})},c.id,b)}else nB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){LC=!0;var c=OC(a,b),d=c.eventId,
e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&tb(a[1])&&sb(a[2])){if(ng(a[1],a[2]),O(74),a[1]==="all"){O(75);var b=!1;try{b=a[2](ej(5),"unknown",{})}catch(c){}b||O(76)}}else O(73)},set:function(a,b){var c=void 0;a.length===2&&td(a[1])?c=ud(a[1],null):a.length===3&&tb(a[1])&&(c={},td(a[2])||Array.isArray(a[2])?c[a[1]]=ud(a[2],null):c[a[1]]=a[2]);if(c){var d=OC(a,b),e=d.eventId,f=d.priorityId;
ud(c,null);ej(5);var g=ud(c,null);$q.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},VC={policy:!0};var XC=function(a){if(WC(a))return a;this.value=a};XC.prototype.getUntrustedMessageValue=function(){return this.value};var WC=function(a){return!a||rd(a)!=="object"||td(a)?!1:"getUntrustedMessageValue"in a};XC.prototype.getUntrustedMessageValue=XC.prototype.getUntrustedMessageValue;var YC=!1,ZC=[];function $C(){if(!YC){YC=!0;for(var a=0;a<ZC.length;a++)Tc(ZC[a])}}function aD(a){YC?Tc(a):ZC.push(a)};var bD=0,cD={},dD=[],eD=[],fD=!1,gD=!1;function hD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function iD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return jD(a)}function kD(a,b){if(!ub(b)||b<0)b=0;var c=Lp(),d=0,e=!1,f=void 0;f=w.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(w.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function lD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(Bb(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function mD(){var a;if(eD.length)a=eD.shift();else if(dD.length)a=dD.shift();else return;var b;var c=a;if(fD||!lD(c.message))b=c;else{fD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Mp(),f=Mp(),c.message["gtm.uniqueEventId"]=Mp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},l={},n={message:(l.event="gtm.init",l["gtm.uniqueEventId"]=f,l),messageContext:{eventId:f}};dD.unshift(n,c);b=h}return b}
function nD(){for(var a=!1,b;!gD&&(b=mD());){gD=!0;delete fk.eventModel;hk();var c=b,d=c.message,e=c.messageContext;if(d==null)gD=!1;else{e.fromContainerExecution&&mk();try{if(sb(d))try{d.call(jk)}catch(L){}else if(Array.isArray(d)){if(tb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),l=ik(f.join("."),2);if(l!=null)try{l[g].apply(l,h)}catch(L){}}}else{var n=void 0;if(Bb(d))a:{if(d.length&&tb(d[0])){var p=UC[d[0]];if(p&&(!e.fromContainerExecution||!VC[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;if(n){var q;for(var r=n,t=r._clear||e.overwriteModelFields,u=m(Object.keys(r)),v=u.next();!v.done;v=u.next()){var x=v.value;x!=="_clear"&&(t&&lk(x),lk(x,r[x]))}Jk||(Jk=r["gtm.start"]);var y=r["gtm.uniqueEventId"];r.event?(typeof y!=="number"&&(y=Mp(),r["gtm.uniqueEventId"]=y,lk("gtm.uniqueEventId",y)),q=tC(r)):q=!1;a=q||a}}}finally{e.fromContainerExecution&&hk(!0);var B=d["gtm.uniqueEventId"];if(typeof B==="number"){for(var D=cD[String(B)]||[],E=0;E<D.length;E++)eD.push(oD(D[E]));D.length&&eD.sort(hD);
delete cD[String(B)];B>bD&&(bD=B)}gD=!1}}}return!a}
function pD(){if(F(109)){var a=!gj.R;}var c=nD();if(F(109)){}try{var e=w[ej(19)],f=ej(5),g=e.hide;if(g&&g[f]!==void 0&&
g.end){g[f]=!1;var h=!0,l;for(l in g)if(g.hasOwnProperty(l)&&g[l]===!0){h=!1;break}h&&(g.end(),g.end=null)}}catch(n){ej(5)}return c}function ex(a){if(bD<a.notBeforeEventId){var b=String(a.notBeforeEventId);cD[b]=cD[b]||[];cD[b].push(a)}else eD.push(oD(a)),eD.sort(hD),Tc(function(){gD||nD()})}function oD(a){return{message:a.message,messageContext:a.messageContext}}
function qD(){function a(f){var g={};if(WC(f)){var h=f;f=WC(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=Ec(ej(19),[]),c=Kp();c.pruned===!0&&O(83);cD=cx().get();dx();FC(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});aD(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(Gp.SANDBOXED_JS_SEMAPHORE>0){f=
[];for(var g=0;g<arguments.length;g++)f[g]=new XC(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});dD.push.apply(dD,h);var l=d.apply(b,f),n=Math.max(100,Number(ij(1,'1000'))||300);if(this.length>n)for(O(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof l!=="boolean"||l;return nD()&&p};var e=b.slice(0).map(function(f){return a(f)});dD.push.apply(dD,e);if(!gj.R){if(F(109)){}Tc(pD)}}var jD=function(a){return w[ej(19)].push(a)};function rD(a){jD(a)};function sD(){var a,b=bl(w.location.href);(a=b.hostname+b.pathname)&&Vn("dl",encodeURIComponent(a));var c;var d=ej(5);if(d){var e=dj(7)?1:0,f,g=sl(),h=rl(g),l=(f=h&&h.context)&&f.fromContainerExecution?1:0,n=f&&f.source||0,p=ej(6);c=d+";"+p+";"+l+";"+n+";"+e}else c=void 0;var q=c;q&&Vn("tdp",q);var r=vm(!0);r!==void 0&&Vn("frm",String(r))};var tD={},uD=void 0;
function vD(){if(bp()||Tl)Vn("csp",function(){return Object.keys(tD).join("~")||void 0},!1),w.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){O(179);var b=Sm(a.effectiveDirective);if(b){var c;var d=Qm(b,a.blockedURI);c=d?Om[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var l=m(c),n=l.next();!n.done;n=l.next()){var p=
n.value;if(!p.jn){p.jn=!0;if(F(59)){var q={eventId:p.eventId,priorityId:p.priorityId};if(bp()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(bp()){var u=hp("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;ap(u)}}}wD(p.endpoint)}}Rm(b,a.blockedURI)}}}}})}
function wD(a){var b=String(a);tD.hasOwnProperty(b)||(tD[b]=!0,Wn("csp",!0),uD===void 0&&F(171)&&(uD=w.setTimeout(function(){if(F(171)){var c=On.csp;On.csp=!0;On.seq=!1;var d=Xn(!1);On.csp=c;On.seq=!0;Mc(d+"&script=1")}uD=void 0},500)))};var xD=void 0;function yD(){F(236)&&w.addEventListener("pageshow",function(a){a&&(Vn("bfc",function(){return xD?"1":"0"}),a.persisted?(xD=!0,Wn("bfc",!0),Zn()):xD=!1)})};function zD(){var a;var b=ql();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Vn("pcid",e)};var AD=/^(https?:)?\/\//;
function BD(){var a=tl();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=id())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=m(e),l=h.next();!l.done;l=h.next()){var n=l.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(AD,"")===d.replace(AD,""))){b=g;break a}}O(146)}else O(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Vn("rtg",String(a.canonicalContainerId)),Vn("slo",String(p)),Vn("hlo",a.htmlLoadOrder||"-1"),
Vn("lst",String(a.loadScriptType||"0")))}else O(144)};
function WD(){};var XD=function(){};XD.prototype.toString=function(){return"undefined"};var YD=new XD;
var $D=function(){Hp("rm",function(){return{}})[ol()]=function(a){if(ZD.hasOwnProperty(a))return ZD[a]}},cE=function(a,b,c){if(a instanceof aE){var d=a,e=d.resolve,f=b,g=String(Mp());bE[g]=[f,c];a=e.call(d,g);b=rb}return{Dq:a,onSuccess:b}},dE=function(a){var b=a?0:1;return function(c){O(a?134:135);var d=bE[c];if(d&&typeof d[b]==="function")d[b]();bE[c]=void 0}},aE=function(a){this.valueOf=this.toString;this.resolve=function(b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]===YD?b:a[d]);return c.join("")}};
aE.prototype.toString=function(){return this.resolve("undefined")};var ZD={},bE={};function eE(){F(212)&&Gk&&(ng("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}}),bC(ol(),function(a){var b,c;b=a.entityId;c=a.securityGroups;var d="__"+b;return sB(d,5)||!(!Qf[d]||!Qf[d][5])||c.includes("cmpPartners")}))};function fE(a,b){function c(g){var h=bl(g),l=Wk(h,"protocol"),n=Wk(h,"host",!0),p=Wk(h,"port"),q=Wk(h,"path").toLowerCase().replace(/\/$/,"");if(l===void 0||l==="http"&&p==="80"||l==="https"&&p==="443")l="web",p="default";return[l,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function gE(a){return hE(a)?1:0}
function hE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=ud(a,{});ud({arg1:c[d],any_of:void 0},e);if(gE(e))return!0}return!1}switch(a["function"]){case "_cn":return Vg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Qg.length;g++){var h=Qg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(l){}f=!1}return f;case "_ew":return Rg(b,c);case "_eq":return Wg(b,c);case "_ge":return Xg(b,c);case "_gt":return Zg(b,c);case "_lc":return Sg(b,c);case "_le":return Yg(b,
c);case "_lt":return $g(b,c);case "_re":return Ug(b,c,a.ignore_case);case "_sw":return ah(b,c);case "_um":return fE(b,c)}return!1};var iE=function(){this.C=this.gppString=void 0};iE.prototype.reset=function(){this.C=this.gppString=void 0};var jE=new iE;[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var kE=function(a,b,c,d){qr.call(this);this.kh=b;this.Of=c;this.Kc=d;this.Xa=new Map;this.mh=0;this.ma=new Map;this.Ia=new Map;this.U=void 0;this.H=a};xa(kE,qr);kE.prototype.M=function(){delete this.C;this.Xa.clear();this.ma.clear();this.Ia.clear();this.U&&(mr(this.H,"message",this.U),delete this.U);delete this.H;delete this.Kc;qr.prototype.M.call(this)};
var lE=function(a){if(a.C)return a.C;a.Of&&a.Of(a.H)?a.C=a.H:a.C=um(a.H,a.kh);var b;return(b=a.C)!=null?b:null},nE=function(a,b,c){if(lE(a))if(a.C===a.H){var d=a.Xa.get(b);d&&d(a.C,c)}else{var e=a.ma.get(b);if(e&&e.vj){mE(a);var f=++a.mh;a.Ia.set(f,{Dh:e.Dh,Up:e.Sm(c),persistent:b==="addEventListener"});a.C.postMessage(e.vj(c,f),"*")}}},mE=function(a){a.U||(a.U=function(b){try{var c;c=a.Kc?a.Kc(b):void 0;if(c){var d=c.Xq,e=a.Ia.get(d);if(e){e.persistent||a.Ia.delete(d);var f;(f=e.Dh)==null||f.call(e,
e.Up,c.payload)}}}catch(g){}},lr(a.H,"message",a.U))};var oE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},pE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},qE={Sm:function(a){return a.listener},vj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Dh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},rE={Sm:function(a){return a.listener},vj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Dh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function sE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,Xq:b.__gppReturn.callId}}
var tE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;qr.call(this);this.caller=new kE(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},sE);this.caller.Xa.set("addEventListener",oE);this.caller.ma.set("addEventListener",qE);this.caller.Xa.set("removeEventListener",pE);this.caller.ma.set("removeEventListener",rE);this.timeoutMs=c!=null?c:500};xa(tE,qr);tE.prototype.M=function(){this.caller.dispose();qr.prototype.M.call(this)};
tE.prototype.addEventListener=function(a){var b=this,c=$l(function(){a(uE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);nE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(l){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(vE,!0);return}a(wE,!0)}}})};
tE.prototype.removeEventListener=function(a){nE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var wE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},uE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},vE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function xE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){jE.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");jE.C=d}}function yE(){try{var a=new tE(w,{timeoutMs:-1});lE(a.caller)&&a.addEventListener(xE)}catch(b){}};function zE(){var a=[["cv",ej(1)],["rv",ej(14)],["tc",Of.filter(function(c){return c}).length]],b=hj(15);b&&a.push(["x",b]);El()&&a.push(["tag_exp",El()]);return a};var AE={},BE={};function kj(a){AE[a]=(AE[a]||0)+1}function lj(a){BE[a]=(BE[a]||0)+1}function CE(a,b){for(var c=[],d=m(Object.keys(b)),e=d.next();!e.done;e=d.next()){var f=e.value;c.push(f+"."+b[f])}return c.length===0?[]:[[a,c.join("~")]]}function DE(){return CE("bdm",AE)}function EE(){return CE("vcm",BE)};var FE={},GE={};function HE(a){var b=a.eventId,c=a.Pd,d=[],e=FE[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=GE[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete FE[b],delete GE[b]);return d};function IE(){return!1}function JE(){var a={};return function(b,c,d){}};function KE(){var a=LE;return function(b,c,d){var e=d&&d.event;ME(c);var f=Fh(b)?void 0:1,g=new db;Ab(c,function(r,t){var u=Jd(t,void 0,f);u===void 0&&t!==void 0&&O(44);g.set(r,u)});a.Mb(fg());var h={Fm:tg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Uf:e!==void 0?function(r){e.Lc.Uf(r)}:void 0,Jb:function(){return b},log:function(){},bq:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},jr:!!sB(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(IE()){var l=JE(),n,p;h.tb={Jj:[],Vf:{},bc:function(r,t,u){t===1&&(n=r);t===7&&(p=u);l(r,t,u)},Bh:Yh()};h.log=function(r){var t=Fa.apply(1,arguments);n&&l(n,4,{level:r,source:p,message:t})}}var q=ef(a,h,[b,g]);a.Mb();q instanceof Ia&&(q.type==="return"?q=q.data:q=void 0);return A(q,void 0,f)}}function ME(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;sb(b)&&(a.gtmOnSuccess=function(){Tc(b)});sb(c)&&(a.gtmOnFailure=function(){Tc(c)})};function NE(a){}NE.K="internal.addAdsClickIds";function OE(a,b){var c=this;}OE.publicName="addConsentListener";var PE=!1;function QE(a){for(var b=0;b<a.length;++b)if(PE)try{a[b]()}catch(c){O(77)}else a[b]()}function RE(a,b,c){var d=this,e;return e}RE.K="internal.addDataLayerEventListener";function SE(a,b,c){}SE.publicName="addDocumentEventListener";function TE(a,b,c,d){}TE.publicName="addElementEventListener";function UE(a){return a.J.rb()};function VE(a){}VE.publicName="addEventCallback";
var WE=function(a){return typeof a==="string"?a:String(Mp())},ZE=function(a,b){XE(a,"init",!1)||(YE(a,"init",!0),b())},XE=function(a,b,c){var d=$E(a);return Ib(d,b,c)},aF=function(a,b,c,d){var e=$E(a),f=Ib(e,b,d);e[b]=c(f)},YE=function(a,b,c){$E(a)[b]=c},$E=function(a){var b=Hp("autoEventsSettings",function(){return{}});b.hasOwnProperty(a)||(b[a]={});return b[a]},bF=function(a,b,c){var d={event:b,"gtm.element":a,"gtm.elementClasses":fd(a,"className"),"gtm.elementId":a.for||Uc(a,"id")||"","gtm.elementTarget":a.formTarget||
fd(a,"target")||""};c&&(d["gtm.triggers"]=c.join(","));d["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||fd(a,"href")||a.src||a.code||a.codebase||"";return d};
function jF(a){}jF.K="internal.addFormAbandonmentListener";function kF(a,b,c,d){}
kF.K="internal.addFormData";var lF={},mF=[],nF={},oF=0,pF=0;
function wF(a,b){}wF.K="internal.addFormInteractionListener";
function DF(a,b){}DF.K="internal.addFormSubmitListener";
function IF(a){}IF.K="internal.addGaSendListener";function JF(a){if(!a)return{};var b=a.bq;return rB(b.type,b.index,b.name)}function KF(a){return a?{originatingEntity:JF(a)}:{}};
var MF=function(a,b,c){LF().updateZone(a,b,c)},OF=function(a,b,c,d,e,f){var g=LF();c=c&&Lb(c,NF);for(var h=g.createZone(a,c),l=0;l<b.length;l++){var n=String(b[l]);if(g.registerChild(n,ej(5),h)){var p=n,q=a,r=d,t=e,u=f;if(Mb(p,"GTM-"))kB(p,void 0,!1,{source:1,fromContainerExecution:!0});else{var v=Sw("js",Gb());kB(p,void 0,!0,{source:1,fromContainerExecution:!0});var x={originatingEntity:t,inheritParentConfig:u};bx(v,q,x);bx(Tw(p,r),q,x)}}}return h},LF=function(){return Hp("zones",function(){return new PF})},
QF={zone:1,cn:1,css:1,ew:1,eq:1,ge:1,gt:1,lc:1,le:1,lt:1,re:1,sw:1,um:1},NF={cl:["ecl"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"]},PF=function(){this.C={};this.H={};this.M=0};k=PF.prototype;k.isActive=function(a,b){for(var c,d=0;d<a.length&&!(c=this.C[a[d]]);d++);if(!c)return!0;if(!this.isActive([c.Bj],b))return!1;for(var e=0;e<c.rg.length;e++)if(this.H[c.rg[e]].Ce(b))return!0;return!1};k.getIsAllowedFn=function(a,b){if(!this.isActive(a,b))return function(){return!1};for(var c,d=0;d<a.length&&
!(c=this.C[a[d]]);d++);if(!c)return function(){return!0};for(var e=[],f=0;f<c.rg.length;f++){var g=this.H[c.rg[f]];g.Ce(b)&&e.push(g)}if(!e.length)return function(){return!1};var h=this.getIsAllowedFn([c.Bj],b);return function(l,n){n=n||[];if(!h(l,n))return!1;for(var p=0;p<e.length;++p)if(e[p].M(l,n))return!0;return!1}};k.unregisterChild=function(a){for(var b=0;b<a.length;b++)delete this.C[a[b]]};k.createZone=function(a,b){var c=String(++this.M);this.H[c]=new RF(a,b);return c};k.updateZone=function(a,
b,c){var d=this.H[a];d&&d.R(b,c)};k.registerChild=function(a,b,c){var d=this.C[a];if(!d&&Gp[a]||!d&&xl(a)||d&&d.Bj!==b)return!1;if(d)return d.rg.push(c),!1;this.C[a]={Bj:b,rg:[c]};return!0};var RF=function(a,b){this.H=null;this.C=[{eventId:a,Ce:!0}];if(b){this.H={};for(var c=0;c<b.length;c++)this.H[b[c]]=!0}};RF.prototype.R=function(a,b){var c=this.C[this.C.length-1];a<=c.eventId||c.Ce!==b&&this.C.push({eventId:a,Ce:b})};RF.prototype.Ce=function(a){for(var b=this.C.length-1;b>=0;b--)if(this.C[b].eventId<=
a)return this.C[b].Ce;return!1};RF.prototype.M=function(a,b){b=b||[];if(!this.H||QF[a]||this.H[a])return!0;for(var c=0;c<b.length;++c)if(this.H[b[c]])return!0;return!1};function SF(a){var b=Gp.zones;return b?b.getIsAllowedFn(pl(),a):function(){return!0}}function TF(){var a=Gp.zones;a&&a.unregisterChild(pl())}
function UF(){dC(ol(),function(a){var b=a.originalEventData["gtm.uniqueEventId"],c=Gp.zones;return c?c.isActive(pl(),b):!0});bC(ol(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return SF(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var VF=function(a,b){this.tagId=a;this.canonicalId=b};
function WF(a,b){var c=this;if(!I(a)||!kh(b)&&!mh(b))throw H(this.getName(),["string","Object|undefined"],arguments);var d=A(b,this.J,1)||{},e=d.firstPartyUrl,f=d.onLoad,g=d.loadByDestination===!0,h=d.isGtmEvent===!0;QE([function(){J(c,"load_google_tags",a,e)}]);if(g){if(yl(a))return a}else if(xl(a))return a;var l=6,n=UE(this);h&&(l=7);n.Jb()==="__zone"&&(l=1);var p={source:l,fromContainerExecution:!0},q=function(r){bC(r,function(t){for(var u=
cC().getExternalRestrictions(0,ol()),v=m(u),x=v.next();!x.done;x=v.next()){var y=x.value;if(!y(t))return!1}return!0},!0);dC(r,function(t){for(var u=cC().getExternalRestrictions(1,ol()),v=m(u),x=v.next();!x.done;x=v.next()){var y=x.value;if(!y(t))return!1}return!0},!0);f&&f(new VF(a,r))};g?nB(a,e,p,q):kB(a,e,!Mb(a,"GTM-"),p,q);f&&n.Jb()==="__zone"&&OF(Number.MIN_SAFE_INTEGER,[a],null,{},JF(UE(this)));return a}WF.K="internal.loadGoogleTag";function XF(a){return new Bd("",function(b){var c=this.evaluate(b);if(c instanceof Bd)return new Bd("",function(){var d=Fa.apply(0,arguments),e=this,f=ud(UE(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(l){return e.evaluate(l)}),h=this.J.pb();h.Md(f);return c.Kb.apply(c,[h].concat(Ba(g)))})})};function YF(a,b,c){var d=this;}YF.K="internal.addGoogleTagRestriction";var ZF={},$F=[];
function gG(a,b){}
gG.K="internal.addHistoryChangeListener";function hG(a,b,c){}hG.publicName="addWindowEventListener";function iG(a,b){return!0}iG.publicName="aliasInWindow";function jG(a,b,c){}jG.K="internal.appendRemoteConfigParameter";function kG(a){var b;return b}
kG.publicName="callInWindow";function lG(a){}lG.publicName="callLater";function mG(a){}mG.K="callOnDomReady";function nG(a){if(!nh(a))throw H(this.getName(),["function"],arguments);J(this,"process_dom_events","window","load");aD(A(a));}nG.K="callOnWindowLoad";function oG(a,b){var c;return c}oG.K="internal.computeGtmParameter";function pG(a,b){var c=this;}pG.K="internal.consentScheduleFirstTry";function qG(a,b){var c=this;}qG.K="internal.consentScheduleRetry";function rG(a){var b;return b}rG.K="internal.copyFromCrossContainerData";function sG(a,b){var c;if(!I(a)||!vh(b)&&b!==null&&!mh(b))throw H(this.getName(),["string","number|undefined"],arguments);J(this,"read_data_layer",a);c=(b||2)!==2?ik(a,1):kk(a,[w,z]);var d=Jd(c,this.J,Fh(UE(this).Jb())?2:1);d===void 0&&c!==void 0&&O(45);return d}sG.publicName="copyFromDataLayer";
function tG(a){var b=void 0;J(this,"read_data_layer",a);a=String(a);var c;a:{for(var d=UE(this).cachedModelValues,e=m(a.split(".")),f=e.next();!f.done;f=e.next()){if(d==null){c=void 0;break a}d=d[f.value]}c=d}b=Jd(c,this.J,1);return b}tG.K="internal.copyFromDataLayerCache";function uG(a){var b;return b}uG.publicName="copyFromWindow";function vG(a){var b=void 0;return Jd(b,this.J,1)}vG.K="internal.copyKeyFromWindow";var wG=function(a){return a===bn.Z.Ga&&un[a]===an.Ja.te&&!Q(K.m.W)};var xG=function(){return"0"},yG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];F(102)&&b.push("gbraid");return cl(a,b,"0")};var zG={},AG={},BG={},CG={},DG={},EG={},FG={},GG={},HG={},IG={},JG={},KG={},LG={},MG={},NG={},OG={},PG={},QG={},RG={},SG={},TG={},UG={},VG={},WG={},XG={},YG={},ZG=(YG[K.m.La]=(zG[2]=[wG],zG),YG[K.m.xf]=(AG[2]=[wG],AG),YG[K.m.jf]=(BG[2]=[wG],BG),YG[K.m.li]=(CG[2]=[wG],CG),YG[K.m.mi]=(DG[2]=[wG],DG),YG[K.m.ni]=(EG[2]=[wG],EG),YG[K.m.oi]=(FG[2]=[wG],FG),YG[K.m.ri]=(GG[2]=[wG],GG),YG[K.m.Ab]=(HG[2]=[wG],HG),YG[K.m.yf]=(IG[2]=[wG],IG),YG[K.m.zf]=(JG[2]=[wG],JG),YG[K.m.Af]=(KG[2]=[wG],KG),YG[K.m.Bf]=(LG[2]=
[wG],LG),YG[K.m.Cf]=(MG[2]=[wG],MG),YG[K.m.Df]=(NG[2]=[wG],NG),YG[K.m.Ef]=(OG[2]=[wG],OG),YG[K.m.Ff]=(PG[2]=[wG],PG),YG[K.m.wb]=(QG[1]=[wG],QG),YG[K.m.Wc]=(RG[1]=[wG],RG),YG[K.m.dd]=(SG[1]=[wG],SG),YG[K.m.be]=(TG[1]=[wG],TG),YG[K.m.Te]=(UG[1]=[function(a){return F(102)&&wG(a)}],UG),YG[K.m.ed]=(VG[1]=[wG],VG),YG[K.m.Ca]=(WG[1]=[wG],WG),YG[K.m.Wa]=(XG[1]=[wG],XG),YG),$G={},aH=($G[K.m.wb]=xG,$G[K.m.Wc]=xG,$G[K.m.dd]=xG,$G[K.m.be]=xG,$G[K.m.Te]=xG,$G[K.m.ed]=function(a){if(!td(a))return{};var b=ud(a,
null);delete b.match_id;return b},$G[K.m.Ca]=yG,$G[K.m.Wa]=yG,$G),bH={},cH={},dH=(cH[R.A.Ma]=(bH[2]=[wG],bH),cH),eH={};var fH=function(a,b,c,d){this.C=a;this.M=b;this.R=c;this.U=d};fH.prototype.getValue=function(a){a=a===void 0?bn.Z.Fb:a;if(!this.M.some(function(b){return b(a)}))return this.R.some(function(b){return b(a)})?this.U(this.C):this.C};fH.prototype.H=function(){return rd(this.C)==="array"||td(this.C)?ud(this.C,null):this.C};
var gH=function(){},hH=function(a,b){this.conditions=a;this.C=b},iH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new fH(c,e,g,a.C[b]||gH)},jH,kH;var lH=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=m(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;V(this,g,d[g])}},Ov=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,T(a,R.A.Sf))},W=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(jH!=null||(jH=new hH(ZG,aH)),e=iH(jH,b,c));d[b]=e};
lH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return W(this,a,b),!0;if(!td(c))return!1;W(this,a,na(Object,"assign").call(Object,c,b));return!0};var mH=function(a,b){b=b===void 0?{}:b;for(var c=m(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
lH.prototype.copyToHitData=function(a,b,c){var d=P(this.D,a);d===void 0&&(d=b);if(tb(d)&&c!==void 0&&F(92))try{d=c(d)}catch(e){}d!==void 0&&W(this,a,d)};
var T=function(a,b){var c=a.metadata[b];if(b===R.A.Sf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,T(a,R.A.Sf))},V=function(a,b,c){var d=a.metadata,e;c===void 0?e=c:(kH!=null||(kH=new hH(dH,eH)),e=iH(kH,b,c));d[b]=e},nH=function(a,b){b=b===void 0?{}:b;for(var c=m(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},ew=function(a,b,c){var d=ix(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c},oH=function(a){for(var b=new lH(a.target,a.eventName,a.D),c=mH(a),d=m(Object.keys(c)),e=d.next();!e.done;e=d.next()){var f=e.value;W(b,f,c[f])}for(var g=nH(a),h=m(Object.keys(g)),l=h.next();!l.done;l=h.next()){var n=l.value;V(b,n,g[n])}b.isAborted=a.isAborted;return b},pH=function(a){var b=a.D,c=b.eventId,d=b.priorityId;return d?c+"_"+d:String(c)};
lH.prototype.accept=function(){var a=In(Dn.aa.si,{}),b=pH(this),c=this.target.destinationId;a[b]||(a[b]={});a[b][c]=ol();var d=Dn.aa.si;if(En(d)){var e;(e=Fn(d))==null||e.notify()}};lH.prototype.hasBeenAccepted=function(a){var b=Hn(Dn.aa.si);if(!b)return!1;var c=b[pH(this)];return c?c[a!=null?a:this.target.destinationId]!==void 0:!1};function qH(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Ov(a,b)},setHitData:function(b,c){W(a,b,c)},setHitDataIfNotDefined:function(b,c){Ov(a,b)===void 0&&W(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return T(a,b)},setMetadata:function(b,c){V(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return P(a.D,b)},qb:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return td(c)?a.mergeHitDataForKey(b,c):!1},accept:function(){a.accept()},hasBeenAccepted:function(b){return a.hasBeenAccepted(b)}}};function rH(a,b){var c;return c}rH.K="internal.copyPreHit";function sH(a,b){var c=null;if(!I(a)||!I(b))throw H(this.getName(),["string","string"],arguments);J(this,"access_globals","readwrite",a);J(this,"access_globals","readwrite",b);var d=[w,z],e=a.split("."),f=Nb(w,e,d),g=e[e.length-1];if(f===void 0)throw Error("Path "+a+" does not exist.");var h=f[g];if(h)return sb(h)?Jd(h,this.J,2):null;var l;h=function(){if(!sb(l.push))throw Error("Object at "+b+" in window is not an array.");l.push.call(l,
arguments)};f[g]=h;var n=b.split("."),p=Nb(w,n,d),q=n[n.length-1];if(p===void 0)throw Error("Path "+n+" does not exist.");l=p[q];l===void 0&&(l=[],p[q]=l);c=function(){h.apply(h,Array.prototype.slice.call(arguments,0))};return Jd(c,this.J,2)}sH.publicName="createArgumentsQueue";function tH(a){return Jd(function(c){var d=BB();if(typeof c==="function")d(function(){c(function(f,g,h){var l=
BB(),n=l&&l.getByName&&l.getByName(f);return(new w.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.J,1)}tH.K="internal.createGaCommandQueue";function uH(a){return Jd(function(){if(!sb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.J,
Fh(UE(this).Jb())?2:1)}uH.publicName="createQueue";function vH(a,b){var c=null;if(!I(a)||!rh(b))throw H(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new Gd(new RegExp(a,d))}catch(e){}return c}vH.K="internal.createRegex";function wH(a){}wH.K="internal.declareConsentState";function xH(a){var b="";return b}xH.K="internal.decodeUrlHtmlEntities";function yH(a,b,c){var d;return d}yH.K="internal.decorateUrlWithGaCookies";function zH(){}zH.K="internal.deferCustomEvents";function AH(a){var b;return b}AH.K="internal.detectUserProvidedData";
var LH=function(a){var b=Xc(a,["button","input"],50);if(!b)return null;var c=String(b.tagName).toLowerCase();if(c==="button")return b;if(c==="input"){var d=Uc(b,"type");if(d==="button"||d==="submit"||d==="image"||d==="file"||d==="reset")return b}return null},MH=function(a,b,c){var d=c.target;if(d){var e=XE(a,"individualElementIds",[]);if(e.length>0){var f=bF(d,b,e);jD(f)}var g=!1,h=XE(a,"commonButtonIds",[]);if(h.length>0){var l=LH(d);if(l){var n=bF(l,b,h);jD(n);g=!0}}var p=XE(a,"selectorToTriggerIds",
{}),q;for(q in p)if(p.hasOwnProperty(q)){var r=g?p[q].filter(function(v){return h.indexOf(v)===-1}):p[q];if(r.length!==0){var t=Ai(d,q);if(t){var u=bF(t,b,r);jD(u)}}}}};
function NH(a,b){if(!lh(a))throw H(this.getName(),["Object|undefined","any"],arguments);var c=a?A(a):{},d=Db(c.matchCommonButtons),e=!!c.cssSelector,f=WE(b);J(this,"detect_click_events",c.matchCommonButtons,c.cssSelector);var g=c.useV2EventName?"gtm.click-v2":"gtm.click",h=c.useV2EventName?"ecl":"cl",l=function(p){p.push(f);return p};if(e||d){if(d&&aF(h,"commonButtonIds",l,[]),e){var n=Fb(String(c.cssSelector));aF(h,"selectorToTriggerIds",
function(p){p.hasOwnProperty(n)||(p[n]=[]);l(p[n]);return p},{})}}else aF(h,"individualElementIds",l,[]);ZE(h,function(){Rc(z,"click",function(p){MH(h,g,p)},!0)});return f}NH.K="internal.enableAutoEventOnClick";var QH=function(a){if(!OH){var b=function(){var c=z.body;if(c)if(PH)(new MutationObserver(function(){for(var e=0;e<OH.length;e++)Tc(OH[e])})).observe(c,{childList:!0,subtree:!0});else{var d=!1;Rc(c,"DOMNodeInserted",function(){d||(d=!0,Tc(function(){d=!1;for(var e=0;e<OH.length;e++)Tc(OH[e])}))})}};OH=[];z.body?b():Tc(b)}OH.push(a)},PH=!!w.MutationObserver,OH;
var RH=function(a){a.has("PollingId")&&(w.clearInterval(Number(a.get("PollingId"))),a.remove("PollingId"))},TH=function(a,b,c,d){function e(){if(!kx(a.target)){b.has("RecentOnScreen")||b.set("RecentOnScreen",""+SH().toString());b.has("FirstOnScreen")||b.set("FirstOnScreen",""+SH().toString());var g=0;b.has("TotalVisibleTime")&&(g=Number(b.get("TotalVisibleTime")));g+=100;b.set("TotalVisibleTime",""+g.toString());if(g>=c){var h=bF(a.target,"gtm.elementVisibility",[b.uid]),l=mx(a.target);h["gtm.visibleRatio"]=
Math.round(l*1E3)/10;h["gtm.visibleTime"]=c;h["gtm.visibleFirstTime"]=Number(b.get("FirstOnScreen"));h["gtm.visibleLastTime"]=Number(b.get("RecentOnScreen"));jD(h);d()}}}if(!b.has("PollingId")&&(c===0&&e(),!b.has("HasFired"))){var f=w.setInterval(e,100);b.set("PollingId",String(f))}},SH=function(){var a=Number(ik("gtm.start",2))||0;return Hb()-a},UH=function(a,b){this.element=a;this.uid=b};UH.prototype.has=function(a){return!!this.element.dataset["gtmVis"+a+this.uid]};UH.prototype.get=function(a){return this.element.dataset["gtmVis"+
a+this.uid]};UH.prototype.set=function(a,b){this.element.dataset["gtmVis"+a+this.uid]=b};UH.prototype.remove=function(a){delete this.element.dataset["gtmVis"+a+this.uid]};
function VH(a,b){var c=function(u){var v=new UH(u.target,p);u.intersectionRatio>=n?v.has("HasFired")||TH(u,v,l,q==="ONCE"?function(){for(var x=0;x<r.length;x++){var y=new UH(r[x],p);y.set("HasFired","1");RH(y)}px(t);if(h){var B=d;if(OH)for(var D=0;D<OH.length;D++)OH[D]===B&&OH.splice(D,1)}}:function(){v.set("HasFired","1");RH(v)}):(RH(v),q==="MANY_PER_ELEMENT"&&v.has("HasFired")&&(v.remove("HasFired"),v.remove("TotalVisibleTime")),
v.remove("RecentOnScreen"))},d=function(){var u=!1,v=null;if(f==="CSS"){try{v=zi?z.querySelectorAll(g):null}catch(D){}u=!!v&&r.length!==v.length}else if(f==="ID"){var x=z.getElementById(g);x&&(v=[x],u=r.length!==1||r[0]!==x)}v||(v=[],u=r.length>0);if(u){for(var y=0;y<r.length;y++)RH(new UH(r[y],p));r=[];for(var B=0;B<v.length;B++)r.push(v[B]);t>=0&&px(t);r.length>0&&(t=sx(c,r,[n]))}};if(!lh(a))throw H(this.getName(),["Object|undefined","any"],arguments);J(this,"detect_element_visibility_events");
var e=a?A(a):{},f=e.selectorType,g;switch(f){case "ID":g=String(e.id);break;case "CSS":g=String(e.selector);break;default:throw Error("Unrecognized element selector type "+f+". Must be one of 'ID' or 'CSS'.");}var h=!!e.useDomChangeListener,l=Number(e.onScreenDuration)||0,n=(Number(e.onScreenRatio)||50)/100,p=WE(b),q=e.firingFrequency,r=[],t=-1;d();h&&QH(d);return p}VH.K="internal.enableAutoEventOnElementVisibility";function WH(){}WH.K="internal.enableAutoEventOnError";var XH={},YH=[],ZH={},$H=0,aI=0;
function gI(a,b){var c=this;return d}gI.K="internal.enableAutoEventOnFormInteraction";
function lI(a,b){var c=this;return f}lI.K="internal.enableAutoEventOnFormSubmit";
function qI(){var a=this;}qI.K="internal.enableAutoEventOnGaSend";var rI={},sI=[];
var uI=function(a,b){var c=""+b;if(rI[c])rI[c].push(a);else{var d=[a];rI[c]=d;var e=tI("gtm.historyChange-v2"),f=-1;sI.push(function(g){f>=0&&w.clearTimeout(f);b?f=w.setTimeout(function(){e(g,d);f=-1},b):e(g,d)})}},tI=function(a){var b=w.location.href,c={source:null,state:w.history.state||null,url:Zk(bl(b)),cb:Wk(bl(b),"fragment")};return function(d,e){var f=c,g={};g[f.source]=!0;g[d.source]=!0;if(!g.popstate||!g.hashchange||f.cb!==d.cb){var h={event:a,"gtm.historyChangeSource":d.source,"gtm.oldUrlFragment":c.cb,
"gtm.newUrlFragment":d.cb,"gtm.oldHistoryState":c.state,"gtm.newHistoryState":d.state,"gtm.oldUrl":c.url,"gtm.newUrl":d.url};e&&(h["gtm.triggers"]=e.join(","));c=d;jD(h)}}},vI=function(a,b){var c=w.history,d=c[a];if(sb(d))try{c[a]=function(e,f,g){d.apply(c,[].slice.call(arguments,0));var h=w.location.href;b({source:a,state:e,url:Zk(bl(h)),cb:Wk(bl(h),"fragment")})}}catch(e){}},xI=function(a){w.addEventListener("popstate",function(b){var c=wI(b);a({source:"popstate",state:b.state,url:Zk(bl(c)),cb:Wk(bl(c),
"fragment")})})},yI=function(a){w.addEventListener("hashchange",function(b){var c=wI(b);a({source:"hashchange",state:null,url:Zk(bl(c)),cb:Wk(bl(c),"fragment")})})},wI=function(a){var b,c;return((b=a.target)==null?void 0:(c=b.location)==null?void 0:c.href)||w.location.href};
function zI(a,b){var c=this;if(!lh(a))throw H(this.getName(),["Object|undefined","any"],arguments);QE([function(){J(c,"detect_history_change_events")}]);var d=a&&a.get("useV2EventName")?"ehl":"hl",e=Number(a&&a.get("interval"));e>0&&isFinite(e)||(e=0);var f;if(!XE(d,"init",!1)){var g;d==="ehl"?(g=function(l){for(var n=0;n<sI.length;n++)sI[n](l)},f=WE(b),uI(f,e),YE(d,"reg",uI)):g=tI("gtm.historyChange");yI(g);xI(g);vI("pushState",
g);vI("replaceState",g);YE(d,"init",!0)}else if(d==="ehl"){var h=XE(d,"reg");h&&(f=WE(b),h(f,e))}d==="hl"&&(f=void 0);return f}zI.K="internal.enableAutoEventOnHistoryChange";var AI=["http://","https://","javascript:","file://"];
var BI=function(a,b){if(a.which===2||a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)return!1;var c=fd(b,"href");if(c.indexOf(":")!==-1&&!AI.some(function(h){return Mb(c,h)}))return!1;var d=c.indexOf("#"),e=fd(b,"target");if(e&&e!=="_self"&&e!=="_parent"&&e!=="_top"||d===0)return!1;if(d>0){var f=Zk(bl(c)),g=Zk(bl(w.location.href));return f!==g}return!0},CI=function(a,b){for(var c=Wk(bl((b.attributes&&b.attributes.formaction?b.formAction:"")||b.action||fd(b,"href")||b.src||b.code||b.codebase||""),"host"),
d=0;d<a.length;d++)try{if((new RegExp(a[d])).test(c))return!1}catch(e){}return!0},DI=function(){function a(c){var d=c.target;if(d&&c.which!==3&&!(c.C||c.timeStamp&&c.timeStamp===b)){b=c.timeStamp;d=Xc(d,["a","area"],100);if(!d)return c.returnValue;var e=c.defaultPrevented||c.returnValue===!1,f=XE("lcl",e?"nv.mwt":"mwt",0),g;g=e?XE("lcl","nv.ids",[]):XE("lcl","ids",[]);for(var h=[],l=0;l<g.length;l++){var n=g[l],p=XE("lcl","aff.map",{})[n];p&&!CI(p,d)||h.push(n)}if(h.length){var q=BI(c,d),r=bF(d,"gtm.linkClick",
h);r["gtm.elementText"]=Vc(d);r["gtm.willOpenInNewWindow"]=!q;if(q&&!e&&f&&d.href){var t=!!wb(String(fd(d,"rel")||"").split(" "),function(y){return y.toLowerCase()==="noreferrer"}),u=w[(fd(d,"target")||"_self").substring(1)],v=!0,x=kD(function(){var y;if(y=v&&u){var B;a:if(t){var D;try{D=new MouseEvent(c.type,{bubbles:!0})}catch(E){if(!z.createEvent){B=!1;break a}D=z.createEvent("MouseEvents");D.initEvent(c.type,!0,!0)}D.C=!0;c.target.dispatchEvent(D);B=!0}else B=!1;y=!B}y&&(u.location.href=fd(d,
"href"))},f);if(iD(r,x,f))v=!1;else return c.preventDefault&&c.preventDefault(),c.returnValue=!1}else iD(r,function(){},f||2E3);return!0}}}var b=0;Rc(z,"click",a,!1);Rc(z,"auxclick",a,!1)};
function EI(a,b){var c=this;if(!lh(a))throw H(this.getName(),["Object|undefined","any"],arguments);var d=A(a);QE([function(){J(c,"detect_link_click_events",d)}]);var e=d&&!!d.waitForTags,f=d&&!!d.checkValidation,g=d?d.affiliateDomains:void 0,h=WE(b);if(e){var l=Number(d.waitForTagsTimeout);l>0&&isFinite(l)||(l=2E3);var n=function(q){return Math.max(l,q)};aF("lcl","mwt",n,0);f||aF("lcl","nv.mwt",n,0)}var p=function(q){q.push(h);
return q};aF("lcl","ids",p,[]);f||aF("lcl","nv.ids",p,[]);g&&aF("lcl","aff.map",function(q){q[h]=g;return q},{});XE("lcl","init",!1)||(DI(),YE("lcl","init",!0));return h}EI.K="internal.enableAutoEventOnLinkClick";var FI,GI;
var HI=function(a){return XE("sdl",a,{})},II=function(a,b,c){if(b){var d=Array.isArray(a)?a:[a];aF("sdl",c,function(e){for(var f=0;f<d.length;f++){var g=String(d[f]);e.hasOwnProperty(g)||(e[g]=[]);e[g].push(b)}return e},{})}},LI=function(){function a(){JI();KI(a,!0)}return a},MI=function(){function a(){f?e=w.setTimeout(a,c):(e=0,JI(),KI(b));f=!1}function b(){d&&FI();e?f=!0:(e=w.setTimeout(a,c),YE("sdl","pending",!0))}var c=250,d=!1;z.scrollingElement&&z.documentElement&&(c=50,d=!0);var e=0,f=!1;return b},
KI=function(a,b){XE("sdl","init",!1)&&!NI()&&(b?Sc(w,"scrollend",a):Sc(w,"scroll",a),Sc(w,"resize",a),YE("sdl","init",!1))},JI=function(){var a=FI(),b=a.depthX,c=a.depthY,d=b/GI.scrollWidth*100,e=c/GI.scrollHeight*100;OI(b,"horiz.pix","PIXELS","horizontal");OI(d,"horiz.pct","PERCENT","horizontal");OI(c,"vert.pix","PIXELS","vertical");OI(e,"vert.pct","PERCENT","vertical");YE("sdl","pending",!1)},OI=function(a,b,c,d){var e=HI(b),f={},g;for(g in e)if(f={He:f.He},f.He=g,e.hasOwnProperty(f.He)){var h=
Number(f.He);if(!(a<h)){var l={};rD((l.event="gtm.scrollDepth",l["gtm.scrollThreshold"]=h,l["gtm.scrollUnits"]=c.toLowerCase(),l["gtm.scrollDirection"]=d,l["gtm.triggers"]=e[f.He].join(","),l));aF("sdl",b,function(n){return function(p){delete p[n.He];return p}}(f),{})}}},QI=function(){aF("sdl","scr",function(a){a||(a=z.scrollingElement||z.body&&z.body.parentNode);return GI=a},!1);aF("sdl","depth",function(a){a||(a=PI());return FI=a},!1)},PI=function(){var a=0,b=0;return function(){var c=lx(),d=c.height;
a=Math.max(GI.scrollLeft+c.width,a);b=Math.max(GI.scrollTop+d,b);return{depthX:a,depthY:b}}},NI=function(){return!!(Object.keys(HI("horiz.pix")).length||Object.keys(HI("horiz.pct")).length||Object.keys(HI("vert.pix")).length||Object.keys(HI("vert.pct")).length)};
function RI(a,b){var c=this;if(!kh(a))throw H(this.getName(),["Object","any"],arguments);QE([function(){J(c,"detect_scroll_events")}]);QI();if(!GI)return;var d=WE(b),e=A(a);switch(e.horizontalThresholdUnits){case "PIXELS":II(e.horizontalThresholds,d,"horiz.pix");break;case "PERCENT":II(e.horizontalThresholds,d,"horiz.pct")}switch(e.verticalThresholdUnits){case "PIXELS":II(e.verticalThresholds,d,"vert.pix");break;case "PERCENT":II(e.verticalThresholds,
d,"vert.pct")}XE("sdl","init",!1)?XE("sdl","pending",!1)||Tc(function(){JI()}):(YE("sdl","init",!0),YE("sdl","pending",!0),Tc(function(){JI();if(NI()){var f=MI();"onscrollend"in w?(f=LI(),Rc(w,"scrollend",f)):Rc(w,"scroll",f);Rc(w,"resize",f)}else YE("sdl","init",!1)}));return d}RI.K="internal.enableAutoEventOnScroll";function SI(a){return function(){if(a.limit&&a.yj>=a.limit)a.zh&&w.clearInterval(a.zh);else{a.yj++;var b=Hb();jD({event:a.eventName,"gtm.timerId":a.zh,"gtm.timerEventNumber":a.yj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.pn,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.pn,"gtm.triggers":a.Hr})}}}
function TI(a,b){
return f}TI.K="internal.enableAutoEventOnTimer";var uc=Da(["data-gtm-yt-inspected-"]),VI=["www.youtube.com","www.youtube-nocookie.com"],WI,XI=!1;
function gJ(a,b){var c=this;return e}gJ.K="internal.enableAutoEventOnYouTubeActivity";XI=!1;function hJ(a,b){if(!I(a)||!lh(b))throw H(this.getName(),["string","Object|undefined"],arguments);var c=b?A(b):{},d=a,e=!1;return e}hJ.K="internal.evaluateBooleanExpression";var iJ;function jJ(a){var b=!1;return b}jJ.K="internal.evaluateMatchingRules";var kJ=[K.m.W,K.m.X];var qJ="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function rJ(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function sJ(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=na(Object,"assign").call(Object,{},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function tJ(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function uJ(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function vJ(a){if(!uJ(a))return null;var b=rJ(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(qJ).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};
var wJ=function(a){var b={};b[K.m.yf]=a.architecture;b[K.m.zf]=a.bitness;a.fullVersionList&&(b[K.m.Af]=a.fullVersionList.map(function(c){return encodeURIComponent(c.brand||"")+";"+encodeURIComponent(c.version||"")}).join("|"));b[K.m.Bf]=a.mobile?"1":"0";b[K.m.Cf]=a.model;b[K.m.Df]=a.platform;b[K.m.Ef]=a.platformVersion;b[K.m.Ff]=a.wow64?"1":"0";return b},xJ=function(a){var b=0,c=function(h,l){try{a(h,l)}catch(n){}},d=w,e=sJ(d);if(e)c(e);else{var f=tJ(d);if(f){b=Math.min(Math.max(isFinite(b)?b:0,0),
1E3);var g=d.setTimeout(function(){c.jg||(c.jg=!0,O(106),c(null,Error("Timeout")))},b);f.then(function(h){c.jg||(c.jg=!0,O(104),d.clearTimeout(g),c(h))}).catch(function(h){c.jg||(c.jg=!0,O(105),d.clearTimeout(g),c(null,h))})}else c(null)}},zJ=function(){var a=w;if(uJ(a)&&(yJ=Hb(),!tJ(a))){var b=vJ(a);b&&(b.then(function(){O(95)}),b.catch(function(){O(96)}))}},yJ;function FJ(){var a=w.__uspapi;if(sb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};function oK(){return Kr(7)&&Kr(9)&&Kr(10)};function jL(a,b,c,d){}jL.K="internal.executeEventProcessor";function kL(a){var b;if(!I(a))throw H(this.getName(),["string"],arguments);J(this,"unsafe_run_arbitrary_javascript");try{var c=w.google_tag_manager;c&&typeof c.e==="function"&&(b=c.e(a))}catch(d){}return Jd(b,this.J,1)}kL.K="internal.executeJavascriptString";function lL(a){var b;return b};function mL(a){var b="";return b}mL.K="internal.generateClientId";function nL(a){var b={};return Jd(b)}nL.K="internal.getAdsCookieWritingOptions";function oL(a,b){var c=!1;return c}oL.K="internal.getAllowAdPersonalization";function pL(){var a;return a}pL.K="internal.getAndResetEventUsage";function qL(a,b){b=b===void 0?!0:b;var c;return c}qL.K="internal.getAuid";var rL=null;
function sL(){var a=new db;J(this,"read_container_data"),F(49)&&rL?a=rL:(a.set("containerId",'GTM-T4ZBMQJ5'),a.set("version",'176'),a.set("environmentName",''),a.set("debugMode",ug),a.set("previewMode",vg.rn),a.set("environmentMode",vg.Yp),a.set("firstPartyServing",Pk()||gj.C),a.set("containerUrl",Dc),a.Ra(),F(49)&&(rL=a));return a}
sL.publicName="getContainerVersion";function tL(a,b){b=b===void 0?!0:b;var c;return c}tL.publicName="getCookieValues";function uL(){var a="";return a}uL.K="internal.getCorePlatformServicesParam";function vL(){return uo()}vL.K="internal.getCountryCode";function wL(){var a=[];a=nl();return Jd(a)}wL.K="internal.getDestinationIds";function xL(a){var b=new db;return b}xL.K="internal.getDeveloperIds";function yL(a){var b;return b}yL.K="internal.getEcsidCookieValue";function zL(a,b){var c=null;if(!qh(a)||!I(b))throw H(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof HTMLElement))throw Error("getElementAttribute requires an HTML Element.");J(this,"get_element_attributes",d,b);c=Uc(d,b);return c}zL.K="internal.getElementAttribute";function AL(a){var b=null;return b}AL.K="internal.getElementById";function BL(a){var b="";if(!qh(a))throw H(this.getName(),["OpaqueValue"],arguments);var c=a.getValue();if(!(c instanceof HTMLElement))throw Error("getElementInnerText requires an HTML Element.");J(this,"read_dom_element_text",c);b=Vc(c);return b}BL.K="internal.getElementInnerText";function CL(a,b){var c=null;if(!qh(a)||!I(b))throw H(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof HTMLElement))throw Error("getElementProperty requires an HTML element.");J(this,"access_dom_element_properties",d,"read",b);c=d[b];return Jd(c)}CL.K="internal.getElementProperty";function DL(a){var b;if(!qh(a))throw H(this.getName(),["OpaqueValue"],arguments);var c=a.getValue();if(!(c instanceof HTMLElement))throw Error("getElementValue requires an HTML Element.");J(this,"access_element_values",c,"read");b=c instanceof HTMLInputElement?c.value:Uc(c,"value")||"";return b}DL.K="internal.getElementValue";function EL(a){var b=0;return b}EL.K="internal.getElementVisibilityRatio";function FL(a){var b=null;return b}FL.K="internal.getElementsByCssSelector";
function GL(a){var b;if(!I(a))throw H(this.getName(),["string"],arguments);J(this,"read_event_data",a);var c;a:{var d=a,e=UE(this).originalEventData;if(e){for(var f=e,g={},h={},l={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(l);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var x=[],y="",B=m(n),D=B.next();!D.done;D=
B.next()){var E=D.value;E===l?(x.push(y),y=""):y=E===g?y+"\\":E===h?y+".":y+E}y&&x.push(y);for(var L=m(x),G=L.next();!G.done;G=L.next()){if(f==null){c=void 0;break a}f=f[G.value]}c=f}else c=void 0}b=Jd(c,this.J,1);return b}GL.K="internal.getEventData";function HL(a){var b=null;return b}HL.K="internal.getFirstElementByCssSelector";var IL={};IL.disableUserDataWithoutCcd=F(223);IL.enableDecodeUri=F(92);IL.enableGaAdsConversions=F(122);IL.enableGaAdsConversionsClientId=F(121);IL.enableOverrideAdsCps=F(170);IL.enableUrlDecodeEventUsage=F(139);function JL(){return Jd(IL)}JL.K="internal.getFlags";function KL(){var a;return a}KL.K="internal.getGsaExperimentId";function LL(){return new Gd(YD)}LL.K="internal.getHtmlId";function ML(a){var b;return b}ML.K="internal.getIframingState";function NL(a,b){var c={};return Jd(c)}NL.K="internal.getLinkerValueFromLocation";function OL(){var a=new db;return a}OL.K="internal.getPrivacyStrings";function PL(a,b){var c;return c}PL.K="internal.getProductSettingsParameter";function QL(a,b){var c;return c}QL.publicName="getQueryParameters";function RL(a,b){var c;return c}RL.publicName="getReferrerQueryParameters";function SL(a){var b="";if(!rh(a))throw H(this.getName(),["string|undefined"],arguments);J(this,"get_referrer",a);b=Yk(bl(z.referrer),a);return b}SL.publicName="getReferrerUrl";function TL(){return vo()}TL.K="internal.getRegionCode";function UL(a,b){var c;return c}UL.K="internal.getRemoteConfigParameter";function VL(){var a=new db;a.set("width",0);a.set("height",0);return a}VL.K="internal.getScreenDimensions";function WL(){var a="";return a}WL.K="internal.getTopSameDomainUrl";function XL(){var a="";return a}XL.K="internal.getTopWindowUrl";function YL(a){var b="";if(!rh(a))throw H(this.getName(),["string|undefined"],arguments);J(this,"get_url",a);b=Wk(bl(w.location.href),a);return b}YL.publicName="getUrl";function ZL(){J(this,"get_user_agent");return Ac.userAgent}ZL.K="internal.getUserAgent";function $L(){var a;return a?Jd(wJ(a)):a}$L.K="internal.getUserAgentClientHints";function gM(){var a=w;return a.gaGlobal=a.gaGlobal||{}}function hM(){var a=gM();a.hid=a.hid||xb();return a.hid}function iM(a,b){var c=gM();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};function GM(a){(uy(a)||Pk())&&W(a,K.m.ql,vo()||uo());!uy(a)&&Pk()&&W(a,K.m.Bi,"::")}function HM(a){if(Pk()&&!uy(a)&&(yo()||W(a,K.m.Xk,!0),F(78))){Zv(a);$v(a,Yp.Hf.Jn,So(P(a.D,K.m.Ua)));var b=Yp.Hf.Kn;var c=P(a.D,K.m.Ac);$v(a,b,c===!0?1:c===!1?0:void 0);$v(a,Yp.Hf.In,So(P(a.D,K.m.Cb)));$v(a,Yp.Hf.Gn,Us(Ro(P(a.D,K.m.xb)),Ro(P(a.D,K.m.Sb))))}};var bN={AW:Dn.aa.vn,G:Dn.aa.So,DC:Dn.aa.Oo};function cN(a){var b=tj(a);return""+vs(b.map(function(c){return c.value}).join("!"))}function dN(a){var b=aq(a);return b&&bN[b.prefix]}function eN(a,b){var c=a[b];c&&(c.clearTimerId&&w.clearTimeout(c.clearTimerId),c.clearTimerId=w.setTimeout(function(){delete a[b]},36E5))};var KN=function(a){for(var b={},c=String(JN.cookie).split(";"),d=0;d<c.length;d++){var e=c[d].split("="),f=e[0].trim();if(f&&a(f)){var g=e.slice(1).join("=").trim();g&&(g=decodeURIComponent(g));var h=void 0,l=void 0;((h=b)[l=f]||(h[l]=[])).push(g)}}return b};var LN=window,JN=document,MN=function(a){var b=LN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||JN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&LN["ga-disable-"+a]===!0)return!0;try{var c=LN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(f){}for(var d=KN(function(f){return f==="AMP_TOKEN"}).AMP_TOKEN||[],e=0;e<d.length;e++)if(d[e]=="$OPT_OUT")return!0;return JN.getElementById("__gaOptOutExtension")?!0:!1};function XN(a){Ab(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[K.m.Vb]||{};Ab(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function BO(a,b){}function CO(a,b){var c=function(){};return c}
function DO(a,b,c){}var EO=Fg.P.pk,FO=Fg.P.qk;var GO=CO;function HO(a,b){if(F(240)){var c=nl();c&&c.indexOf(b)>-1&&(a[R.A.Ll]=!0)}}var IO=function(a,b,c){for(var d=0;d<b.length;d++)a.hasOwnProperty(b[d])&&(a[String(b[d])]=c(a[String(b[d])]))};function JO(a,b,c){var d=this;if(!I(a)||!lh(b)||!lh(c))throw H(this.getName(),["string","Object|undefined","Object|undefined"],arguments);var e=b?A(b):{};QE([function(){return J(d,"configure_google_tags",a,e)}]);var f=c?A(c):{},g=UE(this);f.originatingEntity=JF(g);bx(Tw(a,e),g.eventId,f);}JO.K="internal.gtagConfig";
function LO(a,b){}
LO.publicName="gtagSet";function MO(){var a={};return a};function NO(a){}NO.K="internal.initializeServiceWorker";function OO(a,b){}OO.publicName="injectHiddenIframe";var PO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function QO(a,b,c,d,e){if(!((I(a)||qh(a))&&nh(b)&&nh(c)&&uh(d)&&uh(e)))throw H(this.getName(),["string|OpaqueValue","function","function","boolean|undefined","boolean|undefined"],arguments);var f=UE(this);d&&PO(3);e&&(PO(1),PO(2));var g=f.eventId,h=f.Jb(),l=PO(void 0);if(Rl){var n=String(l)+h;FE[g]=FE[g]||[];FE[g].push(n);GE[g]=GE[g]||[];GE[g].push("p"+h)}
if(d&&e)throw Error("useIframe and supportDocumentWrite cannot both be true.");J(this,"unsafe_inject_arbitrary_html",d,e);var p=A(b,this.J),q=A(c,this.J),r=A(a,this.J,1);RO(r,p,q,!!d,!!e,f);}
var SO=function(a,b,c,d){return function(){try{if(b.length>0){var e=b.shift(),f=SO(a,b,c,d),g=e;if(String(g.nodeName).toUpperCase()==="SCRIPT"&&g.type==="text/gtmscript"){var h=g.text||g.textContent||g.innerHTML||"",l=g.getAttribute("data-gtmsrc"),n=g.charset||"";l?Mc(l,f,d,{async:!1,id:e.id,text:h,charset:n},a):(g=z.createElement("script"),g.async=!1,g.type="text/javascript",g.id=e.id,g.text=h,g.charset=n,f&&(g.onload=f),a.insertBefore(g,null));l||f()}else if(e.innerHTML&&e.innerHTML.toLowerCase().indexOf("<script")>=
0){for(var p=[];e.firstChild;)p.push(e.removeChild(e.firstChild));a.insertBefore(e,null);SO(e,p,f,d)()}else a.insertBefore(e,null),f()}else c()}catch(q){d()}}},RO=function(a,b,c,d,e,f){if(z.body){var g=cE(a,b,c);a=g.Dq;b=g.onSuccess;if(d){}else e?
TO(a,b,c):SO(z.body,Wc(a),b,c)()}else w.setTimeout(function(){RO(a,b,c,d,e,f)})};QO.K="internal.injectHtml";var UO={};
function WO(a,b,c,d){}var XO={dl:1,id:1},YO={};
function ZO(a,b,c,d){}F(160)?ZO.publicName="injectScript":WO.publicName="injectScript";ZO.K="internal.injectScript";function $O(){return zo()}$O.K="internal.isAutoPiiEligible";function aP(a){var b=!0;return b}aP.publicName="isConsentGranted";function bP(a){var b=!1;return b}bP.K="internal.isDebugMode";function cP(){return xo()}cP.K="internal.isDmaRegion";function dP(a){var b=!1;return b}dP.K="internal.isEntityInfrastructure";function eP(a){var b=!1;if(!vh(a))throw H(this.getName(),["number"],[a]);b=F(a);return b}eP.K="internal.isFeatureEnabled";function fP(){var a=!1;return a}fP.K="internal.isFpfe";function gP(){var a=!1;return a}gP.K="internal.isGcpConversion";function hP(){var a=!1;return a}hP.K="internal.isLandingPage";function iP(){var a=!1;a=Gk;return a}iP.K="internal.isOgt";function jP(){var a;return a}jP.K="internal.isSafariPcmEligibleBrowser";function kP(){var a=Sh(function(b){UE(this).log("error",b)});a.publicName="JSON";return a};function lP(a){var b=void 0;if(!I(a))throw H(this.getName(),["string"],arguments);b=bl(a);return Jd(b)}lP.K="internal.legacyParseUrl";function mP(){return!1}
var nP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function oP(){try{J(this,"logging")}catch(d){return}if(!console)return;for(var a=Array.prototype.slice.call(arguments,0),b=0;b<a.length;b++)a[b]=A(a[b],this.J);var c=UE(this);console.log.apply(console,a);JF(c);}oP.publicName="logToConsole";function pP(a,b){}pP.K="internal.mergeRemoteConfig";function qP(a,b,c){c=c===void 0?!0:c;var d=[];return Jd(d)}qP.K="internal.parseCookieValuesFromString";function rP(a){var b=void 0;if(typeof a!=="string")return;a&&Mb(a,"//")&&(a=z.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(x){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],l=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],l]:e[h].push(l):e[h]=l}c=Jd({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=bl(a)}catch(x){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),u=t[0],v=Vk(t.splice(1).join("="))||"";v=v.replace(/\+/g," ");p.hasOwnProperty(u)?typeof p[u]==="string"?p[u]=[p[u],v]:p[u].push(v):p[u]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=Jd(n);
return b}rP.publicName="parseUrl";function sP(a){}sP.K="internal.processAsNewEvent";function tP(a,b,c){var d;return d}tP.K="internal.pushToDataLayer";function uP(a){var b=Fa.apply(1,arguments),c=!1;if(!I(a))throw H(this.getName(),["string"],arguments);for(var d=[this,a],e=m(b),f=e.next();!f.done;f=e.next())d.push(A(f.value,this.J,1));try{J.apply(null,d),c=!0}catch(g){return!1}return c}uP.publicName="queryPermission";function vP(a){var b=this;}vP.K="internal.queueAdsTransmission";function wP(a){var b=void 0;return b}wP.publicName="readAnalyticsStorage";function xP(){var a="";return a}xP.publicName="readCharacterSet";function yP(){return ej(19)}yP.K="internal.readDataLayerName";function zP(){var a="";return a}zP.publicName="readTitle";function AP(a,b){var c=this;}AP.K="internal.registerCcdCallback";function BP(a,b){return!0}BP.K="internal.registerDestination";var CP=["config","event","get","set"];function DP(a,b,c){}DP.K="internal.registerGtagCommandListener";function EP(a,b){var c=!1;return c}EP.K="internal.removeDataLayerEventListener";function FP(a,b){}
FP.K="internal.removeFormData";function GP(){}GP.publicName="resetDataLayer";function HP(a,b,c){var d=void 0;return d}HP.K="internal.scrubUrlParams";function IP(a){}IP.K="internal.sendAdsHit";function JP(a,b,c,d){}JP.K="internal.sendGtagEvent";function KP(a,b,c){}KP.publicName="sendPixel";function LP(a,b){}LP.K="internal.setAnchorHref";function MP(a){}MP.K="internal.setContainerConsentDefaults";function NP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}NP.publicName="setCookie";function OP(a){}OP.K="internal.setCorePlatformServices";function PP(a,b){}PP.K="internal.setDataLayerValue";function QP(a){}QP.publicName="setDefaultConsentState";function RP(a,b){}RP.K="internal.setDelegatedConsentType";function SP(a,b){}SP.K="internal.setFormAction";function TP(a,b,c){c=c===void 0?!1:c;}TP.K="internal.setInCrossContainerData";function UP(a,b,c){return!1}UP.publicName="setInWindow";function VP(a,b,c){}VP.K="internal.setProductSettingsParameter";function WP(a,b,c){}WP.K="internal.setRemoteConfigParameter";function XP(a,b){}XP.K="internal.setTransmissionMode";function YP(a,b,c,d){var e=this;}YP.publicName="sha256";function ZP(a,b,c){}
ZP.K="internal.sortRemoteConfigParameters";function $P(a){}$P.K="internal.storeAdsBraidLabels";function aQ(a,b){var c=void 0;return c}aQ.K="internal.subscribeToCrossContainerData";function bQ(a){}bQ.K="internal.taskSendAdsHits";var cQ={},dQ={};cQ.getItem=function(a){var b=null;J(this,"access_template_storage");var c=UE(this).Jb();dQ[c]&&(b=dQ[c].hasOwnProperty("gtm."+a)?dQ[c]["gtm."+a]:null);return b};cQ.setItem=function(a,b){J(this,"access_template_storage");var c=UE(this).Jb();dQ[c]=dQ[c]||{};dQ[c]["gtm."+a]=b;};
cQ.removeItem=function(a){J(this,"access_template_storage");var b=UE(this).Jb();if(!dQ[b]||!dQ[b].hasOwnProperty("gtm."+a))return;delete dQ[b]["gtm."+a];};cQ.clear=function(){J(this,"access_template_storage"),delete dQ[UE(this).Jb()];};cQ.publicName="templateStorage";function eQ(a,b){var c=!1;if(!qh(a)||!I(b))throw H(this.getName(),["OpaqueValue","string"],arguments);var d=a.getValue();if(!(d instanceof RegExp))return!1;c=d.test(b);return c}eQ.K="internal.testRegex";function fQ(a){var b;return b};function gQ(a,b){}gQ.K="internal.trackUsage";function hQ(a,b){var c;return c}hQ.K="internal.unsubscribeFromCrossContainerData";function iQ(a){}iQ.publicName="updateConsentState";function jQ(a){var b=!1;return b}jQ.K="internal.userDataNeedsEncryption";var kQ;function lQ(a,b,c){kQ=kQ||new di;kQ.add(a,b,c)}function mQ(a,b){var c=kQ=kQ||new di;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=sb(b)?yh(a,b):zh(a,b)}
function nQ(){return function(a){var b;var c=kQ;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.J.rb();if(e){var f=!1,g=e.Jb();if(g){Fh(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function oQ(){var a=function(c){return void mQ(c.K,c)},b=function(c){return void lQ(c.publicName,c)};b(OE);b(VE);b(iG);b(kG);b(lG);b(sG);b(uG);b(sH);b(kP());b(uH);b(sL);b(tL);b(QL);b(RL);b(SL);b(YL);b(LO);b(OO);b(aP);b(oP);b(rP);b(uP);b(xP);b(zP);b(KP);b(NP);b(QP);b(UP);b(YP);b(cQ);b(iQ);lQ("Math",Dh());lQ("Object",bi);lQ("TestHelper",fi());lQ("assertApi",Ah);lQ("assertThat",Bh);lQ("decodeUri",Gh);lQ("decodeUriComponent",Hh);lQ("encodeUri",Ih);lQ("encodeUriComponent",Jh);lQ("fail",Oh);lQ("generateRandom",
Ph);lQ("getTimestamp",Qh);lQ("getTimestampMillis",Qh);lQ("getType",Rh);lQ("makeInteger",Th);lQ("makeNumber",Vh);lQ("makeString",Wh);lQ("makeTableMap",Xh);lQ("mock",$h);lQ("mockObject",ai);lQ("fromBase64",lL,!("atob"in w));lQ("localStorage",nP,!mP());lQ("toBase64",fQ,!("btoa"in w));a(NE);a(RE);a(kF);a(wF);a(DF);a(IF);a(YF);a(gG);a(jG);a(mG);a(nG);a(oG);a(pG);a(qG);a(rG);a(tG);a(vG);a(rH);a(tH);a(vH);a(wH);a(xH);a(yH);a(zH);a(AH);a(NH);a(VH);a(WH);a(gI);a(lI);a(qI);a(zI);a(EI);a(RI);a(TI);a(gJ);a(hJ);
a(jJ);a(jL);a(kL);a(mL);a(nL);a(oL);a(pL);a(qL);a(vL);a(wL);a(xL);a(yL);a(zL);a(AL);a(BL);a(CL);a(DL);a(EL);a(FL);a(GL);a(HL);a(JL);a(KL);a(LL);a(ML);a(NL);a(OL);a(PL);a(TL);a(UL);a(VL);a(WL);a(XL);a($L);a(JO);a(NO);a(QO);a(ZO);a($O);a(bP);a(cP);a(dP);a(eP);a(fP);a(gP);a(hP);a(iP);a(jP);a(lP);a(WF);a(pP);a(qP);a(sP);a(tP);a(vP);a(yP);a(AP);a(BP);a(DP);a(EP);a(FP);a(HP);a(IP);a(JP);a(LP);a(MP);a(OP);a(PP);a(RP);a(SP);a(TP);a(VP);a(WP);a(XP);a(ZP);a($P);a(aQ);a(bQ);a(eQ);a(gQ);a(hQ);a(jQ);mQ("internal.IframingStateSchema",
MO());
F(104)&&a(uL);F(160)?b(ZO):b(WO);F(177)&&b(wP);return nQ()};var LE;
function pQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;LE=new cf;qQ();Kf=KE();var e=LE,f=oQ(),g=new Cd("require",f);g.Ra();e.C.C.set("require",g);Za.set("require",g);for(var h=[],l=0;l<c.length;l++){var n=c[l];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[l]&&d[l].length&&eg(n,d[l]);try{LE.execute(n),F(120)&&Rl&&n[0]===50&&h.push(n[1])}catch(r){}}F(120)&&(Xf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");Mk[q]=["sandboxedScripts"]}rQ(b)}function qQ(){LE.Sc(function(a,b,c){Gp.SANDBOXED_JS_SEMAPHORE=Gp.SANDBOXED_JS_SEMAPHORE||0;Gp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{Gp.SANDBOXED_JS_SEMAPHORE--}})}function rQ(a){a&&Ab(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");Mk[e]=Mk[e]||[];Mk[e].push(b)}})};function sQ(a){bx(Rw("developer_id."+a,!0),0,{})};var tQ=Array.isArray;function uQ(a,b){return ud(a,b||null)}function X(a){return window.encodeURIComponent(a)}function vQ(a,b,c){Qc(a,b,c)}
function wQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=Wk(bl(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function xQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function yQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=xQ(b,"parameter","parameterValue");e&&(c=uQ(e,c))}return c}function zQ(a,b,c){return a===void 0||a===c?b:a}function AQ(){try{if(!F(243))return null;var a=[],b;a:{try{b=!!yi('script[data-requiremodule^="mage/"]');break a}catch(g){}b=!1}b&&a.push("ac");var c;a:{try{if(F(242)){c=!!yi('script[src^="//assets.squarespace.com/"]');break a}}catch(g){}c=!1}c&&a.push("sqs");var d;a:{try{if(F(246)){d=!!yi('script[id="d-js-core"]');break a}}catch(g){}d=!1}d&&a.push("dud");var e;a:{try{if(F(247)){e=!!yi('script[src*="woocommerce"],link[href*="woocommerce"],[class|="woocommerce"]');break a}}catch(g){}e=!1}e&&a.push("woo");
var f;a:{try{if(F(248)){f=!!yi('meta[content*="fourthwall"],script[src*="fourthwall"],link[href*="fourthwall"]');break a}}catch(g){}f=!1}f&&a.push("fw");if(a.length>0)return{plf:a.join(".")}}catch(g){}return null};function BQ(a,b,c){return Mc(a,b,c,void 0)}function CQ(a,b){return ik(a,b||2)}function DQ(a,b){w[a]=b}function EQ(a,b,c){var d=w;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}

var FQ={};var Y={securityGroups:{}};
Y.securityGroups.access_template_storage=["google"],Y.__access_template_storage=function(){return{assert:function(){},V:function(){return{}}}},Y.__access_template_storage.F="access_template_storage",Y.__access_template_storage.isVendorTemplate=!0,Y.__access_template_storage.priorityOverride=0,Y.__access_template_storage.isInfrastructure=!1,Y.__access_template_storage["5"]=!1;

Y.securityGroups.access_element_values=["google"],function(){function a(b,c,d,e){return{element:c,operation:d,newValue:e}}(function(b){Y.__access_element_values=b;Y.__access_element_values.F="access_element_values";Y.__access_element_values.isVendorTemplate=!0;Y.__access_element_values.priorityOverride=0;Y.__access_element_values.isInfrastructure=!1;Y.__access_element_values["5"]=!1})(function(b){var c=b.vtp_allowRead,d=b.vtp_allowWrite,e=b.vtp_createPermissionError;return{assert:function(f,g,h,l){if(!(g instanceof
HTMLElement))throw e(f,{},"Element must be a HTMLElement.");if(h!=="read"&&h!=="write")throw e(f,{},"Unknown operation: "+h+".");if(h=="read"&&!c)throw e(f,{},"Attempting to perform disallowed operation: read.");if(h=="write"){if(!d)throw e(f,{},"Attempting to perform disallowed operation: write.");if(!tb(l))throw e(f,{},"Attempting to write value without valid new value.");}},V:a}})}();
Y.securityGroups.access_globals=["google"],function(){function a(b,c,d){var e={key:d,read:!1,write:!1,execute:!1};switch(c){case "read":e.read=!0;break;case "write":e.write=!0;break;case "readwrite":e.read=e.write=!0;break;case "execute":e.execute=!0;break;default:throw Error("Invalid "+b+" request "+c);}return e}(function(b){Y.__access_globals=b;Y.__access_globals.F="access_globals";Y.__access_globals.isVendorTemplate=!0;Y.__access_globals.priorityOverride=0;Y.__access_globals.isInfrastructure=!1;
Y.__access_globals["5"]=!1})(function(b){for(var c=b.vtp_keys||[],d=b.vtp_createPermissionError,e=[],f=[],g=[],h=0;h<c.length;h++){var l=c[h],n=l.key;l.read&&e.push(n);l.write&&f.push(n);l.execute&&g.push(n)}return{assert:function(p,q,r){if(!tb(r))throw d(p,{},"Key must be a string.");if(q==="read"){if(e.indexOf(r)>-1)return}else if(q==="write"){if(f.indexOf(r)>-1)return}else if(q==="readwrite"){if(f.indexOf(r)>-1&&e.indexOf(r)>-1)return}else if(q==="execute"){if(g.indexOf(r)>-1)return}else throw d(p,
{},"Operation must be either 'read', 'write', or 'execute', was "+q);throw d(p,{},"Prohibited "+q+" on global variable: "+r+".");},V:a}})}();
Y.securityGroups.access_dom_element_properties=["google"],function(){function a(b,c,d,e){var f={property:e,read:!1,write:!1};switch(d){case "read":f.read=!0;break;case "write":f.write=!0;break;default:throw Error("Invalid "+b+" operation "+d);}return f}(function(b){Y.__access_dom_element_properties=b;Y.__access_dom_element_properties.F="access_dom_element_properties";Y.__access_dom_element_properties.isVendorTemplate=!0;Y.__access_dom_element_properties.priorityOverride=0;Y.__access_dom_element_properties.isInfrastructure=
!1;Y.__access_dom_element_properties["5"]=!1})(function(b){for(var c=b.vtp_properties||[],d=b.vtp_createPermissionError,e=[],f=[],g=0;g<c.length;g++){var h=c[g],l=h.property;h.read&&e.push(l);h.write&&f.push(l)}return{assert:function(n,p,q,r){if(!tb(r))throw d(n,{},"Property must be a string.");if(q==="read"){if(e.indexOf(r)>-1)return}else if(q==="write"){if(f.indexOf(r)>-1)return}else throw d(n,{},'Operation must be either "read" or "write"');throw d(n,{},'"'+q+'" operation is not allowed.');},V:a}})}();

Y.securityGroups.read_dom_element_text=["google"],function(){function a(b,c){return{element:c}}(function(b){Y.__read_dom_element_text=b;Y.__read_dom_element_text.F="read_dom_element_text";Y.__read_dom_element_text.isVendorTemplate=!0;Y.__read_dom_element_text.priorityOverride=0;Y.__read_dom_element_text.isInfrastructure=!1;Y.__read_dom_element_text["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e){if(!(e instanceof HTMLElement))throw c(d,{},"Wrong element type. Must be HTMLElement.");
},V:a}})}();
Y.securityGroups.get_referrer=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Y.__get_referrer=b;Y.__get_referrer.F="get_referrer";Y.__get_referrer.isVendorTemplate=!0;Y.__get_referrer.priorityOverride=0;Y.__get_referrer.isInfrastructure=!1;Y.__get_referrer["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),
b.vtp_query&&c.push("query"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!tb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!tb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+
h);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},V:a}})}();
Y.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Y.__read_event_data=b;Y.__read_event_data.F="read_event_data";Y.__read_event_data.isVendorTemplate=!0;Y.__read_event_data.priorityOverride=0;Y.__read_event_data.isInfrastructure=!1;Y.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!tb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Pg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},V:a}})}();

Y.securityGroups.process_dom_events=["google"],function(){function a(b,c,d){return{targetType:c,eventName:d}}(function(b){Y.__process_dom_events=b;Y.__process_dom_events.F="process_dom_events";Y.__process_dom_events.isVendorTemplate=!0;Y.__process_dom_events.priorityOverride=0;Y.__process_dom_events.isInfrastructure=!1;Y.__process_dom_events["5"]=!1})(function(b){for(var c=b.vtp_targets||[],d=b.vtp_createPermissionError,e={},f=0;f<c.length;f++){var g=c[f];e[g.targetType]=e[g.targetType]||[];e[g.targetType].push(g.eventName)}return{assert:function(h,
l,n){if(!e[l])throw d(h,{},"Prohibited event target "+l+".");if(e[l].indexOf(n)===-1)throw d(h,{},"Prohibited listener registration for DOM event "+n+".");},V:a}})}();

Y.securityGroups.read_data_layer=["google"],function(){function a(b,c){return{key:c}}(function(b){Y.__read_data_layer=b;Y.__read_data_layer.F="read_data_layer";Y.__read_data_layer.isVendorTemplate=!0;Y.__read_data_layer.priorityOverride=0;Y.__read_data_layer.isInfrastructure=!1;Y.__read_data_layer["5"]=!1})(function(b){var c=b.vtp_allowedKeys||"specific",d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!tb(g))throw e(f,{},"Keys must be strings.");if(c!=="any"){try{if(Pg(g,
d))return}catch(h){throw e(f,{},"Invalid key filter.");}throw e(f,{},"Prohibited read from data layer variable: "+g+".");}},V:a}})}();
Y.securityGroups.detect_element_visibility_events=["google"],function(){function a(){return{}}(function(b){Y.__detect_element_visibility_events=b;Y.__detect_element_visibility_events.F="detect_element_visibility_events";Y.__detect_element_visibility_events.isVendorTemplate=!0;Y.__detect_element_visibility_events.priorityOverride=0;Y.__detect_element_visibility_events.isInfrastructure=!1;Y.__detect_element_visibility_events["5"]=!1})(function(){return{assert:function(){},V:a}})}();


Y.securityGroups.detect_history_change_events=["google"],function(){function a(){return{}}(function(b){Y.__detect_history_change_events=b;Y.__detect_history_change_events.F="detect_history_change_events";Y.__detect_history_change_events.isVendorTemplate=!0;Y.__detect_history_change_events.priorityOverride=0;Y.__detect_history_change_events.isInfrastructure=!1;Y.__detect_history_change_events["5"]=!1})(function(){return{assert:function(){},V:a}})}();

Y.securityGroups.gaawe=["google"],function(){function a(f,g,h){for(var l=0;l<g.length;l++)f.hasOwnProperty(g[l])&&(f[g[l]]=h(f[g[l]]))}function b(f,g,h){var l={},n=function(u,v){l[u]=l[u]||v},p=function(u,v,x){x=x===void 0?!1:x;c.push(FO);if(u){l.items=l.items||[];for(var y={},B=0;B<u.length;y={lg:void 0},B++)y.lg={},Ab(u[B],function(E){return function(L,G){x&&L==="id"?E.lg.promotion_id=G:x&&L==="name"?E.lg.promotion_name=G:E.lg[L]=G}}(y)),l.items.push(y.lg)}if(v)for(var D in v)d.hasOwnProperty(D)?n(d[D],
v[D]):n(D,v[D])},q;f.vtp_getEcommerceDataFrom==="dataLayer"?(q=f.vtp_gtmCachedValues.eventModel)||(q=f.vtp_gtmCachedValues.ecommerce):(q=f.vtp_ecommerceMacroData,td(q)&&q.ecommerce&&!q.items&&(q=q.ecommerce));if(td(q)){var r=!1,t;for(t in q)q.hasOwnProperty(t)&&(r||(c.push(EO),r=!0),t==="currencyCode"?n("currency",q.currencyCode):t==="impressions"&&g===K.m.kc?p(q.impressions,null):t==="promoClick"&&g===K.m.yc?p(q.promoClick.promotions,q.promoClick.actionField,!0):t==="promoView"&&g===K.m.mc?p(q.promoView.promotions,
q.promoView.actionField,!0):e.hasOwnProperty(t)?g===e[t]&&p(q[t].products,q[t].actionField):l[t]=q[t]);uQ(l,h)}}var c=[],d={id:"transaction_id",revenue:"value",list:"item_list_name"},e={click:"select_item",detail:"view_item",add:"add_to_cart",remove:"remove_from_cart",checkout:"begin_checkout",checkout_option:"checkout_option",purchase:"purchase",refund:"refund"};(function(f){Y.__gaawe=f;Y.__gaawe.F="gaawe";Y.__gaawe.isVendorTemplate=!0;Y.__gaawe.priorityOverride=0;Y.__gaawe.isInfrastructure=!1;Y.__gaawe["5"]=
!0})(function(f){var g;g=f.vtp_migratedToV2?String(f.vtp_measurementIdOverride):String(f.vtp_measurementIdOverride||f.vtp_measurementId);if(tb(g)&&g.indexOf("G-")===0){var h=String(f.vtp_eventName),l={};c=[];f.vtp_sendEcommerceData&&(Go.hasOwnProperty(h)||h==="checkout_option")&&b(f,h,l);var n=f.vtp_eventSettingsVariable;if(n)for(var p in n)n.hasOwnProperty(p)&&(l[p]=n[p]);if(f.vtp_eventSettingsTable){var q=xQ(f.vtp_eventSettingsTable,"parameter","parameterValue"),r;for(r in q)l[r]=q[r]}var t=xQ(f.vtp_eventParameters,
"name","value"),u;for(u in t)t.hasOwnProperty(u)&&(l[u]=t[u]);var v=f.vtp_userDataVariable;v&&(l[K.m.mb]=v);if(l.hasOwnProperty(K.m.Vb)||f.vtp_userProperties){var x=l[K.m.Vb]||{};uQ(xQ(f.vtp_userProperties,"name","value"),x);l[K.m.Vb]=x}var y={originatingEntity:rB(1,f.vtp_gtmEntityIndex,f.vtp_gtmEntityName)};if(c.length>0){var B={};y.eventMetadata=(B[R.A.wl]=c,B)}a(l,Ho,function(E){return Db(E)});a(l,Jo,function(E){return Number(E)});var D=f.vtp_gtmEventId;y.noGtmEvent=!0;bx(Uw(g,h,l),D,y);Tc(f.vtp_gtmOnSuccess)}else Tc(f.vtp_gtmOnFailure)})}();


Y.securityGroups.get_element_attributes=["google"],function(){function a(b,c,d){return{element:c,attribute:d}}(function(b){Y.__get_element_attributes=b;Y.__get_element_attributes.F="get_element_attributes";Y.__get_element_attributes.isVendorTemplate=!0;Y.__get_element_attributes.priorityOverride=0;Y.__get_element_attributes.isInfrastructure=!1;Y.__get_element_attributes["5"]=!1})(function(b){var c=b.vtp_allowedAttributes||"specific",d=b.vtp_attributes||[],e=b.vtp_createPermissionError;return{assert:function(f,
g,h){if(!tb(h))throw e(f,{},"Attribute must be a string.");if(!(g instanceof HTMLElement))throw e(f,{},"Wrong element type. Must be HTMLElement.");if(h==="value"||c!=="any"&&(c!=="specific"||d.indexOf(h)===-1))throw e(f,{},'Reading attribute "'+h+'" is not allowed.');},V:a}})}();
Y.securityGroups.detect_link_click_events=["google"],function(){function a(b,c){return{options:c}}(function(b){Y.__detect_link_click_events=b;Y.__detect_link_click_events.F="detect_link_click_events";Y.__detect_link_click_events.isVendorTemplate=!0;Y.__detect_link_click_events.priorityOverride=0;Y.__detect_link_click_events.isInfrastructure=!1;Y.__detect_link_click_events["5"]=!1})(function(b){var c=b.vtp_allowWaitForTags,d=b.vtp_createPermissionError;return{assert:function(e,f){if(!c&&f&&f.waitForTags)throw d(e,
{},"Prohibited option waitForTags.");},V:a}})}();
Y.securityGroups.load_google_tags=["google"],function(){function a(b,c,d){return{tagId:c,firstPartyUrl:d}}(function(b){Y.__load_google_tags=b;Y.__load_google_tags.F="load_google_tags";Y.__load_google_tags.isVendorTemplate=!0;Y.__load_google_tags.priorityOverride=0;Y.__load_google_tags.isInfrastructure=!1;Y.__load_google_tags["5"]=!1})(function(b){var c=b.vtp_allowedTagIds||"specific",d=b.vtp_allowFirstPartyUrls||!1,e=b.vtp_allowedFirstPartyUrls||"specific",f=b.vtp_urls||[],g=b.vtp_tagIds||[],h=b.vtp_createPermissionError;
return{assert:function(l,n,p){(function(q){if(!tb(q))throw h(l,{},"Tag ID must be a string.");if(c!=="any"&&(c!=="specific"||g.indexOf(q)===-1))throw h(l,{},"Prohibited Tag ID: "+q+".");})(n);(function(q){if(q!==void 0){if(!tb(q))throw h(l,{},"First party URL must be a string.");if(d){if(e==="any")return;if(e==="specific")try{if(gh(bl(q),f))return}catch(r){throw h(l,{},"Invalid first party URL filter.");}}throw h(l,{},"Prohibited first party URL: "+q);}})(p)},V:a}})}();
Y.securityGroups.read_container_data=["google"],Y.__read_container_data=function(){return{assert:function(){},V:function(){return{}}}},Y.__read_container_data.F="read_container_data",Y.__read_container_data.isVendorTemplate=!0,Y.__read_container_data.priorityOverride=0,Y.__read_container_data.isInfrastructure=!1,Y.__read_container_data["5"]=!1;



Y.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Y.__get_url=b;Y.__get_url.F="get_url";Y.__get_url.isVendorTemplate=!0;Y.__get_url.priorityOverride=0;Y.__get_url.isInfrastructure=!1;Y.__get_url["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),b.vtp_fragment&&
c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!tb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!tb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+h);}}else if(c)throw e(f,
{},"Prohibited from getting entire URL when components are specified.");},V:a}})}();

Y.securityGroups.unsafe_run_arbitrary_javascript=["google"],function(){function a(){return{}}(function(b){Y.__unsafe_run_arbitrary_javascript=b;Y.__unsafe_run_arbitrary_javascript.F="unsafe_run_arbitrary_javascript";Y.__unsafe_run_arbitrary_javascript.isVendorTemplate=!0;Y.__unsafe_run_arbitrary_javascript.priorityOverride=0;Y.__unsafe_run_arbitrary_javascript.isInfrastructure=!1;Y.__unsafe_run_arbitrary_javascript["5"]=!1})(function(){return{assert:function(){},V:a}})}();




Y.securityGroups.unsafe_inject_arbitrary_html=["google"],function(){function a(b,c,d){return{useIframe:c,supportDocumentWrite:d}}(function(b){Y.__unsafe_inject_arbitrary_html=b;Y.__unsafe_inject_arbitrary_html.F="unsafe_inject_arbitrary_html";Y.__unsafe_inject_arbitrary_html.isVendorTemplate=!0;Y.__unsafe_inject_arbitrary_html.priorityOverride=0;Y.__unsafe_inject_arbitrary_html.isInfrastructure=!1;Y.__unsafe_inject_arbitrary_html["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,
e,f){if(e&&f)throw c(d,{},"Only one of useIframe and supportDocumentWrite can be true.");if(e!==void 0&&typeof e!=="boolean")throw c(d,{},"useIframe must be a boolean.");if(f!==void 0&&typeof f!=="boolean")throw c(d,{},"supportDocumentWrite must be a boolean.");},V:a}})}();
Y.securityGroups.remm=["google"],Y.__remm=function(a){for(var b=a.vtp_input,c=a.vtp_map||[],d=a.vtp_fullMatch,e=a.vtp_ignoreCase?"gi":"g",f=a.vtp_defaultValue,g=0;g<c.length;g++){var h=c[g].key||"";d&&(h="^"+h+"$");var l=new RegExp(h,e);if(l.test(b)){var n=c[g].value;a.vtp_replaceAfterMatch&&(n=String(b).replace(l,n));f=n;break}}return f},Y.__remm.F="remm",Y.__remm.isVendorTemplate=!0,Y.__remm.priorityOverride=0,Y.__remm.isInfrastructure=!0,Y.__remm["5"]=!0;

Y.securityGroups.detect_click_events=["google"],function(){function a(b,c,d){return{matchCommonButtons:c,cssSelector:d}}(function(b){Y.__detect_click_events=b;Y.__detect_click_events.F="detect_click_events";Y.__detect_click_events.isVendorTemplate=!0;Y.__detect_click_events.priorityOverride=0;Y.__detect_click_events.isInfrastructure=!1;Y.__detect_click_events["5"]=!1})(function(b){var c=b.vtp_createPermissionError;return{assert:function(d,e,f){if(e!==void 0&&typeof e!=="boolean")throw c(d,{},"matchCommonButtons must be a boolean.");
if(f!==void 0&&typeof f!=="string")throw c(d,{},"cssSelector must be a string.");},V:a}})}();
Y.securityGroups.logging=["google"],function(){function a(){return{}}(function(b){Y.__logging=b;Y.__logging.F="logging";Y.__logging.isVendorTemplate=!0;Y.__logging.priorityOverride=0;Y.__logging.isInfrastructure=!1;Y.__logging["5"]=!1})(function(b){var c=b.vtp_environments||"debug",d=b.vtp_createPermissionError;return{assert:function(e){var f;if(f=c!=="all"&&!0){var g=!1;f=!g}if(f)throw d(e,{},"Logging is not enabled in all environments");
},V:a}})}();
Y.securityGroups.configure_google_tags=["google"],function(){function a(b,c,d){return{tagId:c,configuration:d}}(function(b){Y.__configure_google_tags=b;Y.__configure_google_tags.F="configure_google_tags";Y.__configure_google_tags.isVendorTemplate=!0;Y.__configure_google_tags.priorityOverride=0;Y.__configure_google_tags.isInfrastructure=!1;Y.__configure_google_tags["5"]=!1})(function(b){var c=b.vtp_allowedTagIds||"specific",d=b.vtp_tagIds||[],e=b.vtp_createPermissionError;return{assert:function(f,
g){if(!tb(g))throw e(f,{},"Tag ID must be a string.");if(c!=="any"&&(c!=="specific"||d.indexOf(g)===-1))throw e(f,{},"Prohibited configuration for Tag ID: "+g+".");},V:a}})}();


Y.securityGroups.detect_scroll_events=["google"],function(){function a(){return{}}(function(b){Y.__detect_scroll_events=b;Y.__detect_scroll_events.F="detect_scroll_events";Y.__detect_scroll_events.isVendorTemplate=!0;Y.__detect_scroll_events.priorityOverride=0;Y.__detect_scroll_events.isInfrastructure=!1;Y.__detect_scroll_events["5"]=!1})(function(){return{assert:function(){},V:a}})}();



var Jp={dataLayer:jk,callback:function(a){Lk.hasOwnProperty(a)&&sb(Lk[a])&&Lk[a]();delete Lk[a]},bootstrap:0};Jp.onHtmlSuccess=dE(!0),Jp.onHtmlFailure=dE(!1);
function GQ(){Ip();vl();mB();Kb(Mk,Y.securityGroups);var a=rl(sl()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;gp(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||O(142);$D(),Tf({Iq:function(d){return d===YD},Rp:function(d){return new aE(d)},Jq:function(d){for(var e=!1,f=!1,g=2;g<d.length;g++)e=e||d[g]===8,f=f||d[g]===16;return e&&f},Zq:function(d){var e;if(d===YD)e=d;else{var f=Mp();ZD[f]=d;e='google_tag_manager["rm"]["'+ol()+'"]('+f+")"}return e}});
Wf={Mp:kg}}var HQ=!1;F(218)&&(HQ=cj(47,HQ));
function ro(){try{if(HQ||!Dl()){zk();F(218)&&(gj.C=cj(50,gj.C));
gj.Xa=ij(4,'ad_storage|analytics_storage|ad_user_data|ad_personalization');gj.Ia=ij(5,'ad_storage|analytics_storage|ad_user_data');gj.ma=ij(11,'5840');gj.ma=ij(10,'5940');
F(218)&&(gj.R=cj(51,gj.R));if(F(109)){}Wa[7]=!0;var a=Hp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});np(a);Fp();yE();Dr();Np();if(wl()){ej(5);TF();cC().removeExternalRestrictions(ol());}else{
zJ();Xp();Uf();Qf=Y;Rf=gE;wy();pQ();GQ();eE();po||(oo=to(),F(244)&&oo["0"]&&In(Dn.aa.qe,JSON.stringify(oo)));Bp();qD();mj();EC();YC=!1;z.readyState==="complete"?$C():Rc(w,"load",$C);yC();Rl&&(Iq(Vq),w.setInterval(Uq,864E5),Iq(zE),Iq(QB),Iq(Iz),Iq(Yq),Iq(HE),Iq(aC),F(120)&&(Iq(VB),Iq(WB),Iq(XB)),AE={},BE={},Iq(DE),Iq(EE),jj());Tl&&(co(),oq(),sD(),BD(),zD(),Vn("bt",String(gj.H?
2:gj.C?1:0)),Vn("ct",String(gj.H?0:gj.C?1:3)),vD(),yD());WD();no(1);UF();Kk=Hb();Jp.bootstrap=Kk;gj.R&&pD();F(109)&&dA();F(134)&&(typeof w.name==="string"&&Mb(w.name,"web-pixel-sandbox-CUSTOM")&&jd()?sQ("dMDg0Yz"):w.Shopify&&(sQ("dN2ZkMj"),jd()&&sQ("dNTU0Yz")))}}}catch(b){no(4),Rq()}}
(function(a){function b(){n=z.documentElement.getAttribute("data-tag-assistant-present");Uo(n)&&(l=h.xl)}function c(){l&&Dc?g(l):a()}if(!w[ej(37)]){var d=!1;if(z.referrer){var e=bl(z.referrer);d=Yk(e,"host")===ej(38)}if(!d){var f=Ds(ej(39));d=!(!f.length||!f[0].length)}d&&(w[ej(37)]=!0,Mc(ej(40)))}var g=function(u){var v="GTM",x="GTM";Gk&&(v="OGT",x="GTAG");var y=ej(23),B=w[y];B||(B=[],w[y]=B,Mc("https://"+ej(3)+"/debug/bootstrap?id="+ej(5)+"&src="+x+"&cond="+String(u)+"&gtm="+es()));var D={messageType:"CONTAINER_STARTING",
data:{scriptSource:Dc,containerProduct:v,debug:!1,id:ej(5),targetRef:{ctid:ej(5),isDestination:ml(),canonicalId:ej(6)},aliases:pl(),destinations:nl()}};D.data.resume=function(){a()};dj(2)&&(D.data.initialPublish=!0);B.push(D)},h={Xo:1,Kl:2,dm:3,jk:4,xl:5};h[h.Xo]="GTM_DEBUG_LEGACY_PARAM";h[h.Kl]="GTM_DEBUG_PARAM";h[h.dm]="REFERRER";h[h.jk]="COOKIE";h[h.xl]="EXTENSION_PARAM";var l=void 0,n=void 0,p=Wk(w.location,"query",!1,void 0,"gtm_debug");Uo(p)&&(l=h.Kl);if(!l&&z.referrer){var q=bl(z.referrer);
Yk(q,"host")===ej(24)&&(l=h.dm)}if(!l){var r=Ds("__TAG_ASSISTANT");r.length&&r[0].length&&(l=h.jk)}l||b();if(!l&&To(n)){var t=!1;Rc(z,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);w.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){!HQ||to()["0"]?ro():qo()});

})()

