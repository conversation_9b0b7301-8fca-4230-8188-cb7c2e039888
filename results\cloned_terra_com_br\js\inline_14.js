
/***** FEED TAGMAN-TGMKEYS ****/
var tempTgm = [];
if(tempTgm.length > 0){
	window.tgmkeys = tempTgm[0];
}else{
	window.tgmkeys = [];
}
var tagmanAreas = [{"s0_altura_minima": "90", "s0_altura_minima_mobile": "50", "t360.home": {"web": {"areas": {"1": {"cards": [{"card_width": 300, "card_height": 250, "full_width": 970, "full_height": 250, "type": "rect"}]}, "2": {"cards": [{"card_width": 300, "card_height": 250, "full_width": "parallax", "full_height": "parallax", "type": "rect"}]}, "3": {"cards": [{"card_width": 300, "card_height": 600, "full_width": 970, "full_height": 250, "type": "vert"}]}}, "viewport_max": 3, "viewport_order": [2, 3, 2, 1]}, "mob": {"areas": {"1": {"cards": [{"card_width": 300, "card_height": 250, "full_width": 320, "full_height": 50, "type": "rect"}]}, "2": {"cards": [{"card_width": 300, "card_height": 600, "type": "vert"}]}, "3": {"cards": [{"card_width": 300, "card_height": 250, "type": "rect"}]}}, "viewport_max": 3, "viewport_order": [3]}}, "home": {"areas": {"1": {"cards": [{"height": 250, "width": 300}]}, "2": {"full": true}, "3": {"cards": [{"height": 600, "width": 300}], "full": true}, "4": {"cards": [{"height": 250, "width": 300}]}}, "viewport_max": 4, "viewport_order": [4, 2, 3, 4]}, "subject": {"home": {"areas": {"1": {"cards": [{"height": 250, "width": 300}]}, "2": {"full": true}, "3": {"cards": [{"height": 600, "width": 300}], "full": true}, "4": {"cards": [{"height": 250, "width": 300}]}}, "viewport_max": 4, "viewport_order": [4, 2, 3, 4]}}, "media_service": {"home": {"areas": {"1": {"cards": [{"height": 250, "width": 300}]}, "2": {"cards": [{"height": 250, "width": 300}]}, "3": {"cards": [{"height": 600, "width": 300}]}, "4": {"cards": [{"height": 250, "width": 300}]}}, "viewport_max": 4, "viewport_order": [4, 2, 3, 4]}, "article": {"areas": {"1": {"cards": [{"height": 250, "width": 300}]}, "2": {"full": true}, "3": {"cards": [{"height": 600, "width": 300}], "full": true}, "4": {"cards": [{"height": 250, "width": 300}]}}, "viewport_max": 4, "viewport_order": [4, 2, 3, 4]}}, "premium": {"home": {"areas": {"1": {"cards": [{"height": 250, "width": 300}]}, "2": {"full": true}, "3": {"cards": [{"height": 600, "width": 300}], "full": true}, "4": {"cards": [{"height": 250, "width": 300}]}}, "viewport_max": 4, "viewport_order": [4, 2, 3, 4]}, "article": {"areas": {"1": {"cards": [{"height": 250, "width": 300}]}, "2": {"full": true}, "3": {"cards": [{"height": 600, "width": 300}], "full": true}, "4": {"cards": [{"height": 250, "width": 300}]}}, "viewport_max": 4, "viewport_order": [4, 2, 3, 4]}}}];
if(tagmanAreas.length > 0){
	tagmanAreas = tagmanAreas[0];
}else{
	tagmanAreas = [];
}
zaz.use(function(pkg){
	pkg.context.page.set("timelineApps", {"id-editorial-table-app": {"id": "id-editorial-table-app", "name": "app.t360.editorialTable", "items": [{"id": "1649674", "type": "P", "logo": null, "label": "M\u00d3DULO: Not\u00edcias", "items": [{"id": "338da230244f58f175c8be18155a0e91rjvl7rr5", "url": "https://www.terra.com.br/mobilidade/carros/ataque-hacker-faz-jaguar-land-rover-parar-producao-no-reino-unido,338da230244f58f175c8be18155a0e91rjvl7rr5.html", "title": "Ataque hacker faz Jaguar Land Rover parar produ\u00e7\u00e3o no ...", "subTitle": null, "cartola": {"id": "0c00891274c4a310VgnVCM4000009bcceb0aRCRD", "path": "Brasil.mobilidade.carros", "label": "afetou f\u00e1bricas", "url": null, "customHat": true, "chooseEditor": true, "is_article_url": true}, "publishedDate": "2025-09-07 19:34:06 -0300", "firstPublishedDate": "2025-09-07 17:00:11 -0300", "socialURL": "https://www.terra.com.br/mobilidade/carros/ataque-hacker-faz-jaguar-land-rover-parar-producao-no-reino-unido,338da230244f58f175c8be18155a0e91rjvl7rr5.html", "source": {"id": "0d80799996d9629de4177e27c58ddc258as1zt8m", "name": "Estad\u00e3o Conte\u00fado", "audience": "estadaoconteudo", "description": null, "image": {"id": "5b5fbb5b4f2d3a84667ca43fe508e0e6e587127w", "url": "http://images.terra.com/2018/06/04/estadao-conteudo.jpg", "alt": "Estadao Conteudo.jpg Foto: Divulga\u00e7\u00e3o", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "estadao-conteudo.jpg", "caption": "Estadao Conteudo.jpg", "width": 2218, "height": 357, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": "Divulga\u00e7\u00e3o", "audience": "divulgacao", "description": null, "image": null, "keywords": null, "publicName": "Divulga\u00e7\u00e3o", "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2018-06-04 10:30:45 -0300", "link": null, "originalURL": "images.terra.com/2018/06/04/estadao-conteudo.jpg"}, "keywords": null, "publicName": "Estad\u00e3o", "url": null, "tgmKey": null, "hasAdvertising": "Y", "types": ["SRC"], "icon": {"id": "7b7a1cc6a4d289ccd8b6cd5cc1bace9cyzvch8eu", "originalURL": "images.terra.com/2018/11/20/estadao.png", "url": "http://images.terra.com/2018/11/20/estadao.png"}}, "channels": [{"id": "0c00891274c4a310VgnVCM4000009bcceb0aRCRD", "name": "Carros", "label": "Carros", "path": "Brasil.mobilidade.carros", "originalPath": "/Brasil/Mobilidade/Carros", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/mobilidade/carros/", "tgmKey": "br.mobilidade_carros", "idMenu": "mob_carros", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": "carrosemotos"}, "metricsChannel": null, "metricsSiteId": null, "description": "Acampanhe os principais lan\u00e7amentos e as \u00faltimas novidades no mercado de autom\u00f3veis. Carros el\u00e9tricos e h\u00edbridos, dicas para motoristas e muito mais.", "title": "Carros: Lan\u00e7amentos, Avalia\u00e7\u00f5es, Dicas | Terra Mobilidade", "terratv": null, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Guia do Carro", "link": "https://www.terra.com.br/parceiros/guia-do-carro/"}, {"display_order": 2, "name": "Mundo do Autom\u00f3vel para PCD", "link": "https://www.terra.com.br/parceiros/mundo-do-automovel-para-pcd/"}, {"display_order": 3, "name": "Revista Carro", "link": "https://revistacarro.com.br/"}, {"display_order": 4, "name": "V\u00eddeos", "link": "https://www.terra.com.br/mobilidade/carros/videos/"}], "usedInURL": true, "display": null}], "breadCrumb": ["20e07ef2795b2310VgnVCM3000009af154d0RCRD", "ebbd507fb533d6f52e4a1d7c7909cf6eolfc71pn", "0c00891274c4a310VgnVCM4000009bcceb0aRCRD"], "locations": null, "image": {"id": "cd67e5fbdbbca8c56529d65cc70ebe0cdlto1oly", "url": "http://images.terra.com/2025/09/07/257271086-gouhzdosk5j3rkmclvium5i5i4.png", "alt": "257271086-gouhzdosk5j3rkmclvium5i5i4.png Foto: JLR/Divulga\u00e7\u00e3o / Estad\u00e3o", "horizontalCrop": "/710/390/0/0/", "verticalCrop": null, "squareCrop": "/434/434/138/0/", "wideCrop": null, "order": 0, "name": "257271086-gouhzdosk5j3rkmclvium5i5i4.png", "caption": "257271086-gouhzdosk5j3rkmclvium5i5i4.png", "width": 710, "height": 433, "representative": false, "author": "JLR/Divulga\u00e7\u00e3o", "locality": null, "source": {"id": null, "name": "Estad\u00e3o Conte\u00fado", "audience": "estadaoconteudo", "description": null, "image": null, "keywords": null, "publicName": "Estad\u00e3o", "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-07 17:00:11 -0300", "link": null, "originalURL": "images.terra.com/2025/09/07/257271086-gouhzdosk5j3rkmclvium5i5i4.png"}, "category": "N", "configs": null, "paidContent": false, "fullTitle": "Ataque hacker faz Jaguar Land Rover parar produ\u00e7\u00e3o no Reino Unido", "ampURL": "https://www.terra.com.br/amp/mobilidade/carros/ataque-hacker-faz-jaguar-land-rover-parar-producao-no-reino-unido,338da230244f58f175c8be18155a0e91rjvl7rr5.html"}, {"id": "9ad83d791640dde8c40e5b95fd5c2b53unawab1w", "url": "https://www.terra.com.br/noticias/brasil/cidades/quem-era-jonas-stefanello-empresario-que-morreu-em-acidente-horas-apos-comprar-porsche-no-rs,9ad83d791640dde8c40e5b95fd5c2b53unawab1w.html", "title": "Quem era o empres\u00e1rio que morreu em acidente horas ap\u00f3s ...", "subTitle": null, "cartola": {"id": "edc151a77fb05310VgnVCM4000009bf154d0RCRD", "path": "Brasil.noticias.brasil.cidades", "label": "Jonas Stefanello", "url": null, "customHat": true, "chooseEditor": true, "is_article_url": true}, "publishedDate": "2025-09-07 12:33:39 -0300", "firstPublishedDate": "2025-09-07 09:38:50 -0300", "socialURL": "https://www.terra.com.br/noticias/brasil/cidades/quem-era-jonas-stefanello-empresario-que-morreu-em-acidente-horas-apos-comprar-porsche-no-rs,9ad83d791640dde8c40e5b95fd5c2b53unawab1w.html", "source": {"id": "9cce34b1d61575e0e75d8dc64e6a1e07b3eh2t87", "name": "Reda\u00e7\u00e3o Terra", "audience": "redacaoterra", "description": null, "image": null, "keywords": "Reda\u00e7\u00e3o Terra", "publicName": "Reda\u00e7\u00e3o Terra", "url": null, "tgmKey": null, "hasAdvertising": "Y", "types": ["SRC"], "icon": null}, "channels": [{"id": "edc151a77fb05310VgnVCM4000009bf154d0RCRD", "name": "Cidades", "label": "Cidades", "path": "Brasil.noticias.brasil.cidades", "originalPath": "/Brasil/Not\u00edcias/Brasil/Cidades", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/noticias/brasil/cidades/", "tgmKey": "br.news_brasil", "idMenu": "not-bras-cida", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": "cidades"}, "metricsChannel": null, "metricsSiteId": null, "description": "Confira as principais not\u00edcias das capitais e grandes cidades do Brasil. Acompanhe o dia a dia das cidades brasileiras no Terra.", "title": "Not\u00edcias das cidades e capitais do Brasil - Terra", "terratv": 4828, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Banda B", "link": "https://www.terra.com.br/parceiros/banda-b"}, {"display_order": 2, "name": "Porto Alegre 24 Horas", "link": "https://www.terra.com.br/parceiros/porto-alegre-24-horas/"}, {"display_order": 3, "name": "Bahia Not\u00edcias", "link": "https://www.terra.com.br/parceiros/bahia-noticias/"}, {"display_order": 4, "name": "Not\u00edcias de Mogi", "link": "https://noticiasdemogi.com.br/"}, {"display_order": 5, "name": "Portal da Prefeitura", "link": "https://portaldeprefeitura.com.br/"}, {"display_order": 6, "name": "Di\u00e1rio do Rio", "link": "https://www.terra.com.br/parceiros/diario-do-rio/"}], "usedInURL": true, "display": null}, {"id": "5a94baa8deb05310VgnVCM4000009bf154d0RCRD", "name": "Brasil", "label": "Brasil", "path": "Brasil.noticias.brasil", "originalPath": "/Brasil/Not\u00edcias/Brasil", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/noticias/brasil/", "tgmKey": "br.news_brasil", "idMenu": "not-bras", "metrics": {"terra_info_channel": "br.noticias.brasil", "terra_info_channeldetail": "/"}, "metricsChannel": "br.noticias.brasil", "metricsSiteId": null, "description": "Confira as \u00faltimas not\u00edcias, fotos e v\u00eddeos do Brasil, casos de pol\u00edticos e corrup\u00e7\u00e3o, crimes e investiga\u00e7\u00f5es policiais, o tr\u00e2nsito das principais cidades e muito mais.", "title": "Not\u00edcias do Brasil: pol\u00edtica, pol\u00edcia, tr\u00e2nsito e sua cidade - Terra", "terratv": 4194, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Checamos", "link": "https://www.terra.com.br/noticias/checamos/"}, {"display_order": 2, "name": "Pol\u00edtica", "link": "https://noticias.terra.com.br/brasil/politica"}, {"display_order": 3, "name": "Carta Capital", "link": "https://www.cartacapital.com.br/"}, {"display_order": 4, "name": "Perfil Brasil", "link": "https://www.terra.com.br/parceiros/perfil-brasil/"}, {"display_order": 5, "name": "Intercept Brasil", "link": "https://www.intercept.com.br/"}, {"display_order": 6, "name": "Ag\u00eancia P\u00fablica", "link": "https://apublica.org/"}], "usedInURL": false, "display": null}, {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "name": "Not\u00edcias", "label": "Not\u00edcias", "path": "Brasil.noticias", "originalPath": "/Brasil/Not\u00edcias", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/noticias/", "tgmKey": "br.news", "idMenu": "not", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": "/"}, "metricsChannel": null, "metricsSiteId": null, "description": "Acompanhe as \u00faltimas not\u00edcias e acontecimentos relevantes de cidades do Brasil e do mundo. Fique por dentro dos principais assuntos no Terra.", "title": "Terra Not\u00edcias: Brasil, Mundo, Cidades, Pol\u00edtica, Clima", "terratv": 4006, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Brasil", "link": "https://www.terra.com.br/noticias/brasil/"}, {"display_order": 2, "name": "Mundo", "link": "https://www.terra.com.br/noticias/mundo/"}, {"display_order": 4, "name": "Checamos", "link": "https://www.terra.com.br/noticias/checamos/"}, {"display_order": 5, "name": "Loterias", "link": "https://www.terra.com.br/noticias/loterias/"}, {"display_order": 6, "name": "Previs\u00e3o do Tempo", "link": "https://www.terra.com.br/noticias/previsao-do-tempo/"}, {"display_order": 7, "name": "V\u00eddeos", "link": "https://www.terra.com.br/noticias/videos/"}], "usedInURL": false, "display": null}], "breadCrumb": ["187775b4786b2310VgnVCM3000009af154d0RCRD", "edc151a77fb05310VgnVCM4000009bf154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "5a94baa8deb05310VgnVCM4000009bf154d0RCRD"], "locations": null, "image": {"id": "d6dd45ad6051898efa682e8a0f413c936wn0n0hn", "url": "http://images.terra.com/2025/09/07/empresario-acidente-rl7ua9goks36.jpg", "alt": "empres\u00e1rio-acidente.jpg Foto: Reprodu\u00e7\u00e3o", "horizontalCrop": "/960/527/0/0/", "verticalCrop": null, "squareCrop": "/640/640/160/0/", "wideCrop": null, "order": 0, "name": "empres\u00e1rio-acidente.jpg", "caption": "empres\u00e1rio-acidente.jpg", "width": 960, "height": 640, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": "Reprodu\u00e7\u00e3o/Instagram", "audience": "reproducaoinstagram", "description": null, "image": null, "keywords": null, "publicName": "Reprodu\u00e7\u00e3o", "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-07 09:34:33 -0300", "link": null, "originalURL": "images.terra.com/2025/09/07/empresario-acidente-rl7ua9goks36.jpg"}, "category": "N", "configs": null, "paidContent": false, "fullTitle": "Quem era o empres\u00e1rio que morreu em acidente horas ap\u00f3s comprar Porsche", "ampURL": "https://www.terra.com.br/amp/noticias/brasil/cidades/quem-era-jonas-stefanello-empresario-que-morreu-em-acidente-horas-apos-comprar-porsche-no-rs,9ad83d791640dde8c40e5b95fd5c2b53unawab1w.html"}], "fixedPosition": null, "source": null, "expirationDate": null, "name": "GROUP", "configs": {"orderWeb": "1", "subjectModule": "true", "idItemMenu": "not"}, "image": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false, "channelLogoDict": {"url": "http://images.terra.com/2024/09/02/agora-marca-2x-uv5to8qvvy2g.png", "width": 280, "height": 114}, "alternativeLogoDict": {"url": "http://images.terra.com/2024/09/02/agora-marca-2x-uv5to8qvvy2g.png", "width": 280, "height": 114}}, "cmsVideo": null, "url": "https://www.terra.com.br/noticias/", "title": "M\u00d3DULO: Not\u00edcias", "subTitle": null, "publishedDate": "2025-09-07 21:26:15 -0300", "publishedTime": 1757291175000, "date": "2025-09-07 19:52:26 -0300", "time": 1757285546000, "tgmKey": "br.news", "paidContent": false}, {"id": "1655400", "type": "P", "logo": null, "label": "M\u00d3DULO: Esporte", "items": [{"id": "7dd470701c32094abd33859439c4f4ce06wmv7dg", "url": "https://www.terra.com.br/esportes/memphis-depay-se-torna-o-maior-artilheiro-da-historia-da-selecao-holandesa,7dd470701c32094abd33859439c4f4ce06wmv7dg.html", "title": "Memphis Depay se torna o maior artilheiro da hist\u00f3ria da ...", "subTitle": null, "cartola": {"id": "2d19f517cd779310VgnVCM5000009ccceb0aRCRD", "path": "Brasil.esportes", "label": "Aos 31 anos", "url": null, "customHat": true, "chooseEditor": true, "channelLogoDict": {"url": "http://images.terra.com/2024/07/18/esportes-1ib8p302hbwol.png", "width": 280, "height": 67}, "alternativeLogoDict": {"url": "http://images.terra.com/2024/07/18/esportes-1ib8p302hbwol.png", "width": 280, "height": 67}, "isMediaService": true, "is_article_url": true}, "publishedDate": "2025-09-07 18:41:23 -0300", "firstPublishedDate": "2025-09-07 18:41:23 -0300", "socialURL": "https://www.terra.com.br/esportes/memphis-depay-se-torna-o-maior-artilheiro-da-historia-da-selecao-holandesa,7dd470701c32094abd33859439c4f4ce06wmv7dg.html", "source": {"id": "9cce34b1d61575e0e75d8dc64e6a1e07b3eh2t87", "name": "Reda\u00e7\u00e3o Terra", "audience": "redacaoterra", "description": null, "image": null, "keywords": "Reda\u00e7\u00e3o Terra", "publicName": "Reda\u00e7\u00e3o Terra", "url": null, "tgmKey": null, "hasAdvertising": "Y", "types": ["SRC"], "icon": null}, "channels": [{"id": "2d19f517cd779310VgnVCM5000009ccceb0aRCRD", "name": "Esportes", "label": "Esportes", "path": "Brasil.esportes", "originalPath": "/Brasil/Esportes", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/esportes/", "tgmKey": "br.sports", "idMenu": "esp", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": "/"}, "metricsChannel": null, "metricsSiteId": null, "description": "Fique por dentro de tudo que rola nos esportes: not\u00edcias de futebol, basquete, v\u00f4lei, surfe, lutas e muito mais! Olimp\u00edadas, Campeonatos e Copas!", "title": "Terra Esportes: Futebol, Automobilismo, Basquete, V\u00f4lei e mais", "terratv": 4005, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Brasileir\u00e3o", "link": "https://www.terra.com.br/esportes/futebol/brasileiro-serie-a/"}, {"display_order": 2, "name": "Copa do Brasil", "link": "https://www.terra.com.br/esportes/futebol/copa-do-brasil/"}, {"display_order": 3, "name": "Libertadores", "link": "https://www.terra.com.br/esportes/futebol/libertadores/"}, {"display_order": 4, "name": "Sul-Americana", "link": "https://www.terra.com.br/esportes/futebol/copa-sul-americana/"}, {"display_order": 5, "name": "Futebol feminino", "link": "https://www.terra.com.br/esportes/futebol/futebol-feminino/"}, {"display_order": 6, "name": "Surfe", "link": "https://www.terra.com.br/esportes/surfe/"}, {"display_order": 7, "name": "NBA", "link": "https://www.terra.com.br/esportes/basquete/nba/"}, {"display_order": 8, "name": "F\u00f3rmula1", "link": "https://www.terra.com.br/esportes/automobilismo/formula1/"}, {"display_order": 9, "name": "V\u00eddeos", "link": "https://www.terra.com.br/esportes/videos/"}], "usedInURL": true, "display": null}], "breadCrumb": ["2d19f517cd779310VgnVCM5000009ccceb0aRCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD"], "locations": null, "image": {"id": "35d1a8b07d88b11d3a9eccd22a22a0a1tl3mm0jc", "url": "http://images.terra.com/2025/08/28/agf20250827306-1k1x94mzxjga9.jpg", "alt": "AGF20250827306.jpg Foto: GIOVANI BACCIN/AGIF - AG\u00caNCIA DE FOTOGRAFIA/ESTAD\u00c3O CONTE\u00daDO", "horizontalCrop": "/2645/1452/0/294/", "verticalCrop": null, "squareCrop": "/1763/1763/467/0/", "wideCrop": null, "order": 0, "name": "AGF20250827306.jpg", "caption": "AGF20250827306.jpg", "width": 2645, "height": 1763, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": "GIOVANI BACCIN/AGIF - AG\u00caNCIA DE FOTOGRAFIA/ESTAD\u00c3O CONTE\u00daDO", "audience": "giovanibaccinagif-agenciadefotografiaestadaoconteudo", "description": null, "image": null, "keywords": null, "publicName": "GIOVANI BACCIN/AGIF - AG\u00caNCIA DE FOTOGRAFIA/ESTAD\u00c3O CONTE\u00daDO", "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-08-28 16:34:46 -0300", "link": null, "originalURL": "images.terra.com/2025/08/28/agf20250827306-1k1x94mzxjga9.jpg"}, "category": "N", "configs": null, "paidContent": true, "fullTitle": "Memphis Depay se torna o maior artilheiro da hist\u00f3ria da sele\u00e7\u00e3o holandesa", "ampURL": "https://www.terra.com.br/amp/esportes/memphis-depay-se-torna-o-maior-artilheiro-da-historia-da-selecao-holandesa,7dd470701c32094abd33859439c4f4ce06wmv7dg.html"}, {"id": "59d84689d968f13e2ff48e72dfd5e3cfawz1ys76", "url": "https://www.terra.com.br/esportes/futebol/internacional/ucraniano-do-benfica-tem-casa-bombardeada-em-ataque-russo,59d84689d968f13e2ff48e72dfd5e3cfawz1ys76.html", "title": "Ucraniano do Benfica tem casa bombardeada em ataque russo", "subTitle": null, "cartola": {"id": "42e8d98736199310VgnVCM4000009bcceb0aRCRD", "path": "Brasil.esportes.futebol.internacional", "label": "Georgiy Sudakov", "url": null, "customHat": true, "chooseEditor": true, "is_article_url": true}, "publishedDate": "2025-09-07 10:30:56 -0300", "firstPublishedDate": "2025-09-07 10:30:56 -0300", "socialURL": "https://www.terra.com.br/esportes/futebol/internacional/ucraniano-do-benfica-tem-casa-bombardeada-em-ataque-russo,59d84689d968f13e2ff48e72dfd5e3cfawz1ys76.html", "source": {"id": "4c5765e35a07013cbc3dc3e80f7916db6hq3weco", "name": "Jogada10", "audience": "jogada10", "description": null, "image": {"id": "29c8d353937f509b1760dd12d0ad64ab9gm6rwnd", "url": "http://images.terra.com/2023/02/16/logo-j10-t4nvihl7ulh3.png", "alt": "LOGO J10.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO J10.png", "caption": "LOGO J10.png", "width": 472, "height": 412, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2023-02-16 10:20:43 -0300", "link": null, "originalURL": "images.terra.com/2023/02/16/logo-j10-t4nvihl7ulh3.png"}, "keywords": "var idItemMenu = 'jogada10';", "publicName": "Jogada10", "url": "https://www.terra.com.br/parceiros/jogada10/", "tgmKey": "cobranded_jogada10", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "5c7270e2d5acb4475812397abd6a0765nzwxl1hd", "originalURL": "images.terra.com/2023/02/16/faviconj10-1hrotx6r2nwog.png", "url": "http://images.terra.com/2023/02/16/faviconj10-1hrotx6r2nwog.png"}}, "channels": [{"id": "42e8d98736199310VgnVCM4000009bcceb0aRCRD", "name": "Internacional", "label": "Futebol Internacional", "path": "Brasil.esportes.futebol.internacional", "originalPath": "/Brasil/Esportes/Futebol/Internacional", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/esportes/futebol/internacional/", "tgmKey": "br.sports_futebolinternacional", "idMenu": "esp-fint", "metrics": {"terra_info_channel": "br.esportes.futebol", "terra_info_channeldetail": "Futebol"}, "metricsChannel": "br.esportes.futebol", "metricsSiteId": null, "description": "Confira tudo sobre Futebol Internacional: Campeonatos Espanhol, Ingl\u00eas, Italiano, Alem\u00e3o, Argentino e mais. Veja jogos ao vivo, tabela e classifica\u00e7\u00e3o, artilheiros e mais.", "title": "Futebol Internacional: Campeonatos Espanhol, Ingl\u00eas, Italiano e mais - Terra", "terratv": 4576, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": true, "display": null}, {"id": "a07ad497af49d8f26a7f561577d0a2f41ug02v2a", "name": "Jogada10", "label": "Jogada10", "path": "Brasil.parceiros.jogada10", "originalPath": "/Brasil/parceiros/Jogada10", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/jogada10/", "tgmKey": "br.cobranded_jogada10", "idMenu": "jogada10", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}], "breadCrumb": ["2d19f517cd779310VgnVCM5000009ccceb0aRCRD", "04d1b3f715879310VgnVCM5000009ccceb0aRCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "42e8d98736199310VgnVCM4000009bcceb0aRCRD", "afa9280af150acfe65477a86ec056f3dxns5uqbe", "a07ad497af49d8f26a7f561577d0a2f41ug02v2a"], "locations": null, "image": {"id": "c19745b97a2851221cf495e959a795f3hso0he9a", "url": "http://images.terra.com/2025/09/07/742484373-imagemredimensionadafinal-610x400.jpg", "alt": "742484373-imagemredimensionadafinal-610x400.jpg Foto: Jogada10", "horizontalCrop": "/610/335/0/0/", "verticalCrop": null, "squareCrop": "/400/400/105/0/", "wideCrop": null, "order": 0, "name": "742484373-imagemredimensionadafinal-610x400.jpg", "caption": "742484373-imagemredimensionadafinal-610x400.jpg", "width": 610, "height": 400, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": "Jogada10", "audience": "jogada10", "description": null, "image": null, "keywords": null, "publicName": "Jogada10", "url": "https://www.terra.com.br/parceiros/jogada10/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-07 10:30:56 -0300", "link": null, "originalURL": "images.terra.com/2025/09/07/742484373-imagemredimensionadafinal-610x400.jpg"}, "category": "N", "configs": null, "paidContent": false, "fullTitle": "Ucraniano do Benfica tem casa bombardeada em ataque russo", "ampURL": "https://www.terra.com.br/amp/esportes/futebol/internacional/ucraniano-do-benfica-tem-casa-bombardeada-em-ataque-russo,59d84689d968f13e2ff48e72dfd5e3cfawz1ys76.html"}], "fixedPosition": null, "source": null, "expirationDate": null, "name": "GROUP", "configs": {"subjectModule": "true", "orderWeb": "1", "idItemMenu": "esp"}, "image": null, "cartola": {"id": "2d19f517cd779310VgnVCM5000009ccceb0aRCRD", "path": "Brasil.esportes", "label": "Esportes", "url": "https://www.terra.com.br/esportes/", "customHat": false, "chooseEditor": false, "channelLogoDict": {"url": "http://images.terra.com/2024/07/18/esportes-1ib8p302hbwol.png", "width": 280, "height": 67}, "alternativeLogoDict": {"url": "http://images.terra.com/2024/07/18/esportes-1ib8p302hbwol.png", "width": 280, "height": 67}, "isMediaService": true}, "cmsVideo": null, "url": "https://www.terra.com.br/esportes/", "title": "M\u00d3DULO: Esporte", "subTitle": null, "publishedDate": "2025-09-07 21:26:15 -0300", "publishedTime": 1757291175000, "date": "2025-09-06 19:50:42 -0300", "time": 1757199042000, "tgmKey": "br.sports", "paidContent": true}, {"id": "1707998", "type": "P", "logo": null, "label": "M\u00f3dulo de Entrete", "items": [{"id": "265bd159cd8b1f5108ba348a4d59ead223m095ut", "url": "https://www.terra.com.br/diversao/gente/gente-pabllo-vittar-pula-no-publico-nao-e-segurada-e-cai-no-chao-assista,265bd159cd8b1f5108ba348a4d59ead223m095ut.html", "title": "Pabllo Vittar pula no p\u00fablico, n\u00e3o \u00e9 segurada e cai no ...", "subTitle": null, "cartola": {"id": "2301493d73088310VgnVCM4000009bcceb0aRCRD", "path": "Brasil.diversao.gente", "label": "V\u00eddeo viralizou", "url": null, "customHat": true, "chooseEditor": true, "is_article_url": true}, "publishedDate": "2025-09-07 19:39:06 -0300", "firstPublishedDate": "2025-09-07 17:48:53 -0300", "socialURL": "https://www.terra.com.br/diversao/gente/gente-pabllo-vittar-pula-no-publico-nao-e-segurada-e-cai-no-chao-assista,265bd159cd8b1f5108ba348a4d59ead223m095ut.html", "source": {"id": "9118538e60d778461a5f24a836dc7e47h0kms1g9", "name": "Mais Novela", "audience": "maisnovela", "description": null, "image": {"id": "91f8ac746aa4447c37b3dfc37963f2426tqa8max", "url": "http://images.terra.com/2023/02/15/logo-mais-novela_80x30-s1b35prz75nm.png", "alt": "Logo-Mais-Novela_80x30.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "Logo-Mais-Novela_80x30.png", "caption": "Logo-Mais-Novela_80x30.png", "width": 80, "height": 30, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2023-02-15 14:12:53 -0300", "link": null, "originalURL": "images.terra.com/2023/02/15/logo-mais-novela_80x30-s1b35prz75nm.png"}, "keywords": "var idItemMenu = 'maisnovela';", "publicName": "Mais Novela", "url": "https://www.terra.com.br/parceiros/mais-novela/", "tgmKey": "cobranded_maisnovela", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4cae2f7e5297531dbd78002a26c3ddb6cyuyvia4", "originalURL": "images.terra.com/2023/02/15/favicon-mais-novela_32x32-vevd4ju3cig4.png", "url": "http://images.terra.com/2023/02/15/favicon-mais-novela_32x32-vevd4ju3cig4.png"}}, "channels": [{"id": "0e3555f9e8bce4372756c45e4eb10d73fclm13z9", "name": "Mais Novela", "label": "Mais Novela", "path": "Brasil.parceiros.mais-novela", "originalPath": "/Brasil/parceiros/Mais Novela", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/mais-novela/", "tgmKey": "br.cobranded_maisnovela", "idMenu": "maisnovela", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "2301493d73088310VgnVCM4000009bcceb0aRCRD", "name": "Gente", "label": "Famosos", "path": "Brasil.diversao.gente", "originalPath": "/Brasil/Divers\u00e3o/Gente", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/diversao/gente/", "tgmKey": "br.entertainment_gente", "idMenu": "dive-difa", "metrics": {"terra_info_channel": "br.diversao.gente", "terra_info_channeldetail": "/"}, "metricsChannel": "br.diversao.gente", "metricsSiteId": null, "description": "Curte uma fofoca? Ent\u00e3o acesse o Terra e confira not\u00edcias do mundo das celebridades, fotos e v\u00eddeos dos famosos e influencers.", "title": "Gente: Not\u00edcias de Famosos e Celebridades, Fofocas dos Artistas | Terra", "terratv": 4936, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": true, "display": null}], "breadCrumb": ["8a8775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "afa9280af150acfe65477a86ec056f3dxns5uqbe", "2301493d73088310VgnVCM4000009bcceb0aRCRD", "0e3555f9e8bce4372756c45e4eb10d73fclm13z9"], "locations": null, "image": {"id": "714d169c0c5660ced10f92643e0432dbgh1yb2ka", "url": "http://images.terra.com/2025/09/07/1570249040-whatsapp-image-2024-11-08-at-110255.jpeg", "alt": "1570249040-whatsapp-image-2024-11-08-at-110255.jpeg Foto: Reprodu\u00e7\u00e3o/Instagram / Mais Novela", "horizontalCrop": "/966/530/0/0/", "verticalCrop": null, "squareCrop": "/544/544/211/0/", "wideCrop": null, "order": 0, "name": "1570249040-whatsapp-image-2024-11-08-at-110255.jpeg", "caption": "1570249040-whatsapp-image-2024-11-08-at-110255.jpeg", "width": 966, "height": 543, "representative": false, "author": "Reprodu\u00e7\u00e3o/Instagram", "locality": null, "source": {"id": null, "name": "Mais Novela", "audience": "maisnovela", "description": null, "image": null, "keywords": null, "publicName": "Mais Novela", "url": "https://www.terra.com.br/parceiros/mais-novela/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-07 17:48:48 -0300", "link": null, "originalURL": "images.terra.com/2025/09/07/1570249040-whatsapp-image-2024-11-08-at-110255.jpeg"}, "category": "N", "configs": null, "paidContent": false, "fullTitle": "Pabllo Vittar pula no p\u00fablico, n\u00e3o \u00e9 segurada e cai no ch\u00e3o; assista", "ampURL": "https://www.terra.com.br/amp/diversao/gente/gente-pabllo-vittar-pula-no-publico-nao-e-segurada-e-cai-no-chao-assista,265bd159cd8b1f5108ba348a4d59ead223m095ut.html"}, {"id": "5339024851d3f0e4aac75a7fa3faf736qpo3qwei", "url": "https://www.terra.com.br/diversao/gente/ator-global-alfineta-jojo-todynho-apos-justica-marcar-audiencia-com-o-pt,5339024851d3f0e4aac75a7fa3faf736qpo3qwei.html", "title": "Ator global alfineta Jojo Todynho ap\u00f3s justi\u00e7a marcar ...", "subTitle": null, "cartola": {"id": "2301493d73088310VgnVCM4000009bcceb0aRCRD", "path": "Brasil.diversao.gente", "label": "nas redes sociais", "url": null, "customHat": true, "chooseEditor": true, "is_article_url": true}, "publishedDate": "2025-09-07 19:40:40 -0300", "firstPublishedDate": "2025-09-07 16:03:49 -0300", "socialURL": "https://www.terra.com.br/diversao/gente/ator-global-alfineta-jojo-todynho-apos-justica-marcar-audiencia-com-o-pt,5339024851d3f0e4aac75a7fa3faf736qpo3qwei.html", "source": {"id": "9118538e60d778461a5f24a836dc7e47h0kms1g9", "name": "Mais Novela", "audience": "maisnovela", "description": null, "image": {"id": "91f8ac746aa4447c37b3dfc37963f2426tqa8max", "url": "http://images.terra.com/2023/02/15/logo-mais-novela_80x30-s1b35prz75nm.png", "alt": "Logo-Mais-Novela_80x30.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "Logo-Mais-Novela_80x30.png", "caption": "Logo-Mais-Novela_80x30.png", "width": 80, "height": 30, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2023-02-15 14:12:53 -0300", "link": null, "originalURL": "images.terra.com/2023/02/15/logo-mais-novela_80x30-s1b35prz75nm.png"}, "keywords": "var idItemMenu = 'maisnovela';", "publicName": "Mais Novela", "url": "https://www.terra.com.br/parceiros/mais-novela/", "tgmKey": "cobranded_maisnovela", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4cae2f7e5297531dbd78002a26c3ddb6cyuyvia4", "originalURL": "images.terra.com/2023/02/15/favicon-mais-novela_32x32-vevd4ju3cig4.png", "url": "http://images.terra.com/2023/02/15/favicon-mais-novela_32x32-vevd4ju3cig4.png"}}, "channels": [{"id": "2301493d73088310VgnVCM4000009bcceb0aRCRD", "name": "Gente", "label": "Famosos", "path": "Brasil.diversao.gente", "originalPath": "/Brasil/Divers\u00e3o/Gente", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/diversao/gente/", "tgmKey": "br.entertainment_gente", "idMenu": "dive-difa", "metrics": {"terra_info_channel": "br.diversao.gente", "terra_info_channeldetail": "/"}, "metricsChannel": "br.diversao.gente", "metricsSiteId": null, "description": "Curte uma fofoca? Ent\u00e3o acesse o Terra e confira not\u00edcias do mundo das celebridades, fotos e v\u00eddeos dos famosos e influencers.", "title": "Gente: Not\u00edcias de Famosos e Celebridades, Fofocas dos Artistas | Terra", "terratv": 4936, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": true, "display": null}, {"id": "25c9e27e67085bb61d3bdee3789fe773sh862gv4", "name": "Lula", "label": "Lula", "path": "Brasil.tags.lula", "originalPath": "/Brasil/tags/Lula", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/tudo-sobre/lula/", "tgmKey": "br.tudo_sobre", "idMenu": null, "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": "Luiz In\u00e1cio Lula da Silva, conhecido como Lula, \u00e9 o atual presidente do Brasil, ex-metal\u00fargico e l\u00edder sindical, com destaque por suas pol\u00edticas de inclus\u00e3o social.", "title": "Lula: not\u00edcias, novidades e carreira", "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "0e3555f9e8bce4372756c45e4eb10d73fclm13z9", "name": "Mais Novela", "label": "Mais Novela", "path": "Brasil.parceiros.mais-novela", "originalPath": "/Brasil/parceiros/Mais Novela", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/mais-novela/", "tgmKey": "br.cobranded_maisnovela", "idMenu": "maisnovela", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}], "breadCrumb": ["8a8775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "9ab0e08326de34d8312de6369a884623etka8k8m", "afa9280af150acfe65477a86ec056f3dxns5uqbe", "25c9e27e67085bb61d3bdee3789fe773sh862gv4", "2301493d73088310VgnVCM4000009bcceb0aRCRD", "0e3555f9e8bce4372756c45e4eb10d73fclm13z9"], "locations": null, "image": {"id": "772bcfac2cc47985d6bf9ced4e2971d2bcxvju5v", "url": "http://images.terra.com/2025/09/07/1249838922-whatsapp-image-2025-08-21-at-143457.jpg", "alt": "1249838922-whatsapp-image-2025-08-21-at-143457.jpg Foto: Mais Novela", "horizontalCrop": "/1920/1054/0/0/", "verticalCrop": null, "squareCrop": "/1082/1082/419/0/", "wideCrop": null, "order": 0, "name": "1249838922-whatsapp-image-2025-08-21-at-143457.jpg", "caption": "1249838922-whatsapp-image-2025-08-21-at-143457.jpg", "width": 1920, "height": 1080, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": "Mais Novela", "audience": "maisnovela", "description": null, "image": null, "keywords": null, "publicName": "Mais Novela", "url": "https://www.terra.com.br/parceiros/mais-novela/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-07 16:03:47 -0300", "link": null, "originalURL": "images.terra.com/2025/09/07/1249838922-whatsapp-image-2025-08-21-at-143457.jpg"}, "category": "N", "configs": null, "paidContent": false, "fullTitle": "Ator global alfineta Jojo Todynho ap\u00f3s justi\u00e7a marcar audi\u00eancia com o PT", "ampURL": "https://www.terra.com.br/amp/diversao/gente/ator-global-alfineta-jojo-todynho-apos-justica-marcar-audiencia-com-o-pt,5339024851d3f0e4aac75a7fa3faf736qpo3qwei.html"}], "fixedPosition": null, "source": null, "expirationDate": null, "name": "GROUP", "configs": {"orderWeb": "1", "subjectModule": "true", "idItemMenu": "dive"}, "image": null, "cartola": {"id": "8a8775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.diversao", "label": "Entret\u00ea", "url": "https://www.terra.com.br/diversao/", "customHat": false, "chooseEditor": false, "channelLogoDict": {"url": "http://images.terra.com/2023/04/05/entrete-u82xkl235hrk.png", "width": 108, "height": 40}}, "cmsVideo": null, "url": "https://www.terra.com.br/diversao/", "title": "M\u00f3dulo de Entrete", "subTitle": null, "publishedDate": "2025-09-07 21:26:15 -0300", "publishedTime": 1757291175000, "date": "2025-09-06 21:35:51 -0300", "time": 1757205351000, "tgmKey": "br.entertainment", "paidContent": false}, {"id": "1474600", "type": "P", "logo": null, "label": "M\u00d3DULO: Vida e Estilo", "items": [{"id": "2ae44b440c293f0a0102be2470868144d78uod3m", "url": "https://www.terra.com.br/diversao/gente/diferentes-mae-de-virginia-curte-post-polemico-alfinetando-paolla-oliveira,2ae44b440c293f0a0102be2470868144d78uod3m.html", "title": "Diferentes? M\u00e3e de Virginia curte post pol\u00eamico ...", "subTitle": null, "cartola": {"id": "2301493d73088310VgnVCM4000009bcceb0aRCRD", "path": "Brasil.diversao.gente", "label": "Eita!", "url": null, "customHat": true, "chooseEditor": true, "is_article_url": true}, "publishedDate": "2025-09-07 19:39:56 -0300", "firstPublishedDate": "2025-09-07 16:45:57 -0300", "socialURL": "https://www.terra.com.br/diversao/gente/diferentes-mae-de-virginia-curte-post-polemico-alfinetando-paolla-oliveira,2ae44b440c293f0a0102be2470868144d78uod3m.html", "source": {"id": "e624e100a27f4a0b5368e8f04d570a485pol4btp", "name": "Contigo", "audience": "contigo", "description": "Contigo", "image": {"id": "ed894c2e0b464b8bde803a949739349b0eedn3pg", "url": "http://images.terra.com/2024/10/24/logo_contigo-urejw6y67o6p.png", "alt": "LOGO_CONTIGO.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_CONTIGO.png", "caption": "LOGO_CONTIGO.png", "width": 535, "height": 180, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2024-10-24 17:30:35 -0300", "link": null, "originalURL": "images.terra.com/2024/10/24/logo_contigo-urejw6y67o6p.png"}, "keywords": null, "publicName": "Contigo", "url": "https://www.terra.com.br/parceiros/contigo/", "tgmKey": "cobranded_contigo", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "ed894c2e0b464b8bde803a949739349b0eedn3pg", "originalURL": "images.terra.com/2024/10/24/logo_contigo-urejw6y67o6p.png", "url": "http://images.terra.com/2024/10/24/logo_contigo-urejw6y67o6p.png"}}, "channels": [{"id": "2301493d73088310VgnVCM4000009bcceb0aRCRD", "name": "Gente", "label": "Famosos", "path": "Brasil.diversao.gente", "originalPath": "/Brasil/Divers\u00e3o/Gente", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/diversao/gente/", "tgmKey": "br.entertainment_gente", "idMenu": "dive-difa", "metrics": {"terra_info_channel": "br.diversao.gente", "terra_info_channeldetail": "/"}, "metricsChannel": "br.diversao.gente", "metricsSiteId": null, "description": "Curte uma fofoca? Ent\u00e3o acesse o Terra e confira not\u00edcias do mundo das celebridades, fotos e v\u00eddeos dos famosos e influencers.", "title": "Gente: Not\u00edcias de Famosos e Celebridades, Fofocas dos Artistas | Terra", "terratv": 4936, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": true, "display": null}, {"id": "9e3bb74a81671bbbb96b092e3fb378640jefzwps", "name": "Virginia Fonseca", "label": "Virginia Fonseca", "path": "Brasil.tags.virginia-fonseca", "originalPath": "/Brasil/tags/Virginia Fonseca", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/tudo-sobre/virginia-fonseca/", "tgmKey": "br.tudo_sobre", "idMenu": null, "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": "Virginia Fonseca, influenciadora, criadora de conte\u00fado e empres\u00e1ria de sucesso no Brasil. Veja not\u00edcias sobre ela", "title": "Virginia Fonseca: not\u00edcias, carreira, curiosidades e novidades\"", "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "ec969998f649a9d21ed012f09f8cbc98d7dndc80", "name": "Paolla Oliveira", "label": "Paolla Oliveira", "path": "Brasil.tags.paolla-oliveira", "originalPath": "/Brasil/tags/Paolla Oliveira", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/tudo-sobre/paolla-oliveira/", "tgmKey": "br.tudo_sobre", "idMenu": null, "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": "Paolla Olivera, atriz brasileira, \u00e9 consagrada por grandes pap\u00e9is e \u00e9 uma estrela do carnaval carioca. Veja not\u00edcias sobre ela", "title": "Paolla Olivera: not\u00edcias, carreira, curiosidades e novidades", "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "b5eb2faf57933162820364148f71be9an94m67m7", "name": "Contigo", "label": "Contigo", "path": "Brasil.parceiros.contigo", "originalPath": "/Brasil/parceiros/Contigo", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/contigo/", "tgmKey": "br.cobranded_contigo", "idMenu": "contigo", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": "Contigo - Terra", "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}], "breadCrumb": ["b5eb2faf57933162820364148f71be9an94m67m7", "8a8775b4786b2310VgnVCM3000009af154d0RCRD", "ec969998f649a9d21ed012f09f8cbc98d7dndc80", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "9ab0e08326de34d8312de6369a884623etka8k8m", "9e3bb74a81671bbbb96b092e3fb378640jefzwps", "afa9280af150acfe65477a86ec056f3dxns5uqbe", "2301493d73088310VgnVCM4000009bcceb0aRCRD"], "locations": null, "image": {"id": "b1a2d564bb0967e8338dab95afc18164a6mw1wch", "url": "http://images.terra.com/2025/09/07/5192862-margareth-serrao-virginia-e-paolla-oliveira.jpg", "alt": "Contigo Foto: Reprodu\u00e7\u00e3o/Instagram / Contigo", "horizontalCrop": "/1920/1054/0/0/", "verticalCrop": null, "squareCrop": "/939/939/5/0/", "wideCrop": null, "order": 0, "name": "5192862-margareth-serrao-virginia-e-paolla-oliveira.jpg", "caption": "Contigo", "width": 1920, "height": 1080, "representative": false, "author": "Reprodu\u00e7\u00e3o/Instagram", "locality": null, "source": {"id": null, "name": "Contigo", "audience": "contigo", "description": "Contigo", "image": null, "keywords": null, "publicName": "Contigo", "url": "https://www.terra.com.br/parceiros/contigo/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-07 16:45:51 -0300", "link": null, "originalURL": "images.terra.com/2025/09/07/5192862-margareth-serrao-virginia-e-paolla-oliveira.jpg"}, "category": "N", "configs": null, "paidContent": false, "fullTitle": "Diferentes? M\u00e3e de Virginia curte post pol\u00eamico alfinetando Paolla Oliveira", "ampURL": "https://www.terra.com.br/amp/diversao/gente/diferentes-mae-de-virginia-curte-post-polemico-alfinetando-paolla-oliveira,2ae44b440c293f0a0102be2470868144d78uod3m.html"}, {"id": "7b2dbe374e2d519bab291a682c2f73f9uladk6xx", "url": "https://www.terra.com.br/diversao/gente/priscilla-surpreende-ao-confirmar-que-esta-casada-saiba-quem-e-o-marido,7b2dbe374e2d519bab291a682c2f73f9uladk6xx.html", "title": "Priscilla surpreende ao confirmar que est\u00e1 casada; saiba ...", "subTitle": null, "cartola": {"id": "2301493d73088310VgnVCM4000009bcceb0aRCRD", "path": "Brasil.diversao.gente", "label": "oficializou a uni\u00e3o", "url": null, "customHat": true, "chooseEditor": true, "is_article_url": true}, "publishedDate": "2025-09-07 19:37:15 -0300", "firstPublishedDate": "2025-09-07 17:54:52 -0300", "socialURL": "https://www.terra.com.br/diversao/gente/priscilla-surpreende-ao-confirmar-que-esta-casada-saiba-quem-e-o-marido,7b2dbe374e2d519bab291a682c2f73f9uladk6xx.html", "source": {"id": "e624e100a27f4a0b5368e8f04d570a485pol4btp", "name": "Contigo", "audience": "contigo", "description": "Contigo", "image": {"id": "ed894c2e0b464b8bde803a949739349b0eedn3pg", "url": "http://images.terra.com/2024/10/24/logo_contigo-urejw6y67o6p.png", "alt": "LOGO_CONTIGO.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_CONTIGO.png", "caption": "LOGO_CONTIGO.png", "width": 535, "height": 180, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2024-10-24 17:30:35 -0300", "link": null, "originalURL": "images.terra.com/2024/10/24/logo_contigo-urejw6y67o6p.png"}, "keywords": null, "publicName": "Contigo", "url": "https://www.terra.com.br/parceiros/contigo/", "tgmKey": "cobranded_contigo", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "ed894c2e0b464b8bde803a949739349b0eedn3pg", "originalURL": "images.terra.com/2024/10/24/logo_contigo-urejw6y67o6p.png", "url": "http://images.terra.com/2024/10/24/logo_contigo-urejw6y67o6p.png"}}, "channels": [{"id": "2301493d73088310VgnVCM4000009bcceb0aRCRD", "name": "Gente", "label": "Famosos", "path": "Brasil.diversao.gente", "originalPath": "/Brasil/Divers\u00e3o/Gente", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/diversao/gente/", "tgmKey": "br.entertainment_gente", "idMenu": "dive-difa", "metrics": {"terra_info_channel": "br.diversao.gente", "terra_info_channeldetail": "/"}, "metricsChannel": "br.diversao.gente", "metricsSiteId": null, "description": "Curte uma fofoca? Ent\u00e3o acesse o Terra e confira not\u00edcias do mundo das celebridades, fotos e v\u00eddeos dos famosos e influencers.", "title": "Gente: Not\u00edcias de Famosos e Celebridades, Fofocas dos Artistas | Terra", "terratv": 4936, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": true, "display": null}, {"id": "b5eb2faf57933162820364148f71be9an94m67m7", "name": "Contigo", "label": "Contigo", "path": "Brasil.parceiros.contigo", "originalPath": "/Brasil/parceiros/Contigo", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/contigo/", "tgmKey": "br.cobranded_contigo", "idMenu": "contigo", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": "Contigo - Terra", "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}], "breadCrumb": ["b5eb2faf57933162820364148f71be9an94m67m7", "8a8775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "afa9280af150acfe65477a86ec056f3dxns5uqbe", "2301493d73088310VgnVCM4000009bcceb0aRCRD"], "locations": null, "image": {"id": "8f39e7224568194d52df4445e9a2486d9ek36pup", "url": "http://images.terra.com/2025/09/07/1910973724-cantora-priscilla.jpg", "alt": "Contigo Foto: Reprodu\u00e7\u00e3o/Instagram / Contigo", "horizontalCrop": "/1920/1054/0/0/", "verticalCrop": null, "squareCrop": "/1082/1082/419/0/", "wideCrop": null, "order": 0, "name": "1910973724-cantora-priscilla.jpg", "caption": "Contigo", "width": 1920, "height": 1080, "representative": false, "author": "Reprodu\u00e7\u00e3o/Instagram", "locality": null, "source": {"id": null, "name": "Contigo", "audience": "contigo", "description": "Contigo", "image": null, "keywords": null, "publicName": "Contigo", "url": "https://www.terra.com.br/parceiros/contigo/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-07 17:54:51 -0300", "link": null, "originalURL": "images.terra.com/2025/09/07/1910973724-cantora-priscilla.jpg"}, "category": "N", "configs": null, "paidContent": false, "fullTitle": "Priscilla surpreende ao confirmar que est\u00e1 casada; saiba quem \u00e9 o marido", "ampURL": "https://www.terra.com.br/amp/diversao/gente/priscilla-surpreende-ao-confirmar-que-esta-casada-saiba-quem-e-o-marido,7b2dbe374e2d519bab291a682c2f73f9uladk6xx.html"}], "fixedPosition": null, "source": null, "expirationDate": null, "name": "GROUP", "configs": {"subjectModule": "true", "orderWeb": "1", "idItemMenu": "vida", "sponsors": {"sponsor_1_logo": {"url": "http://images.terra.com/2025/01/02/logo-terra-astral-negativo-2x-1ib8r0voy6cs4.png", "width": 151, "height": 70}, "sponsor_1_url": "https://www.terra.com.br/vida-e-estilo/horoscopo/terra-astral/?utm_source=portal-terra&utm_medium=espaco-editorial&utm_campaign=banner&utm_content=ROS_nao-assinantes_&utm_term=terra-astral_pg&cdConvenio=CVTR00001937", "sponsor_2_logo": {"url": "http://images.terra.com/2025/06/23/suvinil-positivo-fundo-transp-s1h7r98llvsj.png", "width": 240, "height": 52}, "sponsor_2_url": "https://loja.suvinil.com.br/produto/suvinil-fosco-sempre-limpo?embalagem=metalica", "sponsor_1_type": "1"}, "isBasicMediaService": true}, "image": null, "cartola": {"id": "4e9775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.vida-e-estilo", "label": "Vida e Estilo", "url": "https://www.terra.com.br/vida-e-estilo/", "customHat": false, "chooseEditor": false, "channelLogoDict": {"url": "http://images.terra.com/2023/03/24/terra-voce-qe9wqjkryr7a.png", "width": 97, "height": 40}, "isMediaService": true}, "cmsVideo": null, "url": "https://www.terra.com.br/vida-e-estilo/", "title": "M\u00d3DULO: Vida e Estilo", "subTitle": null, "publishedDate": "2025-09-07 21:26:15 -0300", "publishedTime": 1757291175000, "date": "2025-09-06 17:36:40 -0300", "time": 1757191000000, "tgmKey": "br.lifestyle", "paidContent": true}, {"id": "1649736", "type": "A", "logo": null, "label": "App Editorial", "items": [{"id": "1649749", "type": "A", "logo": null, "label": null, "items": [{"id": "1649749", "url": null, "title": "[Clubes] Brasileiro S\u00e9rie A - 256", "subTitle": null, "cartola": null, "publishedDate": "2024-03-27 14:09:53 -0300", "firstPublishedDate": "2024-03-27 14:09:53 -0300", "socialURL": null, "source": null, "channels": null, "breadCrumb": null, "locations": null, "image": null, "category": "app.t360.teams", "configs": {"id_championship": "256", "cardsizes": ["w2h2"]}, "paidContent": false, "fullTitle": "[Clubes] Brasileiro S\u00e9rie A - 256"}], "fixedPosition": "70", "source": null, "expirationDate": null, "name": "app.t360.teams", "configs": {"id_championship": "256", "cardsizes": ["w2h2"]}, "image": null, "cartola": null, "cmsVideo": null, "url": null, "title": "[Clubes] Brasileiro S\u00e9rie A - 256", "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": null, "paidContent": false, "menuItems": null, "croupier": {"reasons": {"a": ""}}}, {"id": "1649733", "type": "A", "logo": null, "label": null, "items": [{"id": "1649733", "url": null, "title": "[Hor\u00f3scopo]", "subTitle": null, "cartola": null, "publishedDate": "2024-03-27 14:04:35 -0300", "firstPublishedDate": "2024-03-27 14:04:35 -0300", "socialURL": null, "source": null, "channels": null, "breadCrumb": null, "locations": null, "image": null, "category": "app.t360.horoscope", "configs": {"cardsizes": ["w2h2"]}, "paidContent": false, "fullTitle": "[Hor\u00f3scopo]"}], "fixedPosition": "26", "source": null, "expirationDate": null, "name": "app.t360.horoscope", "configs": {"cardsizes": ["w2h2"]}, "image": null, "cartola": null, "cmsVideo": null, "url": null, "title": "[Hor\u00f3scopo]", "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": null, "paidContent": false, "menuItems": null, "croupier": {"reasons": {"a": ""}}}, {"id": "1703486", "type": "A", "logo": null, "label": null, "items": [{"id": "1703486", "url": null, "title": "[Clima]", "subTitle": null, "cartola": null, "publishedDate": "2025-02-27 10:35:59 -0300", "firstPublishedDate": "2025-02-27 10:35:59 -0300", "socialURL": null, "source": null, "channels": null, "breadCrumb": null, "locations": null, "image": null, "category": "app.t360.weather", "configs": {"cardsizes": ["w2h2"]}, "paidContent": false, "fullTitle": "[Clima]"}], "fixedPosition": "13", "source": null, "expirationDate": null, "name": "app.t360.weather", "configs": {"cardsizes": ["w2h2"]}, "image": null, "cartola": null, "cmsVideo": null, "url": null, "title": "[Clima]", "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": null, "paidContent": false, "menuItems": null, "croupier": {"reasons": {"a": ""}}}, {"id": "1649735", "type": "A", "logo": null, "label": null, "items": [{"id": "1649735", "url": "https://www.terra.com.br/noticias/", "title": "[Indicadores Financeiros]", "subTitle": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false, "channelLogoDict": {"url": "http://images.terra.com/2024/09/02/agora-marca-2x-uv5to8qvvy2g.png", "width": 280, "height": 114}, "alternativeLogoDict": {"url": "http://images.terra.com/2024/09/02/agora-marca-2x-uv5to8qvvy2g.png", "width": 280, "height": 114}}, "publishedDate": "2024-03-27 15:57:58 -0300", "firstPublishedDate": "2024-03-27 14:05:03 -0300", "socialURL": "https://www.terra.com.br/noticias/", "source": null, "channels": null, "breadCrumb": null, "locations": null, "image": null, "category": "app.t360.financial", "configs": {"subtype": "exchange", "renderBackend": "true", "cardsizes": ["w2h2"]}, "paidContent": false, "fullTitle": "[Indicadores Financeiros]"}], "fixedPosition": null, "source": null, "expirationDate": null, "name": "app.t360.financial", "configs": {"subtype": "exchange", "renderBackend": "true", "cardsizes": ["w2h2"], "idItemMenu": "not"}, "image": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false, "channelLogoDict": {"url": "http://images.terra.com/2024/09/02/agora-marca-2x-uv5to8qvvy2g.png", "width": 280, "height": 114}, "alternativeLogoDict": {"url": "http://images.terra.com/2024/09/02/agora-marca-2x-uv5to8qvvy2g.png", "width": 280, "height": 114}}, "cmsVideo": null, "url": "https://www.terra.com.br/noticias/", "title": "[Indicadores Financeiros]", "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": "br.news", "paidContent": false, "menuItems": [{"display_order": 1, "name": "Brasil", "link": "https://www.terra.com.br/noticias/brasil/"}, {"display_order": 2, "name": "Mundo", "link": "https://www.terra.com.br/noticias/mundo/"}, {"display_order": 4, "name": "Checamos", "link": "https://www.terra.com.br/noticias/checamos/"}, {"display_order": 5, "name": "Loterias", "link": "https://www.terra.com.br/noticias/loterias/"}, {"display_order": 6, "name": "Previs\u00e3o do Tempo", "link": "https://www.terra.com.br/noticias/previsao-do-tempo/"}, {"display_order": 7, "name": "V\u00eddeos", "link": "https://www.terra.com.br/noticias/videos/"}]}], "fixedPosition": "11", "source": null, "expirationDate": null, "name": "app.t360.editorialApps", "configs": {"orderWeb": "1", "positionWeb": "2", "tableApp": "true"}, "image": null, "cartola": null, "cmsVideo": null, "url": null, "title": "App Editorial", "subTitle": null, "publishedDate": "2025-09-07 21:26:15 -0300", "publishedTime": 1757291175000, "date": "2025-09-01 18:03:06 -0300", "time": 1756760586000, "tgmKey": null, "paidContent": false, "menuItems": null}], "type": "A", "configs": {"tableApp": "true", "positionWeb": "2"}}, "1649725": {"id": "1649725", "type": "A", "logo": null, "label": "card.type.AMPStories (12 itens)", "items": [{"id": "e78c7aa28e336a76371f0dd953b3767fga6w47os", "type": "STR", "logo": null, "label": null, "items": [{"id": "e78c7aa28e336a76371f0dd953b3767fga6w47os", "url": "https://www.terra.com.br/amp/story/noticias/veja-o-que-pode-causar-explosao-de-celulares-e-entenda-como-evitar,e78c7aa28e336a76371f0dd953b3767fga6w47os.html", "title": "Veja o que pode causar explos\u00e3o de celulares e entenda ...", "subTitle": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "publishedDate": "2025-09-02 12:13:07 -0300", "firstPublishedDate": "2025-09-02 12:13:07 -0300", "socialURL": "https://www.terra.com.br/amp/story/noticias/veja-o-que-pode-causar-explosao-de-celulares-e-entenda-como-evitar,e78c7aa28e336a76371f0dd953b3767fga6w47os.html", "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "channels": [{"id": "8a5e7f25db695c716426373e065f8b28tixp0sd6", "name": "Flipar", "label": "Flipar", "path": "Brasil.parceiros.flipar", "originalPath": "/Brasil/parceiros/Flipar", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "br.cobranded_flipar", "idMenu": "flip", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "name": "Not\u00edcias", "label": "Not\u00edcias", "path": "Brasil.noticias", "originalPath": "/Brasil/Not\u00edcias", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/noticias/", "tgmKey": "br.news", "idMenu": "not", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": "/"}, "metricsChannel": null, "metricsSiteId": null, "description": "Acompanhe as \u00faltimas not\u00edcias e acontecimentos relevantes de cidades do Brasil e do mundo. Fique por dentro dos principais assuntos no Terra.", "title": "Terra Not\u00edcias: Brasil, Mundo, Cidades, Pol\u00edtica, Clima", "terratv": 4006, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Brasil", "link": "https://www.terra.com.br/noticias/brasil/"}, {"display_order": 2, "name": "Mundo", "link": "https://www.terra.com.br/noticias/mundo/"}, {"display_order": 4, "name": "Checamos", "link": "https://www.terra.com.br/noticias/checamos/"}, {"display_order": 5, "name": "Loterias", "link": "https://www.terra.com.br/noticias/loterias/"}, {"display_order": 6, "name": "Previs\u00e3o do Tempo", "link": "https://www.terra.com.br/noticias/previsao-do-tempo/"}, {"display_order": 7, "name": "V\u00eddeos", "link": "https://www.terra.com.br/noticias/videos/"}], "usedInURL": true, "display": null}], "breadCrumb": ["187775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "8a5e7f25db695c716426373e065f8b28tixp0sd6", "afa9280af150acfe65477a86ec056f3dxns5uqbe"], "locations": null, "image": {"id": "36be895febc3292de03c349bf0dfe789wzdom9vj", "url": "http://images.terra.com/2025/09/02/1181140041-celularexplode.jpg", "alt": "1181140041-celularexplode.jpg Foto: Reprodu\u00e7\u00e3o / Flipar", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "1181140041-celularexplode.jpg", "caption": "1181140041-celularexplode.jpg", "width": 787, "height": 495, "representative": false, "author": "Reprodu\u00e7\u00e3o", "locality": null, "source": {"id": null, "name": "Flipar", "audience": "flipar", "description": null, "image": null, "keywords": null, "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-02 12:13:05 -0300", "link": null, "originalURL": "images.terra.com/2025/09/02/1181140041-celularexplode.jpg"}, "category": "STR", "configs": null, "paidContent": false, "fullTitle": "Veja o que pode causar explos\u00e3o de celulares e entenda como evitar"}], "fixedPosition": null, "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "expirationDate": null, "name": null, "configs": {"idItemMenu": "flip"}, "image": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "cmsVideo": null, "url": "https://www.terra.com.br/parceiros/flipar/", "title": null, "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": "br.cobranded_flipar", "paidContent": false}, {"id": "b0bbe2d295919fc7a8690bb101901da77o9diwm0", "type": "STR", "logo": null, "label": null, "items": [{"id": "b0bbe2d295919fc7a8690bb101901da77o9diwm0", "url": "https://www.terra.com.br/amp/story/noticias/nao-sao-so-as-aves-os-animais-que-se-reproduzem-em-ovos,b0bbe2d295919fc7a8690bb101901da77o9diwm0.html", "title": "N\u00e3o s\u00e3o s\u00f3 as aves! Os animais que se reproduzem em ovos", "subTitle": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "publishedDate": "2025-09-02 12:31:42 -0300", "firstPublishedDate": "2025-09-02 12:31:42 -0300", "socialURL": "https://www.terra.com.br/amp/story/noticias/nao-sao-so-as-aves-os-animais-que-se-reproduzem-em-ovos,b0bbe2d295919fc7a8690bb101901da77o9diwm0.html", "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "channels": [{"id": "8a5e7f25db695c716426373e065f8b28tixp0sd6", "name": "Flipar", "label": "Flipar", "path": "Brasil.parceiros.flipar", "originalPath": "/Brasil/parceiros/Flipar", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "br.cobranded_flipar", "idMenu": "flip", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "name": "Not\u00edcias", "label": "Not\u00edcias", "path": "Brasil.noticias", "originalPath": "/Brasil/Not\u00edcias", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/noticias/", "tgmKey": "br.news", "idMenu": "not", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": "/"}, "metricsChannel": null, "metricsSiteId": null, "description": "Acompanhe as \u00faltimas not\u00edcias e acontecimentos relevantes de cidades do Brasil e do mundo. Fique por dentro dos principais assuntos no Terra.", "title": "Terra Not\u00edcias: Brasil, Mundo, Cidades, Pol\u00edtica, Clima", "terratv": 4006, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Brasil", "link": "https://www.terra.com.br/noticias/brasil/"}, {"display_order": 2, "name": "Mundo", "link": "https://www.terra.com.br/noticias/mundo/"}, {"display_order": 4, "name": "Checamos", "link": "https://www.terra.com.br/noticias/checamos/"}, {"display_order": 5, "name": "Loterias", "link": "https://www.terra.com.br/noticias/loterias/"}, {"display_order": 6, "name": "Previs\u00e3o do Tempo", "link": "https://www.terra.com.br/noticias/previsao-do-tempo/"}, {"display_order": 7, "name": "V\u00eddeos", "link": "https://www.terra.com.br/noticias/videos/"}], "usedInURL": true, "display": null}], "breadCrumb": ["187775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "8a5e7f25db695c716426373e065f8b28tixp0sd6", "afa9280af150acfe65477a86ec056f3dxns5uqbe"], "locations": null, "image": {"id": "fb41bf6ca7b8dafebf90a65bc8a38747hwwpc4s3", "url": "http://images.terra.com/2025/09/02/2077787893-ostrich-egg-1379646640.jpg", "alt": "2077787893-ostrich-egg-1379646640.jpg Foto: Imagem de W\u00e4lz por Pixabay / Flipar", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "2077787893-ostrich-egg-1379646640.jpg", "caption": "2077787893-ostrich-egg-1379646640.jpg", "width": 480, "height": 640, "representative": false, "author": "Imagem de W\u00e4lz por Pixabay", "locality": null, "source": {"id": null, "name": "Flipar", "audience": "flipar", "description": null, "image": null, "keywords": null, "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-02 12:31:40 -0300", "link": null, "originalURL": "images.terra.com/2025/09/02/2077787893-ostrich-egg-1379646640.jpg"}, "category": "STR", "configs": null, "paidContent": false, "fullTitle": "N\u00e3o s\u00e3o s\u00f3 as aves! Os animais que se reproduzem em ovos"}], "fixedPosition": null, "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "expirationDate": null, "name": null, "configs": {"idItemMenu": "flip"}, "image": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "cmsVideo": null, "url": "https://www.terra.com.br/parceiros/flipar/", "title": null, "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": "br.cobranded_flipar", "paidContent": false}, {"id": "25f2f112c885f779bcfa4f40283bac63nhsn3gts", "type": "STR", "logo": null, "label": null, "items": [{"id": "25f2f112c885f779bcfa4f40283bac63nhsn3gts", "url": "https://www.terra.com.br/amp/story/noticias/naturais-e-apraziveis-lindos-jardins-espalhados-pelo-mundo,25f2f112c885f779bcfa4f40283bac63nhsn3gts.html", "title": "Naturais e apraz\u00edveis: lindos jardins espalhados pelo mundo", "subTitle": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "publishedDate": "2025-09-02 12:31:45 -0300", "firstPublishedDate": "2025-09-02 12:31:45 -0300", "socialURL": "https://www.terra.com.br/amp/story/noticias/naturais-e-apraziveis-lindos-jardins-espalhados-pelo-mundo,25f2f112c885f779bcfa4f40283bac63nhsn3gts.html", "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "channels": [{"id": "8a5e7f25db695c716426373e065f8b28tixp0sd6", "name": "Flipar", "label": "Flipar", "path": "Brasil.parceiros.flipar", "originalPath": "/Brasil/parceiros/Flipar", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "br.cobranded_flipar", "idMenu": "flip", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "name": "Not\u00edcias", "label": "Not\u00edcias", "path": "Brasil.noticias", "originalPath": "/Brasil/Not\u00edcias", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/noticias/", "tgmKey": "br.news", "idMenu": "not", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": "/"}, "metricsChannel": null, "metricsSiteId": null, "description": "Acompanhe as \u00faltimas not\u00edcias e acontecimentos relevantes de cidades do Brasil e do mundo. Fique por dentro dos principais assuntos no Terra.", "title": "Terra Not\u00edcias: Brasil, Mundo, Cidades, Pol\u00edtica, Clima", "terratv": 4006, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Brasil", "link": "https://www.terra.com.br/noticias/brasil/"}, {"display_order": 2, "name": "Mundo", "link": "https://www.terra.com.br/noticias/mundo/"}, {"display_order": 4, "name": "Checamos", "link": "https://www.terra.com.br/noticias/checamos/"}, {"display_order": 5, "name": "Loterias", "link": "https://www.terra.com.br/noticias/loterias/"}, {"display_order": 6, "name": "Previs\u00e3o do Tempo", "link": "https://www.terra.com.br/noticias/previsao-do-tempo/"}, {"display_order": 7, "name": "V\u00eddeos", "link": "https://www.terra.com.br/noticias/videos/"}], "usedInURL": true, "display": null}], "breadCrumb": ["187775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "8a5e7f25db695c716426373e065f8b28tixp0sd6", "afa9280af150acfe65477a86ec056f3dxns5uqbe"], "locations": null, "image": {"id": "dd6dfeb11aaf3fadee4cbcef89fabb6brru6jey7", "url": "http://images.terra.com/2025/09/02/1736713717-jardim-naturezaeasy-resizecom.jpg", "alt": "1736713717-jardim-naturezaeasy-resizecom.jpg Foto: Divulga\u00e7\u00e3o / Flipar", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "1736713717-jardim-naturezaeasy-resizecom.jpg", "caption": "1736713717-jardim-naturezaeasy-resizecom.jpg", "width": 1280, "height": 857, "representative": false, "author": "Divulga\u00e7\u00e3o", "locality": null, "source": {"id": null, "name": "Flipar", "audience": "flipar", "description": null, "image": null, "keywords": null, "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-02 12:31:42 -0300", "link": null, "originalURL": "images.terra.com/2025/09/02/1736713717-jardim-naturezaeasy-resizecom.jpg"}, "category": "STR", "configs": null, "paidContent": false, "fullTitle": "Naturais e apraz\u00edveis: lindos jardins espalhados pelo mundo"}], "fixedPosition": null, "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "expirationDate": null, "name": null, "configs": {"idItemMenu": "flip"}, "image": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "cmsVideo": null, "url": "https://www.terra.com.br/parceiros/flipar/", "title": null, "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": "br.cobranded_flipar", "paidContent": false}, {"id": "71d164d8fea9f200b603091ad6f93f50fslbznm3", "type": "STR", "logo": null, "label": null, "items": [{"id": "71d164d8fea9f200b603091ad6f93f50fslbznm3", "url": "https://www.terra.com.br/amp/story/vida-e-estilo/saude/5-dicas-praticas-para-desinchar-o-corpo,71d164d8fea9f200b603091ad6f93f50fslbznm3.html", "title": "5 dicas pr\u00e1ticas para desinchar o corpo", "subTitle": null, "cartola": {"id": "63096e40c5234310VgnVCM20000099f154d0RCRD", "path": "Brasil.vida-e-estilo.saude", "label": "Sa\u00fade", "url": "https://www.terra.com.br/vida-e-estilo/saude/", "customHat": false, "chooseEditor": false}, "publishedDate": "2025-09-04 16:09:52 -0300", "firstPublishedDate": "2025-09-02 15:46:03 -0300", "socialURL": "https://www.terra.com.br/amp/story/vida-e-estilo/saude/5-dicas-praticas-para-desinchar-o-corpo,71d164d8fea9f200b603091ad6f93f50fslbznm3.html", "source": {"id": "83aa2d34036bc33ee784b0988637e56dpvrtj6i3", "name": "Portal EdiCase", "audience": "portaledicase", "description": null, "image": {"id": "d018c88c43296891c7840ffe33e264f0099sv8j7", "url": "http://images.terra.com/2022/04/12/portaledicase_88x31-(002)-1iv9bmwpqaag6.png", "alt": "portaledicase_88x31 (002).png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "portaledicase_88x31 (002).png", "caption": "portaledicase_88x31 (002).png", "width": 88, "height": 31, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-04-12 11:14:57 -0300", "link": null, "originalURL": "images.terra.com/2022/04/12/portaledicase_88x31-(002)-1iv9bmwpqaag6.png"}, "keywords": "var idItemMenu = 'portaledicase';", "publicName": "Portal EdiCase", "url": "https://www.terra.com.br/parceiros/portal-edicase/", "tgmKey": "cobranded_portaledicase", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "3fdb7bbf5b7ad57732460f1ffe3d3479q4qcl4pv", "originalURL": "images.terra.com/2022/04/12/favicon_portaledicase_32x32-(002)-1hrgf0yh5h6pf.png", "url": "http://images.terra.com/2022/04/12/favicon_portaledicase_32x32-(002)-1hrgf0yh5h6pf.png"}}, "channels": [{"id": "63096e40c5234310VgnVCM20000099f154d0RCRD", "name": "Sa\u00fade", "label": "Sa\u00fade", "path": "Brasil.vida-e-estilo.saude", "originalPath": "/Brasil/Vida e Estilo/Sa\u00fade", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/vida-e-estilo/saude/", "tgmKey": "br.lifestyle_saude", "idMenu": "saud", "metrics": {"terra_info_channel": "br.vidaeestilo.saude", "terra_info_channeldetail": "/"}, "metricsChannel": "br.vidaeestilo.saude", "metricsSiteId": null, "description": "Tudo sobre sa\u00fade, diagn\u00f3sticos, preven\u00e7\u00e3o, tratamento e cura de doen\u00e7as. Tire suas d\u00favidas sobre diabetes, alergia, ins\u00f4nia e outros assuntos.", "title": "Sa\u00fade - Doen\u00e7as, Medicamentos, Preven\u00e7\u00e3o, Sa\u00fade da Mulher | Terra", "terratv": 4176, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": true, "display": null}, {"id": "4a304bc9181417442c42dff9157818c6jhnyzrhq", "name": "Portal EdiCase", "label": "Portal EdiCase", "path": "Brasil.parceiros.portal-edicase", "originalPath": "/Brasil/parceiros/Portal EdiCase", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/portal-edicase/", "tgmKey": "br.cobranded_portaledicase", "idMenu": "portaledicase", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}], "breadCrumb": ["4e9775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "4a304bc9181417442c42dff9157818c6jhnyzrhq", "afa9280af150acfe65477a86ec056f3dxns5uqbe", "63096e40c5234310VgnVCM20000099f154d0RCRD"], "locations": null, "image": {"id": "9ece6849dc1584f0de71e0f7bdecf195u60ebd46", "url": "http://images.terra.com/2025/09/02/1368586150-pernas-inchadas.jpg", "alt": "1368586150-pernas-inchadas.jpg Foto: DimaBerlin | Shutterstock / Portal EdiCase", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "1368586150-pernas-inchadas.jpg", "caption": "1368586150-pernas-inchadas.jpg", "width": 1366, "height": 2048, "representative": false, "author": "DimaBerlin | Shutterstock", "locality": null, "source": {"id": null, "name": "Portal EdiCase", "audience": "portaledicase", "description": null, "image": null, "keywords": null, "publicName": "Portal EdiCase", "url": "https://www.terra.com.br/parceiros/portal-edicase/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-02 15:46:02 -0300", "link": null, "originalURL": "images.terra.com/2025/09/02/1368586150-pernas-inchadas.jpg"}, "category": "STR", "configs": null, "paidContent": false, "fullTitle": "5 dicas pr\u00e1ticas para desinchar o corpo"}], "fixedPosition": null, "source": {"id": "83aa2d34036bc33ee784b0988637e56dpvrtj6i3", "name": "Portal EdiCase", "audience": "portaledicase", "description": null, "image": {"id": "d018c88c43296891c7840ffe33e264f0099sv8j7", "url": "http://images.terra.com/2022/04/12/portaledicase_88x31-(002)-1iv9bmwpqaag6.png", "alt": "portaledicase_88x31 (002).png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "portaledicase_88x31 (002).png", "caption": "portaledicase_88x31 (002).png", "width": 88, "height": 31, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-04-12 11:14:57 -0300", "link": null, "originalURL": "images.terra.com/2022/04/12/portaledicase_88x31-(002)-1iv9bmwpqaag6.png"}, "keywords": "var idItemMenu = 'portaledicase';", "publicName": "Portal EdiCase", "url": "https://www.terra.com.br/parceiros/portal-edicase/", "tgmKey": "cobranded_portaledicase", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "3fdb7bbf5b7ad57732460f1ffe3d3479q4qcl4pv", "originalURL": "images.terra.com/2022/04/12/favicon_portaledicase_32x32-(002)-1hrgf0yh5h6pf.png", "url": "http://images.terra.com/2022/04/12/favicon_portaledicase_32x32-(002)-1hrgf0yh5h6pf.png"}}, "expirationDate": null, "name": null, "configs": {"idItemMenu": "portaledicase"}, "image": null, "cartola": {"id": "63096e40c5234310VgnVCM20000099f154d0RCRD", "path": "Brasil.vida-e-estilo.saude", "label": "Sa\u00fade", "url": "https://www.terra.com.br/vida-e-estilo/saude/", "customHat": false, "chooseEditor": false}, "cmsVideo": null, "url": "https://www.terra.com.br/parceiros/portal-edicase/", "title": null, "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": "br.cobranded_portaledicase", "paidContent": false}, {"id": "8eec32a6e135c7947bb6c10710156313c0l89u81", "type": "STR", "logo": null, "label": null, "items": [{"id": "8eec32a6e135c7947bb6c10710156313c0l89u81", "url": "https://www.terra.com.br/amp/story/noticias/cientistas-apontam-causa-da-morte-de-farao-menino-maldicao-de-tutankamon-deixou-sucessao-de-tragedias,8eec32a6e135c7947bb6c10710156313c0l89u81.html", "title": "Cientistas apontam causa da morte de fara\u00f3 menino: ...", "subTitle": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "publishedDate": "2025-09-03 08:13:16 -0300", "firstPublishedDate": "2025-09-03 08:13:16 -0300", "socialURL": "https://www.terra.com.br/amp/story/noticias/cientistas-apontam-causa-da-morte-de-farao-menino-maldicao-de-tutankamon-deixou-sucessao-de-tragedias,8eec32a6e135c7947bb6c10710156313c0l89u81.html", "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "channels": [{"id": "8a5e7f25db695c716426373e065f8b28tixp0sd6", "name": "Flipar", "label": "Flipar", "path": "Brasil.parceiros.flipar", "originalPath": "/Brasil/parceiros/Flipar", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "br.cobranded_flipar", "idMenu": "flip", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "name": "Not\u00edcias", "label": "Not\u00edcias", "path": "Brasil.noticias", "originalPath": "/Brasil/Not\u00edcias", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/noticias/", "tgmKey": "br.news", "idMenu": "not", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": "/"}, "metricsChannel": null, "metricsSiteId": null, "description": "Acompanhe as \u00faltimas not\u00edcias e acontecimentos relevantes de cidades do Brasil e do mundo. Fique por dentro dos principais assuntos no Terra.", "title": "Terra Not\u00edcias: Brasil, Mundo, Cidades, Pol\u00edtica, Clima", "terratv": 4006, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Brasil", "link": "https://www.terra.com.br/noticias/brasil/"}, {"display_order": 2, "name": "Mundo", "link": "https://www.terra.com.br/noticias/mundo/"}, {"display_order": 4, "name": "Checamos", "link": "https://www.terra.com.br/noticias/checamos/"}, {"display_order": 5, "name": "Loterias", "link": "https://www.terra.com.br/noticias/loterias/"}, {"display_order": 6, "name": "Previs\u00e3o do Tempo", "link": "https://www.terra.com.br/noticias/previsao-do-tempo/"}, {"display_order": 7, "name": "V\u00eddeos", "link": "https://www.terra.com.br/noticias/videos/"}], "usedInURL": true, "display": null}], "breadCrumb": ["187775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "8a5e7f25db695c716426373e065f8b28tixp0sd6", "afa9280af150acfe65477a86ec056f3dxns5uqbe"], "locations": null, "image": {"id": "379ca84e994973d1dac54c926d5d2b87uocuwbjk", "url": "http://images.terra.com/2025/09/03/1975259773-screenshot20250902225212chrome.jpg", "alt": "1975259773-screenshot20250902225212chrome.jpg Foto: Dom\u00ednio p\u00fablico / Flipar", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "1975259773-screenshot20250902225212chrome.jpg", "caption": "1975259773-screenshot20250902225212chrome.jpg", "width": 1050, "height": 767, "representative": false, "author": "Dom\u00ednio p\u00fablico", "locality": null, "source": {"id": null, "name": "Flipar", "audience": "flipar", "description": null, "image": null, "keywords": null, "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-03 08:13:12 -0300", "link": null, "originalURL": "images.terra.com/2025/09/03/1975259773-screenshot20250902225212chrome.jpg"}, "category": "STR", "configs": null, "paidContent": false, "fullTitle": "Cientistas apontam causa da morte de fara\u00f3 menino: 'maldi\u00e7\u00e3o' de Tutankamon deixou sucess\u00e3o de trag\u00e9dias"}], "fixedPosition": null, "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "expirationDate": null, "name": null, "configs": {"idItemMenu": "flip"}, "image": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "cmsVideo": null, "url": "https://www.terra.com.br/parceiros/flipar/", "title": null, "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": "br.cobranded_flipar", "paidContent": false}, {"id": "7ee7d79d39a2a7d73ee69d5e838e08e70ajc5wo8", "type": "STR", "logo": null, "label": null, "items": [{"id": "7ee7d79d39a2a7d73ee69d5e838e08e70ajc5wo8", "url": "https://www.terra.com.br/amp/story/noticias/esposa-de-bruce-willis-sai-de-casa-e-rebate-criticas-dos-fas-do-ator-que-sofre-de-demencia,7ee7d79d39a2a7d73ee69d5e838e08e70ajc5wo8.html", "title": "Esposa de Bruce Willis sai de casa e rebate cr\u00edticas dos ...", "subTitle": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "publishedDate": "2025-09-03 11:19:22 -0300", "firstPublishedDate": "2025-09-03 11:19:22 -0300", "socialURL": "https://www.terra.com.br/amp/story/noticias/esposa-de-bruce-willis-sai-de-casa-e-rebate-criticas-dos-fas-do-ator-que-sofre-de-demencia,7ee7d79d39a2a7d73ee69d5e838e08e70ajc5wo8.html", "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "channels": [{"id": "8a5e7f25db695c716426373e065f8b28tixp0sd6", "name": "Flipar", "label": "Flipar", "path": "Brasil.parceiros.flipar", "originalPath": "/Brasil/parceiros/Flipar", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "br.cobranded_flipar", "idMenu": "flip", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "name": "Not\u00edcias", "label": "Not\u00edcias", "path": "Brasil.noticias", "originalPath": "/Brasil/Not\u00edcias", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/noticias/", "tgmKey": "br.news", "idMenu": "not", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": "/"}, "metricsChannel": null, "metricsSiteId": null, "description": "Acompanhe as \u00faltimas not\u00edcias e acontecimentos relevantes de cidades do Brasil e do mundo. Fique por dentro dos principais assuntos no Terra.", "title": "Terra Not\u00edcias: Brasil, Mundo, Cidades, Pol\u00edtica, Clima", "terratv": 4006, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Brasil", "link": "https://www.terra.com.br/noticias/brasil/"}, {"display_order": 2, "name": "Mundo", "link": "https://www.terra.com.br/noticias/mundo/"}, {"display_order": 4, "name": "Checamos", "link": "https://www.terra.com.br/noticias/checamos/"}, {"display_order": 5, "name": "Loterias", "link": "https://www.terra.com.br/noticias/loterias/"}, {"display_order": 6, "name": "Previs\u00e3o do Tempo", "link": "https://www.terra.com.br/noticias/previsao-do-tempo/"}, {"display_order": 7, "name": "V\u00eddeos", "link": "https://www.terra.com.br/noticias/videos/"}], "usedInURL": true, "display": null}], "breadCrumb": ["187775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "8a5e7f25db695c716426373e065f8b28tixp0sd6", "afa9280af150acfe65477a86ec056f3dxns5uqbe"], "locations": null, "image": {"id": "5758c1f6379ce4ea1a8ff48e03c30c13b3rbit11", "url": "http://images.terra.com/2025/09/03/1444040585-screenshot20250701021924chrome.jpg", "alt": "1444040585-screenshot20250701021924chrome.jpg Foto: Reprodu\u00e7\u00e3o Instagram / Flipar", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "1444040585-screenshot20250701021924chrome.jpg", "caption": "1444040585-screenshot20250701021924chrome.jpg", "width": 1070, "height": 1134, "representative": false, "author": "Reprodu\u00e7\u00e3o Instagram", "locality": null, "source": {"id": null, "name": "Flipar", "audience": "flipar", "description": null, "image": null, "keywords": null, "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-03 11:19:20 -0300", "link": null, "originalURL": "images.terra.com/2025/09/03/1444040585-screenshot20250701021924chrome.jpg"}, "category": "STR", "configs": null, "paidContent": false, "fullTitle": "Esposa de Bruce Willis sai de casa e rebate cr\u00edticas dos f\u00e3s do ator, que sofre de dem\u00eancia"}], "fixedPosition": null, "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "expirationDate": null, "name": null, "configs": {"idItemMenu": "flip"}, "image": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "cmsVideo": null, "url": "https://www.terra.com.br/parceiros/flipar/", "title": null, "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": "br.cobranded_flipar", "paidContent": false}, {"id": "48d3437537117619c601905efd9e4458d6grnwo0", "type": "STR", "logo": null, "label": null, "items": [{"id": "48d3437537117619c601905efd9e4458d6grnwo0", "url": "https://www.terra.com.br/amp/story/noticias/saiba-por-que-padre-fabio-de-melo-foi-denunciado-ao-vaticano,48d3437537117619c601905efd9e4458d6grnwo0.html", "title": "Saiba por que padre F\u00e1bio de Melo foi denunciado ao Vaticano", "subTitle": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "publishedDate": "2025-09-04 08:13:23 -0300", "firstPublishedDate": "2025-09-04 08:13:23 -0300", "socialURL": "https://www.terra.com.br/amp/story/noticias/saiba-por-que-padre-fabio-de-melo-foi-denunciado-ao-vaticano,48d3437537117619c601905efd9e4458d6grnwo0.html", "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "channels": [{"id": "8a5e7f25db695c716426373e065f8b28tixp0sd6", "name": "Flipar", "label": "Flipar", "path": "Brasil.parceiros.flipar", "originalPath": "/Brasil/parceiros/Flipar", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "br.cobranded_flipar", "idMenu": "flip", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "name": "Not\u00edcias", "label": "Not\u00edcias", "path": "Brasil.noticias", "originalPath": "/Brasil/Not\u00edcias", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/noticias/", "tgmKey": "br.news", "idMenu": "not", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": "/"}, "metricsChannel": null, "metricsSiteId": null, "description": "Acompanhe as \u00faltimas not\u00edcias e acontecimentos relevantes de cidades do Brasil e do mundo. Fique por dentro dos principais assuntos no Terra.", "title": "Terra Not\u00edcias: Brasil, Mundo, Cidades, Pol\u00edtica, Clima", "terratv": 4006, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Brasil", "link": "https://www.terra.com.br/noticias/brasil/"}, {"display_order": 2, "name": "Mundo", "link": "https://www.terra.com.br/noticias/mundo/"}, {"display_order": 4, "name": "Checamos", "link": "https://www.terra.com.br/noticias/checamos/"}, {"display_order": 5, "name": "Loterias", "link": "https://www.terra.com.br/noticias/loterias/"}, {"display_order": 6, "name": "Previs\u00e3o do Tempo", "link": "https://www.terra.com.br/noticias/previsao-do-tempo/"}, {"display_order": 7, "name": "V\u00eddeos", "link": "https://www.terra.com.br/noticias/videos/"}], "usedInURL": true, "display": null}], "breadCrumb": ["187775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "8a5e7f25db695c716426373e065f8b28tixp0sd6", "afa9280af150acfe65477a86ec056f3dxns5uqbe"], "locations": null, "image": {"id": "132a7806e4413030c8a61736f6a170f8e0qn29vg", "url": "http://images.terra.com/2025/09/04/1707785687-screenshot20250622214232chrome.jpg", "alt": "1707785687-screenshot20250622214232chrome.jpg Foto: Reprodu\u00e7\u00e3o Instagram / Flipar", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "1707785687-screenshot20250622214232chrome.jpg", "caption": "1707785687-screenshot20250622214232chrome.jpg", "width": 926, "height": 587, "representative": false, "author": "Reprodu\u00e7\u00e3o Instagram", "locality": null, "source": {"id": null, "name": "Flipar", "audience": "flipar", "description": null, "image": null, "keywords": null, "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-04 08:13:19 -0300", "link": null, "originalURL": "images.terra.com/2025/09/04/1707785687-screenshot20250622214232chrome.jpg"}, "category": "STR", "configs": null, "paidContent": false, "fullTitle": "Saiba por que padre F\u00e1bio de Melo foi denunciado ao Vaticano"}], "fixedPosition": null, "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "expirationDate": null, "name": null, "configs": {"idItemMenu": "flip"}, "image": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "cmsVideo": null, "url": "https://www.terra.com.br/parceiros/flipar/", "title": null, "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": "br.cobranded_flipar", "paidContent": false}, {"id": "4d55f8ed8c603f21c79013590985fcb7l46ejrf5", "type": "STR", "logo": null, "label": null, "items": [{"id": "4d55f8ed8c603f21c79013590985fcb7l46ejrf5", "url": "https://www.terra.com.br/amp/story/noticias/bastou-uma-picada-aranha-violinista-mata-jovem-de-23-anos,4d55f8ed8c603f21c79013590985fcb7l46ejrf5.html", "title": "Bastou uma picada: Aranha-violinista mata jovem de 23 anos", "subTitle": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "publishedDate": "2025-09-04 08:46:23 -0300", "firstPublishedDate": "2025-09-04 08:46:23 -0300", "socialURL": "https://www.terra.com.br/amp/story/noticias/bastou-uma-picada-aranha-violinista-mata-jovem-de-23-anos,4d55f8ed8c603f21c79013590985fcb7l46ejrf5.html", "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "channels": [{"id": "8a5e7f25db695c716426373e065f8b28tixp0sd6", "name": "Flipar", "label": "Flipar", "path": "Brasil.parceiros.flipar", "originalPath": "/Brasil/parceiros/Flipar", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "br.cobranded_flipar", "idMenu": "flip", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "name": "Not\u00edcias", "label": "Not\u00edcias", "path": "Brasil.noticias", "originalPath": "/Brasil/Not\u00edcias", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/noticias/", "tgmKey": "br.news", "idMenu": "not", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": "/"}, "metricsChannel": null, "metricsSiteId": null, "description": "Acompanhe as \u00faltimas not\u00edcias e acontecimentos relevantes de cidades do Brasil e do mundo. Fique por dentro dos principais assuntos no Terra.", "title": "Terra Not\u00edcias: Brasil, Mundo, Cidades, Pol\u00edtica, Clima", "terratv": 4006, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Brasil", "link": "https://www.terra.com.br/noticias/brasil/"}, {"display_order": 2, "name": "Mundo", "link": "https://www.terra.com.br/noticias/mundo/"}, {"display_order": 4, "name": "Checamos", "link": "https://www.terra.com.br/noticias/checamos/"}, {"display_order": 5, "name": "Loterias", "link": "https://www.terra.com.br/noticias/loterias/"}, {"display_order": 6, "name": "Previs\u00e3o do Tempo", "link": "https://www.terra.com.br/noticias/previsao-do-tempo/"}, {"display_order": 7, "name": "V\u00eddeos", "link": "https://www.terra.com.br/noticias/videos/"}], "usedInURL": true, "display": null}], "breadCrumb": ["187775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "8a5e7f25db695c716426373e065f8b28tixp0sd6", "afa9280af150acfe65477a86ec056f3dxns5uqbe"], "locations": null, "image": {"id": "0b1639c83b9acac6285588b457813ab8ro36ry3y", "url": "http://images.terra.com/2025/09/04/432464337-loxosceles-43519121280-e1724243999268.jpg", "alt": "432464337-loxosceles-43519121280-e1724243999268.jpg Foto: Francisco Corado Rivera por Pixabay / Flipar", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "432464337-loxosceles-43519121280-e1724243999268.jpg", "caption": "432464337-loxosceles-43519121280-e1724243999268.jpg", "width": 720, "height": 480, "representative": false, "author": "Francisco Corado Rivera por Pixabay", "locality": null, "source": {"id": null, "name": "Flipar", "audience": "flipar", "description": null, "image": null, "keywords": null, "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-04 08:46:20 -0300", "link": null, "originalURL": "images.terra.com/2025/09/04/432464337-loxosceles-43519121280-e1724243999268.jpg"}, "category": "STR", "configs": null, "paidContent": false, "fullTitle": "Bastou uma picada: Aranha-violinista mata jovem de 23 anos"}], "fixedPosition": null, "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "expirationDate": null, "name": null, "configs": {"idItemMenu": "flip"}, "image": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "cmsVideo": null, "url": "https://www.terra.com.br/parceiros/flipar/", "title": null, "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": "br.cobranded_flipar", "paidContent": false}, {"id": "c556d3252f009fd44cbd5df7be003ba4fa49gz2x", "type": "STR", "logo": null, "label": null, "items": [{"id": "c556d3252f009fd44cbd5df7be003ba4fa49gz2x", "url": "https://www.terra.com.br/amp/story/noticias/1-ano-sem-dingo-youtuber-morreu-envenenado-pela-cobra-que-ele-filmava,c556d3252f009fd44cbd5df7be003ba4fa49gz2x.html", "title": "1 ano sem Dingo: YouTuber morreu envenenado pela cobra ...", "subTitle": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "publishedDate": "2025-09-04 09:16:21 -0300", "firstPublishedDate": "2025-09-04 09:16:21 -0300", "socialURL": "https://www.terra.com.br/amp/story/noticias/1-ano-sem-dingo-youtuber-morreu-envenenado-pela-cobra-que-ele-filmava,c556d3252f009fd44cbd5df7be003ba4fa49gz2x.html", "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "channels": [{"id": "8a5e7f25db695c716426373e065f8b28tixp0sd6", "name": "Flipar", "label": "Flipar", "path": "Brasil.parceiros.flipar", "originalPath": "/Brasil/parceiros/Flipar", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "br.cobranded_flipar", "idMenu": "flip", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "name": "Not\u00edcias", "label": "Not\u00edcias", "path": "Brasil.noticias", "originalPath": "/Brasil/Not\u00edcias", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/noticias/", "tgmKey": "br.news", "idMenu": "not", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": "/"}, "metricsChannel": null, "metricsSiteId": null, "description": "Acompanhe as \u00faltimas not\u00edcias e acontecimentos relevantes de cidades do Brasil e do mundo. Fique por dentro dos principais assuntos no Terra.", "title": "Terra Not\u00edcias: Brasil, Mundo, Cidades, Pol\u00edtica, Clima", "terratv": 4006, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Brasil", "link": "https://www.terra.com.br/noticias/brasil/"}, {"display_order": 2, "name": "Mundo", "link": "https://www.terra.com.br/noticias/mundo/"}, {"display_order": 4, "name": "Checamos", "link": "https://www.terra.com.br/noticias/checamos/"}, {"display_order": 5, "name": "Loterias", "link": "https://www.terra.com.br/noticias/loterias/"}, {"display_order": 6, "name": "Previs\u00e3o do Tempo", "link": "https://www.terra.com.br/noticias/previsao-do-tempo/"}, {"display_order": 7, "name": "V\u00eddeos", "link": "https://www.terra.com.br/noticias/videos/"}], "usedInURL": true, "display": null}], "breadCrumb": ["187775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "8a5e7f25db695c716426373e065f8b28tixp0sd6", "afa9280af150acfe65477a86ec056f3dxns5uqbe"], "locations": null, "image": {"id": "8c60947923a9dfaacfbcd2d617649d642i07fkc0", "url": "http://images.terra.com/2025/09/04/1856269181-dingo-dinkelman-e-mamba-verde.jpg", "alt": "1856269181-dingo-dinkelman-e-mamba-verde.jpg Foto: Reprodu\u00e7\u00e3o redes sociais / Flipar", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "1856269181-dingo-dinkelman-e-mamba-verde.jpg", "caption": "1856269181-dingo-dinkelman-e-mamba-verde.jpg", "width": 1240, "height": 698, "representative": false, "author": "Reprodu\u00e7\u00e3o redes sociais", "locality": null, "source": {"id": null, "name": "Flipar", "audience": "flipar", "description": null, "image": null, "keywords": null, "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-04 09:16:18 -0300", "link": null, "originalURL": "images.terra.com/2025/09/04/1856269181-dingo-dinkelman-e-mamba-verde.jpg"}, "category": "STR", "configs": null, "paidContent": false, "fullTitle": "1 ano sem Dingo: YouTuber morreu envenenado pela cobra que ele filmava"}], "fixedPosition": null, "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "expirationDate": null, "name": null, "configs": {"idItemMenu": "flip"}, "image": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "cmsVideo": null, "url": "https://www.terra.com.br/parceiros/flipar/", "title": null, "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": "br.cobranded_flipar", "paidContent": false}, {"id": "f1ea79dc15d7b667c8b36d6ddba819d2nyhkq9mg", "type": "STR", "logo": null, "label": null, "items": [{"id": "f1ea79dc15d7b667c8b36d6ddba819d2nyhkq9mg", "url": "https://www.terra.com.br/amp/story/vida-e-estilo/saude/banana-6-perguntas-e-respostas-sobre-seus-beneficios,f1ea79dc15d7b667c8b36d6ddba819d2nyhkq9mg.html", "title": "Banana: 6 perguntas e respostas sobre seus benef\u00edcios", "subTitle": null, "cartola": {"id": "63096e40c5234310VgnVCM20000099f154d0RCRD", "path": "Brasil.vida-e-estilo.saude", "label": "Sa\u00fade", "url": "https://www.terra.com.br/vida-e-estilo/saude/", "customHat": false, "chooseEditor": false}, "publishedDate": "2025-09-04 15:22:02 -0300", "firstPublishedDate": "2025-09-04 15:22:02 -0300", "socialURL": "https://www.terra.com.br/amp/story/vida-e-estilo/saude/banana-6-perguntas-e-respostas-sobre-seus-beneficios,f1ea79dc15d7b667c8b36d6ddba819d2nyhkq9mg.html", "source": {"id": "83aa2d34036bc33ee784b0988637e56dpvrtj6i3", "name": "Portal EdiCase", "audience": "portaledicase", "description": null, "image": {"id": "d018c88c43296891c7840ffe33e264f0099sv8j7", "url": "http://images.terra.com/2022/04/12/portaledicase_88x31-(002)-1iv9bmwpqaag6.png", "alt": "portaledicase_88x31 (002).png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "portaledicase_88x31 (002).png", "caption": "portaledicase_88x31 (002).png", "width": 88, "height": 31, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-04-12 11:14:57 -0300", "link": null, "originalURL": "images.terra.com/2022/04/12/portaledicase_88x31-(002)-1iv9bmwpqaag6.png"}, "keywords": "var idItemMenu = 'portaledicase';", "publicName": "Portal EdiCase", "url": "https://www.terra.com.br/parceiros/portal-edicase/", "tgmKey": "cobranded_portaledicase", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "3fdb7bbf5b7ad57732460f1ffe3d3479q4qcl4pv", "originalURL": "images.terra.com/2022/04/12/favicon_portaledicase_32x32-(002)-1hrgf0yh5h6pf.png", "url": "http://images.terra.com/2022/04/12/favicon_portaledicase_32x32-(002)-1hrgf0yh5h6pf.png"}}, "channels": [{"id": "63096e40c5234310VgnVCM20000099f154d0RCRD", "name": "Sa\u00fade", "label": "Sa\u00fade", "path": "Brasil.vida-e-estilo.saude", "originalPath": "/Brasil/Vida e Estilo/Sa\u00fade", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/vida-e-estilo/saude/", "tgmKey": "br.lifestyle_saude", "idMenu": "saud", "metrics": {"terra_info_channel": "br.vidaeestilo.saude", "terra_info_channeldetail": "/"}, "metricsChannel": "br.vidaeestilo.saude", "metricsSiteId": null, "description": "Tudo sobre sa\u00fade, diagn\u00f3sticos, preven\u00e7\u00e3o, tratamento e cura de doen\u00e7as. Tire suas d\u00favidas sobre diabetes, alergia, ins\u00f4nia e outros assuntos.", "title": "Sa\u00fade - Doen\u00e7as, Medicamentos, Preven\u00e7\u00e3o, Sa\u00fade da Mulher | Terra", "terratv": 4176, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": true, "display": null}, {"id": "4a304bc9181417442c42dff9157818c6jhnyzrhq", "name": "Portal EdiCase", "label": "Portal EdiCase", "path": "Brasil.parceiros.portal-edicase", "originalPath": "/Brasil/parceiros/Portal EdiCase", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/portal-edicase/", "tgmKey": "br.cobranded_portaledicase", "idMenu": "portaledicase", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}], "breadCrumb": ["4e9775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "4a304bc9181417442c42dff9157818c6jhnyzrhq", "afa9280af150acfe65477a86ec056f3dxns5uqbe", "63096e40c5234310VgnVCM20000099f154d0RCRD"], "locations": null, "image": {"id": "99191a78f22eebb3c8ff22fca75e78c57mqsryr0", "url": "http://images.terra.com/2025/09/04/1103798898-logoportaledicase-1.png", "alt": "1103798898-logoportaledicase-1.png Foto: Logo Portal EdiCase / Portal EdiCase", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "1103798898-logoportaledicase-1.png", "caption": "1103798898-logoportaledicase-1.png", "width": 993, "height": 591, "representative": false, "author": "Logo Portal EdiCase", "locality": null, "source": {"id": null, "name": "Portal EdiCase", "audience": "portaledicase", "description": null, "image": null, "keywords": null, "publicName": "Portal EdiCase", "url": "https://www.terra.com.br/parceiros/portal-edicase/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-04 15:22:00 -0300", "link": null, "originalURL": "images.terra.com/2025/09/04/1103798898-logoportaledicase-1.png"}, "category": "STR", "configs": null, "paidContent": false, "fullTitle": "Banana: 6 perguntas e respostas sobre seus benef\u00edcios"}], "fixedPosition": null, "source": {"id": "83aa2d34036bc33ee784b0988637e56dpvrtj6i3", "name": "Portal EdiCase", "audience": "portaledicase", "description": null, "image": {"id": "d018c88c43296891c7840ffe33e264f0099sv8j7", "url": "http://images.terra.com/2022/04/12/portaledicase_88x31-(002)-1iv9bmwpqaag6.png", "alt": "portaledicase_88x31 (002).png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "portaledicase_88x31 (002).png", "caption": "portaledicase_88x31 (002).png", "width": 88, "height": 31, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-04-12 11:14:57 -0300", "link": null, "originalURL": "images.terra.com/2022/04/12/portaledicase_88x31-(002)-1iv9bmwpqaag6.png"}, "keywords": "var idItemMenu = 'portaledicase';", "publicName": "Portal EdiCase", "url": "https://www.terra.com.br/parceiros/portal-edicase/", "tgmKey": "cobranded_portaledicase", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "3fdb7bbf5b7ad57732460f1ffe3d3479q4qcl4pv", "originalURL": "images.terra.com/2022/04/12/favicon_portaledicase_32x32-(002)-1hrgf0yh5h6pf.png", "url": "http://images.terra.com/2022/04/12/favicon_portaledicase_32x32-(002)-1hrgf0yh5h6pf.png"}}, "expirationDate": null, "name": null, "configs": {"idItemMenu": "portaledicase"}, "image": null, "cartola": {"id": "63096e40c5234310VgnVCM20000099f154d0RCRD", "path": "Brasil.vida-e-estilo.saude", "label": "Sa\u00fade", "url": "https://www.terra.com.br/vida-e-estilo/saude/", "customHat": false, "chooseEditor": false}, "cmsVideo": null, "url": "https://www.terra.com.br/parceiros/portal-edicase/", "title": null, "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": "br.cobranded_portaledicase", "paidContent": false}, {"id": "d98672584a3a23ce0978ce7aa58b2d38f3lqjknn", "type": "STR", "logo": null, "label": null, "items": [{"id": "d98672584a3a23ce0978ce7aa58b2d38f3lqjknn", "url": "https://www.terra.com.br/amp/story/diversao/repete-o-papel-hbo-contrata-ator-dos-filmes-de-harry-potter-para-nova-serie,d98672584a3a23ce0978ce7aa58b2d38f3lqjknn.html", "title": "Repete o papel: HBO contrata ator dos filmes de Harry ...", "subTitle": null, "cartola": {"id": "8a8775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.diversao", "label": "Entret\u00ea", "url": "https://www.terra.com.br/diversao/", "customHat": false, "chooseEditor": false}, "publishedDate": "2025-09-05 08:22:24 -0300", "firstPublishedDate": "2025-09-05 08:22:24 -0300", "socialURL": "https://www.terra.com.br/amp/story/diversao/repete-o-papel-hbo-contrata-ator-dos-filmes-de-harry-potter-para-nova-serie,d98672584a3a23ce0978ce7aa58b2d38f3lqjknn.html", "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "channels": [{"id": "8a5e7f25db695c716426373e065f8b28tixp0sd6", "name": "Flipar", "label": "Flipar", "path": "Brasil.parceiros.flipar", "originalPath": "/Brasil/parceiros/Flipar", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "br.cobranded_flipar", "idMenu": "flip", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "8a8775b4786b2310VgnVCM3000009af154d0RCRD", "name": "Divers\u00e3o", "label": "Entret\u00ea", "path": "Brasil.diversao", "originalPath": "/Brasil/Divers\u00e3o", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/diversao/", "tgmKey": "br.entertainment", "idMenu": "dive", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": "Saiba todas as novidades sobre TV, celebridades, reality shows, m\u00fasica e lan\u00e7amento dos principais filmes e s\u00e9ries com o Terra Entret\u00ea!", "title": "Terra Entret\u00ea: Famosos, TV e Novelas, Divers\u00e3o, Cinema, Filmes e M\u00fasica", "terratv": 4004, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Gente", "link": "https://www.terra.com.br/diversao/gente/"}, {"display_order": 2, "name": "TV", "link": "https://www.terra.com.br/diversao/tv/"}, {"display_order": 3, "name": "Blog Sala de TV", "link": "https://www.terra.com.br/diversao/tv/blog-sala-de-tv/"}, {"display_order": 4, "name": "Entre Telas", "link": "https://www.terra.com.br/diversao/entre-telas/"}, {"display_order": 5, "name": "Reality Shows", "link": "https://www.terra.com.br/diversao/tv/reality-shows/"}, {"display_order": 6, "name": "Velvet", "link": "https://www.terra.com.br/diversao/velvet/"}, {"display_order": 7, "name": "M\u00fasica", "link": "https://www.terra.com.br/diversao/musica/"}, {"display_order": 8, "name": "V\u00eddeos", "link": "https://www.terra.com.br/diversao/videos/"}], "usedInURL": true, "display": null}], "breadCrumb": ["20e07ef2795b2310VgnVCM3000009af154d0RCRD", "8a5e7f25db695c716426373e065f8b28tixp0sd6", "8a8775b4786b2310VgnVCM3000009af154d0RCRD", "afa9280af150acfe65477a86ec056f3dxns5uqbe"], "locations": null, "image": {"id": "7259f3d1a834b468564c0fcdada2ca856biayxoe", "url": "http://images.terra.com/2025/09/05/708028240-screenshot20250904195145whatsapp.jpg", "alt": "708028240-screenshot20250904195145whatsapp.jpg Foto: Divulga\u00e7\u00e3o / Flipar", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "708028240-screenshot20250904195145whatsapp.jpg", "caption": "708028240-screenshot20250904195145whatsapp.jpg", "width": 1080, "height": 1020, "representative": false, "author": "Divulga\u00e7\u00e3o", "locality": null, "source": {"id": null, "name": "Flipar", "audience": "flipar", "description": null, "image": null, "keywords": null, "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-05 08:22:21 -0300", "link": null, "originalURL": "images.terra.com/2025/09/05/708028240-screenshot20250904195145whatsapp.jpg"}, "category": "STR", "configs": null, "paidContent": false, "fullTitle": "Repete o papel: HBO contrata ator dos filmes de Harry Potter para nova s\u00e9rie"}], "fixedPosition": null, "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "expirationDate": null, "name": null, "configs": {"idItemMenu": "flip"}, "image": null, "cartola": {"id": "8a8775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.diversao", "label": "Entret\u00ea", "url": "https://www.terra.com.br/diversao/", "customHat": false, "chooseEditor": false}, "cmsVideo": null, "url": "https://www.terra.com.br/parceiros/flipar/", "title": null, "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": "br.cobranded_flipar", "paidContent": false}, {"id": "4db23d6b3282d900b9ff781f3db96e29zndml7qi", "type": "STR", "logo": null, "label": null, "items": [{"id": "4db23d6b3282d900b9ff781f3db96e29zndml7qi", "url": "https://www.terra.com.br/amp/story/noticias/poodle-e-atacado-por-pitbull-e-morre-caes-ferozes-atacam-em-serie-no-brasil-e-continuam-pelas-ruas,4db23d6b3282d900b9ff781f3db96e29zndml7qi.html", "title": "Poodle \u00e9 atacado por pitbull e morre; c\u00e3es ferozes ...", "subTitle": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "publishedDate": "2025-09-06 08:07:04 -0300", "firstPublishedDate": "2025-09-06 08:07:04 -0300", "socialURL": "https://www.terra.com.br/amp/story/noticias/poodle-e-atacado-por-pitbull-e-morre-caes-ferozes-atacam-em-serie-no-brasil-e-continuam-pelas-ruas,4db23d6b3282d900b9ff781f3db96e29zndml7qi.html", "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "channels": [{"id": "8a5e7f25db695c716426373e065f8b28tixp0sd6", "name": "Flipar", "label": "Flipar", "path": "Brasil.parceiros.flipar", "originalPath": "/Brasil/parceiros/Flipar", "hide": false, "active": true, "canonical": false, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "br.cobranded_flipar", "idMenu": "flip", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": null}, "metricsChannel": null, "metricsSiteId": null, "description": null, "title": null, "terratv": null, "site": "Brasil", "channels": null, "menuItems": null, "usedInURL": false, "display": null}, {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "name": "Not\u00edcias", "label": "Not\u00edcias", "path": "Brasil.noticias", "originalPath": "/Brasil/Not\u00edcias", "hide": false, "active": true, "canonical": true, "reference": false, "videoOnly": false, "hiddenFromParents": false, "logo": null, "logoAutosuggest": null, "channelLogo": {}, "alternativeLogo": {}, "url": "https://www.terra.com.br/noticias/", "tgmKey": "br.news", "idMenu": "not", "metrics": {"terra_info_channel": null, "terra_info_channeldetail": "/"}, "metricsChannel": null, "metricsSiteId": null, "description": "Acompanhe as \u00faltimas not\u00edcias e acontecimentos relevantes de cidades do Brasil e do mundo. Fique por dentro dos principais assuntos no Terra.", "title": "Terra Not\u00edcias: Brasil, Mundo, Cidades, Pol\u00edtica, Clima", "terratv": 4006, "site": "Brasil", "channels": null, "menuItems": [{"display_order": 1, "name": "Brasil", "link": "https://www.terra.com.br/noticias/brasil/"}, {"display_order": 2, "name": "Mundo", "link": "https://www.terra.com.br/noticias/mundo/"}, {"display_order": 4, "name": "Checamos", "link": "https://www.terra.com.br/noticias/checamos/"}, {"display_order": 5, "name": "Loterias", "link": "https://www.terra.com.br/noticias/loterias/"}, {"display_order": 6, "name": "Previs\u00e3o do Tempo", "link": "https://www.terra.com.br/noticias/previsao-do-tempo/"}, {"display_order": 7, "name": "V\u00eddeos", "link": "https://www.terra.com.br/noticias/videos/"}], "usedInURL": true, "display": null}], "breadCrumb": ["187775b4786b2310VgnVCM3000009af154d0RCRD", "20e07ef2795b2310VgnVCM3000009af154d0RCRD", "8a5e7f25db695c716426373e065f8b28tixp0sd6", "afa9280af150acfe65477a86ec056f3dxns5uqbe"], "locations": null, "image": {"id": "2eb63614503bb4377bd1828c71b3d3f1iudwkopq", "url": "http://images.terra.com/2025/09/06/1960263210-screenshot20250905205829chrome.jpg", "alt": "1960263210-screenshot20250905205829chrome.jpg Foto: Flipar", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "1960263210-screenshot20250905205829chrome.jpg", "caption": "1960263210-screenshot20250905205829chrome.jpg", "width": 906, "height": 598, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": "Flipar", "audience": "flipar", "description": null, "image": null, "keywords": null, "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2025-09-06 08:07:00 -0300", "link": null, "originalURL": "images.terra.com/2025/09/06/1960263210-screenshot20250905205829chrome.jpg"}, "category": "STR", "configs": null, "paidContent": false, "fullTitle": "Poodle \u00e9 atacado por pitbull e morre; c\u00e3es ferozes atacam em s\u00e9rie no Brasil e continuam pelas ruas"}], "fixedPosition": null, "source": {"id": "c87634fde6ba6a1f8197ae47b5b1a674g249hs7y", "name": "Flipar", "audience": "flipar", "description": null, "image": {"id": "2bce2dfd88c39d3522d7c856a6cc125axy3pznla", "url": "http://images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png", "alt": "LOGO_FLIPAR_88x31.png Foto:", "horizontalCrop": null, "verticalCrop": null, "squareCrop": null, "wideCrop": null, "order": 0, "name": "LOGO_FLIPAR_88x31.png", "caption": "LOGO_FLIPAR_88x31.png", "width": 552, "height": 195, "representative": false, "author": null, "locality": null, "source": {"id": null, "name": null, "audience": null, "description": null, "image": null, "keywords": null, "publicName": null, "url": null, "tgmKey": null, "hasAdvertising": false}, "imageOrientation": null, "creationDate": "2022-05-27 17:32:12 -0300", "link": null, "originalURL": "images.terra.com/2022/05/27/logo_flipar_88x31-to4m3xktrfp0.png"}, "keywords": "var idItemMenu = 'flipar';", "publicName": "Flipar", "url": "https://www.terra.com.br/parceiros/flipar/", "tgmKey": "cobranded_flipar", "hasAdvertising": "Y", "types": ["SRC", "PRT"], "partner": true, "icon": {"id": "4c5a76f7def680ce9bb73d53d9a2ad67ztb635rg", "originalURL": "images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png", "url": "http://images.terra.com/2022/05/27/flipar_favicon_32x32_mascara-vf0upzz9httf.png"}}, "expirationDate": null, "name": null, "configs": {"idItemMenu": "flip"}, "image": null, "cartola": {"id": "187775b4786b2310VgnVCM3000009af154d0RCRD", "path": "Brasil.noticias", "label": "Not\u00edcias", "url": "https://www.terra.com.br/noticias/", "customHat": false, "chooseEditor": false}, "cmsVideo": null, "url": "https://www.terra.com.br/parceiros/flipar/", "title": null, "subTitle": null, "publishedDate": null, "publishedTime": null, "date": null, "time": null, "tgmKey": "br.cobranded_flipar", "paidContent": false}], "fixedPosition": null, "source": null, "expirationDate": null, "name": "app.t360.stories", "configs": {"positionWeb": "2", "tableApp": "true"}, "image": null, "cartola": null, "cmsVideo": null, "url": null, "title": "card.type.AMPStories (12 itens)", "subTitle": null, "publishedDate": "2025-09-07 21:26:15 -0300", "publishedTime": 1757291175000, "date": "2025-06-16 14:10:03 -0300", "time": 1750093803000, "tgmKey": null, "paidContent": false, "menuItems": null, "croupier": {"reasons": {"e": {}}, "score": "i:4.500996,t:0.00,s:0.00,c:0.00,e:0.00,p:0.00,d:0.00,f:4.501"}, "reason_detail_score": "i:4.500996,t:0.00,s:0.00,c:0.00,e:0.00,p:0.00,d:0.00,f:4.501", "reason": "e", "reason_detail": ""}});
	pkg.context.page.set("tagmanAreas", tagmanAreas);
	pkg.context.page.set("tagmanStampFeed", window.tgmkeys);
	pkg.context.page.set("autoplayIds", ['']);
});
